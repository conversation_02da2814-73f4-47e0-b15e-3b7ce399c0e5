namespace Immybot.Shared.Extensions;
public static class ObjectExtensions
{
  /// <summary>
  /// Invoke an action on an object and return it, allowing for chaining.
  /// </summary>
  /// <typeparam name="T"></typeparam>
  /// <param name="item"></param>
  /// <param name="action"></param>
  /// <returns></returns>
  public static T Apply<T>(this T item, Action<T> action)
  {
    action.Invoke(item);
    return item;
  }

  /// <summary>
  /// Invoke a function on an object and returns the result, allowing for chaining.
  /// </summary>
  /// <typeparam name="TInput"></typeparam>
  /// <typeparam name="TOutput"></typeparam>
  /// <param name="item"></param>
  /// <param name="func"></param>
  /// <returns></returns>
  public static TOutput Apply<TInput, TOutput>(this TInput item, Func<TInput, TOutput> func)
  {
    return func.Invoke(item);
  }

  /// <summary>
  /// Returns an item wrapped in a completed <see cref="Task{T}"/>
  /// </summary>
  /// <typeparam name="T"></typeparam>
  /// <param name="item"></param>
  /// <returns></returns>
  public static Task<T> AsTaskResult<T>(this T item)
  {
    return Task.FromResult(item);
  }
}
