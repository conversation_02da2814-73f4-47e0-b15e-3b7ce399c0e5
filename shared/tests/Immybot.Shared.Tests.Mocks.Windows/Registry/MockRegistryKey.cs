using System.Collections.Concurrent;
using System.Runtime.Versioning;
using Immybot.Shared.Abstractions.Device.Windows;
using Microsoft.Win32;

namespace Immybot.Shared.Tests.Mocks.Windows.Registry;

[SupportedOSPlatform("windows")]
public class MockRegistryKey : IRegistryKey
{
  public required string Name { get; set; }
  public int SubKeyCount => SubKeys.Count;
  public ConcurrentDictionary<string, MockRegistryKey> SubKeys { get; } = [];
  public int ValueCount => Values.Count;
  public ConcurrentDictionary<string, MockRegistryValue> Values { get; } = [];

  public required RegistryView View { get; init; }
  public required bool Writable { get; init; }

  public IRegistryKey CreateSubKey(string subkey)
  {
    return CreateSubKey(subkey, false);
  }

  public IRegistryKey CreateSubKey(string subkey, bool writable)
  {
    ArgumentException.ThrowIfNullOrWhiteSpace(subkey);

    var keyNames = subkey.Split("\\", StringSplitOptions.RemoveEmptyEntries);

    // Range operator ([1..]) will not throw when there's only one element.
    return CreateSubKeyImpl(keyNames[0], keyNames[1..], writable);
  }

  public void DeleteSubKey(string subKey)
  {
    DeleteSubKey(subKey, true);
  }

  public void DeleteSubKey(string subKey, bool throwOnMissingSubKey)
  {
    if (!SubKeys.TryGetValue(subKey, out var result))
    {
      if (throwOnMissingSubKey)
      {
        throw new ArgumentException("Cannot delete a subkey tree because the subkey does not exist.");
      }
      return;
    }

    if (!result.SubKeys.IsEmpty)
    {
      throw new InvalidOperationException("Registry key has subkeys and recursive removes are not supported by this method.");
    }

    _ = SubKeys.TryRemove(subKey, out _);
  }

  public void DeleteSubKeyTree(string subKey)
  {
    DeleteSubKeyTree(subKey, false);
  }

  public void DeleteSubKeyTree(string subKey, bool throwOnMissingSubKey)
  {
    if (!SubKeys.TryRemove(subKey, out _) &&
        throwOnMissingSubKey)
    {
      throw new ArgumentException("Cannot delete a subkey tree because the subkey does not exist.");
    }
  }

  public void DeleteValue(string name)
  {
    DeleteValue(name, true);
  }

  public void DeleteValue(string name, bool throwOnMissingValue)
  {
    if (!Writable)
    {
      throw new UnauthorizedAccessException("Cannot write to the registry key.");
    }

    if (!Values.TryRemove(name, out _) &&
        throwOnMissingValue)
    {
      throw new ArgumentException("No value exists with that name.");
    }
  }

  public void Dispose()
  {
    // Noop.
  }

  public string[] GetSubKeyNames()
  {
    return [.. SubKeys.Keys];
  }

  public object? GetValue(string name)
  {
    if (Values.TryGetValue(name, out var value))
    {
      return value.Value;
    }
    return null;
  }

  public RegistryValueKind GetValueKind(string valueName)
  {
    if (Values.TryGetValue(valueName, out var value))
    {
      return value.Kind;
    }

    throw new IOException("The specified registry key does not exist.");
  }

  public virtual string[] GetValueNames()
  {
    return [.. Values.Keys];
  }

  public IRegistryKey? OpenSubKey(string name, bool writable = false)
  {
    var parts = name.Split("\\", StringSplitOptions.RemoveEmptyEntries);

    var key = this;

    foreach (var part in parts)
    {
      if (!key.SubKeys.TryGetValue(part, out var subkey))
      {
        return null;
      }

      key = subkey;
    }
    return key;
  }

  public void SetValue(string name, object value)
  {
    SetValue(name, value, GetValueKind(value));
  }

  public void SetValue(string name, object value, RegistryValueKind kind)
  {
    if (!Writable)
    {
      throw new UnauthorizedAccessException("Cannot write to the registry key.");
    }
    var valueFake = new MockRegistryValue
    {
      Name = name,
      Value = value,
      Kind = kind
    };
    Values.AddOrUpdate(name, valueFake, (_, _) => valueFake);
  }

  private static RegistryValueKind GetValueKind(object data)
  {
    return data switch
    {
      string => RegistryValueKind.String,
      int => RegistryValueKind.DWord,
      byte[] => RegistryValueKind.Binary,
      long => RegistryValueKind.QWord,
      string[] => RegistryValueKind.MultiString,
      _ => RegistryValueKind.String
    };
  }

  private IRegistryKey CreateSubKeyImpl(string keyName, string[] subkeys, bool writable)
  {
    if (!Writable)
    {
      throw new UnauthorizedAccessException("Cannot write to the registry key.");
    }

    var currentKey = SubKeys.GetOrAdd(keyName, key =>
    {
      var fullPath = $"{Name}\\{key}";
      return new MockRegistryKey
      {
        Name = fullPath,
        View = View,
        // If there are subkeys, the key needs to be writable, since we'll be creating subkeys.
        Writable = subkeys.Length > 0 || writable
      };
    });

    if (subkeys.Length == 0)
    {
      return currentKey;
    }

    return currentKey.CreateSubKeyImpl(subkeys[0], subkeys[1..], writable);
  }
}
