using System;

namespace Immybot.Shared.DataContracts.WindowsRegistry.Exceptions;

/// <summary>
/// Thrown when attempting to set a registry value, and the value's data type
/// does not match the value kind specified.  The Windows registry would normally
/// try to convert the value to the correct data type, but we don't want any
/// unexpected conversions to occur.
/// </summary>
public class RegistryValueDataTypeException : Exception
{
  public RegistryValueDataTypeException() { }
  public RegistryValueDataTypeException(string message) : base(message) { }
  public RegistryValueDataTypeException(string message, Exception inner) : base(message, inner) { }
}
