using Polly;

namespace Immybot.Agent.Persistent.Resilience;

/// <summary>
/// <para>
///   Provides a policies for IO operations in the agent installer service.
/// </para>
/// </summary>
public static class PersistentAgentInstallerIoPolicy
{
  public const string Key = nameof(PersistentAgentInstallerIoPolicy);

  public static TimeSpan Delay { get; } = TimeSpan.FromSeconds(2);
  public static int MaxRetryAttempts { get; } = 5;

  public static void BuildPolicy(ResiliencePipelineBuilder builder)
  {
    builder.AddRetry(new Polly.Retry.RetryStrategyOptions()
    {
      UseJitter = false,
      Delay = Delay,
      MaxRetryAttempts = MaxRetryAttempts,
      ShouldHandle = args =>
      {
        return ValueTask.FromResult(args.Outcome.Exception is IOException);
      }
    });
  }
}
