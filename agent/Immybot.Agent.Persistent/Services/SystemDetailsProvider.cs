using System.Management;
using System.Security.Cryptography;
using System.Text;
using Immybot.Agent.Persistent.Exceptions;
using Microsoft.Azure.Devices.Provisioning.Security;
using Newtonsoft.Json;

namespace Immybot.Agent.Persistent.Services;

public interface ISystemDetailsProvider
{
  /// <summary>
  /// Deprecated.  Do not use.  Only here to update old devices
  /// </summary>
  public string GetBaseBoardSerialNumber(bool strict = true);
  public string GetIdentifier(string pWmiClass, IEnumerable<string> props);
  public string? GetManufacturer();
  public string GetOperatingSystemFullName();
  public string? GetOperatingSystemName();
  public string? GetOperatingSystemArchitecture();
  public string? GetSerialNumber();
  public string GetHardwareId();
  public string GetMainboardId();
  public string? GetTPMId();
  public string GetBIOSId();
  public string GetCPUId();
  public string GetGPUId();
  public bool IsPortable();
  public bool IsDomainController();
}

#pragma warning disable S108 // Nested blocks of code should not be left empty
#pragma warning disable S1751 // Loops with at most one iteration should be refactored
#pragma warning disable S2486 // Generic exceptions should not be ignored
#pragma warning disable S3217 // "Explicit" conversions of "foreach" loops should not be used

public class SystemDetailsProvider : ISystemDetailsProvider
{
  public string GetBaseBoardSerialNumber(bool strict = true)
  {
    try
    {
      var searcher = new ManagementObjectSearcher(@"root\CIMV2", "SELECT * FROM Win32_BaseBoard");
      foreach (ManagementObject wmi in searcher.Get())
      {
        return wmi.GetPropertyValue("SerialNumber")?.ToString() ?? string.Empty;
      }
    }
    catch (Exception ex)
    {
      if (strict)
      {
        throw new InvalidSerialNumberException("Failed to retrieve board serial number", ex);
      }
    }
    if (strict)
    {
      throw new InvalidSerialNumberException();
    }
    return string.Empty;
  }

  public string GetIdentifier(string pWmiClass, IEnumerable<string> props)
  {
    var result = new StringBuilder();
    try
    {
      foreach (ManagementObject item in new ManagementClass(pWmiClass).GetInstances())
      {
        foreach (var prop in props)
        {
          try
          {
            var propResult = item[prop];
            if (propResult is null) continue;
            result.Append(propResult.ToString());
          }
          catch { }
        }
      }
    }
    catch { }
    return result.ToString();
  }

  public string? GetSerialNumber()
  {
    try
    {
      var searcher = new ManagementObjectSearcher(@"root\CIMV2", "SELECT * FROM Win32_Bios");
      foreach (ManagementObject wmi in searcher.Get())
      {
        return wmi.GetPropertyValue("SerialNumber")?.ToString();
      }
    }
    catch
    {
      return null;
    }
    return null;
  }

  public string GetHardwareId()
  {
    var cpu = Hash(GetCPUId());
    var bios = Hash(GetBIOSId());
    var mobo = Hash(GetMainboardId());
    var gpu = Hash(GetGPUId());
    var tpm = GetTPMId() is { } t ? Hash(t) : "NO_TPM";
    return $"IMMYHWID==CPU:{cpu};BIOS:{bios};MOBO:{mobo};GPU:{gpu};TPM:{tpm};";
  }

  public string GetMainboardId()
  {
    return GetIdentifier("Win32_BaseBoard", new[] { "Model", "Manufacturer", "Name", "SerialNumber", "OtherIdentifyingInfo", "PartNumber", "Caption" });
  }

  public string? GetTPMId()
  {
    try
    {
      using var tpm = new SecurityProviderTpmHsm(null);
      return Convert.ToBase64String(tpm.GetEndorsementKey());
    }
    catch { return null; }
  }

  public string GetBIOSId()
  {
    return GetIdentifier("Win32_BIOS", new[] { "Manufacturer", "SMBIOSBIOSVersion", "IdentificationCode", "SerialNumber", "ReleaseDate", "Version" });
  }

  public string GetCPUId()
  {
    return GetIdentifier("Win32_Processor", new[] { "UniqueId", "ProcessorId", "Name", "Manufacturer" });
  }

  public string GetGPUId()
  {
    return GetIdentifier("Win32_VideoController", new[] { "Name", "InstallDate" });
  }

  public string? GetManufacturer()
  {
    try
    {
      var searcher = new ManagementObjectSearcher("SELECT Manufacturer FROM Win32_ComputerSystem");
      foreach (ManagementObject os in searcher.Get())
      {
        return os["Manufacturer"]?.ToString();
      }
    }
    catch (Exception ex)
    {
      throw new InvalidOperatingSystemNameException("Failed to retrieve manufacturer", ex);
    }
    throw new InvalidOperatingSystemNameException("Failed to retrieve manufacturer");
  }

  public string? GetOperatingSystemName()
  {
    try
    {
      var searcher = new ManagementObjectSearcher("SELECT Caption FROM Win32_OperatingSystem");
      foreach (ManagementObject os in searcher.Get())
      {
        return os["Caption"]?.ToString();
      }
    }
    catch (Exception ex)
    {
      throw new InvalidOperatingSystemNameException("Failed to retrieve operating system name", ex);
    }
    throw new InvalidOperatingSystemNameException("Failed to retrieve operating system name");
  }

  public string? GetOperatingSystemArchitecture()
  {
    try
    {
      var searcher = new ManagementObjectSearcher("SELECT OSArchitecture FROM Win32_OperatingSystem");
      foreach (ManagementObject os in searcher.Get())
      {
        return os["OSArchitecture"]?.ToString();
      }
    }
    catch (Exception ex)
    {
      throw new InvalidOperatingSystemArchitectureException("Failed to retrieve operating system architecture", ex);
    }
    throw new InvalidOperatingSystemArchitectureException("Failed to retrieve operating system architecture");
  }

  public string GetOperatingSystemFullName()
    => $"{GetOperatingSystemName()} {GetOperatingSystemArchitecture()}";

  public bool IsPortable()
  {
    try
    {
      var searcher = new ManagementObjectSearcher("SELECT ChassisTypes FROM Win32_SystemEnclosure");
      foreach (ManagementObject os in searcher.Get())
      {
        var serializedTypes = JsonConvert.SerializeObject(os["ChassisTypes"]);
        var types = JsonConvert.DeserializeObject<List<int>>(serializedTypes)
          ?? throw new JsonSerializationException("Failed to deserialize chassis types.");
        // https://docs.microsoft.com/en-us/windows/win32/cimwin32prov/win32-systemenclosure#properties
        return types.Any(a => a == 8 || a == 9 || a == 10 || a == 11 || a == 14);
      }
    }
    catch (Exception ex)
    {
      throw new InvalidPortableDetectionException("Failed to detect whether this device is portable", ex);
    }
    throw new InvalidPortableDetectionException("Failed to detect whether this device is portable");
  }

  public bool IsDomainController()
  {
    try
    {
      var searcher = new ManagementObjectSearcher("Select ProductType from Win32_OperatingSystem");
      foreach (ManagementObject os in searcher.Get())
      {
        var productType = Convert.ToInt32(os["ProductType"]);
        return productType == 2;
      }
    }
    catch (Exception ex)
    {
      throw new InvalidPortableDetectionException("Failed to detect whether this device is a domain controller", ex);
    }
    throw new InvalidPortableDetectionException("Failed to detect whether this device is a domain controller");
  }

  private static string Hash(string input)
  {
    return Convert.ToHexString(SHA1.HashData(Encoding.UTF8.GetBytes(input)));
  }
}
