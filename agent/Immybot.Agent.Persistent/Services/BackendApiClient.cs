using System.Net.Http.Json;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Hosting;

namespace Immybot.Agent.Persistent.Services;
public interface IBackendApiClient
{
  Task<OpResult<RegistrationInformation>> RekeyDevice();
}

internal class BackendApiClient(
  HttpClient _client,
  IRegistrationPersistence _registrationPersistence,
  IHardwareIdentifier _hardwareId,
  IHostApplicationLifetime _hostLifetime) : IBackendApiClient
{
  public async Task<OpResult<RegistrationInformation>> RekeyDevice()
  {
    try
    {
      var configResult = await _registrationPersistence.GetConfigurationParameters();
      if (!configResult.IsSuccess)
      {
        return OpResult.Fail<RegistrationInformation>("Failed to get configuration parameters.");
      }

      if (!Uri.TryCreate(configResult.Value.ReKeyEndpoint, UriKind.Absolute, out var rekeyUri))
      {
        return OpResult.Fail<RegistrationInformation>("Invalid rekey endpoint.");
      }

      var regInfo = configResult.Value;
      var deviceDetails = await _hardwareId.GenerateDeviceDetails();

      var rekeyRequest = new RekeyRequest(regInfo.DeviceId, regInfo.BoardSerialNumber, deviceDetails.SerialNumber);
      var result = await _client.PostAsJsonAsync(rekeyUri, rekeyRequest, _hostLifetime.ApplicationStopping);
      result.EnsureSuccessStatusCode();

      var newRegInfo = await result.Content.ReadFromJsonAsync<RegistrationInformation>(cancellationToken: _hostLifetime.ApplicationStopping);
      if (newRegInfo is null)
      {
        return OpResult.Fail<RegistrationInformation>("Failed to deserialize rekey response.");
      }

      return OpResult.Ok(newRegInfo);
    }
    catch (Exception ex)
    {
      return OpResult.Fail<RegistrationInformation>(ex, "Error while attempting to rekey device.");
    }
  }
}
