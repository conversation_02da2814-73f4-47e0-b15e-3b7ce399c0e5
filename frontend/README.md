# ImmyBot Frontend

## Table of Contents

- [ImmyBot Frontend](#immybot-frontend)
  - [Table of Contents](#table-of-contents)
  - [Installation](#installation)
    - [Setup](#setup)
    - [Clone repo](#clone-repo)
  - [Usage](#usage)
  - [Testing](#testing) - [TODO](#todo)
  - [To deploy](#to-deploy)
  - [Documentation](#documentation)
  - [Versioning](#versioning)
  - [Formatting](#formatting)

## Installation

### Setup

### Clone repo

```powershell
# clone the repo
<NAME_EMAIL>:immense-networks/immybot/immybot

# go into solution directory
cd immybot

# Run the interactive setup script.  This will configure both back-end and front-end.
.\Setup-DevEnvironment.ps1
```

## Usage

- Serve with hot reload at `localhost:5173`:

  ```powershell
  # In frontend directory:
  > yarn serve
  ```

- Build for production with minification:

  ```powershell
  # In frontend directory:
  > yarn build
  ```

- Run linter:

  ```powershell
  # In frontend directory:
  > yarn run lint
  ```

## Testing

- <PERSON> Playwright with UI

  ```cmd
  // In frontend directory:
  yarn e2e --debug
  ```

- <PERSON> Playwright in console

  ```cmd
  // In frontend directory:
  yarn e2e
  ```

- Documentation

  - https://playwright.dev

- Test structure
  - Root test directory is under the folder `pl`
  - Testes are located under the folder `tests/playwright`.

When adding or updating components, add `data-testid` where appropriate.

All form fields should ideally have a `data-testid` attribute that can be used for targeting the element in tests.

## To deploy

```
git commit -m "whatever"
git push origin --tags
git tag x.y.x
git push origin --tags
```

This wil trigger a build in gitlab CI

Login to gitlab CD Pipeline, your tag should be there

Click on the Manual Action Deploy to Production

## Documentation

CoreUI's documentation is hosted on their website [CoreUI](http://coreui.io/)

For a detailed explanation on how things work, check out the [Vue CLI Guide](https://cli.vuejs.org/guide/).

## Versioning

Maintained via [the Semantic Versioning guidelines](http://semver.org/).

## Formatting
