import { test } from "@playwright/test";
import { TargetAssignmentsApi } from "../../../src/api/backend/generated/sdk/TargetAssignmentsApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.TargetAssignmentsController */
export class TargetAssignmentsApiTests extends BaseApiTestSuite<TargetAssignmentsApi> implements RequireTestCoverage<Pick<TargetAssignmentsApi, ApiMethods<TargetAssignmentsApi>>> {
  getChangeRequestDiff = [];
  createChangeRequestForExistingDeployment = [];
  updateChangeRequestForExistingDeployment = [];
  getDeploymentChangeRequest = [];
  getAllChangeRequests = [];
  getAllChangeRequestsForDeployment = [];
  createChangeRequestForNewDeployment = [];
  updateChangeRequestForNewDeployment = [];
  getMaintenanceItemOrder = [];
  updatePriority = [];
  getRecommendedApprovals = [];
  updateRecommendedApprovals = [];
  getAllOptionalTargetAssignmentApprovalsForComputer = [];
  updateOptionalTargetAssignmentApproval = [];
  updateNotesGlobal = [];
  getAllGlobal = [];
  getGlobal = [];
  getGlobalTargetAssignmentType = [];
  createGlobal = [];
  updateGlobal = [];
  deleteGlobal = [];
  updateNotesLocal = [];
  getAllLocal = [];
  batchUpdateLocal = [];
  getDuplicatesLocal = [];
  get = [];
  resolveVisibilityTargetAssignments = [];
  calculateTargetedTenants = [];
  calculateTargetedComputers = [];
  calculateTargetedPersons = [];
  getLocalTargetAssignmentType = [];
  create = [];
  updateTargetAssignment = [];
  deleteTargetAssignment = [];
  overrideLocalTargetAssignment = [];
  overrideGlobal = [];
  duplicate = [];
  migrateToSupersedingAssignment = [];
  migrateToSupersedingAssignmentWhatIf = [];
  migrateDeploymentsToProviderLinks = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(TargetAssignmentsApi.name, async () => new TargetAssignmentsApiTests().runTests());
