import { beforeEach, describe, expect, it, vi } from "vitest";

const mockClearSelection = vi.fn();
const mockRefs = { "computer-immy-list": { clearSelection: mockClearSelection } };

function clearSelection() {
  const list = mockRefs["computer-immy-list"] as { clearSelection: () => void } | undefined;
  if (list) {
    list.clearSelection();
  }
}

describe("computerTable clearSelection", () => {
  beforeEach(() => {
    mockClearSelection.mockClear();
  });

  it("should not throw an error when clearSelection is called with undefined computer-immy-list ref", () => {
    mockRefs["computer-immy-list"] = undefined as any;
    expect(() => {
      clearSelection();
    }).not.toThrow();
  });

  it("should call clearSelection on computer-immy-list ref when it exists", () => {
    mockRefs["computer-immy-list"] = {
      clearSelection: mockClearSelection,
    };
    clearSelection();
    expect(mockClearSelection).toHaveBeenCalled();
  });
});
