[{"softwareVersionID": 1, "displayName": "Foxit PhantomPDF Business", "displayVersion": "8.1.1.1115", "softwareID": 1, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDFBusiness-8.1.1.1115", "installerFile": "FoxitPhantomPDF811_Business_enu_Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" CPDF_DISABLE=1", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Foxit File Association\\n$Result = ((cmd /c assoc .pdf) -like \\\"*foxitphantompdf*\\\")\\n$Message = \\\"\\\"\\nif($result -eq $false)\\n{\\n    $Message = \\\"Foxit is not the default PDF Handler\\\"\\n}\\n $obj = @{}\\n $obj.TestResult = $Result\\n $obj.TestResultMessage = $Message\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\n \\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "7D69CCE13460130F2C4AE65D73B33364", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 2, "displayName": "Mimecast for Outlook", "displayVersion": "7.0.1762.17740", "softwareID": 2, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MimecastforOutlook-7.0.1762.17740", "installerFile": "Mimecast for Outlook 7.0.1762.17740 (32 bit).msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n$block = {\\r\\n    New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n    $Users = (get-process explorer -IncludeUserName).UserName\\r\\n    $Users | %{\\r\\n        $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n    }\\r\\n    $SIDS | %{\\r\\n        (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n    }\\r\\n};\\r\\n \\r\\n$Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n \\r\\n \\r\\n$Message = \\\"Mimecast Plugin is working\\\"\\r\\nif($Result  -eq $false)\\r\\n{\\r\\n    $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n}\\r\\n\\r\\n  \\r\\n$obj = [ordered]@{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "8188D34D536EA551EF24CEFFE27C9E15", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 3, "displayName": "inDrop for Outlook", "displayVersion": "*********", "softwareID": 3, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "inDropforOutlook-*********", "installerFile": "OutlookinDrop_indrop.immense.net_1-*********.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test inDrop LoadBehavior\\n$block = {New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\n$Result = (get-process explorer -IncludeUserName).UserName | %{(New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value} | %{(Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\CloudAttachment.Outlook.AddIn.Connecter\\\\\\') -Name LoadBehavior).LoadBehavior -band 1}}; ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\CloudAttachment.Outlook.AddIn.Connecter\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\n$Message = \\\"\\\"\\nif($Result  -eq $false)\\n{\\n    $Message = \\\"inDrop for Outlook is not set to start with Outlook\\\"\\n}\\n \\n$obj = @{}\\n$obj.TestResult = $Result\\n$obj.TestResultMessage = $Message\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\n  \\n", "testFailedError": "inDrop for Outlook is not set to start with Outlook", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 1, "packageTypeID": 1, "packageHash": "14B43FD3F42F9DB6527156C391EAA04A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 4, "displayName": "Umbrella Roaming Client", "displayVersion": "2.0.255.0", "softwareID": 4, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "UmbrellaRoamingClient-2.0.255.0", "installerFile": "Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 3, "installAction": 5872, "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test OpenDNS against www.internetbadguys.com\\r\\n$OrgInfoPath = \\\"$($env:ProgramData)\\\\OpenDNS\\\\ERC\\\\OrgInfo.json\\\"\\r\\n\\r\\n$DisplayVersion = ((\\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Umbrella Roaming Client\\'})) | %{$_.DisplayVersion}\\r\\n$Result = ($DisplayVersion -ne $null -and $DisplayVersion -notlike \\\"\\\")\\r\\n$ResultMessage = \\\"OpenDNS not found in Programs and Features\\\"\\r\\n\\r\\nif (Test-Path $OrgInfoPath)\\r\\n{\\r\\n    $orginfo = Get-Content -Raw -Path $OrgInfoPath | ConvertFrom-Json\\r\\n    if ([string]::IsNullOrEmpty($orginfo.fingerprint))\\r\\n    {\\r\\n        $Result = $false\\r\\n        $ResultMessage = \\\"OpenDNS is missing organization configuration\\\"\\r\\n    }\\r\\n    else\\r\\n    {\\r\\n        $Result = $true\\r\\n        $ResultMessage = \\'\\'\\r\\n    }\\r\\n\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $ResultMessage\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))", "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 3, "uninstallAction": 6640, "deleted": 0, "packageTypeID": 1, "packageHash": "95E7A158318634912875F6F483B1A0EF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 5, "displayName": "Microsoft Office 365 ProPlus - en-us", "displayVersion": "16.0.10827.20181", "softwareID": 5, "sortOrder": 2, "cachingStrategyID": 4, "relativeCacheSourcePath": "O365ProPlusRetail-Current", "installerFile": "setup.exe", "cacheActionTypeID": 3, "cacheAction": 6579, "installActionTypeID": 3, "installAction": 6904, "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Microsoft Office 2016\\r\\n\\r\\n[bool]$result = $false\\r\\n$associationCheck = ((cmd /c \\\"assoc .docx\\\") 2>&1).ToString() # The \\'.ToString()\\' is so we expect a String regardless of outcome\\r\\nif (!$associationCheck.Contains(\\\"association not found\\\")) {\\r\\n    $associationCheck.Split(\\\"=\\\") | select -skip 1 -first 1 | %{\\r\\n        $result = (cmd /c \\\"ftype $_\\\") -like \\\"*Office16*\\\"\\r\\n    }\\r\\n}\\r\\n\\r\\n$Message = \\\"\\\"\\r\\nif($result -eq $false)\\r\\n{\\r\\n    $Message = \\\"Microsoft Office 2016 not the default handler for Office documents\\\"\\r\\n}\\r\\n \\r\\n$obj = @{}\\r\\n$obj.Result = $result\\r\\n$obj.Message = $Message\\r\\n\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{\\r\\n    $_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")\\r\\n}))  \\r\\n", "testFailedError": "Microsoft Office 2016 not the default handler for Office documents", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$Version = \\\"@Version@\\\"\\r\\n$UpdateClientPath = Get-ItemProperty HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Updates -Name UpdateClientPath | % UpdateClientPath\\r\\ncmd /c \\\"$($env:CommonProgramFiles)\\\\Microsoft Shared\\\\ClickToRun\\\\OfficeC2RClient.exe\\\" /update user updatetoversion=$Version forceappshutdown=True displaylevel=False\\r\\nsleep -Seconds 5\\r\\n$UpdateProcess = gwmi win32_process | ?{$_.CommandLine -like \\\"`\\\"$UpdateClientPath`\\\" /update\\\" }\\r\\nWait-Process -Id $UpdateProcess.Handle", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@\\\"$InstallerFile\\\" /configure uninstall.xml", "deleted": 0, "packageTypeID": 1, "packageHash": "FBA1FB9B8C90AF6968807B01595BDFBC", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 6, "displayName": "Immense Direct", "displayVersion": "15.4.1", "softwareID": 6, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ImmenseDirect-15.4.1", "installerFile": "Immense Direct.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\n$Executables = @()\\n$Paths | %{\\n    $Path = $_\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\n    {\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\n    }\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\n    $ExecutableNames | %{\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\n    }\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\n}\\n\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'Dudley*Machine*\\'}\\n\\n$ProgramsToUninstall | %{\\n    $Program = $_\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\n}\\n\\n\\n\\n$Result = $true\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\n$obj = @{}\\n$obj.TestResult = $Result\\n$obj.TestResultMessage = $Message\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\n  \\n", "deleted": 0, "packageTypeID": 1, "packageHash": "D727FAD87964EB60ED3422560534CDEB", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 7, "displayName": "Microsoft Office 365 Business - en-us", "displayVersion": "16.0.10827.20181", "softwareID": 16, "sortOrder": 1, "cachingStrategyID": 4, "relativeCacheSourcePath": "O365BusinessRetail-Current", "installerFile": "setup.exe", "cacheActionTypeID": 3, "cacheAction": 6580, "installActionTypeID": 3, "installAction": 6905, "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Microsoft Office 2016\\r\\n\\r\\n[bool]$result = $false\\r\\n$associationCheck = ((cmd /c \\\"assoc .docx\\\") 2>&1).ToString() # The \\'.ToString()\\' is so we expect a String regardless of outcome\\r\\nif (!$associationCheck.Contains(\\\"association not found\\\")) {\\r\\n    $associationCheck.Split(\\\"=\\\") | select -skip 1 -first 1 | %{\\r\\n        $result = (cmd /c \\\"ftype $_\\\") -like \\\"*Office16*\\\"\\r\\n    }\\r\\n}\\r\\n\\r\\n$Message = \\\"\\\"\\r\\nif($result -eq $false)\\r\\n{\\r\\n    $Message = \\\"Microsoft Office 2016 not the default handler for Office documents\\\"\\r\\n}\\r\\n \\r\\n$obj = [ordered]@{}\\r\\n$obj.Result = $result\\r\\n$obj.Message = $Message\\r\\n\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{\\r\\n    $_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")\\r\\n}))", "testFailedError": "Microsoft Office 2016 not the default handler for Office documents", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$Version = \\\"@Version@\\\"\\r\\n$UpdateClientPath = Get-ItemProperty HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Updates -Name UpdateClientPath | % UpdateClientPath\\r\\ncmd /c \\\"$($env:CommonProgramFiles)\\\\Microsoft Shared\\\\ClickToRun\\\\OfficeC2RClient.exe\\\" /update user updatetoversion=$Version forceappshutdown=True displaylevel=False\\r\\nsleep -Seconds 5\\r\\n$UpdateProcess = gwmi win32_process | ?{$_.CommandLine -like \\\"`\\\"$UpdateClientPath`\\\" /update\\\" }\\r\\nWait-Process -Id $UpdateProcess.Handle\\r\\n", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@\\\"$InstallerFile\\\" /configure uninstall.xml", "deleted": 0, "packageTypeID": 1, "packageHash": "646EB90A2EE2EC97785B630BA6DC1B55", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 8, "displayName": "FortiClient", "displayVersion": "5.4.2.860", "softwareID": 8, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "FortiClient-5.4.2.0860", "installerFile": "FortiClientSetup_5.4.2.0860_x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 3, "installAction": 6586, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Forticlient\\r\\n\\r\\n$Result = (\\'hklm:\\\\SOFTWARE\\\\WOW6432Node\\\\Fortinet\\\\FortiClient\\\\IPSec\\\\Tunnels\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue ).Count -ne 0\\r\\n$Message = \\\"\\\"\\r\\nif($Result  -eq $false)\\r\\n{\\r\\n    $Message = \\\"VPN Profiles Missing\\\"\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "testFailedError": "VPN Profiles Missing", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "rem @start \\\"\\\" /b /wait wmic product where name=\\\"FortiClient\\\" call uninstall /nointeractive\\r\\n@start \\\"\\\" /b /wait msiexec /X{BDFE6AD5-567D-4441-A3A2-E84F02769682} /qn ANCESTORFOUND=1\\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "094E7A391D6B7E7D45CC6C2D796A7396", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 9, "displayName": "Mimecast for Outlook", "displayVersion": "7.1.1853.18350", "softwareID": 2, "sortOrder": 2, "cachingStrategyID": 0, "relativeCacheSourcePath": "MimecastforOutlook-7.1.1853.18350", "installerFile": "Mimecast for Outlook 7.1.1853.18350 (32 bit).msi", "cacheActionTypeID": 2, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n $block = {\\r\\n     New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n     $Users = (get-process explorer -IncludeUserName).UserName\\r\\n     $Users | %{\\r\\n         $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n     }\\r\\n     $SIDS | %{\\r\\n         (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n     }\\r\\n };\\r\\n  \\r\\n $Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n  \\r\\n  \\r\\n $Message = \\\"Mimecast Plugin is working\\\"\\r\\n if($Result  -eq $false)\\r\\n {\\r\\n     $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n }\\r\\n \\r\\n   \\r\\n $obj = [ordered]@{}\\r\\n $obj.TestResult = $Result\\r\\n $obj.TestResultMessage = $Message\\r\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 0, "upgradeActionTypeID": 2, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "08096DE26C03382DE77784E3E4061DA9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 10, "displayName": "Snagit 11", "displayVersion": "11.4.3", "softwareID": 9, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "SnagIt11-11.4.3.280", "installerFile": "Snagit11.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\" & cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "26EA098B1080CA3CCDFB1EC800BC129C", "productKeyRegExFilter": ".*", "productKeyDisplayName": "TechSmith snagit 11"}, {"softwareVersionID": 11, "displayName": "Immense Package Manager", "displayVersion": "0.10.3", "softwareID": 10, "sortOrder": 2, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@powershell -NoProfile -ExecutionPolicy Bypass -Command \\\"$env:chocolateyVersion = \\'0.10.3\\'; iex ((New-Object System.Net.WebClient).DownloadString(\\'https://chocolatey.org/install.ps1\\'))\\\" && SET \\\"PATH=%PATH%;%ALLUSERSPROFILE%\\\\chocolatey\\\\bin\\\" && cls", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Chocolatey\\n\\n$Result = (cmd /c \\\"choco\\\") -like \\\"*chocolatey*\\\"\\n$Message = \\\"\\\"\\nif($Result  -eq $false)\\n{\\n    $Message = \\\"Software Missing\\\"\\n}\\n\\n$obj = @{}\\n$obj.TestResult = $Result\\n$obj.TestResultMessage = $Message\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\n  \\n", "testFailedError": "Chocolatey Not Installed", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Remove-Item -Recurse -Force \\\"$env:ChocolateyInstall\\\" -WhatIf", "deleted": 1, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 12, "displayName": "Powershell 5.0", "displayVersion": "5.0.10586.117", "softwareID": 11, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "C:\\\\ProgramData\\\\Chocolatey\\\\choco.exe install powershell --version 5.0.10586.20170115 /y", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Powershell\\r\\n\\r\\n$Result = $psversiontable.PSVersion.Major -eq 5\\r\\n$Message = \\\"\\\"\\r\\nif($Result  -eq $false)\\r\\n{\\r\\n    $Message = \\\"Powershell out of date\\\"\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "testFailedError": "Powershell 5 Not Installed", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@dir", "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 13, "displayName": "Immense Direct", "displayVersion": "1.21.0", "softwareID": 6, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "ImmenseDirect-1.21.0", "installerFile": "Immense Direct.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\n$Executables = @()\\n$Paths | %{\\n    $Path = $_\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\n    {\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\n    }\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\n    $ExecutableNames | %{\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\n    }\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\n}\\n\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'Dudley*Machine*\\'}\\n\\n$ProgramsToUninstall | %{\\n    $Program = $_\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\n}\\n\\n\\n\\n$Result = $true\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\n$obj = @{}\\n$obj.TestResult = $Result\\n$obj.TestResultMessage = $Message\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\n  \\n", "deleted": 0, "packageTypeID": 1, "packageHash": "903400E8495C5D8246649C3D68B5DD2B", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 14, "displayName": "Foxit PhantomPDF Business", "displayVersion": "8.2.0.2192", "softwareID": 1, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDFBusiness-8.2.0.2192", "installerFile": "FoxitPhantomPDF82_enu_Setup.msi", "cacheActionTypeID": 0, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" CPDF_DISABLE=1 ProductName=\\\"Foxit PDF Editor\\\" CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Foxit File Association\\r\\n $Result = ((cmd /c assoc .pdf) -like \\\"*foxitphantompdf*\\\")\\r\\n $Message = \\\"\\\"\\r\\n if($result -eq $false)\\r\\n {\\r\\n     $Message = \\\"Foxit is not the default PDF Handler\\\"\\r\\n }\\r\\n  $obj = @{}\\r\\n  $obj.TestResult = $Result\\r\\n  $obj.TestResultMessage = $Message\\r\\n  [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "5B94E5D691766073B3B26BE318C1EE6B", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 15, "displayName": "3CXPhone for Windows", "displayVersion": "15.0.60903", "softwareID": 12, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "3CXPhone-15.0.60903", "installerFile": "3CXPhoneforWindows15.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "48E4D368343B745102CF72AAD539E105", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 16, "displayName": "DDB Ticket Portal", "displayVersion": "1.26.0", "softwareID": 14, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "DDBTicketPortal-1.26", "installerFile": "Dudley <PERSON>.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\r\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\r\\n$Executables = @()\\r\\n$Paths | %{\\r\\n    $Path = $_\\r\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\r\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\r\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\r\\n    {\\r\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\r\\n    }\\r\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\r\\n    $ExecutableNames | %{\\r\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\r\\n    }\\r\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\r\\n}\\r\\n\\r\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'Dudley*Machine*\\'}\\r\\n\\r\\n$ProgramsToUninstall | %{\\r\\n    $Program = $_\\r\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\r\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n$Result = $true\\r\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "deleted": 1, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 17, "displayName": "Needles 4.8.2", "displayVersion": "4.8.2.9.091", "softwareID": 15, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Needles-4.8.2.9.091", "installerFile": "482Install.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "2481415D48B2A2775CE68A320B9569CF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 18, "displayName": "Immense Direct", "displayVersion": "1.28.0", "softwareID": 6, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "ImmenseDirect-1.28.0", "installerFile": "Immense Direct.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\r\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\r\\n$Executables = @()\\r\\n$Paths | %{\\r\\n    $Path = $_\\r\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\r\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\r\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\r\\n    {\\r\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\r\\n    }\\r\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\r\\n    $ExecutableNames | %{\\r\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\r\\n    }\\r\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\r\\n}\\r\\n\\r\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'Dudley*Machine*\\'}\\r\\n\\r\\n$ProgramsToUninstall | %{\\r\\n    $Program = $_\\r\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\r\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n$Result = $true\\r\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "24E848B4478DF124DD2D759199B07E76", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 19, "displayName": "DDB Ticket Portal", "displayVersion": "1.29.0", "softwareID": 14, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "DDBTicketPortal-1.28.0", "installerFile": "DDB Ticket Portal.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\r\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\r\\n$Executables = @()\\r\\n $Paths | %{\\r\\n    $Path = $_\\r\\n    $Icon = $Path + \\\"\\\\Desktop\\\\ddb ticket portal.lnk\\\"\\r\\n    $Startup = $Path + \\\"\\\\AppData\\\\Roaming\\\\Microsoft\\\\Windows\\\\Start Menu\\\\Programs\\\\Startup\\\\ddb ticket portal.lnk\\\"\\r\\n    $Reg = \\\"HKLM:\\\\SOFTWARE\\\\WOW6432Node\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run\\\\deskdirectorportalMachineInstaller\\\"\\r\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\r\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\r\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\r\\n    {\\r\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\r\\n    }\\r\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\r\\n    $ExecutableNames | %{\\r\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\r\\n    }\\r\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $Icon -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $Startup -Recurse -Force -ErrorAction Silently Continue\\r\\n    Remove-Item $Reg -Recurse -Force -ErrorAction SilentlyContinue\\r\\n} \\r\\n\\r\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'DDB Ticket*\\'}\\r\\n\\r\\n$ProgramsToUninstall | %{\\r\\n    $Program = $_\\r\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\r\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n$Result = $true\\r\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n  \\r\\n \\r\\n", "deleted": 1, "packageTypeID": 1, "packageHash": "Compress-Archive : Exception cal", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 20, "displayName": "3CXPhone for Windows", "displayVersion": "15.5.1694.0", "softwareID": 12, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "3CXPhone-15.5.1694.0", "installerFile": "3CXPhoneforWindows15.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "95C349D9FED7C48513359E695FC4A557", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 21, "displayName": "Visual C++ Redistributables", "displayVersion": 1, "softwareID": 17, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@choco install vcredist-all -y -force", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 1, "upgradeAction": "choco upgrade vcredist-all", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@choco uninstall vcredist-all", "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 22, "displayName": "ManicTime", "displayVersion": 1, "softwareID": 18, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "D738D97CB05B4BF6A8D68D21223F90EA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 23, "displayName": "ManicTime", "displayVersion": "3.8.6.0", "softwareID": 18, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "ManicTime-3.8.6.0", "installerFile": "ManicTimeSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" CREATESTARTMENUSHORTCUT=0 RUNFORALLUSERS=1\\r\\nreg add HKLM\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run /v ManicTime /t REG_SZ /d \\\"\\\\\\\"C:\\\\Program Files (x86)\\\\Manictime\\\\ManicTime.exe\\\\\\\" /minimized\\\" && REM", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd /c msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "AB546DB941016FD745AB44C251AA94A9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 24, "displayName": "Veeam Agent for Microsoft Windows", "displayVersion": "2.0.0.700", "softwareID": 19, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "VeeamAgentWindows-2.0.0.700", "installerFile": "VeeamAgentWindows_2.0.0.700.exe", "cacheActionTypeID": 0, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@ \\\"$InstallerFile\\\" /Silent /AcceptEula\\r @ \\\"%ProgramFiles%\\\\Veeam\\\\Endpoint backup\\\\veeam.agent.configurator.exe\\\" -license /f:\\\"$LicenseFilePath\\\"\\r && rem", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": "$env:username", "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": "$env:username", "deleted": 0, "packageTypeID": 1, "packageHash": "D5C3FB9DD935CF669BF304C829EB2126", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 33, "displayName": "Foxit PhantomPDF", "displayVersion": "8.3.1.21155", "softwareID": 1, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDFBusiness-8.3.1.21155", "installerFile": "FoxitPhantomPDF831_enu_Setup.msi", "cacheActionTypeID": 0, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" CPDF_DISABLE=1 ProductName=\\\"Foxit PDF Editor\\\" CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Foxit File Association\\r\\n $Result = ((cmd /c assoc .pdf) -like \\\"*foxitphantompdf*\\\")\\r\\n $Message = \\\"\\\"\\r\\n if($result -eq $false)\\r\\n {\\r\\n     $Message = \\\"Foxit is not the default PDF Handler\\\"\\r\\n }\\r\\n  $obj = @{}\\r\\n  $obj.TestResult = $Result\\r\\n  $obj.TestResultMessage = $Message\\r\\n  [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "84166F9C7E08F8A6A66A2118B4CA0249", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 34, "displayName": "Mimecast for Outlook", "displayVersion": "7.3.2020.19910", "softwareID": 2, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "MimecastforOutlook-7.3.2020.19910", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\nif(\\'@TargetFolderPath@\\' -like \\'*TargetFolderPath*\\')\\r\\n{\\r\\n    $TargetFolderPath = \\'C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-7.3.2020.19910\\'\\r\\n    $InstallerLogFile = \\'C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-7.3.2020.19910\\\\Install.log\\'\\r\\n}\\r\\n$Platform = Get-ItemProperty -Path HKLM:\\\\SOFTWARE\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Configuration -Name Platform | select -ExpandProperty Platform\\r\\nif($Platform -like \\\"x86\\\")\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook 7.3.2020.19910 (32 bit).msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook 7.3.2020.19910 (64 bit).msi\\\"\\r\\n}\\r\\n\\r\\nStart-Process -Wait msiexec -ArgumentList \\\"/i `\\\"$TargetFolderPath\\\\$FileName`\\\" /quiet /l*v `\\\"$InstallerLogFile`\\\" Global.AutoUpdate.Enable=FALSE\\\";\\r\\n \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n $block = {\\r\\n     New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n     $Users = (get-process explorer -IncludeUserName).UserName\\r\\n     $Users | %{\\r\\n         $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n     }\\r\\n     $SIDS | %{\\r\\n         (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n     }\\r\\n };\\r\\n  \\r\\n $Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n  \\r\\n  \\r\\n $Message = \\\"Mimecast Plugin is working\\\"\\r\\n if($Result  -eq $false)\\r\\n {\\r\\n     $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n }\\r\\n \\r\\n   \\r\\n $obj = [ordered]@{}\\r\\n $obj.TestResult = $Result\\r\\n $obj.TestResultMessage = $Message\\r\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 2, "upgradeActionTypeID": 1, "upgradeAction": "asdf", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "FF4F50660A8528D6E8BE9E8CD99B18C3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 35, "displayName": "<PERSON><PERSON>", "displayVersion": "4.9.1", "softwareID": 15, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "Needles-4.9.1", "installerFile": "491Install.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@ start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\"\\r && cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "DD267D702648FCD996C47A72D9517C89", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 36, "displayName": "SQL Anywhere 12", "displayVersion": "12.1.4104", "softwareID": 21, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "SQLAnywhere12-12.1.4104", "installerFile": "setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait $InstallerFile /S \\\"/v: /qn REGKEY=$LicenseValue ML32=0 ML64=0 RS32=0 RS64=0 SAMPLES=0 MOBILE=0 SR32=0 SR64=0 UL=0 AT32=1 AT64=1 SA32=1 SA64=1 SM32=1 SM64=1 CREATE_SC_DT_SHORTCUT=0 /l*v! $InstallerLogFile\\\"\\r && rem", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec.exe /qn /uninstall {1DFA77E6-91B2-4DCC-B8BE-98EA70705D39}", "deleted": 0, "packageTypeID": 1, "packageHash": "91E739C2EF93CEA42E38E31678F4B35C", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 37, "displayName": "Password Reset Server Login Client", "displayVersion": "1.4.0", "softwareID": 22, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ThycoticPRSClient-1.4.0", "installerFile": "PRSLoginInstaller.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\"\\r\\n && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec.exe /qn /uninstall  {568F8040-1EA6-4175-BF90-6F23E3E505F4}", "deleted": 0, "packageTypeID": 1, "packageHash": "3A45D670CA094EE2270E72B2C90C2229", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 38, "displayName": "GlobalProtect", "displayVersion": "4.0.2", "softwareID": 23, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.0.2", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\n}\\nelse\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\n}\\n\\n$args = @\\\"\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\n\\\"@", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 1, "packageHash": "37CEB8E40FFFB3926E20999D0062E8DE", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 39, "displayName": "Bomgar Jump Client 16.1.4 [support.iscgrp.com]", "displayVersion": "16.1.4", "softwareID": 24, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ISCBomgar-16.1.4", "installerFile": "bomgar-scc-w0dc30g6fiyzdx18igehyhhgz11g7exd8fihzfc40jc90.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\"\\r\\nrem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$UninstallString = (Get-package | ?{$_.Name -like \\\"Bomgar Jump Client*16.1.4*[support.iscgrp.com]*\\\"}).Meta.Attributes[\\\"UninstallString\\\"]\\r\\nif($UninstallString -ne $null)\\r\\n{\\r\\n    iex ((iex \\\"echo $UninstallString\\\")[0] + \\\" -uninstall silent\\\")\\r\\n}", "deleted": 0, "packageTypeID": 2, "packageHash": "9852BD2F2FEC92DE440E48A9CB798455", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 40, "displayName": "Microsoft Office Professional Plus 2016", "displayVersion": "16.0.4266.1001", "softwareID": 25, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Office2016ProPlus-16.0.4266.1001", "installerFile": "setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 3, "installAction": 6903, "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Microsoft Office 2016\\r\\n\\r\\n$Result = (cmd /c \\\"assoc .docx\\\").Split(\\\"=\\\")[1] | %{ (cmd /c \\\"ftype $_\\\") -like \\\"*Office16*\\\" }\\r\\n$Message = \\\"\\\"\\r\\nif($Result  -eq $false)\\r\\n{\\r\\n    $Message = \\\"Microsoft Office 2016 not the default handler for Office documents\\\"\\r\\n}\\r\\n \\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n  \\r\\n", "testFailedError": "Microsoft Office 2016 not the default handler for Office documents", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {90160000-0011-0000-1000-0000000FF1CE} /qn", "deleted": 0, "packageTypeID": 1, "packageHash": "4D15A7EAB768098F7C74CACE1B759F22", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 41, "displayName": "Proofpoint Encryption Plug-in for Microsoft Outlook", "displayVersion": "1.3.5.0", "softwareID": 26, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "ProofpointEncryptionPlugin-1.3.5.0", "installerFile": "PE_Plugin_Outlook_1.3.5.0_x64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@ start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && cls\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd.exe /c msiexec /X{6DF7E684-7C14-42BD-A5BA-2B29A16E5716}", "deleted": 0, "packageTypeID": 2, "packageHash": "4020A20E4D2B413E0967FEF966453ECA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 42, "displayName": "Proofpoint Email Feedback Plug-in for Microsoft Outlook", "displayVersion": "2.1.8.0", "softwareID": 27, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ProofpointEmailFeedbackPlugin-2.1.8.0", "installerFile": "ProofpointEmailFeedback_2.1.8.0_x64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@ start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && cls\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "C47C2CA9934DB0674645A425801C6702", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 43, "displayName": "Proofpoint Secure Share Plug-in for Microsoft Outlook", "displayVersion": "1.3.9.0", "softwareID": 28, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ProofpointSecureSharePlugin-1.3.9.0", "installerFile": "ProofpointSecureSharePlugin_1.3.9.0_x64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@ start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd.exe /c msiexec /X{73B03BFA-C471-4611-A7DD-33227EBCE118}", "deleted": 0, "packageTypeID": 2, "packageHash": "F9A429CF358B0B8AB4D944DC1086B645", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 44, "displayName": "McAfee Agent", "displayVersion": "5.0.6001", "softwareID": 29, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeAgent-5.0.6001", "installerFile": "FramePkg.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"McAfee\\\" /b /wait \\\"$InstallerFile\\\" /Install=Agent /Silent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {80684F9A-6B01-4F3F-A8C7-C4B7BDF072F1} /qn FORCEUNINSTALL=1", "deleted": 0, "packageTypeID": 1, "packageHash": "73B912E54FC9A818C090C8CCFB1F84FA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 45, "displayName": "McAfee Endpoint Security Adaptive Threat Protection", "displayVersion": "10.5.1", "softwareID": 30, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeAdaptiveThreatProtection-10.5.1", "installerFile": "setupATP.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\"\\r && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "05A13F2CC23BA70AD1020FE10FE83176", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 46, "displayName": "McAfee Endpoint Security Threat Prevention", "displayVersion": "10.5.1", "softwareID": 32, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeSecurityThreatPrevention-10.5.1", "installerFile": "SetupTP.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\"\\r && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait MsiExec.exe /X{4F574B83-3AE0-419F-8A3B-985C389334B4} /qn", "deleted": 0, "packageTypeID": 1, "packageHash": "77D2C01E3633154058B14E462B8EA2C3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 47, "displayName": "Umbrella Roaming Client", "displayVersion": "2.0.338.0", "softwareID": 4, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "UmbrellaRoamingClient-2.0.338.0", "installerFile": "Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 3, "installAction": 5872, "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test OpenDNS against www.internetbadguys.com\\r\\n$OrgInfoPath = \\\"$($env:ProgramData)\\\\OpenDNS\\\\ERC\\\\OrgInfo.json\\\"\\r\\n\\r\\n$DisplayVersion = ((\\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Umbrella Roaming Client\\'})) | %{$_.DisplayVersion}\\r\\n$Result = ($DisplayVersion -ne $null -and $DisplayVersion -notlike \\\"\\\")\\r\\n$ResultMessage = \\\"OpenDNS not found in Programs and Features\\\"\\r\\n\\r\\nif (Test-Path $OrgInfoPath)\\r\\n{\\r\\n    $orginfo = Get-Content -Raw -Path $OrgInfoPath | ConvertFrom-Json\\r\\n    if ([string]::IsNullOrEmpty($orginfo.fingerprint))\\r\\n    {\\r\\n        $Result = $false\\r\\n        $ResultMessage = \\\"OpenDNS is missing organization configuration\\\"\\r\\n    }\\r\\n    else\\r\\n    {\\r\\n        $Result = $true\\r\\n        $ResultMessage = \\'\\'\\r\\n    }\\r\\n\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $ResultMessage\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))", "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 3, "uninstallAction": 6640, "deleted": 0, "packageTypeID": 1, "packageHash": "FAB8EA0DB5850212747EC11098733D9B", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 48, "displayName": "Orca", "displayVersion": "3.1.3790.0000", "softwareID": 34, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Orca-3.1.3790.0", "installerFile": "orca.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package Orca | Uninstall-Package", "deleted": 0, "packageTypeID": 1, "packageHash": "21B20111C91AC501E52E0BF8071DC1BC", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 49, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.2.32", "softwareID": 33, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.2.32", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 1, "packageHash": "C8DC280DC115B4E4E8E7488D015DDDE4", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 50, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.4.5506.0", "softwareID": 35, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.4.5506.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "C2899D930D8945E3A2DDDC2A2B7510D3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 51, "displayName": "McAfee Endpoint Security Platform", "displayVersion": "10.5.1", "softwareID": 31, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeEndpointSecurityPlatform-10.5.1", "installerFile": "setupCC.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\"\\r && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "7653A91FC8673A68B47603E1079E979C", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 52, "displayName": "Microsoft .NET Framework 4.5.1", "displayVersion": "4.5.1.20140606", "softwareID": 36, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@choco install dotnet4.5.1 -y", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 53, "displayName": "McAfee File and Removable Media Protection", "displayVersion": "5.0.3.105", "softwareID": 37, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeFRMProtection-5.0.3.105", "installerFile": "eeff64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "6D0B112AF7EEEDA95ED8214EAB90C976", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 54, "displayName": "McAfee Drive Encryption Agent", "displayVersion": "7.2.1.16", "softwareID": 38, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDEAgent-7.2.1.16", "installerFile": "MfeEEAgent64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"McAfee Drive Encryption\\\";\\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "466C5AC13BCCD60F3C8AFF2768F0B62A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 55, "displayName": "McAfee Drive Encryption", "displayVersion": "7.2.1.16", "softwareID": 39, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDE-7.2.1.16", "installerFile": "MfeEEPc64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n  \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"McAfee Drive Encryption\\\";", "deleted": 0, "packageTypeID": 1, "packageHash": "C9FCA68A7F593D5867B8FBE19D11F5A3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 56, "displayName": "Veeam Backup & Replication", "displayVersion": "9.5.0.1038", "softwareID": 40, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "VeeamBackupReplication-9.5.0.1038", "installerFile": "VeeamBackupReplication_9.5.0.1038.Update2.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 3, "upgradeActionTypeID": 2, "upgradeAction": "$psDisableJobs = @\\'\\r\\n# Step 1. Disable and save running jobs to file\\r\\nAdd-PSSnapin VeeamPSSnapin\\r\\n# Disable VBR jobs\\r\\n$jobs = Get-VBRJob | ?{$_.IsRunning -and $_.IsBackupSync -or $_.SQLEnabled -or $_.OracleEnabled} | %{$_.Name} \\r\\n$jobs | Out-File @TargetFolderPath@\\\\running_jobs.txt\\r\\nDisable-VBRJob -Job $jobs \\r\\n# Enable EP jobs\\r\\n$epjobs = Get-VBREPJob | ?{$_.IsEnabled} | %{$_.Name} \\r\\n$epjobs | Out-File @TargetFolderPath@\\\\running_epjobs.txt\\r\\n$epjobs | %{ Get-VBREPJob -Name $_ | Disable-VBREPJob }\\r\\n\\r\\n\\'@ | Set-Content -Path @TargetFolderPath@\\\\Disable-VBRRunningJobs.ps1\\r\\nStart-Process -wait powershell.exe -ArgumentList \\\"-file @TargetFolderPath@\\\\Disable-VBRRunningJobs.ps1\\\"\\r\\n# Step 2. Kill the GUI\\r\\nGet-Process veeam.backup.shell | Stop-Process -force\\r\\nStart-Process -Wait $InstallerFile (\\'/silent /noreboot /log \\\"$InstallerLogFile\\\" VBR_AUTO_UPGRADE=1\\')\\r\\n$psEnableJobs = @\\'\\r\\n# Step 3. Re-enable disabled jobs\\r\\nAdd-PSSnapin VeeamPSSnapin\\r\\n# Enable VBR jobs\\r\\nGet-Content @TargetFolderPath@\\\\running_jobs.txt | Enable-VBRJob \\r\\n# Enable EP jobs\\r\\nGet-Content @TargetFolderPath@\\\\running_epjobs.txt | %{ Get-VBREPJob -Name $_ | Enable-VBREPJob }\\r\\n\\'@ | Set-Content -Path @TargetFolderPath@\\\\Enable-VBRRunningJobs.ps1\\r\\nStart-Process -wait powershell.exe -ArgumentList \\\"-file @TargetFolderPath@\\\\Enable-VBRRunningJobs.ps1\\\"\\r\\nGet-Location", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "D581AC718DE61BE5BB8BCCC781A91D41", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 57, "displayName": "Powershell 5.0", "displayVersion": "5.1.14409.1005", "softwareID": 11, "sortOrder": 2, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([System.Environment]::OSVersion.Version.Major -lt 10)\\r\\n{\\r\\n    choco install dotnet4.5 -y\\r\\n}\\r\\n\\r\\nSet-Service wuauserv -StartupType Automatic -ErrorAction SilentlyContinue\\r\\nStart-Service wuauserv\\r\\nchoco install powershell --version 5.1.14409.20170510 -y", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Powershell\\r\\n\\r\\n$Result = $psversiontable.PSVersion.Major -eq 5\\r\\n$Message = \\\"\\\"\\r\\nif($Result  -eq $false)\\r\\n{\\r\\n    $Message = \\\"Powershell out of date\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Message = \\\"Success\\\"\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "testFailedError": "Powershell 5 Not Installed", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 59, "displayName": "Snagit 11", "displayVersion": "13.1.3.7993", "softwareID": 9, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Snagit-13.1.3.7993", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\" & cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 1, "packageTypeID": 1, "packageHash": "392A808BB3D354125BAB9E5BE0BA6F77", "productKeyRegExFilter": ".*", "productKeyDisplayName": "TechSmith snagit 13"}, {"softwareVersionID": 60, "displayName": "Snagit 11", "displayVersion": "13.1.3.7993", "softwareID": 9, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Snagit-13.1.3.7993", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\" & cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 1, "packageTypeID": 1, "packageHash": "392A808BB3D354125BAB9E5BE0BA6F77", "productKeyRegExFilter": ".*", "productKeyDisplayName": "TechSmith snagit 13"}, {"softwareVersionID": 61, "displayName": "Snagit 13", "displayVersion": "13.1.3", "softwareID": 9, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Snagit-13.1.3.7993", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\" & cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 1, "packageTypeID": 1, "packageHash": "392A808BB3D354125BAB9E5BE0BA6F77", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 62, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.4.5511.0", "softwareID": 35, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.4.5511.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "51FC3F1955E51E4A814B5C85BD439067", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 63, "displayName": "eClinicalWorks Client 10.0.80 SP1C-5 (4.0)", "displayVersion": "10.0.80", "softwareID": 41, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "eClinicalWorksClient-10.0.30-SP1C-5-4.0", "installerFile": "ClientSetup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" /SP- /VERYSILENT /LOG=\\\"$InstallerLogFile\\\" /NORESTART /CLOSEAPPLICATIONS\\r", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$Package = Get-Package \\\"eClinicalWorks Client 10.0.80 SP1C-5 (4.0)\\\" | select -ExpandProperty meta | %{ $_.Attributes[\\\"QuietUninstallString\\\"] }\\r\\n$Parsed = iex \\\"echo $package\\\"\\r\\nStart-Process -Wait $Parsed[0] -ArgumentList $Parsed[1]", "deleted": 1, "packageTypeID": 1, "packageHash": "FF8A485D67A9212DAB149697FB2C4535", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 64, "displayName": "eClinicalWorks Client 10.0.80 SP1C-5 (4.0)", "displayVersion": "10.0.80", "softwareID": 41, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "eClinicalWorksClient-10.0.30-SP1C-5-4.0", "installerFile": "ClientSetup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" /SP- /VERYSILENT /LOG=\\\"$InstallerLogFile\\\" /NORESTART /CLOSEAPPLICATIONS\\r", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$Package = Get-Package \\\"eClinicalWorks Client 10.0.80 SP1C-5 (4.0)\\\" | select -ExpandProperty meta | %{ $_.Attributes[\\\"QuietUninstallString\\\"] }\\r\\n$Parsed = iex \\\"echo $package\\\"\\r\\nStart-Process -Wait $Parsed[0] -ArgumentList $Parsed[1]", "deleted": 1, "packageTypeID": 1, "packageHash": "FF8A485D67A9212DAB149697FB2C4535", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 65, "displayName": "Bit Titan Arabi Sling", "displayVersion": "1.4.10803.2712", "softwareID": 42, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "BitTitanDeploymentProArabi-1.4.10803.2712", "installerFile": "BitTitanDMASetup_CEB634F9E42D1FA6__.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\"\\r && REM", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "44645E006A056436F5694251A3C35DC3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 66, "displayName": "Bit Titan LMMC", "displayVersion": "1.4.10803.2712", "softwareID": 42, "sortOrder": 2, "cachingStrategyID": 1, "relativeCacheSourcePath": "BitTitanDeploymentProLMMC-1.4.10803.2712", "installerFile": "BitTitanDMASetup_BA9B90F03D4CB3AE__.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\"\\r\\n && REM", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "093E6F00BA69982B2770EBA019BEC630", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 67, "displayName": "Microsoft Teams", "displayVersion": "1.0.0.22051", "softwareID": 43, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MicrosoftTeams-1.0.00.22051", "installerFile": "Teams_windows.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" -s", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "7122A0A45963881F9DCDF573BD60962F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 69, "displayName": "FortiClient", "displayVersion": "5.6.0.1075", "softwareID": 8, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "FortiClient-5.6.0.1075", "installerFile": "FortiClient_5.6.0.1075_x64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "$Message = \\\"\\\"\\r\\n$version = Get-Package | ?{$_.Name -like \\\"FortiClient\\\"} | %{$_.Version}\\r\\n\\r\\nif ($version -ne \\\"%sqlDisplayVersion%\\\")\\r\\n{\\r\\n    $Result = $false\\r\\n    $Message = \\\"Upgrade failed\\\"\\r\\n}\\r\\nelseif (!(\\'hklm:\\\\SOFTWARE\\\\WOW6432Node\\\\Fortinet\\\\FortiClient\\\\IPSec\\\\Tunnels\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue ).Count -ne 0)\\r\\n{\\r\\n    $Result = $false\\r\\n    $Message = \\\"VPN Profiles Missing\\\"\\r\\n} else\\r\\n{\\r\\n    $Result = $true\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))", "testFailedError": "VPN Profiles Missing", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X{BDFE6AD5-567D-4441-A3A2-E84F02769682} /qn ANCESTORFOUND=1", "deleted": 0, "packageTypeID": 1, "packageHash": "13D8EB9477CD9282B28F70B33E988AC9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 75, "displayName": "Foxit PhantomPDF", "displayVersion": "8.3.2.25013", "softwareID": 1, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDFBusiness-8.3.2.25013", "installerFile": "FoxitPhantomPDF832_enu_Setup.msi", "cacheActionTypeID": 2, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait taskkill /im FoxitPhantomPDF.exe /f 2>null 1>nul\\r\\n@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" CPDF_DISABLE=1 ProductName=\\\"Foxit PDF Editor\\\" CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Foxit File Association\\r\\n$ExpectedVersion = [System.Version]\\'%sqlDisplayVersion%\\';\\r\\n$DetectedVersion = [System.Version](Get-Package \\\"Foxit PhantomPDF\\\" | select -ExpandProperty Version);\\r\\n\\r\\nif($DetectedVersion -lt $ExpectedVersion)\\r\\n{\\r\\n    $Result = $false\\r\\n    $Message = \\\"Update Available\\\"   \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $Result = ((cmd /c assoc .pdf) -like \\\"*foxitphantompdf*\\\")\\r\\n    $Message = \\\"\\\"\\r\\n    if($result -eq $false)\\r\\n    {\\r\\n        $Message = \\\"Foxit is not the default PDF Handler\\\"\\r\\n    } \\r\\n}\\r\\n\\r\\n\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n  \\r\\n \\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "5E0B15383237EE33F6BD174104808485", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 76, "displayName": "GlobalProtect", "displayVersion": "4.0.3", "softwareID": 23, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.0.3", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\n}\\nelse\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\n}\\n\\n$args = @\\\"\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\n\\\"@", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 1, "packageHash": "E61672DDEE8323F2DB3C4BC0274C53DB", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 77, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.5.5606.0", "softwareID": 35, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.5.5606.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "374377E560A1E33C0D6223741A4C7C96", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 78, "displayName": "eClinicalWorks", "displayVersion": "10.0.186", "softwareID": 41, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "eClinicalWorksClient-**********", "installerFile": "cwclient.zip", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "taskkill /im eclinicalworks.exe /f\\r\\n\\r\\nif((Test-Path \\\"C:\\\\Program Files (x86)\\\"))\\r\\n{\\r\\n    $TargetPath = \\\"C:\\\\Program Files (x86)\\\\eClinicalWorks\\\\\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $TargetPath = \\\"C:\\\\Program Files\\\\eClinicalWorks\\\\\\\"\\r\\n}\\r\\nExpand-Archive \\\"C:\\\\Windows\\\\temp\\\\ManagedSoftwareInstallers\\\\eClinicalWorksClient-**********\\\\cwclient.zip\\\" $TargetPath -Force", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "taskkill /im eclinicalworks.exe /f\\r\\n\\r\\nif((Test-Path \\\"C:\\\\Program Files (x86)\\\"))\\r\\n{\\r\\n    $TargetPath = \\\"C:\\\\Program Files (x86)\\\\eClinicalWorks\\\\\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $TargetPath = \\\"C:\\\\Program Files\\\\eClinicalWorks\\\\\\\"\\r\\n}\\r\\nExpand-Archive \\\"C:\\\\Windows\\\\temp\\\\ManagedSoftwareInstallers\\\\eClinicalWorksClient-**********\\\\cwclient.zip\\\" $TargetPath -Force", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$Package = Get-Package \\\"eClinicalWorks Client 10.0.80 SP1C-5 (4.0)\\\" | select -ExpandProperty meta | %{ $_.Attributes[\\\"QuietUninstallString\\\"] }\\r\\n$Parsed = iex \\\"echo $package\\\"\\r\\nStart-Process -Wait $Parsed[0] -ArgumentList $Parsed[1]", "deleted": 0, "packageTypeID": 1, "packageHash": "4D9B3A755D8F1BD96069C670052C09FF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 79, "displayName": "QuickBooks", "displayVersion": "28.0.4001.2806", "softwareID": 44, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "QuickBooksEnt-28.0.4001.2806", "installerFile": "Qbooks\\\\QuickBooks.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" PARENTAPP=\\\"INSTALLMANAGER\\\" Install=1 MULTIUSERINSTALL=1  AgreeToLicense=yes $LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-package quickbooks", "deleted": 1, "packageTypeID": 1, "packageHash": "9F793555B43B236B76986DD9AFBD4089", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 80, "displayName": "FileMaker Pro 15", "displayVersion": "15.0.4.400", "softwareID": 45, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "FileMakerPro-15.0.4.400", "installerFile": "Files\\\\FileMaker Pro 15.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" $LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"FileMaker 15 Pro\\\";", "deleted": 0, "packageTypeID": 1, "packageHash": "25CD495E5FF4B64938C8059DE193C8D1", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 81, "displayName": "Job Runner Extensions", "displayVersion": "1.0.0.0", "softwareID": 46, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "JobRunnerExtensions-1.0.0.0", "installerFile": "Install.bat", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "72C44B1E652A1B59302C300E12394EB8", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 82, "displayName": "Windows 10 1709 Update", "displayVersion": "10.0.16299.0", "softwareID": 47, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Windows10-10.0.16299.0", "installerFile": "Win10_1709_English_x64.iso", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$ImagePath = \\'$InstallerFile\\';\\r\\n$InstallerLogFolder = Join-Path $env:SystemDrive \\\"Windows10UpgradeLogs\\\"\\r\\nmkdir $InstallerLogFolder -force | out-null\\r\\nwget -Uri \\\"https://msp.immense.net/labtech/transfer/tools/setupdiag.exe\\\" -OutFile \\\"C:\\\\setupdiag.exe\\\"\\r\\nif($ImagePath -like \\\"*installerfile*\\\")\\r\\n{\\r\\n    $ImagePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Windows.iso\\\"\\r\\n}\\r\\n\\r\\nif([System.Environment]::OSVersion.Version.Major -lt 8)\\r\\n{\\r\\n    # install third party image mount\\r\\n    $imdiskresponse = & cmd /c imdisk 2>&1\\r\\n    $imdiskinstalled = ($imdiskresponse | select -first 1) -like \\\"*ImDisk Virtual Disk Driver*\\\"\\r\\n    if($imdiskinstalled -ne $true)\\r\\n    {\\r\\n        if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n        { \\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk-x64.exe\\\"\\r\\n        }\\r\\n        else \\r\\n        {\\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk.exe\\\"\\r\\n        }\\r\\n    \\r\\n        $ISOMountInstallFullPath = \\\"$($env:windir)\\\\temp\\\\$ISOMountInstallFileName\\\"\\r\\n        wget \\\"https://msp.immense.net/LabTech/Transfer/Tools/$ISOMountInstallFileName\\\" -OutFile $ISOMountInstallFullPath \\r\\n        if(Test-Path $ISOMountInstallFullPath)\\r\\n        {\\r\\n            Start-Process -Wait -NoNewWindow cmd.exe \\\"/c `\\\"$ISOMountInstallFullPath`\\\" /fullsilent\\\"\\r\\n        }\\r\\n    }\\r\\n    \\r\\n    #choco install -y imdisk imdisk-toolkit --force\\r\\n    $output = (& \\\"imdisk\\\" -a -m `#: -f $ImagePath)\\r\\n    $DriveLetter = $output[1].substring($output[1].IndexOf(\\\"->\\\") - 3, 1)    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $MountResult = Mount-DiskImage -ImagePath $ImagePath -PassThru\\r\\n    $DriveLetter = ($MountResult | Get-Volume).DriveLetter    \\r\\n}\\r\\n$SetupFile = \\\"$($DriveLetter):\\\\setup.exe\\\"\\r\\n$ArgList = @()\\r\\n$ArgList += \\\"/c $SetupFile\\\"\\r\\n$ArgList += \\\"/auto upgrade\\\"\\r\\n$ArgList += \\\"/migratedrivers all\\\"\\r\\n$ArgList += \\\"/quiet\\\"\\r\\n$ArgList += \\\"/DynamicUpdate disable\\\"\\r\\n$ArgList += \\\"/ResizeRecoveryPartition Disable\\\"\\r\\n$ArgList += \\\"/ShowOOBE none\\\"\\r\\n$ArgList += \\\"/CopyLogs `\\\"$InstallerLogFolder`\\\"\\\"\\r\\n$ArgList += \\\"/telemetry disable\\\"\\r\\n$ArgList += \\\"/noreboot\\\"\\r\\n\\r\\nif(\\\"$LicenseValue\\\" -like \\\"*-*-*-*-*\\\")\\r\\n{\\r\\n    $ArgList += \\\"/pkey $LicenseValue\\\"\\r\\n}\\r\\n\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList $ArgList -WorkingDirectory ($env:windir + \\\"\\\\\\\" + \\\"temp\\\")\\r\\n\\\"Setup.exe exited. Running SetupDiag.exe to collect logs\\\"\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList \\\"/c c:\\\\SetupDiag.exe\\\"\\r\\n$SetupDiagResultsPath = \\\"c:\\\\SetupDiagResults.log\\\"\\r\\nif((Test-Path $SetupDiagResultsPath))\\r\\n{\\r\\n    Get-Content -Tail 50 -Path $SetupDiagResultsPath\\r\\n}\\r\\n\\r\\n\\\"# Setuperr.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupErr.log\\\" -Tail 50\\r\\n\\\"# Setupact.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupAct.log\\\" -Tail 50\\r\\n\\r\\nDismount-DiskImage -ImagePath $ImagePath \\r\\n\\r\\n#Write-Output \\\"SuppressReboot=1\\\"; \\r\\n\\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "5E8BDEF20C4B468F868F1F579197F7CF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 83, "displayName": "Samanage Agent", "displayVersion": "1.1.303", "softwareID": 48, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "SamanageAgent-1.1.303", "installerFile": "Samanage_Agent_1.1.303_1.1.36_203_208_installer.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "start \\\"\\\" /b /wait cmd.exe /c \\\"$InstallerFile\\\" --mode unattended", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "start \\\"\\\" /b /wait cmd /c \\\"C:\\\\Program Files (x86)\\\\Samanage Agent\\\\uninstall.exe\\\" --mode unattended", "deleted": 0, "packageTypeID": 1, "packageHash": "55E87147D4A2CEE33B7518195ADFAD45", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 84, "displayName": "Snagit 2018", "displayVersion": "18.0.0.462", "softwareID": 9, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "Snagit-18.0.0.462", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TSC_SOFTWARE_KEY=$LicenseValue TSC_ALLOW_IDENTITY=0", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Snagit 2018\\\";", "deleted": 0, "packageTypeID": 1, "packageHash": "AA70BD5D04ACFC7B91FE99379677C953", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 85, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.7.5809.0", "softwareID": 35, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.7.5809.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "9AE24880BCEA758FBE937D94EE320433", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 86, "displayName": "GlobalProtect", "displayVersion": "4.0.4", "softwareID": 23, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.0.4", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\n}\\nelse\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\n}\\n\\n$args = @\\\"\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\n\\\"@", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 1, "packageHash": "331C14497667231F20475EF0BB24E074", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 87, "displayName": "EnterpriseIQ Client", "displayVersion": "15.3.0.11680", "softwareID": 49, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "EnterpriseIQClient-15.3.0.11680", "installerFile": "silent.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "start \\\"\\\" /D \\\"@TargetFolderPath@\\\" /b /wait cmd /c \\\"$InstallerFile\\\" silent allusermodules\\r\\nxcopy \\\"@TargetFolderPath@\\\\IQWin32\\\" \\\"C:\\\\Program Files (x86)\\\\IQMS\\\\IQWin32\\\" /syi \\r\\n\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 88, "displayName": "McAfee Drive Encryption Agent", "displayVersion": "7.2.2.14", "softwareID": 38, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDEAgent-7.2.2.14", "installerFile": "MfeEEAgent64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"McAfee Drive Encryption\\\";\\r\\n", "deleted": 1, "packageTypeID": 2, "packageHash": "89F181AC38C941D0C6BE30FBAF4F760E", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 89, "displayName": "McAfee Drive Encryption", "displayVersion": "7.2.2.14", "softwareID": 39, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDE-7.2.2.14", "installerFile": "MfeEEPc64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n  \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"McAfee Drive Encryption\\\";\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "A509CED838FEE1E7C9F74664418F5B26", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 90, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.8.5906.0", "softwareID": 35, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.8.5906.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "DC8FFDD816E68F804901D349FFB6D2B0", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 91, "displayName": "Foxit PhantomPDF", "displayVersion": "8.3.5.30351", "softwareID": 1, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDFBusiness-8.3.5.30351", "installerFile": "FoxitPhantomPDF835_enu_Setup.msi", "cacheActionTypeID": 2, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait taskkill /im FoxitPhantomPDF.exe /f 2>null 1>nul\\r\\n@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" CPDF_DISABLE=1 ProductName=\\\"Foxit PDF Editor\\\" CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test Foxit File Association\\r\\n$ExpectedVersion = [System.Version]\\'%sqlDisplayVersion%\\';\\r\\n$DetectedVersion = [System.Version](Get-Package \\\"Foxit PhantomPDF\\\" | select -ExpandProperty Version);\\r\\n\\r\\nif($DetectedVersion -lt $ExpectedVersion)\\r\\n{\\r\\n    $Result = $false\\r\\n    $Message = \\\"Update Available\\\"   \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $Result = ((cmd /c assoc .pdf) -like \\\"*foxitphantompdf*\\\")\\r\\n    $Message = \\\"\\\"\\r\\n    if($result -eq $false)\\r\\n    {\\r\\n        $Message = \\\"Foxit is not the default PDF Handler\\\"\\r\\n    } \\r\\n}\\r\\n\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n  \\r\\n \\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "A19F630B101C7F04BF26585BBD0616AF", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 92, "displayName": "SpecoTech Multi Tech", "displayVersion": "2.11.15.0", "softwareID": 50, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "SpecoTechMultiTech-2.11.15.0", "installerFile": "SpecoTechMultiTech-CCs.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"SpecoTech Multi Tech\\", "deleted": 0, "packageTypeID": 1, "packageHash": "7AA3D0B50DE819B7C1434F30F4F40E93", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 93, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.8.5907.0", "softwareID": 35, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.8.5907.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "9F334AEBB8CD40C053A96A66B17C82F3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 94, "displayName": "Veeam Backup & Replication", "displayVersion": "9.5.0.1536", "softwareID": 40, "sortOrder": 2, "cachingStrategyID": 1, "relativeCacheSourcePath": "VeeamBackupReplication-9.5.0.1536", "installerFile": "VeeamBackupReplication_9.5.0.1536.Update3.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /silent /noreboot  /log \\\"$InstallerLogFile\\\" VBR_AUTO_UPGRADE=1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 3, "upgradeActionTypeID": 2, "upgradeAction": "$psDisableJobs = @\\'\\r\\n# Step 1. Disable and save running jobs to file\\r\\nAdd-PSSnapin VeeamPSSnapin\\r\\n# Disable VBR jobs\\r\\n$jobs = Get-VBRJob | ?{$_.IsRunning -and $_.IsBackupSync -or $_.SQLEnabled -or $_.OracleEnabled} | %{$_.Name} \\r\\n$jobs | Out-File @TargetFolderPath@\\\\running_jobs.txt\\r\\nDisable-VBRJob -Job $jobs \\r\\n# Step 2. Kill the GUI & Management Agent\\r\\nGet-Process veeam.backup.shell | Stop-Process -force\\r\\nGet-Process veeam.backup.manager | Stop-Process -force\\r\\nGet-Process veeam.backup.satellite | Stop-Process -force\\r\\nGet-Process veeam.backup.cloudservice | Stop-Process -force\\r\\n\\r\\nGet-Process veeam.mbp.agent | Stop-Process -force\\r\\nGet-Process veeam.mbp.agentconfigurator | Stop-Process -force\\r\\n\\r\\n# Enable EP jobs\\r\\n$epjobs = Get-VBREPJob | ?{$_.IsEnabled} | %{$_.Name} \\r\\n$epjobs | Out-File @TargetFolderPath@\\\\running_epjobs.txt\\r\\n$epjobs | %{ Get-VBREPJob -Name $_ | Disable-VBREPJob }\\r\\n\\r\\n\\'@ | Set-Content -Path @TargetFolderPath@\\\\Disable-VBRRunningJobs.ps1\\r\\nStart-Process -wait powershell.exe -ArgumentList \\\"-file @TargetFolderPath@\\\\Disable-VBRRunningJobs.ps1\\\"\\r\\nStart-Process -Wait $InstallerFile (\\'/silent /noreboot /log \\\"$InstallerLogFile\\\" VBR_AUTO_UPGRADE=1\\')\\r\\n$psEnableJobs = @\\'\\r\\n# Step 3. Re-enable disabled jobs\\r\\nAdd-PSSnapin VeeamPSSnapin\\r\\n# Enable VBR jobs\\r\\nGet-Content @TargetFolderPath@\\\\running_jobs.txt | Enable-VBRJob \\r\\n# Enable EP jobs\\r\\nGet-Content @TargetFolderPath@\\\\running_epjobs.txt | %{ Get-VBREPJob -Name $_ | Enable-VBREPJob }\\r\\n\\'@ | Set-Content -Path @TargetFolderPath@\\\\Enable-VBRRunningJobs.ps1\\r\\nStart-Process -wait powershell.exe -ArgumentList \\\"-file @TargetFolderPath@\\\\Enable-VBRRunningJobs.ps1\\\"\\r\\nGet-Location", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "CAA379A6DD24B12782EBF611E9435825", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 95, "displayName": "McAfee File and Removable Media Protection", "displayVersion": "5.0.5.112", "softwareID": 37, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeFRMProtection-5.0.5.112", "installerFile": "eeff64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "3464E5AC0AAB7A8A4E7ABE0CB952B01A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 96, "displayName": "McAfee Agent", "displayVersion": "5.5.0.447", "softwareID": 29, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeAgent-5.5.0.447", "installerFile": "FramePkg.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"McAfee\\\" /b /wait \\\"$InstallerFile\\\" /Install=Agent /Silent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {80684F9A-6B01-4F3F-A8C7-C4B7BDF072F1} /qn FORCEUNINSTALL=1", "deleted": 0, "packageTypeID": 2, "packageHash": "0D638720DFEAD1FA21FCBAAC3237EA9F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 97, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.130.242", "softwareID": 33, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.130.242", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "198B8BFD35BFDAE0AED2AEA7753AC779", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 98, "displayName": "GlobalProtect", "displayVersion": "4.0.5", "softwareID": 23, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.0.5", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\n}\\nelse\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\n}\\n\\n$args = @\\\"\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\n\\\"@", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "2CAB0CDE87E7BD73C78D771AC0FB0851", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 99, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.9.6008.0", "softwareID": 35, "sortOrder": 7, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.9.6008.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "A33E0983AE78C2345814FBBFCC5174C4", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 100, "displayName": "Mimecast for Outlook", "displayVersion": "7.4.2183.20730", "softwareID": 2, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "MimecastforOutlook-7.4.2183.20730", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\n$FileVersion = \\'%sqlDisplayVersion%\\'\\r\\nif(\\'@TargetFolderPath@\\' -like \\'*TargetFolderPath*\\')\\r\\n{\\r\\n    $TargetFolderPath = \\\"C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\"\\r\\n    $InstallerLogFile = \\'C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\\Install.log\\'\\r\\n}\\r\\n$Platform = Get-ItemProperty -Path HKLM:\\\\SOFTWARE\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Configuration -Name Platform | select -ExpandProperty Platform\\r\\nif($Platform -like \\\"x86\\\")\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (32 bit).msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (64 bit).msi\\\"\\r\\n}\\r\\n\\r\\nStart-Process -Wait msiexec -ArgumentList \\\"/i `\\\"$TargetFolderPath\\\\$FileName`\\\" /quiet /l*v `\\\"$InstallerLogFile`\\\" Global.AutoUpdate.Enable=FALSE\\\";\\r\\n \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n $block = {\\r\\n     New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n     $Users = (get-process explorer -IncludeUserName).UserName\\r\\n     $Users | %{\\r\\n         $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n     }\\r\\n     $SIDS | %{\\r\\n         (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n     }\\r\\n };\\r\\n  \\r\\n $Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n  \\r\\n  \\r\\n $Message = \\\"Mimecast Plugin is working\\\"\\r\\n if($Result  -eq $false)\\r\\n {\\r\\n     $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n }\\r\\n \\r\\n   \\r\\n $obj = [ordered]@{}\\r\\n $obj.Result = $Result\\r\\n $obj.Message = $Message\\r\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 2, "upgradeActionTypeID": 1, "upgradeAction": "asdf", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "CAE970B96A7F4D970C0C45366E3256E2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 101, "displayName": "Foxit PhantomPDF", "displayVersion": "9.0.1.1049", "softwareID": 1, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDF-9.0.1.1049", "installerFile": "FoxitPhantomPDF901_enu_Setup_Distributor_Build.msi", "cacheActionTypeID": 2, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait taskkill /im FoxitPhantomPDF.exe /f 2>null 1>nul\\r\\n@start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" ADDLOCAL=\\\"FX_PDFVIEWER,FX_SE,FX_FIREFOXPLUGIN,FX_OCR,FX_CREATOR,FX_CONVERTEXT,FX_CREATORWORDADDIN,FX_CREATOREXCELADDIN,FX_CREATOROUTLOOKADDIN,FX_CREATORPPTADDIN,FX_IFILTER\\\" MAKEDEFAULT=1 VIEW_IN_BROWSER=1 CPDF_DISABLE=1 CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0 EDITION=BUSINESS\\r\\n", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n    $ProgramFiles = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProgramFiles = $env:ProgramFiles\\r\\n}\\r\\n$PDFPreviewHandlerGUIDPath = \\\"HKLM:\\\\SOFTWARE\\\\Classes\\\\.pdf\\\\shellex\\\\{8895b1c6-b41f-4c1c-a562-0d564250836f}\\\"\\r\\n$PDFPreviewHandlerGUID = Get-ItemProperty -Path $PDFPreviewHandlerGUIDPath -ErrorAction SilentlyContinue | select -Expand \\\"(Default)\\\"\\r\\n$DesiredPDFPreviewHandlerGUID = \\\"{1B96FAD8-1C10-416E-8027-6EFF94045F6F}\\\"\\r\\n$PDFHandlerPath = Get-ItemProperty -Path \\\"HKLM:\\\\SOFTWARE\\\\Classes\\\\WOW6432Node\\\\CLSID\\\\{1B96FAD8-1C10-416E-8027-6EFF94045F6F}\\\\LocalServer32\\\" -ErrorAction SilentlyContinue | select -Expand \\\"(Default)\\\"\\r\\n$ErrorMessages = @()\\r\\nif($PDFPreviewHandlerGUID -eq $DesiredPDFPreviewHandlerGUID -and (Test-Path $PDFHandlerPath))\\r\\n{\\r\\n    $Result = $True\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ErrorMessages += \\\"PDF Previews are not enabled\\\"    \\r\\n}\\r\\n\\r\\n$Message=$ErrorMessages -Join \\\", \\\"\\r\\n\\\"Result=$Result|Message=$Message\\\";\\r\\n\\r\\n\\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 2, "packageHash": "637F19E4021466576F2C2E701DF14E4C", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 102, "displayName": "Electronic Service Control", "displayVersion": "15.0.0.30", "softwareID": 51, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "ElectronicServiceControl-15.0.0.30", "installerFile": "ESC.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" && CLS", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Electronic Service Control\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "3FAD8B30EAA15101B675D474A94F2C99", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 103, "displayName": "Windows 10 1709 Update", "displayVersion": "10.0.16299.0", "softwareID": 52, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Windows10-10.0.16299.0", "installerFile": "Win10_1709_English_x32.iso", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$ImagePath = \\'$InstallerFile\\';\\r\\n$InstallerLogFolder = Join-Path $env:SystemDrive \\\"Windows10UpgradeLogs\\\"\\r\\nmkdir $InstallerLogFolder -force | out-null\\r\\nwget -Uri \\\"https://msp.immense.net/labtech/transfer/tools/setupdiag.exe\\\" -OutFile \\\"C:\\\\setupdiag.exe\\\"\\r\\nif($ImagePath -like \\\"*installerfile*\\\")\\r\\n{\\r\\n    $ImagePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Windows.iso\\\"\\r\\n}\\r\\n\\r\\nif([System.Environment]::OSVersion.Version.Major -lt 8)\\r\\n{\\r\\n    # install third party image mount\\r\\n    $imdiskresponse = & cmd /c imdisk 2>&1\\r\\n    $imdiskinstalled = ($imdiskresponse | select -first 1) -like \\\"*ImDisk Virtual Disk Driver*\\\"\\r\\n    if($imdiskinstalled -ne $true)\\r\\n    {\\r\\n        if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n        { \\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk-x64.exe\\\"\\r\\n        }\\r\\n        else \\r\\n        {\\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk.exe\\\"\\r\\n        }\\r\\n    \\r\\n        $ISOMountInstallFullPath = \\\"$($env:windir)\\\\temp\\\\$ISOMountInstallFileName\\\"\\r\\n        wget \\\"https://msp.immense.net/LabTech/Transfer/Tools/$ISOMountInstallFileName\\\" -OutFile $ISOMountInstallFullPath \\r\\n        if(Test-Path $ISOMountInstallFullPath)\\r\\n        {\\r\\n            Start-Process -Wait -NoNewWindow cmd.exe \\\"/c `\\\"$ISOMountInstallFullPath`\\\" /fullsilent\\\"\\r\\n        }\\r\\n    }\\r\\n    \\r\\n    #choco install -y imdisk imdisk-toolkit --force\\r\\n    $output = (& \\\"imdisk\\\" -a -m `#: -f $ImagePath)\\r\\n    $DriveLetter = $output[1].substring($output[1].IndexOf(\\\"->\\\") - 3, 1)    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $MountResult = Mount-DiskImage -ImagePath $ImagePath -PassThru\\r\\n    $DriveLetter = ($MountResult | Get-Volume).DriveLetter    \\r\\n}\\r\\n$SetupFile = \\\"$($DriveLetter):\\\\setup.exe\\\"\\r\\n$ArgList = @()\\r\\n$ArgList += \\\"/c $SetupFile\\\"\\r\\n$ArgList += \\\"/auto upgrade\\\"\\r\\n$ArgList += \\\"/migratedrivers all\\\"\\r\\n$ArgList += \\\"/quiet\\\"\\r\\n$ArgList += \\\"/DynamicUpdate disable\\\"\\r\\n$ArgList += \\\"/ResizeRecoveryPartition Disable\\\"\\r\\n$ArgList += \\\"/ShowOOBE none\\\"\\r\\n$ArgList += \\\"/CopyLogs `\\\"$InstallerLogFolder`\\\"\\\"\\r\\n$ArgList += \\\"/telemetry disable\\\"\\r\\n$ArgList += \\\"/noreboot\\\"\\r\\n\\r\\nif(\\\"$LicenseValue\\\" -like \\\"*-*-*-*-*\\\")\\r\\n{\\r\\n    $ArgList += \\\"/pkey $LicenseValue\\\"\\r\\n}\\r\\n\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList $ArgList -WorkingDirectory ($env:windir + \\\"\\\\\\\" + \\\"temp\\\")\\r\\n\\\"Setup.exe exited. Running SetupDiag.exe to collect logs\\\"\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList \\\"/c c:\\\\SetupDiag.exe\\\"\\r\\n$SetupDiagResultsPath = \\\"c:\\\\SetupDiagResults.log\\\"\\r\\nif((Test-Path $SetupDiagResultsPath))\\r\\n{\\r\\n    Get-Content -Tail 50 -Path $SetupDiagResultsPath\\r\\n}\\r\\n\\r\\n\\\"# Setuperr.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupErr.log\\\" -Tail 50\\r\\n\\\"# Setupact.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupAct.log\\\" -Tail 50\\r\\n\\r\\n#Write-Output \\\"SuppressReboot=1\\\"; \\r\\n\\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "98B59F9927EB0AECC10526E08C70F907", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 104, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.9.6009.0", "softwareID": 35, "sortOrder": 8, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.9.6009.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "0DB59823F25D815EBF8D163108EA0B2D", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 105, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.150.12", "softwareID": 33, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.150.12", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "4B562C674306C371ADE9DD1DE2C65A55", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 106, "displayName": "PhishMe Reporter", "displayVersion": "3.1.4.0", "softwareID": 53, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "PhishMeReporter-3.1.4.0", "installerFile": "PhishMe Reporter 3.1.4 Setup - ISC Constructors.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" && CLS", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Stop-Process -ProcessName outlook -Force -ErrorAction SilentlyContinue \\r\\nGet-Package \\\"PhishMe Reporter\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 2, "packageHash": "1132FDE09F27C2DD4EEEE72661F82FBB", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 107, "displayName": "Snagit 13", "displayVersion": "13.1.3.7993", "softwareID": 9, "sortOrder": 3, "cachingStrategyID": 0, "relativeCacheSourcePath": "SnagIt-13.1.3.7993", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\" & cls", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 2, "packageHash": null, "productKeyRegExFilter": ".*", "productKeyDisplayName": "TechSmith snagit 13"}, {"softwareVersionID": 108, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.200.1002", "softwareID": 33, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.200.1002", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "BE45D6C36BD43BC23F8EFE40D444F500", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 109, "displayName": "GlobalProtect", "displayVersion": "4.0.6", "softwareID": 23, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.0.6", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\n}\\nelse\\n{\\n$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\n}\\n\\n$args = @\\\"\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\n\\\"@", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "D6F235F223AC5C2928E5E5683AEE9E2A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 110, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "3.10.6104.0", "softwareID": 35, "sortOrder": 9, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-3.10.6104.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "FA6F9021EF5F1273B12AAED5D13F1167", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 111, "displayName": "3CXPhone for Windows", "displayVersion": "15.5.8801.3", "softwareID": 12, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "3CXPhone-15.5.8801.3", "installerFile": "3CXPhoneforWindows15.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 2, "packageHash": "B61BC3120DB977F616A43596D35DC117", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 112, "displayName": "Design", "displayVersion": "10.5.0.27", "softwareID": 54, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "20-20Design-10.5.0.27", "installerFile": "20-20Design.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /QB /PASSIVE /L*V \\\"$InstallerLogFile\\\" EDITBOX_REG_PROP_SI=#0", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package Design", "deleted": 0, "packageTypeID": 1, "packageHash": "669946D7800FEF187D92385864CAD84F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 113, "displayName": "Snagit 2018", "displayVersion": "18.1.0.775", "softwareID": 9, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "Snagit-18.1.0.775", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TSC_SOFTWARE_KEY=$LicenseValue TSC_ALLOW_IDENTITY=0", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Snagit 2018\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "174089CE6F7444BB682B7C33698720C7", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 114, "displayName": "ManicTime", "displayVersion": "4.0.12.1", "softwareID": 18, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "ManicTime-4.0.12.1", "installerFile": "ManicTimeSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" CREATESTARTMENUSHORTCUT=0 RUNFORALLUSERS=1\\r\\nreg add HKLM\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run /v ManicTime /t REG_SZ /d \\\"\\\\\\\"C:\\\\Program Files (x86)\\\\Manictime\\\\ManicTime.exe\\\\\\\" /minimized\\\" && REM", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd /c msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "59908353F5FB051DB79C04C9D005E1A1", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 115, "displayName": "Remote Desktop Manager", "displayVersion": "13.0.6.0", "softwareID": 55, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "RemoteDesktopManager-13.0.6.0", "installerFile": "Setup.RemoteDesktopManager.13.0.6.0.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "get-package \\\"Remote desktop manager\\\" | Uninstall-package  \\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "6E80E41540889B866E294FBF7D61AE81", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 116, "displayName": "GlobalProtect", "displayVersion": "4.1.0", "softwareID": 23, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.1.0", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "895E11D42D2D649163CB5ED2A43FD7F5", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 117, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.1.6308.0", "softwareID": 35, "sortOrder": 10, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.1.6308.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "AE476AFEECA42E2118559FC4AB5A2509", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 118, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.2.6402.0", "softwareID": 35, "sortOrder": 11, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.2.6402.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "6E141DEE6E7999FC10CEAD519D052DD2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 119, "displayName": "USSC Web Components", "displayVersion": "3.0.6.51", "softwareID": 56, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "USSCWebComponents-3.0.6.51", "installerFile": "USSCWebComponents.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" /verysilent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "start \\\"\\\" /b /wait cmd /c \\\"C:\\\\Program Files (x86)\\\\USSC Web Components\\\\unins000.exe\\\" /verysilent", "deleted": 0, "packageTypeID": 2, "packageHash": "5618AFAD125BF727979DE1816C4F3A34", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 120, "displayName": "pVault Imaging System", "displayVersion": "4.2.0.169", "softwareID": 57, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "pVaultImaging-4.2.0.169", "installerFile": "ClientSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\\"@TargetFolderPath@\\\";\\r\\n$args = @\\\"\\r\\n    /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TRANSFORMS=\\\"$LicenseFilePath\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n\\r\\nif([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$ProgramFiles32 = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$ProgramFiles32 = $env:ProgramFiles\\r\\n}\\r\\n\\r\\n\\r\\nCopy-Item -Recurse (Join-Path -Path $TargetFolderPath -ChildPath \\\"pVault\\\") -Destination (Join-Path -Path $ProgramFiles32 -ChildPath \\\"Paperless Environments\\\\pVault Imaging System\\\") -Force -Confirm:$false\\r\\n\\r\\n$PaperlessPath = \\\"$ProgramFiles32\\\\Paperless Environments\\\\pVault Imaging System\\\\pVault\\\"\\r\\n$Acl = Get-Acl $PaperlessPath\\r\\n$Ar = New-Object  system.security.accesscontrol.filesystemaccessrule(\\\"Everyone\\\",\\\"Read,Write,Modify\\\", \\\"ContainerInherit,ObjectInherit\\\" ,\\\"None\\\", \\\"Allow\\\")\\r\\n$Acl.SetAccessRule($Ar)\\r\\nSet-Acl $PaperlessPath $Acl -Verbose\\r\\n$PrinterInstallArgs = @\\\"\\r\\n/c \\\"$TargetFolderPath\\\\PrinterSetup\\\\pnsetup.exe\\\" /PASSCODE=0078003E0059004D005400320036002C004C000800770043004E00250066000E00560023002C0022004D002D0009001D0008006E\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $PrinterInstallArgs", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"pVault Imaging System\\", "deleted": 0, "packageTypeID": 1, "packageHash": "310FE7C6C61531870CC8205D42CB1EE0", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 121, "displayName": "AirTame", "displayVersion": "3.1.1.0", "softwareID": 58, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AirTame-3.1.1.0", "installerFile": "airtame-application-3.1.1-setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /qn /l*v $InstallerLogFile && REM", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 0, "uninstallAction": "Uninstall-Package \\\"AirTame\\\" -force", "deleted": 0, "packageTypeID": 2, "packageHash": "C303440B4130799700F88D76B6D6A1AF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 122, "displayName": "Quick Bid", "displayVersion": "4.92.0.6", "softwareID": 59, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "QuickBid-4.92.0.6", "installerFile": "QB49206Setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /S /v/qn\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Quick Bid\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "AFAE251387199A6A57B1156F43E20ED1", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 123, "displayName": "Bluebeam Revu", "displayVersion": "11.7.0", "softwareID": 60, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "BluebeamRevu-11.7.0", "installerFile": "Bluebeam Revu x64 11.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && REM", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Bluebeam Revu x64 11\\\";", "deleted": 1, "packageTypeID": 2, "packageHash": "FB9E2FB5D29EC7EAA5CE2B647645D20A", "productKeyRegExFilter": null, "productKeyDisplayName": "Bluebeam Revu"}, {"softwareVersionID": 124, "displayName": "Dell SupportAssist", "displayVersion": "1.0.0.0", "softwareID": 62, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Dell SupportAssist*\\\" | Uninstall-Package -Force", "deleted": 1, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 125, "displayName": "Sage 300 Construction and Real Estate Client", "displayVersion": "16.1.0", "softwareID": 63, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Sage300CRE-16.1.0", "installerFile": "setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$BasePath = \\\"@TargetFolderPath@\\\"\\r\\n$LogPath = \\\"$InstallerLogFile\\\"\\r\\n$PreReqs = @()\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\PreReqChecker\\\\PreReqChecker.exe`\\\" /silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_8_0\\\\vcredist_x86.exe`\\\" /Q\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_8_0\\\\vcredist_x64.exe`\\\" /Q\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_9_0\\\\vcredist_x86.exe`\\\" /qb\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_9_0\\\\vcredist_x64.exe`\\\" /qb\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_10_0\\\\vcredist_x64.exe`\\\" /passive /norestart\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_10_0\\\\vcredist_x86.exe`\\\" /passive /norestart\\\"\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\VC_13_0\\\\vcredist_x86.exe`\\\" /install /passive /norestart\\\"\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$BasePath\\\\Prerequisites\\\\Crystal Runtime Engine (x86)\\\\CRRuntime_32bit_13_0_8.msi`\\\" /qn /L*v `\\\"$LogPath`\\\"\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Event1\\\\Setup.exe`\\\" /q /ComponentArgs Setup1:$LogPath\\\" -WorkingDirectory \\\"$BasePath\\\\Prerequisites\\\\Event1\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\PostCrystalUFLInstall\\\\PostCrystalUFLInstall.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\SASimClientInst\\\\SASimClientInst.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\StopSageServices\\\\StopSageServices.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Pre PSQL Install\\\\PrePSQLInstall.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Pervasive PSQL v12 Client Edition (x86)\\\\SetupClient32_x86.exe`\\\" /S /v/qn\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /p `\\\"$BasePath\\\\Prerequisites\\\\Pervasive PSQL v12 Client Patch (x86)\\\\PSQLv12Patch_Client32_x86.msp`\\\" /qn /L*v `\\\"$LogPath`\\\" REINSTALL=ALL REINSTALLMODE=omus\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Pervasive PSQL v12 Server Edition (x86)\\\\SetupServer32_x86.exe`\\\" /S /v/qn\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /p `\\\"$BasePath\\\\Pervasive PSQL v12 Server Patch (x86)\\\\PSQLv12Patch_Server32_x86.msp`\\\" /qn /L*v `\\\"$LogPath`\\\" REINSTALL=ALL REINSTALLMODE=omus\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Pervasive PSQL v12 Server Edition (x64)\\\\SetupServer64_x64.exe`\\\" /S /v/qn\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /p `\\\"$BasePath\\\\Prerequisites\\\\Pervasive PSQL v12 Server Patch (x64)\\\\PSQLv12Patch_Server64_x64.msp`\\\" /qn /L*v `\\\"$LogPath`\\\" REINSTALL=ALL REINSTALLMODE=omus\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Post PSQL Install\\\\PostPSQLInstall.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Post PSQL64 Install\\\\PostPSQL64Install.exe`\\\" /Silent\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$BasePath\\\\Prerequisites\\\\Visual Studio 2010 Tools For Office\\\\vstor_redist.exe`\\\" /q /norestart\\\"\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$BasePath\\\\Prerequisites\\\\Crystal Runtime Engine (x86)\\\\CRRuntime_32bit_13_0_8.msi`\\\" /qn /L*v `\\\"$LogPath`\\\"\\\"\\r\\n\\r\\n\\r\\n$MainMSIPath = \\\"$BasePath\\\\Main\\\\Timberline Accounting Client.msi\\\"\\r\\n$Mainparams = @\\\"\\r\\nRESPONSEFILE=\\\"None\\\" REBOOT=REALLYSUPPRESS INSTALL_MODE=\\\"Install\\\" ADDLOCAL=\\\"AB,AbCli,AbCliCom,AP,ApCli,ApCliCom,AR,ArCli,ArCliCom,BL,BlCli,BlCliCom,CM,CmCli,CmCliCom,CN,CnCli,CnCliCom,DT,DtCli,DtCliCom,EQ,EqCli,EqCliCom,FS,FsCli,FsCliCom,GL,GlCli,GlCliCom,IA,IaCli,IaCliCom,ID,IdCli,IdCliCom,IV,IvCli,IvCliCom,JC,JcCli,JcCliCom,MC,McCli,McCliCom,OD,OdCli,OdCliCom,PR,PrCli,PrCliCom,PJ,PjCli,PjCliCom,PO,PoCli,PoCliCom,RT,RtCli,RtCliCom,TR,TrCli,TrCliCom,SI,SiCli,SiCliCom,TX,TxCli,TxCliCom,TS,TsCli,TsCliCom,TsHelp,zBDE\\\" ACCOUNTING_UPGRADE=\\\"false\\\" SETUPEXEDIR=\\\"$(Split-Path $MainMSIPath -Parent)\\\" SETUPEXENAME=\\\"setup.exe\\\" RebootYesNo=No\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$MainMSIPath`\\\" /qn /L*v `\\\"$LogPath`\\\" $MainParams\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "F8249BC56C60E7B6C2A826FFF915C005", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 126, "displayName": "On-Screen Takeoff", "displayVersion": "*******", "softwareID": 64, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "On-ScreenTakeoff-*******", "installerFile": "OST3906Setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /S /v/qn\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "BA3EE05510C0891449FCD70F1C2B42DF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 127, "displayName": "On-Screen Takeoff", "displayVersion": "3.94.2.21", "softwareID": 64, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "On-ScreenTakeoff-3.94.2.21", "installerFile": "OST394221.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"On-Screen Takeoff\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "9574EEE4C8A05C3941A93B7CC8E67244", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 128, "displayName": "Quick Bid", "displayVersion": "4.96.0.12", "softwareID": 59, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "QuickBid-4.96.0.12", "installerFile": "QB496012.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Quick Bid\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "FC7E685F9A470BB32662BE3F12353AE1", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 129, "displayName": "Bluebeam Revu x64 2017.0.40", "displayVersion": "17.0.40", "softwareID": 60, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "BluebeamRevu-17.0.40", "installerFile": "Bluebeam Revu x64 17.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" BB_DEFAULTVIEWER=1 $LicenseValue\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args\\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Bluebeam Revu x64 2017.0.40\\\" ;", "deleted": 1, "packageTypeID": 2, "packageHash": "CB4DCED9C4151507B557C6CCC6E5872F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 130, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.300.842", "softwareID": 33, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.300.842", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "464F8A63AEAF6BAB2D11A24E6C800DE1", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 131, "displayName": "Cisco AnyConnect", "displayVersion": "1.0.0.0", "softwareID": 66, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"*anyconnect*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 132, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.3.6507.0", "softwareID": 35, "sortOrder": 12, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.3.6507.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "A3DE3EB78B653E21B18AE8CBA416DB64", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 133, "displayName": "Microsoft ReportViewer 2010 Redistributable", "displayVersion": "10.0.30319", "softwareID": 67, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MSReportViewerRedist2010-10.0.30319", "installerFile": "ReportViewer_2010.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c `\\\"$InstallerFile`\\\" /q /norestart\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "98C89B18D0BBD4D7D3981445BB139237", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 134, "displayName": "Microsoft System CLR Types for SQL Server 2012", "displayVersion": "11.1.3000.0", "softwareID": 68, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MSSQLCLRTypes2012-11.1.3000.0", "installerFile": "SQLSysClrTypes32.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$InstallerFile`\\\" /qn /l*v `\\\"$InstallerLogFile`\\\"\\\";\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "9A9DDD76CB9001DA2B6E4D8912C9C636", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 135, "displayName": "Microsoft System CLR Types for SQL Server 2014", "displayVersion": "12.0.2402.11", "softwareID": 69, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MSSQLCLRTypes2014-12.0.2402.11", "installerFile": "SQLSysClrTypes32.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$InstallerFile`\\\" /qn /l*v `\\\"$InstallerLogFile`\\\"\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "2DDE39E3731FC8FCFAD37E0B4FF4D2C2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 136, "displayName": "Microsoft Report Viewer 2012 Runtime", "displayVersion": "11.1.3452.0", "softwareID": 70, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MSReportViewerRuntime2012-11.1.3452.0", "installerFile": "ReportViewer_2012.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$InstallerFile`\\\" /qn /l*v `\\\"$InstallerLogFile`\\\"\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "7E337DDE8AA22139EBC847977A230D04", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 137, "displayName": "Microsoft Report Viewer 2015 Runtime", "displayVersion": "12.0.2402.15", "softwareID": 71, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MSReportViewerRuntime2015-12.0.2402.15", "installerFile": "ReportViewer_2015.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /i `\\\"$InstallerFile`\\\" /quiet /l*v `\\\"$InstallerLogFile`\\\"\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Microsoft Report Viewer 2015 Runtime\\", "deleted": 0, "packageTypeID": 2, "packageHash": "A52F235241A570DDC0F7E72F64658D02", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 138, "displayName": "Bluebeam Revu x64 2018", "displayVersion": 1, "softwareID": 60, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "BluebeamRevu-18.0.3", "installerFile": "Bluebeam Revu x64 18.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" BB_DEFAULTVIEWER=1 $LicenseValue\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args\\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Bluebeam Revu x64 2017.0.40\\\" ;", "deleted": 1, "packageTypeID": 2, "packageHash": "83CB97CD41686641E108AFBE9032652F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 139, "displayName": "3CX Plugins", "displayVersion": "14.0.897.704", "softwareID": 13, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "3CXPlugins-14.0.897.704", "installerFile": "3CXPlugins.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Get-process \\\"*3cx*\\\" | Stop-Process -Force\\r\\n$ExeInstaller = \\\"$InstallerFile\\\"\\r\\n$ParentFolder = Split-Path $ExeInstaller\\r\\nStart-Process -Wait $ExeInstaller -ArgumentList \\\"/extract:`\\\"$ParentFolder`\\\"\\\"\\r\\nif ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n{ \\r\\n    $FileName = \\\"3CXPlugins.x64.msi\\\"\\r\\n}\\r\\nelse \\r\\n{\\r\\n    $FileName = \\\"3CXPlugins.msi\\\" \\r\\n}\\r\\n\\r\\n$MsiInstaller = Join-Path $ParentFolder $FileName\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i $MsiInstaller /qn /l*v \\\"$InstallerLogFile\\\" PLUGINSELECTED=TAPI\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$InstallerLogFile = \\\"C:\\\\3cxuninstall.log\\\"\\r\\nGet-process \\\"*3cx*\\\" | Stop-Process -Force\\r\\n$ExeInstaller = \\\"$InstallerFile\\\"\\r\\n$ParentFolder = Split-Path $ExeInstaller\\r\\nStart-Process -Wait $ExeInstaller -ArgumentList \\\"/extract:`\\\"$ParentFolder`\\\"\\\"\\r\\nif ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n{ \\r\\n    $FileName = \\\"3CXPlugins.x64.msi\\\"\\r\\n}\\r\\nelse \\r\\n{\\r\\n    $FileName = \\\"3CXPlugins.msi\\\" \\r\\n}\\r\\n\\r\\n$MsiInstaller = Join-Path $ParentFolder $FileName\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /x $MsiInstaller /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n\\r\\n\\r\\n \\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "0C3FC927BADE7ACFF610F7655EA18CA3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 141, "displayName": "FortiClient", "displayVersion": "5.6.6.1167", "softwareID": 8, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "FortiClient-5.6.6.1167", "installerFile": "x86\\\\FortiClient.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\nif($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $ProperInstallFile = \\\"x64\\\\FortiClient.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProperInstallFile = \\\"x86\\\\FortiClient.msi\\\"\\r\\n}\\r\\n$Installer = Join-Path $TargetFolderPath $ProperInstallFile\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args = @\\\"\\r\\n/c wmic product where name=\\\"FortiClient\\\" call uninstall /nointeractive >> $InstallerLogFile\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "deleted": 0, "packageTypeID": 1, "packageHash": "C7213FE7FAB4907C8542AB7B9046766D", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 142, "displayName": "AutoCAD LT 2018", "displayVersion": "22.0.161.0", "softwareID": 72, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutocadLT2018-22.0.49.0", "installerFile": "AutocadLT2018", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadLT2018-22.0.49.0\\\\AutocadLT2018\\\\Img\\\\Setup.exe /W /q /I C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadLT2018-22.0.49.0\\\\AutocadLT2018\\\\IMG\\\\AUtocadLT2018.ini /language en-us\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 144, "displayName": "Log Me In Hamachi", "displayVersion": "2.2.0.579", "softwareID": 73, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"LogMeIn Hamachi\\\";", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 145, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.4.6709.0", "softwareID": 35, "sortOrder": 13, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.4.6709.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "EC72ECAAEFFA192C947DDC88B61E4001", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 146, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.4.6711.0", "softwareID": 35, "sortOrder": 14, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.4.6711.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "2944786C186920AA5D1A99333CD51820", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 147, "displayName": "McAfee File and Removable Media Protection", "displayVersion": "5.0.6.141", "softwareID": 37, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeFRMProtection-5.0.6.141", "installerFile": "eeff64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "53B707328313CAFD4D1BA0C66A69E17E", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 148, "displayName": "Windows 10 1803 Update", "displayVersion": "10.0.17134.0", "softwareID": 47, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "Windows10-10.0.17134.0", "installerFile": "en_windows_10_business_edition_version_1803_updated_aug_2018_x64_dvd_5d7e729e.iso", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$ImagePath = \\'$InstallerFile\\';\\r\\n$InstallerLogFolder = Join-Path $env:SystemDrive \\\"Windows10UpgradeLogs\\\"\\r\\nmkdir $InstallerLogFolder -force | out-null\\r\\nwget -Uri \\\"https://msp.immense.net/labtech/transfer/tools/setupdiag.exe\\\" -OutFile \\\"C:\\\\setupdiag.exe\\\"\\r\\nif($ImagePath -like \\\"*installerfile*\\\")\\r\\n{\\r\\n    $ImagePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Windows.iso\\\"\\r\\n}\\r\\n\\r\\nif([System.Environment]::OSVersion.Version.Major -lt 8)\\r\\n{\\r\\n    # install third party image mount\\r\\n    $imdiskresponse = & cmd /c imdisk 2>&1\\r\\n    $imdiskinstalled = ($imdiskresponse | select -first 1) -like \\\"*ImDisk Virtual Disk Driver*\\\"\\r\\n    if($imdiskinstalled -ne $true)\\r\\n    {\\r\\n        if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n        { \\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk-x64.exe\\\"\\r\\n        }\\r\\n        else \\r\\n        {\\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk.exe\\\"\\r\\n        }\\r\\n    \\r\\n        $ISOMountInstallFullPath = \\\"$($env:windir)\\\\temp\\\\$ISOMountInstallFileName\\\"\\r\\n        wget \\\"https://msp.immense.net/LabTech/Transfer/Tools/$ISOMountInstallFileName\\\" -OutFile $ISOMountInstallFullPath \\r\\n        if(Test-Path $ISOMountInstallFullPath)\\r\\n        {\\r\\n            Start-Process -Wait -NoNewWindow cmd.exe \\\"/c `\\\"$ISOMountInstallFullPath`\\\" /fullsilent\\\"\\r\\n        }\\r\\n    }\\r\\n    \\r\\n    #choco install -y imdisk imdisk-toolkit --force\\r\\n    $output = (& \\\"imdisk\\\" -a -m `#: -f $ImagePath)\\r\\n    $DriveLetter = $output[1].substring($output[1].IndexOf(\\\"->\\\") - 3, 1)    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $MountResult = Mount-DiskImage -ImagePath $ImagePath -PassThru\\r\\n    $DriveLetter = ($MountResult | Get-Volume).DriveLetter    \\r\\n}\\r\\n$SetupFile = \\\"$($DriveLetter):\\\\setup.exe\\\"\\r\\n$ArgList = @()\\r\\n$ArgList += \\\"/c $SetupFile\\\"\\r\\n$ArgList += \\\"/auto upgrade\\\"\\r\\n$ArgList += \\\"/migratedrivers all\\\"\\r\\n$ArgList += \\\"/quiet\\\"\\r\\n$ArgList += \\\"/DynamicUpdate disable\\\"\\r\\n$ArgList += \\\"/ResizeRecoveryPartition Disable\\\"\\r\\n$ArgList += \\\"/ShowOOBE none\\\"\\r\\n$ArgList += \\\"/CopyLogs `\\\"$InstallerLogFolder`\\\"\\\"\\r\\n$ArgList += \\\"/telemetry disable\\\"\\r\\n$ArgList += \\\"/noreboot\\\"\\r\\n\\r\\nif(\\\"$LicenseValue\\\" -like \\\"*-*-*-*-*\\\")\\r\\n{\\r\\n    $ArgList += \\\"/pkey $LicenseValue\\\"\\r\\n}\\r\\n\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList $ArgList -WorkingDirectory ($env:windir + \\\"\\\\\\\" + \\\"temp\\\")\\r\\n\\\"Setup.exe exited. Running SetupDiag.exe to collect logs\\\"\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList \\\"/c c:\\\\SetupDiag.exe\\\"\\r\\n$SetupDiagResultsPath = \\\"c:\\\\SetupDiagResults.log\\\"\\r\\nif((Test-Path $SetupDiagResultsPath))\\r\\n{\\r\\n    Get-Content -Tail 50 -Path $SetupDiagResultsPath\\r\\n}\\r\\n\\r\\n\\\"# Setuperr.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupErr.log\\\" -Tail 50\\r\\n\\\"# Setupact.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupAct.log\\\" -Tail 50\\r\\n\\r\\nDismount-DiskImage -ImagePath $ImagePath \\r\\n\\r\\n#Write-Output \\\"SuppressReboot=1\\\"; \\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "C6282AA4A45E1D1E64A99886EB49E6BF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 149, "displayName": "Windows 10 1803 Update", "displayVersion": "10.0.17134.0", "softwareID": 52, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "Windows10-10.0.17134.0", "installerFile": "Win10_1803_English_x32.iso", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$ImagePath = \\'$InstallerFile\\';\\r\\n$InstallerLogFolder = Join-Path $env:SystemDrive \\\"Windows10UpgradeLogs\\\"\\r\\nmkdir $InstallerLogFolder -force | out-null\\r\\nwget -Uri \\\"https://msp.immense.net/labtech/transfer/tools/setupdiag.exe\\\" -OutFile \\\"C:\\\\setupdiag.exe\\\"\\r\\nif($ImagePath -like \\\"*installerfile*\\\")\\r\\n{\\r\\n    $ImagePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Windows.iso\\\"\\r\\n}\\r\\n\\r\\nif([System.Environment]::OSVersion.Version.Major -lt 8)\\r\\n{\\r\\n    # install third party image mount\\r\\n    $imdiskresponse = & cmd /c imdisk 2>&1\\r\\n    $imdiskinstalled = ($imdiskresponse | select -first 1) -like \\\"*ImDisk Virtual Disk Driver*\\\"\\r\\n    if($imdiskinstalled -ne $true)\\r\\n    {\\r\\n        if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n        { \\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk-x64.exe\\\"\\r\\n        }\\r\\n        else \\r\\n        {\\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk.exe\\\"\\r\\n        }\\r\\n    \\r\\n        $ISOMountInstallFullPath = \\\"$($env:windir)\\\\temp\\\\$ISOMountInstallFileName\\\"\\r\\n        wget \\\"https://msp.immense.net/LabTech/Transfer/Tools/$ISOMountInstallFileName\\\" -OutFile $ISOMountInstallFullPath \\r\\n        if(Test-Path $ISOMountInstallFullPath)\\r\\n        {\\r\\n            Start-Process -Wait -NoNewWindow cmd.exe \\\"/c `\\\"$ISOMountInstallFullPath`\\\" /fullsilent\\\"\\r\\n        }\\r\\n    }\\r\\n    \\r\\n    #choco install -y imdisk imdisk-toolkit --force\\r\\n    $output = (& \\\"imdisk\\\" -a -m `#: -f $ImagePath)\\r\\n    $DriveLetter = $output[1].substring($output[1].IndexOf(\\\"->\\\") - 3, 1)    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $MountResult = Mount-DiskImage -ImagePath $ImagePath -PassThru\\r\\n    $DriveLetter = ($MountResult | Get-Volume).DriveLetter    \\r\\n}\\r\\n$SetupFile = \\\"$($DriveLetter):\\\\setup.exe\\\"\\r\\n$ArgList = @()\\r\\n$ArgList += \\\"/c $SetupFile\\\"\\r\\n$ArgList += \\\"/auto upgrade\\\"\\r\\n$ArgList += \\\"/migratedrivers all\\\"\\r\\n$ArgList += \\\"/quiet\\\"\\r\\n$ArgList += \\\"/DynamicUpdate disable\\\"\\r\\n$ArgList += \\\"/ResizeRecoveryPartition Disable\\\"\\r\\n$ArgList += \\\"/ShowOOBE none\\\"\\r\\n$ArgList += \\\"/CopyLogs `\\\"$InstallerLogFolder`\\\"\\\"\\r\\n$ArgList += \\\"/telemetry disable\\\"\\r\\n$ArgList += \\\"/noreboot\\\"\\r\\n\\r\\nif(\\\"$LicenseValue\\\" -like \\\"*-*-*-*-*\\\")\\r\\n{\\r\\n    $ArgList += \\\"/pkey $LicenseValue\\\"\\r\\n}\\r\\n\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList $ArgList -WorkingDirectory ($env:windir + \\\"\\\\\\\" + \\\"temp\\\")\\r\\n\\\"Setup.exe exited. Running SetupDiag.exe to collect logs\\\"\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList \\\"/c c:\\\\SetupDiag.exe\\\"\\r\\n$SetupDiagResultsPath = \\\"c:\\\\SetupDiagResults.log\\\"\\r\\nif((Test-Path $SetupDiagResultsPath))\\r\\n{\\r\\n    Get-Content -Tail 50 -Path $SetupDiagResultsPath\\r\\n}\\r\\n\\r\\n\\\"# Setuperr.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupErr.log\\\" -Tail 50\\r\\n\\\"# Setupact.log\\\"\\r\\nGet-Content -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupAct.log\\\" -Tail 50", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "166D99B85390D58017DC0BEF14E5F8EB", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 150, "displayName": "AutoCAD LT 2019", "displayVersion": "*********", "softwareID": 74, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutocadLT2019-*********", "installerFile": "AutocadLT2019", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadLT2019-*********\\\\AutocadLT2019\\\\Img\\\\Setup.exe /W /q /I C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadLT2019-*********\\\\AutocadLT2019\\\\IMG\\\\AUtocadLT2019.ini /language en-us\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 1, "packageTypeID": 1, "packageHash": "8093C168B72F8651381E794C5BBF33B9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 151, "displayName": "AutoCAD 2018", "displayVersion": "22.0.49.0", "softwareID": 75, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Autocad2018-22.0.49.0", "installerFile": "Autocad2018", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\Autocad2018-22.0.49.0\\\\Autocad2018\\\\Img\\\\Setup.exe /W /q /I C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\Autocad2018-22.0.49.0\\\\Autocad2018\\\\IMG\\\\Autocad2018.ini /language en-us\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"AutoCAD 2018 - English\\\";", "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 152, "displayName": "Sage300CREEstimatingSQL", "displayVersion": "17.13.17326.401", "softwareID": 76, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Sage300CREEstimatingSQL-17.13.17326.401", "installerFile": "Sage300CREEstimatingSQL", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = @\\\"\\r\\n$InstallerFile\\r\\n\\\"@\\r\\n\\r\\n$args1 = @\\\"\\r\\n/c \\\"$TargetFolderPath\\\\Prerequisites\\\\Preppy.exe\\\" /iAcceptSageEULA=Yes /quiet\\r\\n\\\"@\\r\\n\\r\\n\\r\\n$args2 = @\\\"\\r\\n/c msiexec /i \\\"$TargetFolderPath\\\\Main\\\\EstimatingInstall.msi\\\" /qn iAcceptSageEULA=yes\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args2", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Sage Estimating*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 1, "packageHash": "6E29BB65A2BF2A268FF1BDF1D5A134C4", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 153, "displayName": "AutoCAD 2019", "displayVersion": "*********", "softwareID": 77, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Autocad2019-*********", "installerFile": "Autocad2019", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\Autocad2019-*********\\\\Autocad2019\\\\Img\\\\Setup.exe /W /q /I C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\Autocad2019-*********\\\\Autocad2019\\\\IMG\\\\Autocad2019.ini /language en-us\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"AutoCAD 2019 - English\\", "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 154, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.400.152", "softwareID": 33, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.400.152", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "BC202219179573AF722B5AC34405B2E3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 155, "displayName": "CRM Outlook Add-In", "displayVersion": "13.1.1", "softwareID": 78, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "RJCRMPlugin-13.1.1", "installerFile": "Install.ps1", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": ". \\\"$InstallerFile\\\";", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "AB9F9E900ABC82F954BEC9DDAF66363A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 156, "displayName": "Microsoft Visual Studio Code", "displayVersion": "1.23.0", "softwareID": 79, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "VisualStudioCode-1.23.0", "installerFile": "VSCodeSetup-x64-1.23.0.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /SP- /VERYSILENT\\r\\n\\\"@\\r\\n\\r\\nStart-Process -wait -nonewwindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "\\r\\n\\r\\nif($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $workingdirectory = \\\"${env:ProgramFiles(x86)}\\\\Microsoft VS Code\\\\\\\"\\r\\n    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $workingdirectory = \\\"${env:ProgramFiles}\\\\Microsoft VS Code\\\\\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c \\\"$workingdirectory\\\\unins000.exe\\\" /SILENT\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow -WorkingDirectory $workingdirectory cmd -ArgumentList $args\\r\\n \\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "D6F8C30EF2DAEA6923AF0564DE9310CE", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 157, "displayName": "AutoCAD Mechanical 2019", "displayVersion": "*********", "softwareID": 80, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutocadMechanical2019-*********", "installerFile": "AutocadMech2019", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadMechanical2019-*********\\\\AutocadMech2019\\\\Img\\\\Setup.exe /W /q /I C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadMechanical2019-*********\\\\AutocadMech2019\\\\IMG\\\\AutocadMechanical2019.ini /language en-us\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 158, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.5.6806.0", "softwareID": 35, "sortOrder": 15, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.5.6806.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "33C2CE55568454804A9CD5BBED06E806", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 159, "displayName": "AutoCAD Mechanical 2018", "displayVersion": "22.0.46.0", "softwareID": 81, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutocadMechanical2018-22.0.46.0", "installerFile": "AutocadMech2018", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\WINDOWS\\\\temp\\\\ManagedSoftwareInstallers\\\\AutocadMechanical2018-22.0.46.0\\\\AutocadMech2018\\\\Img\\\\Setup.exe /W /q /I C:\\\\WINDOWS\\\\temp\\\\ManagedSoftwareInstallers\\\\AutocadMechanical2018-22.0.46.0\\\\AutocadMech2018\\\\Img\\\\AutocadMechanical2018.ini /language en-us\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 160, "displayName": "Any PDF to DWG Converter 2010", "displayVersion": 2010, "softwareID": 82, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AnyPDFtoDWG-2010", "installerFile": "pdfdwg.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "$args1 = @\\\"\\r\\n/c $InstallerFile /silent\\r\\n\\\"@\\r\\nStart-Process -Wait cmd.exe -NoNewWindow -ArgumentList $args1\\r\\n\\r\\n$WshShell = New-Object -comObject WScript.Shell\\r\\n$Shortcut = $WshShell.CreateShortcut(\\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Any PDF to DWG Converter.lnk\\\")\\r\\n$Shortcut.TargetPath = \\\"C:\\\\Program Files (x86)\\\\Any PDF to DWG Converter\\\\pdf_dwg.exe\\\"\\r\\n$Shortcut.Save()\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args1 = @\\\"\\r\\n\\\"C:\\\\Program Files (x86)\\\\Any PDF to DWG Converter\\\\unins000.exe\\\" /silent\\r\\n\\\"@\\r\\nStart-Process -Wait psexec -NoNewWindow -ArgumentList $args1\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "A77909DDFFC2C1486E4626C8B8AF6B61", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 161, "displayName": "CrosbySlingCalculator", "displayVersion": 5.1, "softwareID": 83, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrosbySlingCalculator-5.1", "installerFile": "slingcalculator_51.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i $InstallerFile /qn\\r\\n\\\"@\\r\\nStart-Process -Wait cmd.exe -NoNewWindow -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Crosby Sling Calculator*\\\" | Uninstall-Package;", "deleted": 0, "packageTypeID": 2, "packageHash": "EC6DCAE993A070EA576AC2580CB0434C", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 162, "displayName": "FloorRight Commercial 3D v10.0", "displayVersion": 10, "softwareID": 84, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Start-Process -Wait \\\"C:\\\\Program Files (x86)\\\\FloorRight Commercial 3D v10.0\\\\unins000.exe\\\" -ArgumentList \\\"/SILENT\\", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 163, "displayName": "FloorRight Commercial 3D v10.1 SP4", "displayVersion": 10.1, "softwareID": 84, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "FloorRight-10.1", "installerFile": "FRCv10_1_SP4_setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "#$InstallerFile = \\\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FRCv10_1_SP4_setup.exe\\\"\\r\\n#$InstallerLogFile = \\\"FloorRightInstall.log\\\"\\r\\n\\r\\n$Args = @\\\"\\r\\n/VERYSILENT /LOG=\\\"$InstallerLogFile\\\" /NORESTART /COMPONENTS=main,main\\\\floorright,main\\\\templates,main\\\\templates\\\\imperial_units,main\\\\samples,main\\\\tutorials\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow -FilePath \\\"$InstallerFile\\\" -ArgumentList $Args -WorkingDirectory (Split-Path $InstallerFile)\\r\\n\\r\\n$Folder = Split-Path \\\"$InstallerFile\\\"\\r\\n$WibuInstallPath = Join-Path $Folder \\\"Wibu\\\\Setup.exe\\\"\\r\\nStart-Process -Wait -NoNewWindow -FilePath $WibuInstallPath", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "if($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $ProgramFiles = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProgramFiles = $env:ProgramFiles\\r\\n}\\r\\n\\r\\n$UninstallerFilePath = Join-Path $ProgramFiles \\\"FloorRight Commercial 3D v10.1\\\\unins000.exe\\\"\\r\\n\\r\\nStart-Process -Wait -FilePath $UninstallerFilePath -ArgumentList \\\"/SILENT\\\"\\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "E55FF12E19D7BE158D6F963C8664D528", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 164, "displayName": "LICCON Work Planner", "displayVersion": "6.0.0", "softwareID": 85, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "LICCONWorkPlanner-6.0.0", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$LicconPath = @\\\"\\r\\n$InstallerFile\\r\\n\\\"@\\r\\n\\r\\n$PathtoTAB = $LicconPath + \\\"Tab.zip\\\"\\r\\n$PathtoSilent = $LicconPath + \\\"LICCON Work Planner Installation v5.3\\\"\\r\\n\\r\\nExpand-Archive -LiteralPath $PathtoTAB -DestinationPath \\\"C:\\\\Program Files (x86)\\\\Likaplan\\\" -force\\r\\n$args1 = @\\\"\\r\\n/c silent.bat \\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -ArgumentList $args1 -WorkingDirectory $PathtoSilent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "B81F234ACAE7A5D286BE16DD3C3CB2AE", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 165, "displayName": "FloorRight Viewer 3D v10.1 SP4", "displayVersion": 10.1, "softwareID": 86, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "FloorRightViewer-10.1", "installerFile": "FRVv10_1_SP4_setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "#$InstallerFile = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FRVv10_1_SP4_setup.exe\\\"\\r\\n#$InstallerLogFile = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FloorRightViewerInstall.log\\\"\\r\\n\\r\\n$Args = @\\\"\\r\\n/VERYSILENT /LOG=\\\"$InstallerLogFile\\\" /NORESTART\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow -FilePath \\\"$InstallerFile\\\" -ArgumentList $Args -WorkingDirectory (Split-Path \\\"$InstallerFile\\\")\\r\\n\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "if($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $ProgramFiles = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProgramFiles = $env:ProgramFiles\\r\\n}\\r\\n\\r\\n$UninstallerFilePath = Join-Path $ProgramFiles \\\"FloorRight Viewer 3D v10.1\\\\unins000.exe\\\"\\r\\n\\r\\nStart-Process -Wait -FilePath $UninstallerFilePath -ArgumentList \\\"/SILENT\\\"\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "80080AC82C8DE9AAB26E9BFC5BF14E0A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 166, "displayName": "VistaViewpoint", "displayVersion": "6.13.0.20", "softwareID": 87, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "VistaViewpoint-6.13.0.20", "installerFile": "Vista Client.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Vista*\\\" | Uninstall-Package;", "deleted": 0, "packageTypeID": 2, "packageHash": "4915031B1BF14B12720F1D2C54C5DEF9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 167, "displayName": "Riverbed Steelhead Mobile Client", "displayVersion": "69.2.20508.28", "softwareID": 88, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "RiverbedSteelheadMobile-69.2.20508.28", "installerFile": "SteelheadMobile.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -ArgumentList $args1\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "uninstall-Package \\\"Riverbed Steelhead Mobile\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "3FADC565A159FCD42BD3944286805A97", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 169, "displayName": "Umbrella Roaming Client", "displayVersion": "2.1.0.0", "softwareID": 4, "sortOrder": 3, "cachingStrategyID": 0, "relativeCacheSourcePath": "UmbrellaRoamingClient-2.1.0.0", "installerFile": "Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 3, "installAction": 5872, "licenseTypeID": 0, "testRequired": 1, "testActionTypeID": 2, "testAction": "# Test OpenDNS against www.internetbadguys.com\\r\\n$OrgInfoPath = \\\"$($env:ProgramData)\\\\OpenDNS\\\\ERC\\\\OrgInfo.json\\\"\\r\\n\\r\\n$DisplayVersion = ((\\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Umbrella Roaming Client\\'})) | %{$_.DisplayVersion}\\r\\n$Result = ($DisplayVersion -ne $null -and $DisplayVersion -notlike \\\"\\\")\\r\\n$ResultMessage = \\\"OpenDNS not found in Programs and Features\\\"\\r\\n\\r\\nif (Test-Path $OrgInfoPath)\\r\\n{\\r\\n    $orginfo = Get-Content -Raw -Path $OrgInfoPath | ConvertFrom-Json\\r\\n    if ([string]::IsNullOrEmpty($orginfo.fingerprint))\\r\\n    {\\r\\n        $Result = $false\\r\\n        $ResultMessage = \\\"OpenDNS is missing organization configuration\\\"\\r\\n    }\\r\\n    else\\r\\n    {\\r\\n        $Result = $true\\r\\n        $ResultMessage = \\'\\'\\r\\n    }\\r\\n\\r\\n}\\r\\n\\r\\n$obj = @{}\\r\\n$obj.Result = $Result\\r\\n$obj.Message = $ResultMessage\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))", "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 3, "uninstallAction": 6640, "deleted": 0, "packageTypeID": 2, "packageHash": "7C77B0A373F94B4D5FF993E8BAE1BA91", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 170, "displayName": "MitelConnect", "displayVersion": "************.0", "softwareID": 89, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MitelConnect-************.0", "installerFile": "ShoreTelConnect.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" /S /v\\\" /qn\\\" && REM", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Mitel*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 2, "packageHash": "303999F20858033D3C914870F13497E8", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 171, "displayName": "CraneManager", "displayVersion": 2014, "softwareID": 90, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "CraneManager-2014", "installerFile": "CraneManager", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$Path1 = \\\"$InstallerFile\\\" + \\\"\\\\CM2014-Basic-d1\\\\cm5_setup.exe\\\"\\r\\n$Path2 = \\\"$InstallerFile\\\" + \\\"\\\\CM2014-Basic-d2\\\\cm5_setup.exe\\\"\\r\\n\\r\\n\\r\\n\\r\\n$args1 = @\\\"\\r\\n/c \\\"$Path1\\\" /S\\r\\n\\\"@\\r\\n$args2 = @\\\"\\r\\n/c \\\"$Path2\\\" /S\\r\\n\\\"@\\r\\n\\r\\nStart-Process -NoNewWindow cmd.exe -argumentlist $args1\\r\\nStart-Sleep -s 60\\r\\nStop-Process -ProcessName cm5_setup -Force\\r\\n\\r\\nStart-Sleep -s 1\\r\\nStart-Process -NoNewWindow cmd.exe -argumentlist $args2\\r\\nStart-Sleep -s 60\\r\\nStop-Process -ProcessName cm5_setup -Force\\r\\n\\r\\n\\r\\n\\r\\n$path = \\\"C:\\\\Program Files\\\\CRANIMAX\\\"\\r\\n$Acl = Get-Acl $Path\\r\\n$Ar = New-Object System.Security.AccessControl.FileSystemAccessRule(\\\"BUILTIN\\\\users\\\", \\\"FullControl\\\", \\\"ContainerInherit,ObjectInherit\\\", \\\"None\\\", \\\"Allow\\\")\\r\\n$Acl.SetAccessRule($Ar)\\r\\nSet-Acl $path $Acl\\r\\n\\r\\n$TargetFile = \\\"C:\\\\Program Files\\\\CRANIMAX\\\\CM 5\\\\cm5.exe\\\"\\r\\n$ShortcutFile = \\\"$env:Public\\\\Desktop\\\\Crane Manager 2014.lnk\\\"\\r\\n$WScriptShell = New-Object -ComObject WScript.Shell\\r\\n$Shortcut = $WScriptShell.CreateShortcut($ShortcutFile)\\r\\n$Shortcut.TargetPath = $TargetFile\\r\\n$Shortcut.Save()", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 172, "displayName": "GlobalProtect", "displayVersion": "4.1.1", "softwareID": 23, "sortOrder": 7, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.1.1", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "2339782E531F4D391310CC51BB374605", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 174, "displayName": "Microsoft Online Services Sign-in Assistant", "displayVersion": "7.250.4303.0", "softwareID": 91, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": "Microsoft Online Services Sign-in Assistant-7.250.4303.0", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$InstallerFilePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\"\\r\\n$InstallerLogFile = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\msoidcli_64bit.log\\\"\\r\\n$Platform = (Get-WmiObject -Class Win32_OperatingSystem | Select-Object OSArchitecture -ErrorAction Stop).OSArchitecture\\r\\nif($Platform -like \\\"64-bit\\\")\\r\\n{\\r\\n$FileName = \\\"\\\\msoidcli_64bit.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n$FileName = \\\"\\\\msoidcli_32bit.msi\\\"\\r\\n} \\r\\n\\r\\n$path = $InstallerFilePath + $FileName \\r\\n\\r\\n$args1 = @\\\"\\r\\n/c msiexec /i \\\"$path\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Microsoft Online Services Sign-in Assistant\\\"  -ErrorAction SilentlyContinue | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 180, "displayName": "Microsoft BitLocker Encryption", "displayVersion": "2.5.1100.0", "softwareID": 96, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "MBAM Client-2.5.1100.0", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$Platform = (Get-WmiObject -Class Win32_OperatingSystem | Select-Object OSArchitecture -ErrorAction Stop).OSArchitecture\\r\\nif($Platform -like \\\"64-bit\\\")\\r\\n{\\r\\n$FileName = \\\"x64\\\\MbamClientSetup-2.5.1100.0.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n$FileName = \\\"x86\\\\MbamClientSetup-2.5.1100.0.msi\\\"\\r\\n} \\r\\n\\r\\n$path = \\\"$InstallerFile\\\" + $FileName \\r\\n\\r\\n$args1 = @\\\"\\r\\n/c msiexec /i \\\"$path\\\" /qn ALLUSERS=1 REBOOT=ReallySuppress /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -ArgumentList $args1\\r\\n\\r\\nSleep -Seconds 15\\r\\n\\r\\n $TPM = Get-WmiObject win32_tpm -Namespace root\\\\cimv2\\\\security\\\\microsofttpm | where {$_.IsEnabled().Isenabled -eq \\'True\\'} -ErrorAction SilentlyContinue\\r\\n$WindowsVer = Get-WmiObject -Query \\'select * from Win32_OperatingSystem where (Version like \\\"6.2%\\\" or Version like \\\"6.3%\\\" or Version like \\\"10.0%\\\") and ProductType = \\\"1\\\"\\' -ErrorAction SilentlyContinue\\r\\n$BitLockerReadyDrive = Get-BitLockerVolume -MountPoint $env:SystemDrive -ErrorAction SilentlyContinue\\r\\n \\r\\n \\r\\n#If all of the above prequisites are met, then create the key protectors, then enable BitLocker and backup the Recovery key to AD.\\r\\nif ($WindowsVer -and $TPM -and $SystemDriveBitLockerRDY) {\\r\\n \\r\\n#Creating the recovery key\\r\\nStart-Process \\'manage-bde.exe\\' -ArgumentList \\\" -protectors -add $env:SystemDrive -recoverypassword\\\" -Verb runas -Wait\\r\\n \\r\\n#Adding TPM key\\r\\nStart-Process \\'manage-bde.exe\\' -ArgumentList \\\" -protectors -add $env:SystemDrive  -tpm\\\" -Verb runas -Wait\\r\\nsleep -Seconds 15 #This is to give sufficient time for the protectors to fully take effect.\\r\\n \\r\\n#Enabling Encryption\\r\\nStart-Process \\'manage-bde.exe\\' -ArgumentList \\\" -on $env:SystemDrive -em aes256\\\" -Verb runas -Wait\\r\\n \\r\\n#Getting Recovery Key GUID\\r\\n$RecoveryKeyGUID = (Get-BitLockerVolume -MountPoint $env:SystemDrive).keyprotector | where {$_.Keyprotectortype -eq \\'RecoveryPassword\\'} | Select-Object -ExpandProperty KeyProtectorID\\r\\n \\r\\n#Backing up the Recovery to AD.\\r\\nmanage-bde.exe  -protectors $env:SystemDrive -adbackup -id $RecoveryKeyGUID\\r\\n \\r\\n#Restarting the computer, to begin the encryption process\\r\\nRestart-Computer} \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"MD<PERSON> MBAM\\\"  -ErrorAction SilentlyContinue", "deleted": 0, "packageTypeID": 1, "packageHash": "6E2F0DEF773445B86E7EFFDFFC753B75", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 184, "displayName": "DOSbox", "displayVersion": "0.74.0.0", "softwareID": 99, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "DosBox-0.74.0.0", "installerFile": "DOSBox0.74-win32-installer.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c $InstallerFile /S\\r\\n\\\"@\\r\\nStart-Process -Wait cmd.exe -NoNewWindow -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "D98336D46509BC7FA840A82457F72827", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 185, "displayName": "Event 1 Office Connector", "displayVersion": "1.0.28", "softwareID": 100, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Event1OfficeConnector-1.0.28", "installerFile": "Setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "9B73E7D9F4F8ABB0B95A4E9D7915B520", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 186, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.6.6905.0", "softwareID": 35, "sortOrder": 16, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.6.6905.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "8B9DAE8D67CDC8DA20C0E217408D2063", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 187, "displayName": "ManicTime", "displayVersion": "4.1.5.0", "softwareID": 18, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "ManicTime-4.1.5.0", "installerFile": "ManicTimeSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" CREATESTARTMENUSHORTCUT=0 RUNFORALLUSERS=1\\r\\nreg add HKLM\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run /v ManicTime /t REG_SZ /d \\\"\\\\\\\"C:\\\\Program Files (x86)\\\\Manictime\\\\ManicTime.exe\\\\\\\" /minimized\\\" && REM", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd /c msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "8BBED20A92692CBAECEDF51F50D5BDE4", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 188, "displayName": "Intel ProSet/Wireless Enterprise Software", "displayVersion": "1.0.0", "softwareID": 103, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$Descriptions = @(\\\"*PROSet*Wireless Enterprise Software\\\")\\r\\n$Descriptions | %{\\r\\n    $Description = $_\\r\\n    $PackageReference = get-package \\\"$Description\\\" | select -ExpandProperty FastPackageReference\\r\\n    Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /X$PackageReference REBOOT=ReallySuppress /qn\\\"\\r\\n}\\r\\n", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 189, "displayName": "Intel PROSet/Wireless WiFi Software", "displayVersion": "1.0.0", "softwareID": 104, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$Descriptions = @(\\\"*PROSet/Wireless WiFi Software\\\" )\\r\\n$Descriptions | %{\\r\\n    $Description = $_\\r\\n    $PackageReference = get-package \\\"$Description\\\" | select -ExpandProperty FastPackageReference\\r\\n    Start-Process -Wait -NoNewWindow cmd -ArgumentList \\\"/c msiexec /X$PackageReference REBOOT=ReallySuppress /qn\\\"\\r\\n}\\r\\n", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 190, "displayName": "AutoCAD LT 2019", "displayVersion": "*********", "softwareID": 74, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutocadLT2019-*********", "installerFile": "AutocadLT2019", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutocadLT2019-*********\\\\AutocadLT2019\\\\Img\\\\Setup.exe /W /q /I \\\"$LicenseFilePath\\\" /language en-us\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd.exe -argumentlist $args1\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "8093C168B72F8651381E794C5BBF33B9", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 191, "displayName": "FaxFinder Client", "displayVersion": "4.0.0.39941", "softwareID": 105, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "FaxFinderClient-4.0.0.39941", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n$Installer = \\\"@TargetFolderPath@\\\\FFClient-x64-4.0.0.39941.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n$Installer = \\\"@TargetFolderPath@\\\\FFClient-x86-4.0.0.39941.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -wait -nonewwindow cmd -argumentlist $args\\r\\nget-content \\\"$InstallerLogFile\\\";\\r\\n$ServerAddress = \\'$LicenseValue\\'\\r\\n\\r\\n$filestomanipulate = gci ((Split-Path $env:public) + \\\"\\\\\\\") | ?{ $_.PSIsContainer -and (Test-Path ($_.FullName + \\'\\\\AppData\\\\Roaming\\\\FaxFinder Client Software\\\\Settings\\\\servers.xml\\'))} | %{ ($_.FullName + \\'\\\\AppData\\\\Roaming\\\\FaxFinder Client Software\\\\Settings\\\\servers.xml\\')}\\r\\nif(($filestomanipulate | measure) -ne 0)\\r\\n{\\r\\n    & taskkill /IM faxfinderclient.exe /F\\r\\n    $filestomanipulate | %{\\r\\n        $path = $_\\r\\n        $xml = [xml](Get-Content $path)\\r\\n        $xml.settings.server.connection.address = $ServerAddress\\r\\n        Write-Output (\\\"Saving \\\" + $path)\\r\\n        $xml.save($path)\\r\\n    }\\r\\n}\\r\\n \\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 3, "upgradeActionTypeID": 2, "upgradeAction": "Get-Package \\\"FaxFinder*Client*\\\" | Uninstall-Package -Force\\r\\nsleep -s 120\\r\\nif([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n$Installer = \\\"@TargetFolderPath@\\\\FFClient-x64-4.0.0.39941.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n$Installer = \\\"@TargetFolderPath@\\\\FFClient-x86-4.0.0.39941.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -wait -nonewwindow cmd -argumentlist $args\\r\\nget-content \\\"$InstallerLogFile\\\";\\r\\n\\r\\n", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"FaxFinder*Client*\\\" | Uninstall-Package -Force\\r\\n", "deleted": 0, "packageTypeID": 1, "packageHash": "9908ABE19CFB1F9FD63A5645C8555633", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 192, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.7.7002.0", "softwareID": 35, "sortOrder": 17, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.7.7002", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "967AEA3ED50DBA0A677CD6DCD197862A", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 193, "displayName": "EclipseSE IPro", "displayVersion": "17.4.34112.4610", "softwareID": 106, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "EclipseSE IPro-17.4.34112.4610", "installerFile": "Setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /S /v/qn\\r\\n\\\"@\\r\\n$config = Get-Content -Path \\\"$LicenseFilePath\\\"\\r\\nSet-Content -Path \\\"C:\\\\ProgramData\\\\Ipro Tech\\\\EclipseSE\\\\Login\\\\SystemDir.txt\\\" -Value $config\\r\\nNew-Item -ItemType Directory -Force -Path \\\"C:\\\\ProgramData\\\\Ipro Tech\\\\EclipseSE\\\\Login\\\"\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"EclipseSE*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 1, "packageHash": "DD46404B43ED45E377BA13299A7F4E26", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 194, "displayName": "McAfee Agent", "displayVersion": "4.6.0.1694", "softwareID": 107, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$args1 =@\\\"\\r\\n/c \\\"C:\\\\Program Files (x86)\\\\McAfee\\\\Common Framework\\\\frminst.exe /remove=agent\\\" \\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow  -ArgumentList $args1\\r\\nGet-Package \\\"McAfee Agent\\\" | Uninstall-Package\\r\\n", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 195, "displayName": "McAfee Agent", "displayVersion": "5.5.1.342", "softwareID": 29, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeAgent-5.5.1.342", "installerFile": "FramePkg.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"McAfee\\\" /b /wait \\\"$InstallerFile\\\" /Install=Agent /Silent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {80684F9A-6B01-4F3F-A8C7-C4B7BDF072F1} /qn FORCEUNINSTALL=1", "deleted": 0, "packageTypeID": 2, "packageHash": "32A47877A44807DA0E74F62F7C038DFC", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 196, "displayName": "FortiClient", "displayVersion": "6.0.0.67", "softwareID": 8, "sortOrder": 5, "cachingStrategyID": 1, "relativeCacheSourcePath": "FortiClient-6.0.0.67", "installerFile": "FortiClient.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\nif($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $ProperInstallFile = \\\"x64\\\\FortiClient.msi\\\"\\r\\n    $ProperMSTFile = \\\"x64\\\\FortiClient.mst\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProperInstallFile = \\\"x86\\\\FortiClient.msi\\\"\\r\\n    $ProperMSTFile = \\\"x64\\\\FortiClient.mst\\\"\\r\\n}\\r\\n$Installer = Join-Path $TargetFolderPath $ProperInstallFile\\r\\n$MST = Join-Path $TargetFolderPath $ProperMSTFile\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" TRANSFORMS=$ProperMSTFile /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args = @\\\"\\r\\n/c msiexec /X \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ANCESTORFOUND=1\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "deleted": 0, "packageTypeID": 1, "packageHash": "DF8EA2D8295226965B770795CBFC27A7", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 197, "displayName": "TimeClick", "displayVersion": "12.0.0", "softwareID": 108, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "TimeClick-12.0.0", "installerFile": "TimeClickClient12.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn\\r\\n\\\"@\\r\\n\\r\\n$config = Get-Content -Path \\\"$LicenseFilePath\\\"\\r\\nNew-Item -Path \\\"C:\\\\ProgramData\\\\Hawkeye Technology\\\\TimeClick\\\\TimeClickWS.ini\\\" -force\\r\\nSet-Content -Path \\\"C:\\\\ProgramData\\\\Hawkeye Technology\\\\TimeClick\\\\TimeClickWS.ini\\\" -Value $config\\r\\n\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"TimeClick*\\\" | Uninstall-Package\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "5ABC928A082FB4B05CE0AF0D37F024C6", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 198, "displayName": "TimeClick", "displayVersion": "18.0.0.2", "softwareID": 108, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "TimeClick-18.0.0.2", "installerFile": "TimeClickWS-installer.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c \\\"$InstallerFile\\\" --mode unattended\\r\\n\\\"@\\r\\n\\r\\n$config = Get-Content -Path \\\"$LicenseFilePath\\\"\\r\\nNew-Item -Path \\\"C:\\\\ProgramData\\\\Hawkeye Technology\\\\TimeClick\\\\TimeClickWS.ini\\\" -force\\r\\nSet-Content -Path \\\"C:\\\\ProgramData\\\\Hawkeye Technology\\\\TimeClick\\\\TimeClickWS.ini\\\" -Value $config\\r\\n\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args1 = @\\\"\\r\\n/c \\\"C:\\\\Program Files (x86)\\\\TimeClick WS\\\\Uninstall TimeClick.exe\\\" --mode unattended\\r\\n\\\"@\\r\\n\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1", "deleted": 0, "packageTypeID": 2, "packageHash": "156236FC6F906C7FC681A64773F4C849", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 199, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.8.7101", "softwareID": 35, "sortOrder": 18, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.8.7101", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "4255775D0D3728D6C1A4FADF7DD62C25", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 200, "displayName": "GlobalProtect", "displayVersion": "4.1.2", "softwareID": 23, "sortOrder": 8, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.1.2", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "F846A44384160A0C28FCCD7CC3C13C21", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 201, "displayName": "McAfee Agent", "displayVersion": "4.6.0.1694", "softwareID": 29, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": "@start \\\"McAfee\\\" /b /wait \\\"$InstallerFile\\\" /Install=Agent /Silent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "$args1 =@\\\"\\r\\n/c \\\"C:\\\\Program Files (x86)\\\\McAfee\\\\Common Framework\\\\frminst.exe /remove=agent\\\" \\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow  -ArgumentList $args1\\r\\nGet-Package \\\"McAfee Agent\\\" | Uninstall-Package\\r\\n", "deleted": 1, "packageTypeID": 0, "packageHash": "F512A74E7178ADD279D9066DBBB54A53", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 202, "displayName": "McAfee Agent", "displayVersion": "4.6.0.109", "softwareID": 107, "sortOrder": 2, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$args1 =@\\\"\\r\\n/c \\\"C:\\\\Program Files (x86)\\\\McAfee\\\\Common Framework\\\\frminst.exe /remove=agent\\\" \\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow  -ArgumentList $args1\\r\\nGet-Package \\\"McAfee Agent\\\" | Uninstall-Package\\r\\n", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 203, "displayName": "Windows 10 1511 Update", "displayVersion": "10.0.10586.0", "softwareID": 47, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Windows10-10.0.10586.0", "installerFile": "win10x64.iso", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$ImagePath = \\'$InstallerFile\\';\\r\\n$InstallerLogFolder = Join-Path (Split-Path \\\"$InstallerLogFile\\\" -Parent) ([io.path]::GetFileNameWithoutExtension(\\\"$InstallerLogFile\\\"))\\r\\n\\r\\nif($ImagePath -like \\\"*installerfile*\\\")\\r\\n{\\r\\n    $ImagePath = \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Windows.iso\\\"\\r\\n}\\r\\n\\r\\nif([System.Environment]::OSVersion.Version.Major -lt 8)\\r\\n{\\r\\n    # install third party image mount\\r\\n    $imdiskresponse = & cmd /c imdisk 2>&1\\r\\n    $imdiskinstalled = ($imdiskresponse | select -first 1) -like \\\"*ImDisk Virtual Disk Driver*\\\"\\r\\n    if($imdiskinstalled -ne $true)\\r\\n    {\\r\\n        if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n        { \\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk-x64.exe\\\"\\r\\n        }\\r\\n        else \\r\\n        {\\r\\n            $ISOMountInstallFileName = \\\"ImDiskTk.exe\\\"\\r\\n        }\\r\\n    \\r\\n        $ISOMountInstallFullPath = \\\"$($env:windir)\\\\temp\\\\$ISOMountInstallFileName\\\"\\r\\n        wget \\\"https://msp.immense.net/LabTech/Transfer/Tools/$ISOMountInstallFileName\\\" -OutFile $ISOMountInstallFullPath \\r\\n        if(Test-Path $ISOMountInstallFullPath)\\r\\n        {\\r\\n            Start-Process -Wait -NoNewWindow cmd.exe \\\"/c `\\\"$ISOMountInstallFullPath`\\\" /fullsilent\\\"\\r\\n        }\\r\\n    }\\r\\n    \\r\\n    #choco install -y imdisk imdisk-toolkit --force\\r\\n    $output = (& \\\"imdisk\\\" -a -m `#: -f $ImagePath)\\r\\n    $DriveLetter = $output[1].substring($output[1].IndexOf(\\\"->\\\") - 3, 1)    \\r\\n}\\r\\nelse\\r\\n{\\r\\n    $MountResult = Mount-DiskImage -ImagePath $ImagePath -PassThru\\r\\n    $DriveLetter = ($MountResult | Get-Volume).DriveLetter    \\r\\n}\\r\\n$SetupFile = \\\"$($DriveLetter):\\\\setup.exe\\\"\\r\\n$ArgList = @\\\"\\r\\n/c $SetupFile /auto upgrade /migratedrivers all /quiet /DynamicUpdate disable /ResizeRecoveryPartition Disable /ShowOOBE none /CopyLogs \\\"$InstallerLogFolder\\\" /telemetry disable /Uninstall enable /noreboot\\r\\n\\\"@\\r\\nif(\\\"$LicenseValue\\\" -like \\\"*-*-*-*-*\\\")\\r\\n{\\r\\n    $ArgList += \\\" /pkey $LicenseValue\\\"\\r\\n}\\r\\n\\r\\nStart-Process -NoNewWindow -Wait -FilePath \\\"cmd.exe\\\" -ArgumentList $ArgList -WorkingDirectory ($env:windir + \\\"\\\\\\\" + \\\"temp\\\")\\r\\n\\r\\nGet-Content -Raw -Path \\\"$InstallerLogFolder\\\\Panther\\\\SetupAct.log\\\"\\r\\n#Write-Output \\\"SuppressReboot=1\\\";\\r\\n \\r\\n", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "1073A625AA88564B94B7554F2B071DE3", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 204, "displayName": "VMware vCenter Converter Standalone Agent", "displayVersion": "6.2.0.7348398", "softwareID": 109, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "VMwareConverterAgent-6.2.0.7348398", "installerFile": "VMware-Converter-Agent.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/S /V/qn\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow \\\"$InstallerFile\\\" -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args = @\\\"\\r\\n/S /x /V/qn\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow \\\"$InstallerFile\\\" -ArgumentList $args \\r\\n\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "76AACE1519B45604CB6DDD847FF214B2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 205, "displayName": "AzureAD", "displayVersion": "2.0.1.1", "softwareID": 12, "sortOrder": 3, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 1, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 206, "displayName": "AzureADTest", "displayVersion": "2.0.1.10", "softwareID": 110, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 207, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.9.7202.0", "softwareID": 35, "sortOrder": 19, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.9.7202", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "DF10171A8AEAE6A4C39A232FEEBFFA36", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 208, "displayName": "AccessDatabaseAgent", "displayVersion": "14.0.4763.1000", "softwareID": 111, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AccessDatabaseAgent", "installerFile": "AceRedist.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "01D36265A259F1996052F0A09A51B290", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 209, "displayName": "On-Screen Takeoff", "displayVersion": "3.95.0.50", "softwareID": 64, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "On-ScreenTakeoff-3.95.0.50", "installerFile": "OST36Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"On-Screen Takeoff\\\";\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "017565C781BA5AD797E078256D867AC7", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 210, "displayName": "Quick Bid", "displayVersion": "4.97.0.13", "softwareID": 59, "sortOrder": 3, "cachingStrategyID": 2, "relativeCacheSourcePath": "QuickBid-4.97.0.13", "installerFile": "QBSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Quick Bid\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "BABC1C7ABA35F314DD52C38A7B46DB49", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 211, "displayName": "Visual C++ Redistributable 2013", "displayVersion": 1, "softwareID": 112, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "choco install vcredist2013 -y -force", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 3, "upgradeActionTypeID": 2, "upgradeAction": "choco upgrade vcredist2013", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 212, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.10.7304.0", "softwareID": 35, "sortOrder": 20, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.10.7304", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "DE9093F81C9E2E3C9A731DF12DAC6C40", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 213, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.405.42", "softwareID": 33, "sortOrder": 7, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.405.42", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "A57D59920B055488B702A82E89B3E1DC", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 214, "displayName": "On-Screen Takeoff", "displayVersion": "3.95.2.51", "softwareID": 64, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "On-ScreenTakeoff-3.95.2.51", "installerFile": "OST36Setup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" REBOOT=REALLYSUPPRESS\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"On-Screen Takeoff\\\";\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "A28A02A9429CB2FBCCF39E905DC811F2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 215, "displayName": "Quick Bid", "displayVersion": "4.97.2.26", "softwareID": 59, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "QuickBid-4.97.2.26", "installerFile": "QBSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Quick Bid\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "1639F68F2F4F35FA99CF1F5D76690056", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 216, "displayName": "Microsoft OneDrive", "displayVersion": "18.131.0701.0004", "softwareID": 113, "sortOrder": 1, "cachingStrategyID": 1, "relativeCacheSourcePath": "MicrosoftOneDrive-18.131.0701.0004", "installerFile": "OneDriveSetup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /silent\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "9718D1FEC4C85DDA503BC43EE5106FC5", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 217, "displayName": "McAfee File and Removable Media Protection", "displayVersion": "5.0.7.111", "softwareID": 37, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeFRMProtection-5.0.7.111", "installerFile": "eeff64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet /norestart /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "4C4A89EC5A59F7769CDABB34C9D4D365", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 218, "displayName": "iSpring Suite 9", "displayVersion": "9.1.25298", "softwareID": 114, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "iSpring Suite 9", "installerFile": "e37283.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" $LicenseValue\\r\\n\\\"@\\r\\nStart-Process cmd -NoNewWindow -Wait -ArgumentList $args1", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "E78E0F84F03BB36CF3DD0F113494C5CA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 219, "displayName": "Teamviewer", "displayVersion": 1, "softwareID": 115, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "\\r\\n$yo = Get-Package \\\"TeamViewer*\\\" -ErrorAction SilentlyContinue |Select Version\\r\\n[version]$TeamviewerVersion = $yo.Version\\r\\n\\r\\nif(Test-Path \\\"C:\\\\Program Files (x86)\\\\TeamViewer\\\\uninstall.exe\\\")\\r\\n{\\r\\n    $args1 = @\\\"\\r\\n/c \\\"C:\\\\Program Files (x86)\\\\TeamViewer\\\\uninstall.exe\\\" /S\\r\\n\\\"@\\r\\n    Start-Process -NoNewWindow -Wait cmd -ArgumentList $args1\\r\\n}\\r\\n\\r\\nif(Test-Path \\\"C:\\\\Program Files\\\\TeamViewer\\\\Version\\\\uninstall.exe\\\")\\r\\n{\\r\\n    $args1 = @\\\"\\r\\n/c \\\"C:\\\\Program Files\\\\TeamViewer\\\\uninstall.exe\\\" /S\\r\\n\\\"@\\r\\n    Start-Process -NoNewWindow -Wait cmd -ArgumentList $args1\\r\\n}", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 220, "displayName": "GoToMyPC", "displayVersion": 1, "softwareID": 116, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "if(Get-Package \\\"GoToMyPC\\\")\\r\\n{\\r\\n\\r\\n    $GetPath = \\\"C:\\\\ProgramData\\\\GoToMyPC\\\\\\\"\\r\\n    $Folders = Get-ChildItem -Path $GetPath | Select Name\\r\\n    \\r\\n    $Path = $GetPath + $Folders.Name\\r\\n    $msi = Get-ChildItem -Path $Path | Where-Object {$_.Name -like \\\"*.msi\\\"} | Select Name\\r\\n    $msipath = $Path + \\\"\\\\\\\" + $msi.Name\\r\\n\\r\\n    $args1 = @\\\"\\r\\n/c msiexec /i \\\"$msipath\\\" /qn\\r\\n\\\"@\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n}\\r\\n", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 221, "displayName": "LogMeIn", "displayVersion": 1, "softwareID": 117, "sortOrder": 1, "cachingStrategyID": 0, "relativeCacheSourcePath": null, "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 0, "installAction": null, "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"LogMeIn*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 0, "packageHash": null, "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 222, "displayName": "Foxit PhantomPDF", "displayVersion": "9.2.0.9297", "softwareID": 1, "sortOrder": 7, "cachingStrategyID": 2, "relativeCacheSourcePath": "FoxitPhantomPDF-9.2.0.9297", "installerFile": "FoxitPhantomPDF92_enu_Setup_Website.msi", "cacheActionTypeID": 2, "cacheAction": "$env:username", "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait taskkill /im FoxitPhantomPDF.exe /f 2>null 1>nul\\r\\n@start \\\"\\\" /b /wait cmd.exe /c msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" KEYPATH=\\\"$LicenseFilePath\\\" ADDLOCAL=\\\"FX_PDFVIEWER,FX_SE,FX_FIREFOXPLUGIN,FX_OCR,FX_CREATOR,FX_CONVERTEXT,FX_CREATORWORDADDIN,FX_CREATOREXCELADDIN,FX_CREATOROUTLOOKADDIN,FX_CREATORPPTADDIN,FX_IFILTER\\\" MAKEDEFAULT=1 VIEW_IN_BROWSER=1 CPDF_DISABLE=1 CPDF_SERVICE_AUTO_START=0 AUTO_UPDATE=0 CREATESHORTCUT=1 LAUNCHCHECKDEFAULT=0 CONNECTEDPDFLOG=0 EDITION=BUSINESS\\r\\n", "licenseTypeID": 1, "testRequired": 1, "testActionTypeID": 2, "testAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n    $ProgramFiles = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProgramFiles = $env:ProgramFiles\\r\\n}\\r\\n$PDFPreviewHandlerGUIDPath = \\\"HKLM:\\\\SOFTWARE\\\\Classes\\\\.pdf\\\\shellex\\\\{8895b1c6-b41f-4c1c-a562-0d564250836f}\\\"\\r\\n$PDFPreviewHandlerGUID = Get-ItemProperty -Path $PDFPreviewHandlerGUIDPath -ErrorAction SilentlyContinue | select -Expand \\\"(Default)\\\"\\r\\n$DesiredPDFPreviewHandlerGUID = \\\"{1B96FAD8-1C10-416E-8027-6EFF94045F6F}\\\"\\r\\n$PDFHandlerPath = Get-ItemProperty -Path \\\"HKLM:\\\\SOFTWARE\\\\Classes\\\\WOW6432Node\\\\CLSID\\\\{1B96FAD8-1C10-416E-8027-6EFF94045F6F}\\\\LocalServer32\\\" -ErrorAction SilentlyContinue | select -Expand \\\"(Default)\\\"\\r\\n$ErrorMessages = @()\\r\\nif($PDFPreviewHandlerGUID -eq $DesiredPDFPreviewHandlerGUID -and (Test-Path $PDFHandlerPath))\\r\\n{\\r\\n    $Result = $True\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ErrorMessages += \\\"PDF Previews are not enabled\\\"    \\r\\n}\\r\\n\\r\\n$Message=$ErrorMessages -Join \\\", \\\"\\r\\n\\\"Result=$Result|Message=$Message\\\";\\r\\n\\r\\n\\r\\n", "testFailedError": "Foxit PhantomPDF is not registered as the default PDF handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": "$env:username", "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 2, "packageHash": "07B9C5315A5E49BA4DD9655FDA79BEE4", "productKeyRegExFilter": "SN=(.*)", "productKeyDisplayName": "Foxit Software Foxit PhantomPDF"}, {"softwareVersionID": 223, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.10.7310.0", "softwareID": 35, "sortOrder": 21, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.10.7310", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "F7E34D37F72FA676AF79B672DB5698B6", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 224, "displayName": "GlobalProtect", "displayVersion": "4.1.3", "softwareID": 23, "sortOrder": 9, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.1.3", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "E50BC90D1C0284A7738EE806E16CF35B", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 225, "displayName": "LICCON Work Planner", "displayVersion": 6.6, "softwareID": 85, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "LICCONWorkPlanner-6.6", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$LicconPath = @\\\"\\r\\n$InstallerFile\\r\\n\\\"@\\r\\n\\r\\n$PathtoTAB = $LicconPath + \\\"Tab.zip\\\"\\r\\n$PathtoSilent = $LicconPath + \\\"LIKAPLAN.zip\\\"\\r\\n$ShortcutPath = $LicconPath + \\\"LICCON Work Planner.lnk\\\"\\r\\n\\r\\n\\r\\nExpand-Archive -LiteralPath $PathtoTAB -DestinationPath \\\"C:\\\\ProgramData\\\\LIEBHERR\\\\LIKAPLAN\\\" -force\\r\\nExpand-Archive -LiteralPath $PathtoSilent -DestinationPath \\\"C:\\\\Program Files (x86)\\\\LIEBHERR\\\\\\\" -force\\r\\nMove-Item -LiteralPath $ShortcutPath -Destination \\\"C:\\\\Users\\\\<USER>\\\\Desktop\\\" -Force\\r\\n\\r\\n\\r\\n$Path = \\\"HKLM:\\\\Software\\\\WOW6432Node\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\LIKAPLAN\\\"\\r\\n\\r\\n\\r\\n    New-Item -Force -Path $Path | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"Contact\\\" -Value \\\"<EMAIL>\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"DisplayName\\\" -Value \\\"LICCON Work Planner\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"DisplayVersion\\\" -Value \\\"6.6\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"Publisher\\\" -Value \\\"LIEBHERR-Werk Ehingen GmbH\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"URLInfoAbout\\\" -Value \\\"http://www.liebherr.com\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"UninstallString\\\" -Value \\\"C:\\\\Program Files (x86)\\\\LIEBHERR\\\\LIKAPLAN\\\\Uninstall.exe\\\" | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"NoModify\\\" -Value 1 | Out-Null\\r\\n    New-ItemProperty -Force -Path $Path -Name \\\"NoRepair\\\" -Value 1 | Out-Null", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "C0412A107FA48C62065D68927D21C33B", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 226, "displayName": "Teramind-Gator", "displayVersion": "0.1.197", "softwareID": 118, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "Teramind-0.1.197\\\\\\\\GatorTrial", "installerFile": "teramind_agent_x64_s.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn\\r\\n\\\"@\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "FB89BEACD8C6DAB77F5A0281940B3BCF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 227, "displayName": "Teramind-Burk", "displayVersion": "0.1.197", "softwareID": 118, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "Teramind-0.1.197\\\\\\\\BurkTrial", "installerFile": "teramind_agent_x64_s.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn\\r\\n\\\"@\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "B87096011C60C7E42CE2A8403DCBBEB2", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 228, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.500.582", "softwareID": 33, "sortOrder": 8, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.500.582", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "2733F57A6F68CED602B9DDD5EAA28A8F", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 229, "displayName": "MitelConnect", "displayVersion": "************.0", "softwareID": 89, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "MitelConnect-************.0", "installerFile": "Mitel Connect.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c msiexec /i \\\"$InstallerFile\\\" /qn\\r\\n\\\"@\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Mitel*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 2, "packageHash": "24854880C08801B749BBC6E26F0CC789", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 230, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.11.7402.0", "softwareID": 35, "sortOrder": 22, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.11.7402", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "F77A06FCAC523C217037D8B28D5A4987", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 231, "displayName": "EclipseSE IPro", "displayVersion": "17.5.19814.1822", "softwareID": 106, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "EclipseSE IPro-17.5.19814.1822", "installerFile": "Setup.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args1 = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /S /v/qn\\r\\n\\\"@\\r\\n$config = Get-Content -Path \\\"$LicenseFilePath\\\"\\r\\nSet-Content -Path \\\"C:\\\\ProgramData\\\\Ipro Tech\\\\EclipseSE\\\\Login\\\\SystemDir.txt\\\" -Value $config\\r\\nNew-Item -ItemType Directory -Force -Path \\\"C:\\\\ProgramData\\\\Ipro Tech\\\\EclipseSE\\\\Login\\\"\\r\\nStart-Process -NoNewWindow -Wait cmd.exe -ArgumentList $args1\\r\\n", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"EclipseSE*\\\" | Uninstall-Package", "deleted": 0, "packageTypeID": 1, "packageHash": "969B0709D6FC4C46ABA73414C4EFD49C", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 232, "displayName": "Snagit 2018", "displayVersion": "18.2.1.1590", "softwareID": 9, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "SnagIt-18.2.1.1590", "installerFile": "snagit.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" TSC_SOFTWARE_KEY=$LicenseValue TSC_ALLOW_IDENTITY=0", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": "SnagIt not registered as Print Screen handler", "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package \\\"Snagit 2018\\\";", "deleted": 0, "packageTypeID": 2, "packageHash": "2B6A505699B25D3C8E06E24830B83462", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 233, "displayName": "Mimecast for Outlook", "displayVersion": "7.5.2350.20950", "softwareID": 2, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "MimecastforOutlook-7.5.2350.20950", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\n$FileVersion = \\'%sqlDisplayVersion%\\'\\r\\nif(\\'@TargetFolderPath@\\' -like \\'*TargetFolderPath*\\')\\r\\n{\\r\\n    $TargetFolderPath = \\\"C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\"\\r\\n    $InstallerLogFile = \\'C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\\Install.log\\'\\r\\n}\\r\\n$Platform = Get-ItemProperty -Path HKLM:\\\\SOFTWARE\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Configuration -Name Platform | select -ExpandProperty Platform\\r\\nif($Platform -like \\\"x86\\\")\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (32 bit).msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (64 bit).msi\\\"\\r\\n}\\r\\n\\r\\nStart-Process -Wait msiexec -ArgumentList \\\"/i `\\\"$TargetFolderPath\\\\$FileName`\\\" /quiet /l*v `\\\"$InstallerLogFile`\\\" Global.AutoUpdate.Enable=FALSE\\\";\\r\\n \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n $block = {\\r\\n     New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n     $Users = (get-process explorer -IncludeUserName).UserName\\r\\n     $Users | %{\\r\\n         $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n     }\\r\\n     $SIDS | %{\\r\\n         (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n     }\\r\\n };\\r\\n  \\r\\n $Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n  \\r\\n  \\r\\n $Message = \\\"Mimecast Plugin is working\\\"\\r\\n if($Result  -eq $false)\\r\\n {\\r\\n     $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n }\\r\\n \\r\\n   \\r\\n $obj = [ordered]@{}\\r\\n $obj.Result = $Result\\r\\n $obj.Message = $Message\\r\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 2, "upgradeActionTypeID": 1, "upgradeAction": "asdf", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "8B4BBDD62253BE184CB2862FC5AECAFC", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 235, "displayName": "SmartPSS Camera Viewer", "displayVersion": "2.02.0", "softwareID": 119, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "SmartPSS-2.02.0", "installerFile": "DH_SmartPSS_ChnEng_Win32_IS_V2.02.0.R.171124.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -NoNew<PERSON>indow -Wait \\\"$InstallerFile\\\" -ArgumentList \\\"/S\\\";\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "# This will never return because the geniuses that packaged it insisted on throwing a Success popup at the end of the uninstall \\r\\n# This makes it hang forever\\r\\n$UninstallString = (Get-Package \\\"SmartPSS*\\\" | select -ExpandProperty meta).Attributes[\\\"UninstallString\\\"]\\r\\nStart-Process -NoNewWindow -wait $UninstallString -ArgumentList \\\"/S\\", "deleted": 0, "packageTypeID": 2, "packageHash": "A437C6EC275CB20FC391F741A8D5B380", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 236, "displayName": "NVMS7000", "displayVersion": "2.6.2.50", "softwareID": 120, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "NVMS7000-2.6.2.50", "installerFile": "NVMS7000.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "Start-Process -NoNewWindow -Wait \\\"$InstallerFile\\\" -ArgumentList \\\"/s /f1.\\\\setup.iss /f2$InstallerLogFile\\\";\\r\\nif(Test-Path \\\"$LicenseFilePath\\\")\\r\\n{\\r\\nExpand-Archive -Path \\\"$LicenseFilePath\\\" -DestinationPath (Join-Path $env:ProgramFiles \\\"NVMS7000 Station\\\\NVMS7000\\\\NVMS7000 Client\\\") -Force\\r\\n}", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 0, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "FA048C4E8A5CC7E8783352DE4618644C", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 237, "displayName": "3CXPhone for Windows", "displayVersion": "15.5.12227.5", "softwareID": 12, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "3CXPhone-15.5.12227.5", "installerFile": "3CXPhoneforWindows15.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet  /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 2, "packageHash": "B50A4A503DA67CE2542D556F78487054", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 238, "displayName": "ManicTime", "displayVersion": "4.1.7.0", "softwareID": 18, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "ManicTime-4.1.7.0", "installerFile": "ManicTimeSetup.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c msiexec /i \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" CREATESTARTMENUSHORTCUT=0 RUNFORALLUSERS=1\\r\\nreg add HKLM\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run /v ManicTime /t REG_SZ /d \\\"\\\\\\\"C:\\\\Program Files (x86)\\\\Manictime\\\\ManicTime.exe\\\\\\\" /minimized\\\" && REM", "licenseTypeID": 1, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait cmd /c msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "9ABA702414D0928450327B6662EFDFDA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 239, "displayName": "Mimecast for Outlook", "displayVersion": "7.6.0.26320", "softwareID": 2, "sortOrder": 6, "cachingStrategyID": 2, "relativeCacheSourcePath": "MimecastforOutlook-7.6.0.26320", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\n$FileVersion = \\'%sqlDisplayVersion%\\'\\r\\nif(\\'@TargetFolderPath@\\' -like \\'*TargetFolderPath*\\')\\r\\n{\\r\\n    $TargetFolderPath = \\\"C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\"\\r\\n    $InstallerLogFile = \\'C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\MimecastforOutlook-$FileVersion\\\\Install.log\\'\\r\\n}\\r\\n$Platform = Get-ItemProperty -Path HKLM:\\\\SOFTWARE\\\\Microsoft\\\\Office\\\\ClickToRun\\\\Configuration -Name Platform | select -ExpandProperty Platform\\r\\nif($Platform -like \\\"x86\\\")\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (32 bit).msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $FileName = \\\"Mimecast for Outlook $FileVersion (64 bit).msi\\\"\\r\\n}\\r\\n\\r\\nStart-Process -Wait msiexec -ArgumentList \\\"/i `\\\"$TargetFolderPath\\\\$FileName`\\\" /quiet /l*v `\\\"$InstallerLogFile`\\\" Global.AutoUpdate.Enable=FALSE\\\";\\r\\n \\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": "# Test Mimecast LoadBehavior\\r\\n $block = {\\r\\n     New-PsDrive HKU Registry HKEY_Users -ErrorAction SilentlyContinue | Out-Null; \\r\\n     $Users = (get-process explorer -IncludeUserName).UserName\\r\\n     $Users | %{\\r\\n         $SIDS = (New-Object System.Security.Principal.NTAccount($_)).Translate([System.Security.Principal.SecurityIdentifier]).value\\r\\n     }\\r\\n     $SIDS | %{\\r\\n         (Get-ItemProperty -ErrorAction SilentlyContinue (\\'HKU:\\\\\\' + $_ + \\'\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior).LoadBehavior -band 1\\r\\n     }\\r\\n };\\r\\n  \\r\\n $Result = ((Get-ItemProperty (\\'HKLM:\\\\Software\\\\Microsoft\\\\Office\\\\Outlook\\\\Addins\\\\MimecastServicesForOutlook.AddinModule\\\\\\') -Name LoadBehavior -ErrorAction SilentlyContinue).LoadBehavior -band 1) -or (& $block)\\r\\n  \\r\\n  \\r\\n $Message = \\\"Mimecast Plugin is working\\\"\\r\\n if($Result  -eq $false)\\r\\n {\\r\\n     $Message = \\\"Mimecast Plugin not set to startup with Outlook\\\"     \\r\\n }\\r\\n \\r\\n   \\r\\n $obj = [ordered]@{}\\r\\n $obj.Result = $Result\\r\\n $obj.Message = $Message\\r\\n [string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")})) \\r\\n", "testFailedError": "Mimecast for Outlook is not set to start with Outlook", "upgradeStrategyID": 2, "upgradeActionTypeID": 1, "upgradeAction": "asdf", "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /x \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" && rem", "deleted": 0, "packageTypeID": 1, "packageHash": "CAD3B6435D5346F366EA82073F0D7ECF", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 240, "displayName": "GlobalProtect", "displayVersion": "4.1.5", "softwareID": 23, "sortOrder": 10, "cachingStrategyID": 2, "relativeCacheSourcePath": "PaloAltoGlobalProtect-4.1.5", "installerFile": "GlobalProtect64.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect64.msi\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$Installer = \\\"@TargetFolderPath@\\\\GlobalProtect.msi\\\"\\r\\n}\\r\\n\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" /qn /l*v \\\"$InstallerLogFile\\\" ENABLEADVANCEDVIEW=\\\"no\\\" CANCHANGEPORTAL=\\\"no\\\" CONNECTMETHOD=\\\"user-logon\\\" DISABLEALLOWED=\\\"no\\\" USESSO=\\\"yes\\\"\\r\\n\\\"@\\r\\n\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Uninstall-Package GlobalProtect", "deleted": 0, "packageTypeID": 2, "packageHash": "ABD9261BFE9940F943CD959AAF291032", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 241, "displayName": "Citrix Receiver", "displayVersion": "14.12.0.18020", "softwareID": 121, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "CitrixReceiver-14.12.0.18020", "installerFile": "CitrixReceiver.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$args = @\\\"\\r\\n/c \\\"$InstallerFile\\\" /silent /noreboot /AutoUpdateCheck=disabled\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "Get-Package \\\"Citrix Receiver*\\\" |Uninstall-Package\\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "94B413DA74A1DB68D4F127A96C0745A7", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 242, "displayName": "Immense Direct", "displayVersion": "1.80.0", "softwareID": 6, "sortOrder": 4, "cachingStrategyID": 2, "relativeCacheSourcePath": "ImmenseDirect-1.80", "installerFile": "ImmenseDirect.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait msiexec /i \\\"$InstallerFile\\\" /quiet /l*v \\\"$InstallerLogFile\\\" & cls", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 1, "uninstallActionTypeID": 2, "uninstallAction": "$ProfileList = (\\'hklm:\\\\SOFTWARE\\\\Microsoft\\\\Windows NT\\\\CurrentVersion\\\\ProfileList\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue )\\r\\n$Paths = ($ProfileList | ?{$_.PSChildName.Length -gt 10} | %{$_.ProfileImagePath}) \\r\\n$Executables = @()\\r\\n$Paths | %{\\r\\n    $Path = $_\\r\\n    $LocalAppData = $Path + \\\"\\\\AppData\\\\Local\\\\deskdirectorportal\\\"\\r\\n    $RoamingAppData = $Path + \\\"\\\\AppData\\\\Roaming\\\\DeskDirector Portal\\\"\\r\\n    if((Test-Path $LocalAppData) -and (Test-Path $RoamingAppData))\\r\\n    {\\r\\n        $Executables += dir $LocalAppData | ?{$_.PSIsContainer -and $_.name -like \\'app-*\\'} | %{ dir $_.FullName *.exe }\\r\\n    }\\r\\n    $ExecutableNames = $Executables | Group Name | %{$_.Name}\\r\\n    $ExecutableNames | %{\\r\\n        taskkill /IM $_ /F 2>&1 | Out-Null\\r\\n    }\\r\\n    Remove-Item $LocalAppdata -Recurse -Force -ErrorAction SilentlyContinue\\r\\n    Remove-Item $RoamingAppData -Recurse -Force -ErrorAction SilentlyContinue\\r\\n}\\r\\n\\r\\n$ProgramsToUninstall = \\'hklm:/Software/Microsoft/Windows/CurrentVersion/Uninstall\\',\\'hklm:/Software/WOW6432Node/Microsoft/Windows/CurrentVersion/Uninstall\\' | gci -ErrorAction SilentlyContinue | Get-ItemProperty -ErrorAction SilentlyContinue  | ?{$_.DisplayName -like \\'Immense Direct*\\' -or $_.DisplayName -like \\'Dudley*Machine*\\'}\\r\\n\\r\\n$ProgramsToUninstall | %{\\r\\n    $Program = $_\\r\\n    $UninstallString = $Program.UninstallString.Replace(\\\"/I\\\",\\\"/X\\\") + \\\" /qn\\\"\\r\\n    iex \\\"cmd /c \\'$UninstallString\\'\\\"\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n$Result = $true\\r\\n$Message = \\\"DeskDirector 1.XX Removed Successfully\\\"\\r\\n$obj = @{}\\r\\n$obj.TestResult = $Result\\r\\n$obj.TestResultMessage = $Message\\r\\n[string]::Join(\\\"|\\\",($obj.GetEnumerator() | %{$_.Name.ToString().Replace(\\\"|\\\",\\\"%7C\\\") + \\\"=\\\" + $_.Value.ToString().Replace(\\\"|\\\",\\\"%7C\\\")}))\\r\\n  \\r\\n", "deleted": 0, "packageTypeID": 2, "packageHash": "42F1D6FA622C5D214F7981493FB043D7", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 243, "displayName": "CrowdStrike Windows Sensor", "displayVersion": "4.11.7702.0", "softwareID": 35, "sortOrder": 23, "cachingStrategyID": 2, "relativeCacheSourcePath": "CrowdStrikeWindowsSensor-4.11.7702.0", "installerFile": "WindowsSensor.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@\\\"$InstallerFile\\\" /install /quiet /norestart CID=$LicenseValue", "licenseTypeID": 2, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 2, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "61747DFE7EA77F40E02542932A724F5B", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 246, "displayName": "McAfee DLP Endpoint", "displayVersion": "11.0.600.72", "softwareID": 33, "sortOrder": 9, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeDLPEndpoint-11.0.600.72", "installerFile": "DLPAgentInstaller.x64.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait \\\"$InstallerFile\\\" /exenoui /exelog \\\"$InstallerLogFile\\\" && rem", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {EEAFAB3F-FA0C-425B-94A0-DBFB2887F644} /qn REMOVE=ALL", "deleted": 0, "packageTypeID": 2, "packageHash": "22983BA5DCB5C7FBC6E3C1DE6FB94475", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 247, "displayName": "McAfee Agent", "displayVersion": "5.5.1.388", "softwareID": 29, "sortOrder": 5, "cachingStrategyID": 2, "relativeCacheSourcePath": "McAfeeAgent-5.5.1.388", "installerFile": "FramePkg.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"McAfee\\\" /b /wait \\\"$InstallerFile\\\" /Install=Agent /Silent", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 1, "uninstallAction": "@start \\\"\\\" /b /wait msiexec /X {80684F9A-6B01-4F3F-A8C7-C4B7BDF072F1} /qn FORCEUNINSTALL=1", "deleted": 0, "packageTypeID": 2, "packageHash": "637D125FFF128FFE70424606AD03C9D8", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 248, "displayName": "Microsoft Teams", "displayVersion": "1.0.0.25457", "softwareID": 43, "sortOrder": 2, "cachingStrategyID": 2, "relativeCacheSourcePath": "MicrosoftTeams-1.0.00.25457", "installerFile": "Teams_windows.exe", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 1, "installAction": "@start \\\"\\\" /b /wait cmd /c \\\"$InstallerFile\\\" -s", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 2, "packageHash": "DA0293C0746E44F33BF35F46036539AA", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 250, "displayName": "Navisworks Freedom 2018", "displayVersion": "15.2.1319.56", "softwareID": 122, "sortOrder": 1, "cachingStrategyID": 2, "relativeCacheSourcePath": "AutodeskNavisworksFreedom-2018", "installerFile": null, "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$Path = \\\"C:\\\\Windows\\\\Temp\\\\ManagedSoftwareInstallers\\\\AutodeskNavisworksFreedom-2018\\\"\\r\\n$InstallP = \\\"$Path\\\\Img\\\\setup.exe\\\"\\r\\n$ConfigPath = \\\"$Path\\\\Img\\\\Navisworks.ini\\\"\\r\\n$args = @\\\"\\r\\n/c $InstallP /W /q /I $ConfigPath\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 0, "testAction": null, "testFailedError": null, "upgradeStrategyID": 2, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 0, "uninstallAction": null, "deleted": 0, "packageTypeID": 1, "packageHash": "D31E57A4CFC9F9088B139EF1EC6F5876", "productKeyRegExFilter": null, "productKeyDisplayName": null}, {"softwareVersionID": 251, "displayName": "FortiClient", "displayVersion": "6.0.2.128", "softwareID": 8, "sortOrder": 6, "cachingStrategyID": 1, "relativeCacheSourcePath": "FortiClient-6.0.2.0128", "installerFile": "FortiClient.msi", "cacheActionTypeID": 0, "cacheAction": null, "installActionTypeID": 2, "installAction": "$TargetFolderPath = \\'@TargetFolderPath@\\'\\r\\nif($env:PROCESSOR_ARCHITECTURE -like \\\"AMD64\\\")\\r\\n{\\r\\n    $ProperInstallFile = \\\"x64\\\\FortiClient.msi\\\"\\r\\n    $ProperMSTFile = \\\"x64\\\\FortiClient.mst\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProperInstallFile = \\\"x86\\\\FortiClient.msi\\\"\\r\\n    $ProperMSTFile = \\\"x64\\\\FortiClient.mst\\\"\\r\\n}\\r\\n$Installer = Join-Path $TargetFolderPath $ProperInstallFile\\r\\n$MST = Join-Path $TargetFolderPath $ProperMSTFile\\r\\n$args = @\\\"\\r\\n/c msiexec /i \\\"$Installer\\\" TRANSFORMS=$ProperMSTFile /qn /l*v \\\"$InstallerLogFile\\\"\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args\\r\\n", "licenseTypeID": 0, "testRequired": 0, "testActionTypeID": 2, "testAction": null, "testFailedError": null, "upgradeStrategyID": 1, "upgradeActionTypeID": 0, "upgradeAction": null, "uninstallIfNotInTarget": 0, "uninstallActionTypeID": 2, "uninstallAction": "$args = @\\\"\\r\\n/c msiexec /X \\\"$InstallerFile\\\" /qn /l*v \\\"$InstallerLogFile\\\" ANCESTORFOUND=1\\r\\n\\\"@\\r\\nStart-Process -Wait -NoNewWindow cmd -ArgumentList $args", "deleted": 0, "packageTypeID": 1, "packageHash": "54D63AA59CD761E1937E5C8910419B7C", "productKeyRegExFilter": null, "productKeyDisplayName": null}]