[{"targetAssignmentID": 2, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1179, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 3, "targetTypeID": 3, "softwareVersionID": 3, "softwareDisplayName": null, "targetID": 1179, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 5, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1179, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 6, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1193, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 14, "targetTypeID": 4, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 15, "targetTypeID": 4, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": "CN=U - SAFETY,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 16, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1176, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 17, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1176, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 34, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 35, "targetTypeID": 3, "softwareVersionID": 57, "softwareDisplayName": null, "targetID": 1064, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 36, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1183, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 37, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1184, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 38, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2871, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 40, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 77, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 41, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2870, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 43, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2223, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 44, "targetTypeID": 2, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 2223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 53, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 3124, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 54, "targetTypeID": 3, "softwareVersionID": 21, "softwareDisplayName": null, "targetID": 1194, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 56, "targetTypeID": 2, "softwareVersionID": 20, "softwareDisplayName": null, "targetID": 3121, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 58, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 60, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1196, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 61, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 772, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 62, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 757, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 64, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1198, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 106, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1200, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 108, "targetTypeID": 2, "softwareVersionID": 24, "softwareDisplayName": null, "targetID": 3122, "exception": 1, "desiredProductKey": null, "licenseFile": "ImmenseNetworks\\\\veeamagentWin2017.lic", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 110, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3243, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 118, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1184, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 120, "targetTypeID": 3, "softwareVersionID": 36, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": null, "licenseFile": "QEDDE-C888A-6N000-67510-11131", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 128, "targetTypeID": 3, "softwareVersionID": 39, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 130, "targetTypeID": 3, "softwareVersionID": 41, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 131, "targetTypeID": 3, "softwareVersionID": 43, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 141, "targetTypeID": 3, "softwareVersionID": 42, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 142, "targetTypeID": 3, "softwareVersionID": 52, "softwareDisplayName": null, "targetID": 101, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 167, "targetTypeID": 3, "softwareVersionID": 56, "softwareDisplayName": null, "targetID": 1192, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 173, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 251, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 175, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 251, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 177, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 251, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 179, "targetTypeID": 3, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 251, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 185, "targetTypeID": 2, "softwareVersionID": 64, "softwareDisplayName": null, "targetID": 3281, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 186, "targetTypeID": 3, "softwareVersionID": 65, "softwareDisplayName": null, "targetID": 1220, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 187, "targetTypeID": 2, "softwareVersionID": 65, "softwareDisplayName": null, "targetID": 3269, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 196, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1220, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 202, "targetTypeID": 2, "softwareVersionID": 248, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 206, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 3292, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 207, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 3220, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 208, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 3242, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 209, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 3291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 211, "targetTypeID": 3, "softwareVersionID": 238, "softwareDisplayName": null, "targetID": 1293, "exception": 0, "desiredProductKey": null, "licenseFile": "GatorMillworks\\\\ManicTimeSettings.xml", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 214, "targetTypeID": 3, "softwareVersionID": 67, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 215, "targetTypeID": 3, "softwareVersionID": 67, "softwareDisplayName": null, "targetID": 1189, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 216, "targetTypeID": 3, "softwareVersionID": 78, "softwareDisplayName": null, "targetID": 1225, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 217, "targetTypeID": 4, "softwareVersionID": 79, "softwareDisplayName": null, "targetID": "CN=Quickbooks,OU=Groups,OU=Gator Staff,DC=GatorMillworks,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": "QB_LICENSENUM=1837-6831-7213-526 QB_PRODUCTNUM=381-236", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 219, "targetTypeID": 3, "softwareVersionID": 80, "softwareDisplayName": null, "targetID": 1227, "exception": 0, "desiredProductKey": null, "licenseFile": "AI_ORGANIZATION=\\\"Pacific Solutions\\\" AI_LICENSEKEY=393JT-NNKJ5-7766J-K8XVX-M2MKN-J8JJV-115NJ", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 220, "targetTypeID": 3, "softwareVersionID": 81, "softwareDisplayName": null, "targetID": 1227, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 221, "targetTypeID": 2, "softwareVersionID": 80, "softwareDisplayName": null, "targetID": 3304, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 222, "targetTypeID": 2, "softwareVersionID": 81, "softwareDisplayName": null, "targetID": 3304, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 224, "targetTypeID": 2, "softwareVersionID": 203, "softwareDisplayName": null, "targetID": 2673, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 226, "targetTypeID": 2, "softwareVersionID": 83, "softwareDisplayName": null, "targetID": 3304, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 227, "targetTypeID": 3, "softwareVersionID": 83, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 228, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 3455, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 229, "targetTypeID": 3, "softwareVersionID": 87, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": "PodPack\\\\client.conf", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 230, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3593, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 231, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 2809, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 236, "targetTypeID": 3, "softwareVersionID": 92, "softwareDisplayName": null, "targetID": 1228, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 237, "targetTypeID": 2, "softwareVersionID": 92, "softwareDisplayName": null, "targetID": 3269, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 241, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2818, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 244, "targetTypeID": 3, "softwareVersionID": 94, "softwareDisplayName": null, "targetID": 1192, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 247, "targetTypeID": 3, "softwareVersionID": 245, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 254, "targetTypeID": 2, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 3982, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 256, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 257, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 2212, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 258, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 260, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 263, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1287, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 264, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 267, "targetTypeID": 3, "softwareVersionID": 102, "softwareDisplayName": null, "targetID": 1288, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 268, "targetTypeID": 3, "softwareVersionID": 173, "softwareDisplayName": null, "targetID": 1288, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 269, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1353, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 271, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1179, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 272, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 274, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1288, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 276, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1289, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 277, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 72, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 278, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1290, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 279, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1288, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 280, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 281, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1226, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 282, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1226, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 283, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 284, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1292, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 285, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 286, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1292, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 287, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 288, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1293, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 289, "targetTypeID": 3, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 1293, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 292, "targetTypeID": 2, "softwareVersionID": 105, "softwareDisplayName": null, "targetID": 4004, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 293, "targetTypeID": 2, "softwareVersionID": 104, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 296, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4048, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 298, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4050, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 300, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4051, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 317, "targetTypeID": 3, "softwareVersionID": 107, "softwareDisplayName": null, "targetID": 1293, "exception": 0, "desiredProductKey": null, "licenseFile": "gator.mst", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 319, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3157, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 324, "targetTypeID": 3, "softwareVersionID": 240, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 328, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 966, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 329, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1318, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 332, "targetTypeID": 3, "softwareVersionID": 237, "softwareDisplayName": null, "targetID": 1296, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 334, "targetTypeID": 2, "softwareVersionID": 112, "softwareDisplayName": null, "targetID": 4015, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 335, "targetTypeID": 2, "softwareVersionID": 112, "softwareDisplayName": null, "targetID": 3886, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 336, "targetTypeID": 2, "softwareVersionID": 112, "softwareDisplayName": null, "targetID": 3838, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 337, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 2263, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 338, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 339, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 280, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 340, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 2466, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 341, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 3005, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 343, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 964, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 345, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1104, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 346, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1296, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 348, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5406, "exception": 1, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 350, "targetTypeID": 2, "softwareVersionID": 114, "softwareDisplayName": null, "targetID": 1762, "exception": 0, "desiredProductKey": null, "licenseFile": "CornerstoneFlooring\\\\ManicTimeSettings.xml", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 351, "targetTypeID": 2, "softwareVersionID": 114, "softwareDisplayName": null, "targetID": 2393, "exception": 0, "desiredProductKey": null, "licenseFile": "CornerstoneFlooring\\\\ManicTimeSettings.xml", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 352, "targetTypeID": 2, "softwareVersionID": 114, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": "CornerstoneFlooring\\\\ManicTimeSettings.xml", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 353, "targetTypeID": 1, "softwareVersionID": 57, "softwareDisplayName": null, "targetID": null, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 355, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 3012, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 356, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2467, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 358, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1298, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 359, "targetTypeID": 3, "softwareVersionID": 88, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 361, "targetTypeID": 3, "softwareVersionID": 115, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 1, "changed": 0}, {"targetAssignmentID": 363, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 3466, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 366, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 36, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 367, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1478, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 372, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1998, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 374, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 3411, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 380, "targetTypeID": 1, "softwareVersionID": 11, "softwareDisplayName": null, "targetID": null, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 381, "targetTypeID": 3, "softwareVersionID": 237, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 382, "targetTypeID": 2, "softwareVersionID": 103, "softwareDisplayName": null, "targetID": 3272, "exception": 0, "desiredProductKey": null, "licenseFile": "NGXGR-VJ3H2-4FBJM-MY4BC-KW3GT", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 384, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4186, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 385, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 70, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 386, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3848, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 389, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3509, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 390, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3567, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 391, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3455, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 392, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3563, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 393, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3864, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 394, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 3486, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 395, "targetTypeID": 2, "softwareVersionID": 119, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 396, "targetTypeID": 2, "softwareVersionID": 23, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 401, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1300, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 403, "targetTypeID": 2, "softwareVersionID": 121, "softwareDisplayName": null, "targetID": 2587, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 409, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1309, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 415, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 3378, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 429, "targetTypeID": 2, "softwareVersionID": 126, "softwareDisplayName": null, "targetID": 4402, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 433, "targetTypeID": 4, "softwareVersionID": 138, "softwareDisplayName": null, "targetID": "CN=Estimating Software,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": "BB_EDITION=2 BB_SERIALNUMBER=1377665 BB_ProductKey=14F86-5A3N9T6", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 434, "targetTypeID": 4, "softwareVersionID": 138, "softwareDisplayName": null, "targetID": "CN=Project Management-Security,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": "BB_EDITION=2 BB_SERIALNUMBER=1377665 BB_ProductKey=14F86-5A3N9T6", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 435, "targetTypeID": 2, "softwareVersionID": 138, "softwareDisplayName": null, "targetID": 4402, "exception": 0, "desiredProductKey": null, "licenseFile": "BB_EDITION=2 BB_SERIALNUMBER=1377665 BB_ProductKey=14F86-5A3N9T6", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 436, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4419, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 437, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4422, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 438, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4416, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 439, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4049, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 440, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4418, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 442, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4260, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 443, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4435, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 445, "targetTypeID": 3, "softwareVersionID": 246, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 446, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2115, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 447, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3195, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 448, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3851, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 449, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 3849, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 456, "targetTypeID": 3, "softwareVersionID": 243, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": "6D565459FD8348C780D0BA5750EC6111-F2", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 458, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4498, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 459, "targetTypeID": 5, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": "# Determines if a particular user has an Office subscription\\n # \\n #\\n #\\n $DesiredPlan = \\\"OFFICESUBSCRIPTION\\\"\\n #$UPN = \\'@UserPrincipalName@\\'\\n $TenantID = \\\"@Office365TenantID@\\\"\\n $O365Creds = Get-LabtechData \\\"select Username, CONVERT( AES_DECRYPT(passwords.Password, SHA(concat(\\' \\', clientid + 1))) USING UTF8) `Password` FROM Passwords where clientid=1 and Title like \\'Office365\\'\\\"\\n \\n if([string]::IsNullOrEmpty($UPN) -or $UPN -like \\\"*@UPN@*\\\")\\n {\\n     $GetUPNQuery = \\\"select Email from plugin_ad_users where ObjectSID = (SELECT UserSID FROM labtech.plugin_indeploy_logon where computerid=$ComputerID order by LogonID desc limit 1)\\\"\\n     $UPN = Get-LabTechData $GetUPNQuery | select -first 1 -ExpandProperty Email     \\n }\\n Import-Module MsOnline\\n $Credential = New-Object System.Management.Automation.PSCredential($O365Creds.Username,($O365Creds.Password | ConvertTo-SecureString -asPlainText -Force))\\n Connect-MsolService -Credential $Credential\\n  \\n $TenantsWithDomains = Get-MsolPartnerContract | %{ new-object psobject -Property @{\\\"Domains\\\" = ($_ | Get-MsolDomain  | select -ExpandProperty Name); \\\"Tenant\\\"=$_} } \\n $TenantWithDomain = $TenantsWithDomains | ?{$_.Domains -contains $UPN.Split(\\\"@\\\")[1]}\\n if($TenantWithDomain)\\n {\\n     $MsolUser = $TenantWithDomain.tenant | Get-MsolUser -UserPrincipalName $UPN\\n }\\n if($MsolUser -eq $null)\\n {\\n     try\\n     {\\n         $MsolUser = Get-MsolPartnerContract | Get-MsolUser -UserPrincipalName $UPN -ErrorAction SilentlyContinue\\n     }\\n     catch\\n     {\\n         $MsolUser = $null\\n     }\\n }\\n if($Msoluser)\\n {\\n     $ServicePlans = $MsolUser | select -ExpandProperty Licenses | select -ExpandProperty ServiceStatus | ?{$_.ProvisioningStatus -like \\\"Success\\\"} \\n     $ServicePlans.ServicePlan.ServiceName -contains $DesiredPlan\\n }\\n else\\n {\\n     $false\\n }\\n \\n", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 460, "targetTypeID": 5, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": "# Determines if a particular user has an Office subscription\\n # \\n #\\n #\\n $DesiredPlan = \\\"OFFICE_BUSINESS\\\"\\n #$UPN = \\'@UserPrincipalName@\\'\\n $TenantID = \\\"@Office365TenantID@\\\"\\n $O365Creds = Get-LabtechData \\\"select Username, CONVERT( AES_DECRYPT(passwords.Password, SHA(concat(\\' \\', clientid + 1))) USING UTF8) `Password` FROM Passwords where clientid=1 and Title like \\'Office365\\'\\\"\\n \\n if([string]::IsNullOrEmpty($UPN) -or $UPN -like \\\"*@UPN@*\\\")\\n {\\n     $GetUPNQuery = \\\"select Email from plugin_ad_users where ObjectSID = (SELECT UserSID FROM labtech.plugin_indeploy_logon where computerid=$ComputerID order by LogonID desc limit 1)\\\"\\n     $UPN = Get-LabTechData $GetUPNQuery | select -first 1 -ExpandProperty Email     \\n }\\n Import-Module MsOnline\\n $Credential = New-Object System.Management.Automation.PSCredential($O365Creds.Username,($O365Creds.Password | ConvertTo-SecureString -asPlainText -Force))\\n Connect-MsolService -Credential $Credential\\n  \\n $TenantsWithDomains = Get-MsolPartnerContract | %{ new-object psobject -Property @{\\\"Domains\\\" = ($_ | Get-MsolDomain  | select -ExpandProperty Name); \\\"Tenant\\\"=$_} } \\n $TenantWithDomain = $TenantsWithDomains | ?{$_.Domains -contains $UPN.Split(\\\"@\\\")[1]}\\n if($TenantWithDomain)\\n {\\n     $MsolUser = $TenantWithDomain.tenant | Get-MsolUser -UserPrincipalName $UPN\\n }\\n if($MsolUser -eq $null)\\n {\\n     try\\n     {\\n         $MsolUser = Get-MsolPartnerContract | Get-MsolUser -UserPrincipalName $UPN -ErrorAction SilentlyContinue\\n     }\\n     catch\\n     {\\n         $MsolUser = $null\\n     }\\n }\\n if($Msoluser)\\n {\\n     $ServicePlans = $MsolUser | select -ExpandProperty Licenses | select -ExpandProperty ServiceStatus | ?{$_.ProvisioningStatus -like \\\"Success\\\"} \\n     $ServicePlans.ServicePlan.ServiceName -contains $DesiredPlan\\n }\\n else\\n {\\n     $false\\n }\\n \\n", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 471, "targetTypeID": 2, "softwareVersionID": 120, "softwareDisplayName": null, "targetID": 4650, "exception": 0, "desiredProductKey": null, "licenseFile": "<PERSON><PERSON><PERSON>\\\\Silent.mst", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 473, "targetTypeID": 3, "softwareVersionID": 139, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 475, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1310, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 477, "targetTypeID": 3, "softwareVersionID": 237, "softwareDisplayName": null, "targetID": 1194, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 478, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4464, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 479, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4469, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 481, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4050, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 482, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4484, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 483, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4253, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 484, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4471, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 485, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4478, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 486, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4060, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 489, "targetTypeID": 3, "softwareVersionID": 217, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 490, "targetTypeID": 2, "softwareVersionID": 144, "softwareDisplayName": null, "targetID": 4004, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 491, "targetTypeID": 3, "softwareVersionID": 139, "softwareDisplayName": null, "targetID": 1296, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 492, "targetTypeID": 3, "softwareVersionID": 139, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 493, "targetTypeID": 3, "softwareVersionID": 139, "softwareDisplayName": null, "targetID": 1194, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 494, "targetTypeID": 3, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 1290, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 495, "targetTypeID": 3, "softwareVersionID": 149, "softwareDisplayName": null, "targetID": 1289, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 518, "targetTypeID": 2, "softwareVersionID": 150, "softwareDisplayName": null, "targetID": 4004, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 519, "targetTypeID": 2, "softwareVersionID": 152, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 520, "targetTypeID": 2, "softwareVersionID": 151, "softwareDisplayName": null, "targetID": 4004, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 521, "targetTypeID": 2, "softwareVersionID": 151, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 522, "targetTypeID": 2, "softwareVersionID": 150, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 523, "targetTypeID": 2, "softwareVersionID": 153, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 524, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 526, "targetTypeID": 3, "softwareVersionID": 67, "softwareDisplayName": null, "targetID": 1196, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 527, "targetTypeID": 2, "softwareVersionID": 155, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 528, "targetTypeID": 2, "softwareVersionID": 156, "softwareDisplayName": null, "targetID": 1231, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 529, "targetTypeID": 2, "softwareVersionID": 156, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 530, "targetTypeID": 3, "softwareVersionID": 155, "softwareDisplayName": null, "targetID": 1291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 537, "targetTypeID": 2, "softwareVersionID": 161, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 538, "targetTypeID": 2, "softwareVersionID": 160, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 539, "targetTypeID": 3, "softwareVersionID": 21, "softwareDisplayName": null, "targetID": 1223, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 540, "targetTypeID": 3, "softwareVersionID": 21, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 541, "targetTypeID": 3, "softwareVersionID": 21, "softwareDisplayName": null, "targetID": 1296, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 542, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 545, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1571, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 546, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 75, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 548, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1063, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 550, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 551, "targetTypeID": 4, "softwareVersionID": 163, "softwareDisplayName": null, "targetID": "CN=FloorRight Users,OU=SBSUsers,OU=Users,OU=MyBusiness,DC=Cornerstone,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 552, "targetTypeID": 3, "softwareVersionID": 165, "softwareDisplayName": null, "targetID": 1227, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 553, "targetTypeID": 2, "softwareVersionID": 163, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 554, "targetTypeID": 2, "softwareVersionID": 165, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 555, "targetTypeID": 2, "softwareVersionID": 91, "softwareDisplayName": null, "targetID": 4726, "exception": 1, "desiredProductKey": "FPM27108", "licenseFile": "ImmenseNetworks\\\\fpmkey8.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 556, "targetTypeID": 2, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 557, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4037, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 558, "targetTypeID": 2, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 2281, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 559, "targetTypeID": 2, "softwareVersionID": 167, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 560, "targetTypeID": 2, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 4188, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 561, "targetTypeID": 2, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 3202, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 562, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1182, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 563, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 564, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 72, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 565, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1184, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 566, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1189, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 567, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1338, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 568, "targetTypeID": 2, "softwareVersionID": 229, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 569, "targetTypeID": 2, "softwareVersionID": 229, "softwareDisplayName": null, "targetID": 4858, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 571, "targetTypeID": 2, "softwareVersionID": 150, "softwareDisplayName": null, "targetID": 4862, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 572, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 4858, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 573, "targetTypeID": 2, "softwareVersionID": 161, "softwareDisplayName": null, "targetID": 4858, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 574, "targetTypeID": 3, "softwareVersionID": 229, "softwareDisplayName": null, "targetID": 1176, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 575, "targetTypeID": 4, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 576, "targetTypeID": 4, "softwareVersionID": 161, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 577, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 4862, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 578, "targetTypeID": 2, "softwareVersionID": 150, "softwareDisplayName": null, "targetID": null, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 579, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 4697, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 580, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 4696, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 581, "targetTypeID": 2, "softwareVersionID": 171, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 582, "targetTypeID": 2, "softwareVersionID": 171, "softwareDisplayName": null, "targetID": 4995, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 583, "targetTypeID": 2, "softwareVersionID": 241, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 585, "targetTypeID": 2, "softwareVersionID": 179, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 633, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 634, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4222, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 635, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4416, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 636, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4419, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 637, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4909, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 638, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4422, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 639, "targetTypeID": 2, "softwareVersionID": 176, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 640, "targetTypeID": 2, "softwareVersionID": 81, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 641, "targetTypeID": 2, "softwareVersionID": 181, "softwareDisplayName": null, "targetID": 171, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 644, "targetTypeID": 2, "softwareVersionID": 144, "softwareDisplayName": null, "targetID": 4726, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 645, "targetTypeID": 3, "softwareVersionID": 131, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 647, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4903, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 659, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 660, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4719, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 661, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4725, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 662, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4934, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 663, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4824, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 664, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4723, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 665, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4718, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 666, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4809, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 667, "targetTypeID": 2, "softwareVersionID": 184, "softwareDisplayName": null, "targetID": 4812, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 668, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4872, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 669, "targetTypeID": 3, "softwareVersionID": 144, "softwareDisplayName": null, "targetID": 1301, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 670, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 671, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 4947, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 672, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1195, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 673, "targetTypeID": 3, "softwareVersionID": 189, "softwareDisplayName": null, "targetID": 1301, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 674, "targetTypeID": 3, "softwareVersionID": 188, "softwareDisplayName": null, "targetID": 1301, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 675, "targetTypeID": 2, "softwareVersionID": 238, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 677, "targetTypeID": 2, "softwareVersionID": 190, "softwareDisplayName": null, "targetID": 4935, "exception": 0, "desiredProductKey": null, "licenseFile": "Le<PERSON>ine\\\\AutocadLT2019.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 678, "targetTypeID": 2, "softwareVersionID": 126, "softwareDisplayName": null, "targetID": 4972, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 680, "targetTypeID": 2, "softwareVersionID": 33, "softwareDisplayName": null, "targetID": 2887, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 681, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1351, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 683, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4473, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 684, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 4995, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 685, "targetTypeID": 2, "softwareVersionID": 190, "softwareDisplayName": null, "targetID": 4995, "exception": 0, "desiredProductKey": null, "licenseFile": "DSCR\\\\AutocadLT2019.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 686, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 4995, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 687, "targetTypeID": 3, "softwareVersionID": 239, "softwareDisplayName": null, "targetID": 1178, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 688, "targetTypeID": 3, "softwareVersionID": 239, "softwareDisplayName": null, "targetID": 1176, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 689, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1692, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 693, "targetTypeID": 2, "softwareVersionID": 197, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": "BR\\\\TimeClickWS.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 694, "targetTypeID": 3, "softwareVersionID": 191, "softwareDisplayName": null, "targetID": 1320, "exception": 0, "desiredProductKey": null, "licenseFile": "10.15.1.41", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 696, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 3975, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 697, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 3012, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 698, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 3411, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 699, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 3989, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 700, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 4451, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 701, "targetTypeID": 2, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 3986, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 702, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 3374, "exception": 0, "desiredProductKey": null, "licenseFile": "AHMC8-S5HSC-CBYDD-CZAZF-YAD3A", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 703, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 4684, "exception": 0, "desiredProductKey": null, "licenseFile": "AHMC8-S5HSC-CBYDD-CZAZF-YAD3A", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 704, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 3381, "exception": 0, "desiredProductKey": null, "licenseFile": "AHMC8-S5HSC-CBYDD-CZAZF-YAD3A", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 705, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 4685, "exception": 0, "desiredProductKey": null, "licenseFile": "AHMC8-S5HSC-CBYDD-CZAZF-YAD3A", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 706, "targetTypeID": 2, "softwareVersionID": 125, "softwareDisplayName": null, "targetID": 4655, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 708, "targetTypeID": 3, "softwareVersionID": 239, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 709, "targetTypeID": 2, "softwareVersionID": 218, "softwareDisplayName": null, "targetID": 103, "exception": 0, "desiredProductKey": null, "licenseFile": "LICENSEKEY=\\\"J8NH3-0G926-89224-87288-5009B-4HZKE-693VS-GKHYS-6A5GP-QS1N4\\\" NAME=\\\"<PERSON>\\\" COMPANY=\\\"DeepSouthCrane and Rigging\\", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 710, "targetTypeID": 2, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 4123, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 711, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 4123, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 712, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 4175, "exception": 0, "desiredProductKey": null, "licenseFile": "AHMC8-S5HSC-CBYDD-CZAZF-YAD3A", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 713, "targetTypeID": 2, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 714, "targetTypeID": 2, "softwareVersionID": 194, "softwareDisplayName": null, "targetID": 2587, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 715, "targetTypeID": 3, "softwareVersionID": 231, "softwareDisplayName": null, "targetID": 1194, "exception": 0, "desiredProductKey": null, "licenseFile": "LongLawFirm\\\\SystemDir.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 716, "targetTypeID": 2, "softwareVersionID": 151, "softwareDisplayName": null, "targetID": null, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 718, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4418, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 719, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 4049, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 720, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1329, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 721, "targetTypeID": 3, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 722, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 4872, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 723, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1324, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 724, "targetTypeID": 3, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 1321, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 725, "targetTypeID": 2, "softwareVersionID": 201, "softwareDisplayName": null, "targetID": 4941, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 726, "targetTypeID": 3, "softwareVersionID": 202, "softwareDisplayName": null, "targetID": 1301, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 727, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1081, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 728, "targetTypeID": 3, "softwareVersionID": 197, "softwareDisplayName": null, "targetID": 1322, "exception": 0, "desiredProductKey": null, "licenseFile": "BR\\\\TimeClickWS.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 729, "targetTypeID": 3, "softwareVersionID": 198, "softwareDisplayName": null, "targetID": 1323, "exception": 0, "desiredProductKey": null, "licenseFile": "Nola\\\\TimeClickWS.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 730, "targetTypeID": 3, "softwareVersionID": 5, "softwareDisplayName": null, "targetID": 1325, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 731, "targetTypeID": 2, "softwareVersionID": 203, "softwareDisplayName": null, "targetID": 5110, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 743, "targetTypeID": 2, "softwareVersionID": 203, "softwareDisplayName": null, "targetID": 4378, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 746, "targetTypeID": 3, "softwareVersionID": 167, "softwareDisplayName": null, "targetID": 1081, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 747, "targetTypeID": 3, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 1331, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 748, "targetTypeID": 3, "softwareVersionID": 142, "softwareDisplayName": null, "targetID": 1328, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 749, "targetTypeID": 4, "softwareVersionID": 229, "softwareDisplayName": null, "targetID": "CN=SUPERINTENDENTS,OU=Contacts,DC=DeepSouth,DC=local", "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 750, "targetTypeID": 4, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": "CN=SUPERINTENDENTS,OU=Contacts,DC=DeepSouth,DC=local", "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 751, "targetTypeID": 4, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 752, "targetTypeID": 4, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": "CN=SAFETY USERS,OU=Departments,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 753, "targetTypeID": 4, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": "CN=BR CLERICAL,OU=Contacts,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 754, "targetTypeID": 4, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": "CN=HOU CLERICAL,OU=Contacts,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 755, "targetTypeID": 4, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": "CN=LC CLERICAL,OU=Contacts,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 756, "targetTypeID": 4, "softwareVersionID": 171, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 757, "targetTypeID": 6, "softwareVersionID": 206, "softwareDisplayName": null, "targetID": "<EMAIL>", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 758, "targetTypeID": 3, "softwareVersionID": 133, "softwareDisplayName": null, "targetID": 1301, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 759, "targetTypeID": 3, "softwareVersionID": 134, "softwareDisplayName": null, "targetID": 1301, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 761, "targetTypeID": 3, "softwareVersionID": 135, "softwareDisplayName": null, "targetID": 1301, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 762, "targetTypeID": 3, "softwareVersionID": 136, "softwareDisplayName": null, "targetID": 1301, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 763, "targetTypeID": 3, "softwareVersionID": 137, "softwareDisplayName": null, "targetID": 1301, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 764, "targetTypeID": 2, "softwareVersionID": 208, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 765, "targetTypeID": 4, "softwareVersionID": 208, "softwareDisplayName": null, "targetID": "CN=Y - ESTIMATING,OU=Drives,OU=DSCR Baton Rouge,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 767, "targetTypeID": 3, "softwareVersionID": 239, "softwareDisplayName": null, "targetID": 1197, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 768, "targetTypeID": 3, "softwareVersionID": 21, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 769, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 803, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 770, "targetTypeID": 2, "softwareVersionID": 190, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": "DSCR\\\\AutocadLT2019.ini", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 771, "targetTypeID": 2, "softwareVersionID": 142, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 772, "targetTypeID": 3, "softwareVersionID": 204, "softwareDisplayName": null, "targetID": 1327, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 773, "targetTypeID": 2, "softwareVersionID": 137, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 774, "targetTypeID": 2, "softwareVersionID": 133, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 775, "targetTypeID": 2, "softwareVersionID": 134, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 776, "targetTypeID": 2, "softwareVersionID": 135, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 777, "targetTypeID": 2, "softwareVersionID": 136, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 779, "targetTypeID": 2, "softwareVersionID": 171, "softwareDisplayName": null, "targetID": 5162, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 780, "targetTypeID": 2, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 781, "targetTypeID": 4, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": "CN=Estimating Software,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 782, "targetTypeID": 4, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": "CN=Drywall Estimating,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 783, "targetTypeID": 4, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": "CN=Estimating Software,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 784, "targetTypeID": 4, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": "CN=Drywall Estimating,OU=Security Groups,OU=Groups,DC=lemoinecompany,DC=com", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 785, "targetTypeID": 2, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": 4318, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 786, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4318, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 787, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4872, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 788, "targetTypeID": 2, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": 4872, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 789, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4647, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 790, "targetTypeID": 2, "softwareVersionID": 215, "softwareDisplayName": null, "targetID": 4647, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 791, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4362, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 792, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4661, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 793, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4936, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 794, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 5073, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 795, "targetTypeID": 3, "softwareVersionID": 211, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 796, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 5191, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 797, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1321, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 798, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4971, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 799, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4891, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 800, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4335, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 801, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4410, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 802, "targetTypeID": 3, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1336, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 803, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4833, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 804, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 5193, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 805, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 5230, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 806, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 5090, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 807, "targetTypeID": 2, "softwareVersionID": 214, "softwareDisplayName": null, "targetID": 4644, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 808, "targetTypeID": 2, "softwareVersionID": 138, "softwareDisplayName": null, "targetID": 4947, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 809, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1337, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 810, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2184, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 811, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2170, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 812, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 4998, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 813, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5016, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 815, "targetTypeID": 3, "softwareVersionID": 216, "softwareDisplayName": null, "targetID": 1104, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 816, "targetTypeID": 2, "softwareVersionID": 166, "softwareDisplayName": null, "targetID": 3544, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 817, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 819, "targetTypeID": 2, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 4925, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 820, "targetTypeID": 2, "softwareVersionID": 221, "softwareDisplayName": null, "targetID": 4726, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 821, "targetTypeID": 3, "softwareVersionID": 67, "softwareDisplayName": null, "targetID": 1293, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 822, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 5247, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 823, "targetTypeID": 2, "softwareVersionID": 225, "softwareDisplayName": null, "targetID": 5247, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 824, "targetTypeID": 2, "softwareVersionID": 204, "softwareDisplayName": null, "targetID": 4319, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 825, "targetTypeID": 2, "softwareVersionID": 204, "softwareDisplayName": null, "targetID": 4281, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 826, "targetTypeID": 0, "softwareVersionID": 0, "softwareDisplayName": null, "targetID": null, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 827, "targetTypeID": 2, "softwareVersionID": 39, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 828, "targetTypeID": 4, "softwareVersionID": 142, "softwareDisplayName": null, "targetID": "CN=ENGINEERS,OU=Contacts,DC=DeepSouth,DC=local", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 829, "targetTypeID": 2, "softwareVersionID": 142, "softwareDisplayName": null, "targetID": 2563, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 830, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 5179, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 831, "targetTypeID": 2, "softwareVersionID": 232, "softwareDisplayName": null, "targetID": 5251, "exception": 0, "desiredProductKey": null, "licenseFile": "MFBC7-GCAKC-URACN-PU3WB-CAD46", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 832, "targetTypeID": 3, "softwareVersionID": 219, "softwareDisplayName": null, "targetID": 1293, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 833, "targetTypeID": 3, "softwareVersionID": 220, "softwareDisplayName": null, "targetID": 1293, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 834, "targetTypeID": 3, "softwareVersionID": 221, "softwareDisplayName": null, "targetID": 1293, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 835, "targetTypeID": 2, "softwareVersionID": 227, "softwareDisplayName": null, "targetID": 2588, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 836, "targetTypeID": 2, "softwareVersionID": 226, "softwareDisplayName": null, "targetID": 5179, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 837, "targetTypeID": 2, "softwareVersionID": 226, "softwareDisplayName": null, "targetID": 3888, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 838, "targetTypeID": 2, "softwareVersionID": 226, "softwareDisplayName": null, "targetID": 2263, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 839, "targetTypeID": 2, "softwareVersionID": 226, "softwareDisplayName": null, "targetID": 2466, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 840, "targetTypeID": 2, "softwareVersionID": 226, "softwareDisplayName": null, "targetID": 5251, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 841, "targetTypeID": 2, "softwareVersionID": 227, "softwareDisplayName": null, "targetID": 1619, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 842, "targetTypeID": 2, "softwareVersionID": 227, "softwareDisplayName": null, "targetID": 2754, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 843, "targetTypeID": 2, "softwareVersionID": 227, "softwareDisplayName": null, "targetID": 5027, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 844, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5238, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 845, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 2850, "exception": 0, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 846, "targetTypeID": 3, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 1346, "exception": 1, "desiredProductKey": "SN=FPM32913", "licenseFile": "ImmenseNetworks\\\\fpmkey.txt", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 847, "targetTypeID": 2, "softwareVersionID": 231, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 849, "targetTypeID": 2, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 5378, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 851, "targetTypeID": 3, "softwareVersionID": 180, "softwareDisplayName": null, "targetID": 1347, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 852, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 2012, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 854, "targetTypeID": 3, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 1291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 855, "targetTypeID": 6, "softwareVersionID": 235, "softwareDisplayName": null, "targetID": "<EMAIL>", "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 856, "targetTypeID": 6, "softwareVersionID": 236, "softwareDisplayName": null, "targetID": "<EMAIL>", "exception": 0, "desiredProductKey": null, "licenseFile": "CornerstoneFlooring\\\\NVMSconfigCornerstone.zip", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 857, "targetTypeID": 2, "softwareVersionID": 235, "softwareDisplayName": null, "targetID": 3982, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 858, "targetTypeID": 2, "softwareVersionID": 236, "softwareDisplayName": null, "targetID": 3982, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 859, "targetTypeID": 3, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 1348, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 860, "targetTypeID": 3, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 1349, "exception": 0, "desiredProductKey": null, "licenseFile": "TN23P-2GCDW-8449W-47TBM-B4G6Y", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 861, "targetTypeID": 2, "softwareVersionID": 142, "softwareDisplayName": null, "targetID": 835, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 862, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5406, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 863, "targetTypeID": 3, "softwareVersionID": 239, "softwareDisplayName": null, "targetID": 1346, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 865, "targetTypeID": 2, "softwareVersionID": 157, "softwareDisplayName": null, "targetID": 5439, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 866, "targetTypeID": 2, "softwareVersionID": 188, "softwareDisplayName": null, "targetID": 462, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 867, "targetTypeID": 2, "softwareVersionID": 189, "softwareDisplayName": null, "targetID": 462, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 868, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5247, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 869, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5439, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 870, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5463, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 871, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 835, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 872, "targetTypeID": 2, "softwareVersionID": 7, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 873, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 874, "targetTypeID": 2, "softwareVersionID": 191, "softwareDisplayName": null, "targetID": 4726, "exception": 0, "desiredProductKey": null, "licenseFile": "10.15.1.41", "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 876, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 4726, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 877, "targetTypeID": 2, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 2587, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 878, "targetTypeID": 3, "softwareVersionID": 37, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 879, "targetTypeID": 3, "softwareVersionID": 106, "softwareDisplayName": null, "targetID": 1206, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 880, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1350, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 881, "targetTypeID": 2, "softwareVersionID": 242, "softwareDisplayName": null, "targetID": 8, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 882, "targetTypeID": 3, "softwareVersionID": 241, "softwareDisplayName": null, "targetID": 1291, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 883, "targetTypeID": 2, "softwareVersionID": 229, "softwareDisplayName": null, "targetID": 5437, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 885, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5503, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 886, "targetTypeID": 3, "softwareVersionID": 247, "softwareDisplayName": null, "targetID": 1206, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 887, "targetTypeID": 3, "softwareVersionID": 67, "softwareDisplayName": null, "targetID": 1352, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 888, "targetTypeID": 3, "softwareVersionID": 40, "softwareDisplayName": null, "targetID": 1325, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 889, "targetTypeID": 2, "softwareVersionID": 189, "softwareDisplayName": null, "targetID": 4848, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 890, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5556, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 891, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5325, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 892, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5413, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 893, "targetTypeID": 2, "softwareVersionID": 222, "softwareDisplayName": null, "targetID": 5547, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 909, "targetTypeID": 3, "softwareVersionID": 250, "softwareDisplayName": null, "targetID": 1354, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 910, "targetTypeID": 2, "softwareVersionID": 82, "softwareDisplayName": null, "targetID": 1182, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 911, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 559, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 912, "targetTypeID": 2, "softwareVersionID": 159, "softwareDisplayName": null, "targetID": 5546, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 913, "targetTypeID": 3, "softwareVersionID": 251, "softwareDisplayName": null, "targetID": 1355, "exception": 1, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 914, "targetTypeID": 2, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 3176, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 915, "targetTypeID": 2, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 3207, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 916, "targetTypeID": 2, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 3660, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}, {"targetAssignmentID": 917, "targetTypeID": 2, "softwareVersionID": 148, "softwareDisplayName": null, "targetID": 3330, "exception": 0, "desiredProductKey": null, "licenseFile": null, "autoUpdate": 0, "changed": 0}]