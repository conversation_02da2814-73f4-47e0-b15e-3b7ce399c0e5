<template>
  <Page no-bg>
    <template #header>
      <PageHeader>
        <template #title>
          Subscription &amp; Billing
          <ImmyButton variant="primary" class="ml-3" @click="openPortal">
            Click here to edit your subscription
          </ImmyButton>
        </template>
      </PageHeader>
    </template>
    <ImmyTabs>
      <template #tabs>
        <ImmyTabItem v-model="selectedTab" :tab-value="0" title="Subscription" />
        <ImmyTabItem v-model="selectedTab" :tab-value="1">
          <i
            v-if="hasSomeFeatureExceededUsage"
            title="One of the features has met or exceeded its allotted usage"
            class="fa fa-exclamation-triangle text-warning"
          />
          Features
        </ImmyTabItem>
      </template>
      <template #content>
        <ImmyTabBody v-show="selectedTab == 0" class="p-3">
          <div v-if="loadingBillingDetails" class="mb-2">
            <i class="fa fa-spinner fa-spin" />&nbsp;&nbsp;Loading billing details...
          </div>
          <div>
            <div title="Plan name">
              <label class="subscription-label">Plan</label>
              <p class="m-0">
                {{ plan.name }}
              </p>
              <span v-if="plan.description" title="Plan description">
                <em>{{ plan.description }}</em>
              </span>
            </div>
          </div>
          <div class="mt-2" title="Current subscription status">
            <label class="subscription-label">Status</label>
            <span>{{ statusName(subscriptionDetails?.status) }}</span>
          </div>
          <div
            v-if="subscriptionDetails?.status === SubscriptionStatus.InTrial"
            class="mt-2"
            title="When did the subscription trial start"
          >
            <label class="subscription-label">Trial Start</label>
            <span>{{ formatDate(subscriptionDetails.trialStartUtc) }}</span>
          </div>
          <div
            v-if="subscriptionDetails?.status === SubscriptionStatus.InTrial"
            class="mt-2"
            title="When will the subscription trial end"
          >
            <label class="subscription-label">Trial End</label>
            <span>{{ formatDate(subscriptionDetails.trialEndUtc) }}</span>
          </div>
          <div
            v-if="subscriptionDetails?.status === SubscriptionStatus.Active"
            class="mt-2"
            title="When did the subscription start"
          >
            <label class="subscription-label">Activated</label>
            <span>{{ formatDate(subscriptionDetails.subscriptionActivatedDateUtc) }}</span>
          </div>
        </ImmyTabBody>
        <ImmyTabBody v-show="selectedTab == 1">
          <div v-if="loadingBillingDetails" class="mb-2">
            <i class="fa fa-spinner fa-spin" />&nbsp;&nbsp;Loading billing details...
          </div>
          <ImmyAlert
            v-if="subscriptionDetails != null && featuresEnabledFromSubscription.length === 0"
            show
            variant="warning"
            class="m-0"
          >
            No features are included in your subscription. To change or increase features,
            <ImmyButton
              variant="link"
              class="p-0"
              style="vertical-align: baseline"
              @click="openPortal"
            >
              edit your subscription
            </ImmyButton>.
          </ImmyAlert>
          <div
            v-for="feature in featuresEnabledFromSubscription"
            :key="feature.featureId"
            class="feature"
          >
            <h3 class="feature-title">
              <i
                v-if="feature.usageExceeded"
                class="fas fa-exclamation-triangle text-warning mr-2"
              /><i v-else class="fa fa-check-circle text-success mr-2" />{{ feature.featureInfo?.featureName ?? feature.featureId }}
            </h3>
            <p
              v-if="feature.featureInfo?.featureDescription"
              title="Feature description"
              class="text-muted"
            >
              {{ feature.featureInfo.featureDescription }}
            </p>
            <ImmyAlert v-if="feature.usageExceeded" show variant="warning">
              All of the allotted units for this feature
              {{ feature.intervalUnitText }} have been used. To change or increase features,
              <ImmyButton
                variant="link"
                class="p-0"
                style="vertical-align: baseline"
                @click="openPortal"
              >
                edit your subscription
              </ImmyButton>.
            </ImmyAlert>
            <ImmyAlert v-if="feature.maxCount === 0" show variant="info">
              This feature is not currently enabled for your subscription. To change or increase
              features,
              <ImmyButton
                variant="link"
                class="p-0"
                style="vertical-align: baseline"
                @click="openPortal"
              >
                edit your subscription
              </ImmyButton>.
            </ImmyAlert>
            <template v-else-if="feature.isUsageBased">
              <p class="m-0">
                The current subscription allows <em>{{ feature.allowanceText }}</em>
              </p>
              <p>
                {{ feature.usedSoFarText }}: <strong>{{ feature.itemCount ?? 0 }}</strong><template v-if="feature.sinceDateText != null">
                  &nbsp;<em class="small text-muted">({{ feature.sinceDateText }})</em>
                </template>
              </p>
            </template>
            <template v-else>
              <p>
                {{ feature.allowanceText }}
              </p>
            </template>
          </div>
        </ImmyTabBody>
      </template>
    </ImmyTabs>
  </Page>
</template>

<script setup lang="ts">
import { capitalize } from "lodash";

import { computed, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import {
  FeatureEntitlementType,
  FeatureResetInterval,
  IFeatureUsageDetails,
  IGetProductCatalogItemsResponse,
  IGetSubscriptionDetailsResponse,
  SubscriptionStatus,
} from "@/api/backend/generated/contracts";
import { billingApi } from "@/api/backend/v1";
import { useViewBase } from "@/composables/ViewBase";
import { useBillingStore } from "@/store/pinia";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { formatDate } from "@/utils/misc";

const props = withDefaults(
  defineProps<{
    tabName?: keyof typeof TABS | "";
    editSubscription?: boolean;
  }>(),
  {
    tabName: "subscription",
    editSubscription: false,
  },
);
const router = useRouter();
const billingStore = useBillingStore();
const appAlertsStore = useAppAlertsStore();

const TABS = {
  subscription: 0,
  features: 1,
};

const TABS_ARY = Object.entries(TABS)
  .sort(([, v1], [, v2]) => v1 - v2)
  .map(([k]) => k);

const selectedTab = ref(TABS[props.tabName || "subscription"]);
const loadingBillingDetails = ref(true);
const subscriptionDetails = ref<IGetSubscriptionDetailsResponse>();
const productCatalogItems = ref<IGetProductCatalogItemsResponse>();

watch(
  () => props.tabName,
  (val) => {
    if (val == null || val == "") {
      if (router.currentRoute.value.name != null)
        router.push({ name: router.currentRoute.value.name, params: { tabName: "subscription" } });
      return;
    }
    setDocumentTitle();
    const tabIndex = TABS[val];
    if (selectedTab.value === tabIndex)
      return;
    selectedTab.value = tabIndex;
  },
  { immediate: true },
);
watch(selectedTab, (val) => {
  const tabName = TABS_ARY[val];
  if (tabName === props.tabName)
    return;
  if (router.currentRoute.value.name != null)
    router.push({ name: router.currentRoute.value.name, params: { tabName } });
});

const featuresEnabledFromSubscription = computed(() => {
  return (subscriptionDetails.value?.featuresEnabledFromSubscription ?? []).map((u) => {
    const featureInfo = productCatalogItems.value?.features.find(f => f.featureId === u.featureId);
    const featureUsage = u.featureUsage;
    const isUsageBased = u.isUsageBased;
    const featureCustomLevel = featureInfo?.levels.find(l => l.value === u.featureCustomValue);
    const isUnlimited = isUsageBased && featureUsage != null && featureUsage?.maxCount == null;
    const usageExceeded
      = !isUnlimited
        && isUsageBased
        && featureUsage != null
        && featureUsage.maxCount !== 0
        && (featureUsage.itemCount ?? 0) >= (featureUsage.maxCount ?? 0);
    const allowanceText
      = u.featureEntitlementType === FeatureEntitlementType.Quantity
        ? u.isUsageBased
          ? `${isUnlimited ? "an unlimited amount" : featureUsage?.maxCount} ${resetIntervalText(
            featureUsage?.resetInterval,
          )}`
          : `${u.featureCustomValue}`
        : u.featureEntitlementType === FeatureEntitlementType.Custom
          ? `${featureInfo?.featureDescription} ${featureCustomLevel?.name}`
          : "";
    const intervalUnitText = resetIntervalUnitText(featureUsage?.resetInterval);
    let usedSoFarText: string;
    if (featureInfo?.unit != null) {
      // if the feature in chargebee has a unit, use that in the used-so-far text
      usedSoFarText = `Current ${featureInfo.unit} count${
        featureUsage?.resetInterval == null ? "" : ` so far ${intervalUnitText}`
      }`;
      // e.g. "Current device count" or "Current device with scheduled maintenance count so far this month"
    }
    else {
      // if the feature in chargebee doesn't have a unit text, word it more generically
      usedSoFarText
        = featureUsage?.resetInterval == null ? "Used" : `Used so far ${intervalUnitText}`;
      // e.g. "Used" or "Used so far this month"
    }
    const formattedStartDate = featureUsage != null ? getFormattedStartDate(featureUsage) : null;
    const sinceDateText
      = featureUsage?.resetInterval != null && formattedStartDate != null
        ? `since ${formattedStartDate}`
        : null;
    return {
      featureId: u.featureId,
      maxCount: featureUsage?.maxCount,
      itemCount: featureUsage?.itemCount,
      isUsageBased,
      isUnlimited,
      featureInfo,
      usageExceeded,
      allowanceText,
      intervalUnitText,
      usedSoFarText,
      sinceDateText,
    };
  });
});

const hasSomeFeatureExceededUsage = computed(() =>
  featuresEnabledFromSubscription.value.some(f => f.usageExceeded),
);

const plan = computed(() => {
  const planId = subscriptionDetails.value?.planId;
  if (planId == null)
    return { name: "No plan", description: undefined };
  if (productCatalogItems.value == null)
    return { name: planId, description: undefined };
  const plan = productCatalogItems.value.plans.find(p => p.itemId === planId);
  return {
    name: plan?.itemName ?? planId,
    description: plan?.itemDescription,
  };
});

useViewBase({
  breadcrumbs: computed(() => [{ text: "Billing", to: { name: "Billing" } }]),
});

// on component render, get billing subscription details
Promise.all([billingApi.getSubscriptionDetails(), billingApi.getProductCatalogItems()])
  .then(([d, e]) => {
    subscriptionDetails.value = d;
    productCatalogItems.value = e;
  })
  .catch((err) => {
    appAlertsStore.addAlert({
      text: "There was a problem loading the billing subscription details.  Please contact support for assistance.",
      details: err,
    });
  })
  .finally(() => {
    loadingBillingDetails.value = false;
  });

function setDocumentTitle() {
  document.title = `Billing | ${capitalize(props.tabName)}`;
}

function resetIntervalText(val: FeatureResetInterval | undefined) {
  switch (val) {
    case FeatureResetInterval.Daily:
      return "per day";
    case FeatureResetInterval.Monthly:
      return "per month";
    case undefined:
    case null:
      return "";
  }
}
function resetIntervalUnitText(val: FeatureResetInterval | undefined) {
  switch (val) {
    case FeatureResetInterval.Daily:
      return "today";
    case FeatureResetInterval.Monthly:
      return "this month";
    case undefined:
    case null:
      return "";
  }
}

function getFormattedStartDate(feature: IFeatureUsageDetails) {
  if (feature.featureTrackStartDateUtc == null)
    return null;
  switch (feature.resetInterval) {
    case FeatureResetInterval.Daily:
      return `${formatDate(feature.featureTrackStartDateUtc, "h:MM Z", undefined, "forever")}`;
    case FeatureResetInterval.Monthly:
    case undefined:
    case null:
      return formatDate(feature.featureTrackStartDateUtc, undefined, undefined, "forever");
  }
}

function statusName(status: SubscriptionStatus | undefined) {
  switch (status) {
    case undefined:
      return "Unknown";
    case SubscriptionStatus.Active:
      return "Active";
    case SubscriptionStatus.InTrial:
      return "In Trial";
    case SubscriptionStatus.Inactive:
      return "Inactive";
  }
}

async function openPortal() {
  try {
    const cbInstance = await billingStore.getChargebeeInstance();
    cbInstance.setPortalCallbacks({
      close: () => {
        cbInstance.logout();
      },
    });
    cbInstance.setPortalSession(async () => await billingApi.createCustomerPortalSession());
    const cbPortal = cbInstance.createChargebeePortal();
    cbPortal.open();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "There was a problem loading the customer portal.  Please contact support for assistance.",
      details: err,
    });
    throw new Error("No customer portal url was returned.");
  }
}

onMounted(async () => {
  if (props.editSubscription) {
    await openPortal();
  }
});
</script>

<style lang="scss" scoped>
.subscription-label {
  color: var(--text-secondary) !important;
  margin-bottom: 0;
  display: block;
}

.feature {
  padding: 1rem;
}
</style>
