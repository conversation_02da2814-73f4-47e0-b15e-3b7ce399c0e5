<template>
  <Page>
    <template #header>
      <page-header :title="userId ? `Edit User` : 'New User'">
        <template #primary-action>
          <LoadButton
            v-if="canImpersonateUser"
            variant="warning"
            :handler="impersonateUser"
            :data-testid="`impersonate-${user.email}`"
          >
            Impersonate
          </LoadButton>
        </template>
      </page-header>
    </template>
    <form @submit.prevent="onSubmit">
      <p>
        <strong>Tenant:</strong>
        <span class="pii">{{ user.companyName }}</span>
      </p>
      <p>
        <strong>Name:</strong>
        <span class="pii">{{ user.name }}</span>
      </p>
      <p>
        <strong>Email:</strong>
        <span class="pii">{{ user.email }}</span>
      </p>
      <p>
        <strong>Is Expired:</strong>
        {{ user.isExpired }}
      </p>
      <p>
        <strong>Expiration Date:</strong>
        {{ formatDate(user.expirationDateUTC) }}
        <ImmyFormGroup
          v-if="user.personId != null"
          class="ml-4 mt-2"
          :label="user.expirationDateUTC != null ? `Reset Expiration` : `Set Expiration`"
          label-class="font-weight-bold"
        >
          <ImmyRadioGroup v-model="updateExpirationTime" :options="updateExpirationTimeOptions" />
        </ImmyFormGroup>
      </p>
      <ImmyFormGroup description="When enabled, the user can login and perform actions against other computers. Otherwise, they can only perform self-service actions.">
        <ImmyCheckBox v-if="featuresStore.isEnabled(FeatureEnum.SelfServiceFeature)" v-model="user.hasManagementAccess" name="admin-checkbox">
          Has Management Access
        </ImmyCheckBox>
      </ImmyFormGroup>
      <template v-if="user.hasManagementAccess || !featuresStore.isEnabled(FeatureEnum.SelfServiceFeature)">
        <ImmyFormGroup>
          <ImmyCheckBox id="admin-checkbox" v-model="user.isAdmin" data-testid="admin-checkbox">
            Admin
          </ImmyCheckBox>
        </ImmyFormGroup>
        <ImmyFormGroup
          label="Non-Admin Capabilities"
          description="Admins inherently have these capabilities"
        >
          <ImmyCheckBox
            id="can-manage-cross-tenant-deployments-checkbox"
            v-model="user.canManageCrossTenantDeployments"
            :disabled="user.isAdmin"
          >
            Can Manage Cross Tenant Deployments
          </ImmyCheckBox>
        </ImmyFormGroup>
      </template>
      <ImmyButton v-if="userId" type="submit" variant="primary" data-testid="update-user-button">
        Update
      </ImmyButton>
    </form>
  </Page>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { ExpirationTime, FeatureEnum, IGetUserResponse } from "@/api/backend/generated/contracts";
import { personsApi, usersApi } from "@/api/backend/v1";
import { useViewBase } from "@/composables/ViewBase";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useFeaturesStore } from "@/store/pinia/features-store";
import { usePreferencesStore } from "@/store/pinia/preferences-store";
import { useUsersStore } from "@/store/pinia/users-store";
import { formatDate } from "@/utils/misc";
import { toast } from "@/utils/toast";

const props = defineProps<{
  userId?: number;
}>();
dayjs.extend(utc);
const preferencesStore = usePreferencesStore();
const appAlertsStore = useAppAlertsStore();
const usersStore = useUsersStore();
const authStore = useAuthStore();
const featuresStore = useFeaturesStore();
const router = useRouter();

const user = ref<IGetUserResponse>({
  id: 0,
  email: "",
  name: "",
  tenantId: 0,
  isAdmin: false,
  companyName: "",
  type: "",
  azurePrincipalId: "",
  isExpired: false,
  expirationDateUTC: "",
  canManageCrossTenantDeployments: false,
  hasManagementAccess: false,
  roles: [],
});

const updateExpirationTime = ref<ExpirationTime | null>(null);

async function onSubmit() {
  if (props.userId == null)
    return;

  try {
    const hasManagementAccess = featuresStore.isEnabled(FeatureEnum.SelfServiceFeature) ? user.value.hasManagementAccess : true;
    if (updateExpirationTime.value != null && user.value.personId != null) {
      await personsApi.grantAccess(user.value.personId, {
        isAdmin: user.value.isAdmin,
        expirationTime: updateExpirationTime.value,
        hasManagementAccess,
      });
    }
    user.value = await usersStore.update(props.userId, {
      id: user.value.id,
      isAdmin: user.value.isAdmin,
      tenantId: user.value.tenantId,
      canManageCrossTenantDeployments: user.value.canManageCrossTenantDeployments,
      hasManagementAccess,
    });
    updateExpirationTime.value = null;
    toast.success(`Successfully saved user.`);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while saving the user.",
      details: err,
    });
  }
}
useViewBase({
  breadcrumbs: computed(() => [
    {
      text: "Users",
      to: { name: "User List" },
    },
    {
      text: props.userId?.toString() ?? "New",
    },
  ]),
  initialize: async () => {
    if (props.userId != null) {
      try {
        user.value = await usersStore.get(props.userId);
      }
      catch (err) {
        appAlertsStore.addAlert({
          text: "An error occurred while getting the user.",
          details: err,
        });
      }
    }
  },
});

const updateExpirationTimeOptions = computed(() => {
  const options = [
    { value: ExpirationTime.OneHour, text: "One Hour" },
    { value: ExpirationTime.OneDay, text: "One Day" },
    { value: ExpirationTime.ThreeDays, text: "Three Days" },
  ];

  if (user.value.expirationDateUTC != null)
    options.push({ value: ExpirationTime.Indefinite, text: "Never" });

  return options.filter(o => o != null);
});

const canImpersonateUser = computed(() => {
  return preferencesStore.appPreferences?.enableUserImpersonation && !!authStore.isAdmin && !!authStore.isMSP;
});

async function impersonateUser() {
  try {
    if (props.userId == null)
      return;
    await usersApi.impersonateUser(props.userId, {
      expiresAtUtc: dayjs().utc().add(1, "hour").toISOString(),
    });
    await authStore.checkAuthorization();
    await router.push("/");
    toast.success("You are now impersonating the user.");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while trying to impersonate the user.",
      details: err,
    });
  }
}
</script>

<style></style>
