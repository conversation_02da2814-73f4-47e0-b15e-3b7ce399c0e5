<template>
  <Page no-bg :no-shadow="!selectedProviderType">
    <template #header>
      <!-- page header -->
      <page-header>
        <template #title>
          <span v-if="!state.loading && !selectedProviderType">New Integration</span>
          <div v-else class="d-flex align-items-center gap-2">
            <template v-if="selectedProviderType && selectedProviderType.logoSrc">
              <DynamicIntegrationLogo
                v-if="selectedProviderType.isDynamic"
                :media-id="parseInt(selectedProviderType.logoSrc)"
                :source="selectedProviderType.source"
              />
              <img v-else :src="selectedProviderType.logoSrc" height="56">
            </template>
            <span>{{ state.providerLink && state.providerLink.name }}</span>
          </div>
        </template>
        <template #primary-action>
          <LoadButton v-if="canManage && providerLinkId && !state.loading" :handler="reloadIntegration">
            Reload integration
          </LoadButton>
        </template>
        <template #under-header>
          <DynamicIntegrationTypeBadge
            v-if="state.providerLink && state.providerLink.providerTypeId"
            inline
            show-release-state-text
            :provider-type-id="state.providerLink.providerTypeId"
          />
          <div
            v-if="isNew && state.providerLinkExternalReferenceData != null"
            class="mt-2 alert alert-primary"
          >
            Creating an integration linked with the
            <strong>{{ state.providerLinkExternalReferenceData.providerLinkName }}</strong>
            integration
          </div>
        </template>
      </page-header>

      <!-- health alert -->
      <ImmyAlert
        v-if="!state.loading && providerLinkId"
        show
        :variant="healthData.variant"
        class="max-width-xxxl health-status-message"
      >
        <i :class="healthData.icon" /> <strong>Health Check</strong> - This integration
        {{ healthData.text }}.
        <p v-if="healthData.healthStatusMessage" class="m-0">
          {{ healthData.healthStatusMessage }}
        </p>
      </ImmyAlert>
    </template>

    <!-- type selector -->
    <IntegrationTypeSelector
      v-if="
        !state.loading
          && state.providerLink != null
          && isNew
          && selectedProviderType == null
      "
      @select="onProviderTypeSelected"
    />

    <ImmyTabs v-else-if="!state.loading">
      <template #tabs>
        <ImmyTabItem v-model="state.selectedTab" :tab-value="0" title="Setup" />
        <ImmyTabItem
          v-if="!isNew && supportsClients"
          v-model="state.selectedTab"
          :tab-value="1"
          title="Clients"
        />
        <ImmyTabItem
          v-if="props.providerLinkId && supportsAgents"
          v-model="state.selectedTab"
          :tab-value="2"
          title="Agents"
        />
        <ImmyTabItem
          v-if="props.providerLinkId"
          v-model="state.selectedTab"
          :tab-value="3"
          title="Audit"
        />
      </template>
      <template #content>
        <ImmyTabHeader :active="state.selectedTab == IntegrationTab.Setup">
          <template #header>
            Integration Setup
          </template>
        </ImmyTabHeader>
        <ImmyTabBody v-show="state.selectedTab == 0">
          <div class="row tab-body">
            <div class="provider-link-form col-lg-5 border-lg-right">
              <div class="max-width-xxl mr-lg-5">
                <FormGroupWithHelp
                  v-if="state.providerLink != null"
                  id="name-input-group"
                  label="Name"
                  :disabled="!canManage"
                  required
                  description="Enter a distinct name to help identify this integration."
                  :is-invalid="!nameIsValid"
                  invalid-feedback="Cannot be blank"
                >
                  <ImmyInput v-model="state.providerLink.name" type="text" />
                </FormGroupWithHelp>
                <ImmyAlert v-if="!canManage" variant="warning" show>
                  This integration is managed by ImmyBot.
                </ImmyAlert>
                <div class="provider-info-form border-lg-top-0">
                  <template v-if="selectedProviderType">
                    <template v-if="state.providerLinkExternalReferenceData != null">
                      <div class="alert alert-info">
                        <p>
                          This data was retrieved from the
                          {{ state.providerLinkExternalReferenceData.providerLinkName }}
                          integration.
                        </p>
                        <p v-if="state.providerLinkExternalReferenceData.description">
                          {{ state.providerLinkExternalReferenceData.description }}
                        </p>
                      </div>
                    </template>
                    <IntegrationDynamicForm
                      v-else-if="selectedProviderType.configurationForm"
                      :dynamic-form-bind-result="state.dynamicFormBindResult"
                      :initial-parameter-values="initialParameterValues"
                      :provider-type-id="selectedProviderType.providerTypeId"
                      :provider-link-id="providerLinkId"
                      @is-valid="isConfigurationFormValid = $event"
                      @update:form="onConfigurationFormUpdated"
                    />
                  </template>
                  <FormGroupWithHelp
                    v-if="
                      state.providerLinkExternalReferenceData != null
                        && supportsCrossProviderClientLinkages
                    "
                    label="Cross-Integration Client Linking"
                    description="Select whether or not to utilize the external link between clients in this integration and clients in the integration being linked to"
                    :is-invalid="!crossProviderClientLinkingSupportIsValid"
                    invalid-feedback="Cannot be blank"
                  >
                    <template #help-alert-content>
                      <div>
                        <p>
                          Allows for assignments that target clients in one integration to utilize
                          an "external link" between that integration and another integration, to
                          determine the targeted devices. The "external link" is stored in the
                          integration provider (e.g. the RMM or the PSA) and enabling this feature
                          will not update that value in any way.
                        </p>
                      </div>
                      <div>
                        <span style="text-decoration: underline">
                          Specifically for
                          <em>{{
                            state.providerLinkExternalReferenceData.providerType.displayName
                          }}</em>
                          -
                          <em>{{ selectedProviderType?.displayName }}</em>
                          linking:
                        </span>
                        <p>
                          {{
                            state.providerLinkExternalReferenceData
                              .crossProviderClientExternalLinkingDescription
                          }}
                        </p>
                      </div>
                    </template>
                    <ImmyRadioGroup
                      v-model="state.providerLinkExternalReferenceData.enableClientExternalLinking"
                      :options="[
                        { text: 'Enable', value: true },
                        { text: 'Disable', value: false },
                      ]"
                    />
                  </FormGroupWithHelp>
                  <div v-if="canManage" class="mt-3">
                    <span v-if="hasIssues" class="text-danger">Fix all issues in the form above</span>
                    <template v-else>
                      <template v-if="providerLinkId">
                        <ImmyButton
                          variant="primary"
                          :disabled="state.submitting"
                          @click="update(false)"
                        >
                          <template v-if="state.submitting && !togglingDisabled">
                            Updating, please wait
                            <i class="fa fa-spinner fa-spin" />
                          </template>
                          <template v-else>
                            Update
                          </template>
                        </ImmyButton>
                        <p class="mt-1 fs-85 text-muted">
                          The integration will be restarted when changes are applied.
                        </p>
                      </template>
                      <template v-else>
                        <ImmyButton
                          variant="primary"
                          class="mr-2"
                          :disabled="state.submitting"
                          @click="create"
                        >
                          <template v-if="state.submitting">
                            Creating, please wait
                            <i class="fa fa-spinner fa-spin" />
                          </template>
                          <template v-else>
                            Create
                          </template>
                        </ImmyButton>
                      </template>
                    </template>

                    <FormGroupWithHelp
                      v-if="state.providerLink != null"
                      class="mt-3"
                      label="Enable Integration"
                    >
                      <toggle
                        id="disabled-toggle"
                        :disabled="state.submitting"
                        :model-value="!state.providerLink.disabled"
                        @change="update(true)"
                      />
                      <span class="d-block fs-85 text-muted">Toggle to enable or disable this integration.</span>
                      <p v-if="togglingDisabled">
                        Loading<i class="ml-2 fa fa-spin fa-spinner" />
                      </p>
                    </FormGroupWithHelp>
                    <ImmyAlert v-if="updateErrorMessage" show class="mt-3" variant="warning">
                      <h3 class="alert-heading">
                        Initialization Failed
                      </h3>
                      <em>
                        immy.bot's attempt to authenticate to your integration was unsuccessful.
                        Please double check that the information you provided is correct, and try
                        again. If the problem persists, reach out to our support for help.
                      </em>
                      <p class="pt-2 m-0">
                        {{ updateErrorMessage }}
                      </p>
                    </ImmyAlert>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-7">
              <div class="pl-lg-5 pt-3">
                <div class="d-flex align-items-start gap-3">
                  <template v-if="selectedProviderType && selectedProviderType.logoSrc">
                    <DynamicIntegrationLogo
                      v-if="selectedProviderType.isDynamic"
                      :media-id="parseInt(selectedProviderType.logoSrc)"
                      :source="selectedProviderType.source"
                    />
                    <img v-else :src="selectedProviderType.logoSrc" height="56">
                  </template>
                  <div>
                    <h3 v-if="selectedProviderType">
                      <span><strong>{{ selectedProviderType.displayName }}</strong> integration
                        capabilities</span>
                    </h3>
                    <span class="text-muted">Toggle off a capability to disable its functionality</span>
                    <ul
                      v-for="capability in supportedInterfaceCapabilities"
                      :key="capability.key"
                      class="my-3 m-0 p-0"
                    >
                      <li class="d-flex align-items-center gap-3">
                        <toggle
                          v-if="canManage"
                          class="capability-toggle"
                          :model-value="!excludedCapabilities.includes(capability.key)"
                          @update:model-value="
                            updatedExcludeCapabilities(capability.key, $event)
                          "
                        />
                        <i v-else class="fa fa-check-circle text-success" />
                        <span>{{ capability.description }}</span>
                      </li>
                    </ul>
                    <div v-if="hasChangedCapabilities && canManage" class="mt-3">
                      <ImmyAlert variant="warning" show>
                        Only perform this action when necessary as it can cause unintended behavior.
                      </ImmyAlert>
                      <span v-if="hasIssues" class="text-danger">Fix all issues in the form before updating capabilities</span>
                      <template v-else>
                        <ImmyButton
                          variant="primary"
                          :disabled="state.submitting"
                          @click="update(false)"
                        >
                          <template v-if="state.submitting && !togglingDisabled">
                            Updating, please wait
                            <i class="fa fa-spinner fa-spin" />
                          </template>
                          <template v-else>
                            Save Changes
                          </template>
                        </ImmyButton>
                      </template>
                    </div>
                  </div>
                </div>
                <hr>
                <h1>Documentation</h1>
                <ImmyDocs :src="docsUrl" />
              </div>
            </div>
          </div>
        </ImmyTabBody>

        <ImmyTabHeader :active="state.selectedTab == IntegrationTab.Clients">
          <template #header>
            Integration Clients
          </template>
          <template #header-actions>
            <div class="d-flex align-items-center gap-2">
              <LoadButton :handler="fetchProviderClients" :disabled="isSyncingClientsFromProvider">
                <i class="fa-regular fa-rotate mr-1" />Refresh clients
              </LoadButton>
              <ImmyButton
                v-if="state.syncingClients"
                variant="secondary"
                :disabled="state.syncingClients || !state.selectedClients.length"
                @click="bulkSync(true)"
              >
                Updating links, please wait
                <i class="fa fa-spinner fa-spin" />
              </ImmyButton>

              <ImmyButton
                v-if="!state.syncingClients"
                id="link-all-sekected-button"
                :disabled="state.syncingClients || !state.selectedClients.length"
                @click="bulkSync(true)"
              >
                Link selected clients ({{ state.selectedClients.length }})
              </ImmyButton>

              <LoadButton
                title="Sync agents for selected linked tenants"
                :disabled="syncingAgents || !state.selectedClients.length"
                variant="secondary"
                :handler="syncAgentsForSelectedClients"
              >
                Sync agents for selected clients ({{ state.selectedClients.length }})
              </LoadButton>
            </div>
          </template>
        </ImmyTabHeader>
        <ImmyTabBody v-show="state.selectedTab == 1">
          <div class="tab-body">
            <div v-if="supportedExternalProviders.length > 0">
              <h3>Supported External Integration Linkages</h3>
              <div v-for="p in supportedExternalProviders" :key="p.providerType.providerTypeId">
                <h4 v-if="!p.linkedExternalLink">
                  <span>{{ p.providerType.displayName }}</span>
                  <small v-if="p.linkedExternalLink" class="ml-1 text-muted">(linked)</small>
                </h4>
                <div v-if="p.linkedExternalLink != null">
                  Currently linked to
                  <router-link
                    :to="`/settings/integrations/${p.linkedExternalLink.providerLinkId}`"
                  >
                    {{ p.linkedExternalLink.providerLinkName }}
                  </router-link>
                  <ImmyButton
                    class="btn-sm ml-1"
                    variant="warning"
                    @click="unlinkFromExternalLink(p)"
                  >
                    Unlink
                  </ImmyButton>
                </div>
                <FormGroupWithHelp v-else>
                  <template v-if="p.initializationSupport" #help-alert-content>
                    {{ p.initializationSupport?.description }}
                  </template>
                  <ImmyButton variant="secondary" @click="triggerExternalProviderSetupProcess(p)">
                    Create new Integration for {{ p.providerType.displayName }}
                  </ImmyButton>
                  <p v-if="p.linkableExternalProviderLinks.length" class="mt-1">
                    Or select one that already exists:
                    <v-select
                      :options="p.linkableExternalProviderLinks"
                      label="name"
                      :reduce="prop('id')"
                      @update:model-value="linkToExternalLink(p, $event)"
                    />
                  </p>
                </FormGroupWithHelp>
                <FormGroupWithHelp
                  v-if="p.externalClientLinking != null && p.linkedExternalLink"
                  label="Sync clients in the linked integration with clients from this integration"
                >
                  <template #help-alert-content>
                    <div>
                      <p>
                        Automatically link clients from the external integration linkage to tenants
                        that have already been linked below in the client list.
                      </p>
                    </div>
                    <div>
                      <span style="text-decoration: underline">
                        Specifically for
                        <em>{{ selectedProviderType?.displayName }}</em>
                        -
                        <em>{{ p.providerType.displayName }}</em>
                        linking:
                      </span>
                      <p>{{ p.externalClientLinking.description }}</p>
                    </div>
                  </template>
                  <ImmyButton
                    :disabled="state.performingExternalClientSync"
                    variant="warning"
                    @click="performExternalClientSync(p)"
                  >
                    Perform Sync
                    <i v-if="state.performingExternalClientSync" class="fa fa-spinner fa-spin" />
                  </ImmyButton>
                </FormGroupWithHelp>
              </div>
            </div>
            <template v-if="state.providerLink != null && !isNew && supportsClients">
              <ImmyAlert
                v-if="state.syncingClientsFromProviderProgress != null"
                class="mt-2 max-width-xxl"
                show
                dismissible
              >
                <span v-if="isSyncingClientsFromProvider">Refreshing clients...</span>
                <span v-else-if="isClientSyncFromProviderSuccessful">Refreshed clients successfully</span>
                <span v-else-if="isClientSyncFromProviderFailed">Sync failed: {{ state.syncingClientsFromProviderProgress.message }}</span>
              </ImmyAlert>
              <ProviderLinkDetailsSuggestion
                :unlinked-clients-match-name-with-tenant-amount="clientsMatchingTenantsByName?.length ?? 0"
                :unlinked-clients-amount="unlikedClients?.length ?? 0"
                :syncing-clients="state.syncingClients"
                @show-matching-name="filterExternalClientName(clientsMatchingTenantsByName ?? [])"
                @show-unlinked="filterExternalClientName(unlikedClients ?? [])"
                @accept-link-by-matching-name="linkClientsWithExistingTenantsByName()"
                @accept-link="linkUnlinkedWithNoMatchingTenatByName"
                @accept-all="bulkSync(false)"
              />
              <p v-if="supportsRunningScripts" class="mt-3">
                <i class="fa-solid fa-triangle-exclamation text-danger mr-3" />
                <strong>Scripts will start executing on computers for linked clients!</strong>
              </p>
              <ProviderClientList
                v-if="providerClients != null && !state.loading"
                ref="providerClientList"
                table-id="provider-clients-list"
                :clients="providerClients"
                :extra-columns="state.extraColumns"
                :clients-matching-tenants-by-name="clientsMatchingTenantsByName"
                @client-unlinked="handleClientUnlinked"
                @client-linked="handleClientLinked"
                @selection-changed="providerClientListSelectionChanged"
              >
                <template #status="{ customer }">
                  {{ getStatusText(customer.status) }}
                </template>
                <template #types="{ customer }">
                  {{ getTypesText(customer.types) }}
                </template>
              </ProviderClientList>
            </template>
          </div>
        </ImmyTabBody>

        <ImmyTabHeader :active="state.selectedTab == IntegrationTab.Agents">
          <template #header>
            Integration Agents
          </template>
          <template #header-actions>
            <div class="d-flex align-items-center gap-2 flex-wrap">
              <ImmySelect v-model="agentFilter" class="max-width-md" :options="agentFilterOptions" />
              <ImmyCheckBox v-model="includeOfflineAgents" class="offline-checkbox">
                Include <ImmyBadge variant="danger">
                  Disconnected
                </ImmyBadge>
              </ImmyCheckBox>
              <LoadButton
                v-if="supportsAgentsSyncingOnDemand"
                class="whitespace-nowrap"
                title="Sync all agents for this integration"
                :disabled="syncingAgents"
                variant="secondary"
                :handler="syncAgents"
              >
                Sync agents
              </LoadButton>
              <ImmyButton
                v-if="agentFilter == ProviderAgentFilter.AssociatedToDeletedComputers"
                id="undelete-computers-button"
                :disabled="syncingAgents || state.selectedAgents.length === 0"
                variant="primary"
                class="whitespace-nowrap"
                @click="undeleteComputers"
              >
                Restore Computers
              </ImmyButton>
            </div>
          </template>
        </ImmyTabHeader>
        <ImmyTabBody v-show="state.selectedTab == 2">
          <p class="text-muted px-3 pt-3 m-0">
            This table shows the agents that have been imported into immy.bot from
            {{ state.providerLink?.name ?? "this integration" }}.
          </p>
          <AgentTable
            ref="agentTable"
            :only-pending="false"
            :include-offline="includeOfflineAgents"
            :provider-link-id="props.providerLinkId"
            :agent-filter="agentFilter"
            :selection="agentFilter == ProviderAgentFilter.AssociatedToDeletedComputers"
            @selection-changed="onAgentTableSelectionChanged"
          />
        </ImmyTabBody>

        <ImmyTabHeader :active="state.selectedTab == IntegrationTab.Audit">
          <template #header>
            Integration Audit Logs
          </template>
        </ImmyTabHeader>
        <ImmyTabBody v-show="state.selectedTab == 3" v-if="props.providerLinkId">
          <ProviderAuditLogTable :provider-link-id="props.providerLinkId" />
        </ImmyTabBody>
      </template>
    </ImmyTabs>

    <div v-else>
      <i class="fa fa-spin fa-spinner" />
      Loading data
    </div>
    <RecommendIntegrationDeploymentModal
      v-if="state.showRecommendedIntegrationDeploymentModal"
      :deployments="recommendedDeploymentsForIntegration"
      @done="goToEditPage"
    />
  </Page>
</template>

<script setup lang="ts">
import { DxDataGridTypes } from "devextreme-vue/data-grid";
import { prop } from "ramda";
import Swal from "sweetalert2";
import { computed, onBeforeUnmount, reactive, ref, watch } from "vue";
import { ComponentExposed } from "vue-component-type-helpers";
import { useRouter } from "vue-router";
import {
  Constants,
  HealthStatus,
  IClientStatus,
  IClientType,
  ICreateProviderLinkRequestBody,
  IGetExternalLinkInitializationInfoResponse,
  IGetProviderClientResponse,
  IGetProviderLinkResponse,
  IParameterValue,
  IProviderClientLinkToTenantByExactNameResponse,
  IProviderClientSyncProgressEvent,
  IProviderTypeDto,
  IUpdateProviderLinkPayload,
  ProviderAgentFilter,
  ProviderCapabilities,
  SyncState,
} from "@/api/backend/generated/contracts";

import { IDynamicFormBindResult } from "@/api/backend/generated/interfaces";
import UserHub from "@/api/backend/signalr-hubs/user-hub";
import UserHubEventInstance from "@/api/backend/signalr-hubs/UserHubEventInstance";
import { computersApi, providerLinksApi, providerTypesApi } from "@/api/backend/v1";
import TargetAssignment from "@/api/models/TargetAssignment";
import AgentTable from "@/components/AgentTable.vue";
import ProviderClientList, { Client } from "@/components/ProviderClientList.vue";
import { useViewBase } from "@/composables/ViewBase";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useGlobalTargetAssignmentsStore } from "@/store/pinia/global-target-assignments-store";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";
import { useProviderTypesStore } from "@/store/pinia/provider-types-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { IMMY_AGENT_PROVIDER_TYPE_ID, NEW_TENANT_ID } from "@/utils/constants";
import { IListItem } from "@/utils/ImmyListItem";
import { toast } from "@/utils/toast";

const props = defineProps<{
  providerLinkId?: number;
  providerTypeId?: string;
  providerLinkExternalReference?: number;
}>();
enum IntegrationTab {
  Setup = 0,
  Clients = 1,
  Agents = 2,
  Audit = -1,
}
const appAlertsStore = useAppAlertsStore();
const providerTypesStore = useProviderTypesStore();
const globalTargetAssignmentsStore = useGlobalTargetAssignmentsStore();
const router = useRouter();
const providerLinksStore = useProviderLinksStore();
const tenantsStore = useTenantsStore();
const authStore = useAuthStore();
// used to exclude n-central if not allowed
const userHubEventInstance = new UserHubEventInstance();

const AUTOMATE_PROVIDER_TYPE_ID = Constants.CwAutomateProviderId;
const CONTROL_PROVIDER_TYPE_ID = Constants.CwControlProviderTypeId;
const NCENTRAL_PROVIDER_TYPE_ID = Constants.NCentralProviderTypeId;
const HALO_PROVIDER_TYPE_ID = Constants.HaloPsaProviderTypeId;

interface LinkedExternalLink {
  providerLinkId: number;
  providerLinkName: string;
  isExternalClientLinkingEnabled: boolean;
  isExternalProviderInitializedFromThisProvider: boolean;
}

interface SupportedExternalProvider {
  providerType: IProviderTypeDto;
  linkableExternalProviderLinks: IGetProviderLinkResponse[];
  externalClientLinking?: {
    description: string;
  };
  initializationSupport?: {
    description: string;
  };
  linkedExternalLink?: LinkedExternalLink;
}

interface ProviderTypeCapability {
  key: string;
  displayName: string;
  description: string;
}

export type LinkClient = IGetProviderClientResponse & Client;

interface ISelectedAgent {
  id: number;
  computerId?: number;
}

const agentTable = ref(null);

const state = reactive({
  showRecommendedIntegrationDeploymentModal: false,
  selectedTab: 0,
  unchangedFormData: null as IGetProviderLinkResponse["providerTypeFormData"],
  providerLink: null as Partial<IGetProviderLinkResponse> | null,
  providerLinkFormDataIsValid: false,
  loading: true,
  submitting: false,
  syncingClients: false,
  providerTypes: [] as IProviderTypeDto[],
  selectedClients: [] as IGetProviderClientResponse[],
  performingExternalClientSync: false,
  canSelectProviderType: true,
  providerLinkExternalReferenceData: null as
    | (IGetExternalLinkInitializationInfoResponse & {
      enableClientExternalLinking: boolean;
      description: string;
    })
    | null,
  syncingClientsFromProviderProgress: null as IProviderClientSyncProgressEvent | null,
  newLinkId: null as number | null,
  extraColumns: [] as (DxDataGridTypes.Column & { slotName: string })[],
  clientStatuses: [] as { text: string; value: string }[],
  clientTypes: [] as { text: string; value: string }[],
  selectedAgents: [] as ISelectedAgent[],
  dynamicFormBindResult: {} as IDynamicFormBindResult,
});

const updateErrorMessage = ref<string>();
const togglingDisabled = ref<boolean | null>(null);

// name must match the "ref" used in the template
const providerClientList = ref<ComponentExposed<typeof ProviderClientList<LinkClient>> | null>(
  null,
);

function clientDatagridIsAccesible() {
  const clientsDataGridInstance = providerClientList.value?.clientsDataGrid?.dataGrid?.instance;
  if (clientsDataGridInstance == null) {
    appAlertsStore.addAlert({
      text: "Unexpected error: clients table is not accessible from the provider links details page",
    });
    return null;
  }
  return clientsDataGridInstance;
}

function filterExternalClientName(values: LinkClient[]) {
  const instance = clientDatagridIsAccesible();
  const filterValues = values.map(a => a.externalClientName?.toLowerCase()) ?? [];
  if (instance && filterValues.length > 0) {
    const filter: any[] = [];
    filterValues.forEach((a, index) => {
      if (index > 0) {
        filter.push("or");
      }
      filter.push(["externalClientName", "=", a]);
    });

    instance.filter(filter);
    instance.refresh();
  }
}

async function undeleteComputers() {
  if (state.selectedAgents.length === 0) {
    return;
  }
  const { value } = await Swal.fire({
    title: "Are you sure?",
    text: "Computers associated to the selected agents will be restored.",
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
  });
  if (value) {
    try {
      const computerIds = state.selectedAgents
        .reduce((acc, agent) => {
          if (agent.computerId != null) {
            acc.add(agent.computerId);
          }
          return acc;
        }, new Set<number>());
      const result = await computersApi.restoreComputers([...computerIds.values()]);
      const toastMessage = result ? "Computers were successfully restored" : "No computers were found to restore";
      toast.success(toastMessage);
      if (agentTable?.value) {
        (agentTable?.value as typeof AgentTable).clearAndRefresh();
      }
    }
    catch (err) {
      appAlertsStore.addAlert({
        text: "An error occurred while restoring computers",
        details: err,
      });
    }
  }
}

function onAgentTableSelectionChanged(eventData: IListItem[]) {
  state.selectedAgents = eventData.reduce((agg, cur) => {
    if ("computerId" in cur) {
      agg.push({ id: cur.id as number, computerId: cur.computerId as number });
    }

    return agg;
  }, [] as ISelectedAgent[]);
}

function updatedExcludeCapabilities(capability: string, value: boolean) {
  if (value) {
    excludedCapabilities.value = excludedCapabilities.value.filter(c => c !== capability);
  }
  else {
    excludedCapabilities.value.push(capability);
  }
}

const hasChangedCapabilities = computed(() => {
  return !(
    originalExcludedCapabilities.value.every(c => excludedCapabilities.value.includes(c))
    && originalExcludedCapabilities.value.length === excludedCapabilities.value.length
  );
});

const excludedCapabilities = ref<string[]>([]);
let originalExcludedCapabilities = ref<string[]>([]);
const supportedInterfaceCapabilities = computed(() => {
  const allPossibleInterfaces
    = state.providerLink?.providerTypeId != null
      ? providerTypesStore.providerTypes
        .find(t => t.providerTypeId === state.providerLink?.providerTypeId)
        ?.providerCapabilities
        .map(c => ({ ...ProviderCapabilities[c], key: c })) ?? undefined
      : undefined;

  if (allPossibleInterfaces === undefined)
    return undefined;

  const supportedInterfaces: ProviderTypeCapability[] = allPossibleInterfaces.map((c) => {
    return {
      key: c.key ?? "",
      displayName: c.DisplayName ?? "",
      description: c.Description ?? "",
    };
  });

  return supportedInterfaces;
});

const isConfigurationFormValid = ref(true);
function onConfigurationFormUpdated(form: Record<string, unknown>) {
  if (state.providerLink == null)
    return;
  state.providerLink.providerTypeFormData = form;
}

// agent table configuration
const includeOfflineAgents = ref(true);
const agentFilter = ref(ProviderAgentFilter.All);

const agentFilterOptions = [
  { text: "All Agents", value: ProviderAgentFilter.All },
  {
    text: "Only Agents Pending Identification",
    value: ProviderAgentFilter.OnlyPendingIdentification,
  },
  { text: "Only Identified Agents", value: ProviderAgentFilter.OnlyIdentified },
  { text: "Only Associated To Deleted Computers", value: ProviderAgentFilter.AssociatedToDeletedComputers },
];

const providerTypeCapabilities = computed(() => {
  return state.providerLink?.providerTypeId != null
    ? providerTypesStore.providerTypesCapabilityCheckers.get(state.providerLink.providerTypeId)
    : null;
});

const supportsProviderClientStatus = computed(
  () =>
    providerTypeCapabilities.value?.supportsProviderClientStatus === true
    && !state.providerLink?.excludedCapabilities?.includes("ISupportsProviderClientStatus"),
);

const supportsProviderClientType = computed(
  () =>
    providerTypeCapabilities.value?.supportsProviderClientType === true
    && !state.providerLink?.excludedCapabilities?.includes("ISupportsProviderClientType"),
);

const supportsAgentsSyncingOnDemand = computed(
  () =>
    providerTypeCapabilities.value?.supportsSyncingAgentsOnDemand === true
    && !state.providerLink?.excludedCapabilities?.includes("ISupportsSyncingAgentsOnDemand"),
);

const isNew = computed(() => props.providerLinkId == null);

const allTenants = computed(() => tenantsStore.allTenants);
const mspTenants = computed(() => allTenants.value.filter(t => t.isMsp));
const providerClients = computed<LinkClient[] | undefined>(() => {
  return state.providerLink?.providerClients?.map((c) => {
    const tenant = allTenants.value.find(t => t.id === c.linkedToTenantId);
    return {
      ...c,
      linkedTenantIsMsp: tenant?.isMsp ?? false,
    };
  });
});

const clientsMatchingTenantsByName = computed(() => {
  return providerClients.value?.filter(
    client =>
      client.linkedToTenantId == null
      && allTenants.value.find(
        tenant => tenant.name.toLowerCase() === client.externalClientName?.toLowerCase(),
      ),
  );
});

const unlikedClients = computed(() => {
  return providerClients.value?.filter(
    client => client.linkedToTenantId == null
      && !allTenants.value.find(
        tenant => tenant.name.toLowerCase() === client.externalClientName?.toLowerCase(),
      ),
  );
});

const initialParameterValues = computed(() => {
  const formData: Record<string, unknown> = state.providerLink?.providerTypeFormData;
  if (!formData)
    return new Map<string, IParameterValue>();

  const correctedFormData = new Map<string, IParameterValue>();

  for (const [key, value] of Object.entries(formData)) {
    if (value == null)
      correctedFormData.set(key, { value: null });
    // if value is an object and has a property called value, then skip it
    else if (typeof value === "object" && "value" in value)
      correctedFormData.set(key, value);
    else correctedFormData.set(key, { value });
  }

  return correctedFormData;
});
useViewBase({
  loading: computed(() => state.loading || state.submitting),
  breadcrumbs: computed(() => [
    {
      text: "Integrations",
      to: { name: "Integrations List" },
    },
    {
      text: props.providerLinkId
        ? state.providerLink?.name ?? `Id: ${props.providerLinkId}`
        : "New",
    },
  ]),
  initialize: async () => {
    try {
      state.loading = true;
      state.providerTypes = await providerTypesStore.getAllProviderTypes(true);

      await tenantsStore.getAllTenants();
      await providerLinksStore.getAllProviderLinks();

      state.canSelectProviderType = true;
      if (isNew.value) {
        state.providerLink = {
          providerTypeFormData: {},
        };
        state.unchangedFormData = null;
        state.providerLinkExternalReferenceData = null;
        if (state.providerTypes.length === 1)
          state.providerLink.providerTypeId = state.providerTypes[0].providerTypeId;

        if (props.providerTypeId != null) {
          // if the form is supposed to be initializing only one specific
          // type of provider, only allow that type
          if (state.providerTypes.some(t => t.providerTypeId === props.providerTypeId)) {
            state.providerLink.providerTypeId = props.providerTypeId;
            state.canSelectProviderType = false;
          }
          else {
            throw new Error(`Unknown provider type: ${props.providerTypeId}`);
          }
        }
        if (props.providerLinkExternalReference != null) {
          // the form is creating a new provider link for the purpose of
          // referencing it from an existing provider link. Get the details of
          // this reference support from the server before continuing
          if (props.providerTypeId == null) {
            throw new Error(
              "Provider type id is required if providerLinkExternalReference is provided",
            );
          }
          state.providerLinkExternalReferenceData = Object.assign(
            { enableClientExternalLinking: false, description: "" },
            await providerLinksApi.getExternalProviderLinkInitializationInfo(
              props.providerLinkExternalReference,
              props.providerTypeId,
            ),
          );
        }
        await Promise.all([
          globalTargetAssignmentsStore.loadAll(),
          globalTargetAssignmentsStore.loadAllApprovals(),
        ]);
      }
      else {
        const link = await providerLinksStore.getProviderLink(props.providerLinkId!);

        state.providerLink = Object.assign({}, link);

        state.dynamicFormBindResult = await bindProviderLinkFormData(link);

        excludedCapabilities.value = state.providerLink.excludedCapabilities ?? [];
        originalExcludedCapabilities.value = [...excludedCapabilities.value];

        if (state.providerLink.includeClientsFailedMessage != null) {
          appAlertsStore.addAlert({
            text: `An error occurred while getting the clients from the integration: ${state.providerLink.includeClientsFailedMessage}`,
          });
        }
        if (state.providerLink.getFormSchemaFailedMessage != null) {
          appAlertsStore.addAlert({
            text: `An error occurred while getting the integration information: ${state.providerLink.getFormSchemaFailedMessage}`,
          });
        }
        if (state.providerLink.getProviderFailedMessage != null) {
          appAlertsStore.addAlert({
            text: `An error occurred while getting the integration information: ${state.providerLink.getProviderFailedMessage}`,
          });
        }
        state.unchangedFormData = Object.assign({}, state.providerLink.providerTypeFormData);
      }

      // copied from provideClientList vue component
      if (
        state.providerLink?.healthStatus === HealthStatus.Healthy
        && !state.providerLink.disabled
      ) {
        const providerLink = state.providerLink;
        const reqs: Promise<void>[] = [];
        if (supportsProviderClientType.value) {
          reqs.push(
            (async () => {
              const types: IClientType[] = await providerLinksApi.getProviderClientTypes(
                providerLink.id!,
              );
              if (types.length) {
                state.clientTypes = types.map((a) => {
                  return {
                    text: a.clientTypeDisplayName,
                    value: a.clientTypeId,
                  };
                });
              }
            })(),
          );
        }

        if (supportsProviderClientStatus.value) {
          reqs.push(
            (async () => {
              const statuses: IClientStatus[] = await providerLinksApi.getProviderClientStatuses(
                providerLink.id!,
              );
              if (statuses.length) {
                state.clientStatuses = statuses.map((a) => {
                  return {
                    text: a.clientStatusDisplayName,
                    value: a.clientStatusId,
                  };
                });
              }
            })(),
          );
        }
        await Promise.all(reqs);
        initExtraColumns();
      }

      if (props.providerLinkId) {
        userHubEventInstance.joinProviderLinkGroup(props.providerLinkId).then(() => {
          setupEventHubListeners();
        });
      }
      else {
        setupEventHubListeners();
      }
    }
    catch (err) {
      appAlertsStore.addAlert({
        text: "An error occurred while getting the integration data",
        details: err,
      });
    }
    finally {
      state.loading = false;
    }
  },
});

const recommendedDeploymentsForIntegration = computed(() => {
  if (!selectedProviderType.value)
    return [];
  const providerTypeId = selectedProviderType.value.providerTypeId;

  return globalTargetAssignmentsStore.unacknowledgedDeployments.filter(
    (a: TargetAssignment) => a.integrationTypeId === providerTypeId,
  );
});
const docsUrl = computed(() => {
  if (!selectedProviderType.value)
    return undefined;
  return selectedProviderType.value.docsUrl;
});

const canManage = computed(() => {
  return selectedProviderType.value?.canManage ?? false;
});

const isSyncingClientsFromProvider = computed(() => {
  return state.syncingClientsFromProviderProgress?.state === SyncState.Running;
});
const isClientSyncFromProviderSuccessful = computed(() => {
  return state.syncingClientsFromProviderProgress?.state === SyncState.Success;
});
const isClientSyncFromProviderFailed = computed(() => {
  return state.syncingClientsFromProviderProgress?.state === SyncState.Failed;
});
const healthData = computed(() => {
  if (state.providerLink?.disabled) {
    return { variant: "danger", text: "is disabled", icon: "fa-solid fa-heart-circle-xmark" };
  }
  else if (state.providerLink?.healthStatus === HealthStatus.Unhealthy) {
    return {
      variant: "warning",
      text: "is experiencing issues",
      icon: "fa-solid fa-heart-circle-exclamation",
      healthStatusMessage: state.providerLink.healthStatusMessage,
    };
  }
  else if (state.providerLink?.healthStatus === HealthStatus.Degraded) {
    return {
      variant: "warning",
      text: "has recently started experiencing issues",
      icon: "fa-solid fa-heart-circle-exclamation",
      healthStatusMessage: state.providerLink.healthStatusMessage,
    };
  }
  else if (state.providerLink?.healthStatus === HealthStatus.Healthy) {
    return {
      variant: "primary",
      text: "is working as expected",
      icon: "fa-solid fa-heart-circle-check",
    };
  }
  return {};
});

const showNoMspTenantAssignedMessage = computed(() => {
  const mspTenantIds = mspTenants.value.map(a => a.id);
  return !!(
    props.providerLinkId
    && state.providerLink?.providerClients != null
    && state.providerLink.providerClients.length > 0
    && state.providerLink.providerClients.every(
      a => a.linkedToTenantId == null || !mspTenantIds.includes(a.linkedToTenantId),
    )
  );
});
const allProviderLinks = computed<IGetProviderLinkResponse[]>(() => {
  return providerLinksStore.allProviderLinks;
});
const crossProviderClientLinkingSupportIsValid = computed(() => {
  return (
    state.providerLinkExternalReferenceData == null
    || !state.providerLinkExternalReferenceData.supportsCrossProviderClientExternalLinking
    || state.providerLinkExternalReferenceData.enableClientExternalLinking != null
  );
});
const hasIssues = computed(() => {
  return (
    !nameIsValid.value
    || !crossProviderClientLinkingSupportIsValid.value
    || !isConfigurationFormValid.value
  );
});
const nameIsValid = computed(() => {
  return (
    state.providerLink && state.providerLink.name != null && state.providerLink.name.length > 0
  );
});
const selectedProviderType = computed(() => {
  const link = state.providerLink;
  if (link == null || link.providerTypeId == null)
    return null;
  return state.providerTypes.find(p => p.providerTypeId === link.providerTypeId);
});

const supportsClients = computed(
  () =>
    providerTypeCapabilities.value != null && providerTypeCapabilities.value.supportsListingClients,
);

const supportsAgents = computed(() => {
  return (
    state.providerLink != null
    && (providerTypeCapabilities.value?.supportsListingAgents === true
      || state.providerLink.providerTypeId === NCENTRAL_PROVIDER_TYPE_ID
      || state.providerLink.providerTypeId === HALO_PROVIDER_TYPE_ID
      || state.providerLink.providerTypeId === IMMY_AGENT_PROVIDER_TYPE_ID
      || state.providerLink.providerTypeId === CONTROL_PROVIDER_TYPE_ID
      || state.providerLink.providerTypeId === AUTOMATE_PROVIDER_TYPE_ID)
  );
});

const supportedCrossProviderInitializationLinkages = computed(() => {
  return state.providerLink?.supportedCrossProviderInitializationLinkages ?? [];
});
const supportsCrossProviderClientLinkages = computed(() => {
  return (
    state.providerLink?.supportedCrossProviderClientLinkages
    && !state.providerLink.excludedCapabilities?.includes("ISupportsCrossProviderClientLinking")
  );
});
const supportedCrossProviderClientLinkages = computed(() => {
  return state.providerLink?.supportedCrossProviderClientLinkages ?? [];
});
const supportsRunningScripts = computed(() => {
  return providerTypeCapabilities.value != null && providerTypeCapabilities.value.runScriptProvider;
});
const providersLinkedFromThisProvider = computed(() => {
  return state.providerLink?.providersLinkedFromThisProvider ?? [];
});

const supportedExternalProviders = computed(() => {
  const providerTypes = state.providerTypes.reduce(
    (agg, t) => {
      return Object.assign(agg, {
        [t.providerTypeId]: t,
      });
    },
    {} as { [x: string]: IProviderTypeDto },
  );
  const linkedProviderLinks = providersLinkedFromThisProvider.value.reduce(
    (agg, t) => {
      return Object.assign(agg, {
        [t.providerTypeId]: {
          providerLinkId: t.providerLinkId,
          providerLinkName: t.providerLinkName,
          isExternalClientLinkingEnabled: t.isExternalClientLinkingEnabled,
          isExternalProviderInitializedFromThisProvider:
            t.isExternalProviderInitializedFromThisProvider,
        },
      });
    },
    {} as { [x: string]: LinkedExternalLink },
  );

  const ret: {
    [x: string]: SupportedExternalProvider;
  } = {};
  for (const e of supportedCrossProviderClientLinkages.value) {
    const obj
      = ret[e.providerTypeId]
        || (ret[e.providerTypeId] = {
          providerType: providerTypes[e.providerTypeId],
          linkableExternalProviderLinks: allProviderLinks.value.filter(
            l => l.providerTypeId === e.providerTypeId,
          ),
        });
    obj.externalClientLinking = {
      description: e.description,
    };
    const linkedExternalLink = linkedProviderLinks[e.providerTypeId];
    if (linkedExternalLink != null)
      obj.linkedExternalLink = linkedExternalLink;
  }
  for (const e of supportedCrossProviderInitializationLinkages.value) {
    const obj
      = ret[e.providerTypeId]
        || (ret[e.providerTypeId] = {
          providerType: providerTypes[e.providerTypeId],
          linkableExternalProviderLinks: allProviderLinks.value.filter(
            l => l.providerTypeId === e.providerTypeId,
          ),
        });
    obj.initializationSupport = {
      description: e.description,
    };
    const linkedExternalLink = linkedProviderLinks[e.providerTypeId];
    if (linkedExternalLink != null)
      obj.linkedExternalLink = linkedExternalLink;

    // unsure if this is relevant still, but initInfos is not defined anywhere
    // const initInfo = initInfos[e.providerTypeId];
    // if (initInfo) {
    //   obj.initializationSupport.fetchedInfo = initInfo;
    // }
  }
  return [...Object.values(ret)];
});

async function bindProviderLinkFormData(link: IGetProviderLinkResponse) {
  const bindParametersRequestBody: Map<string, any> = new Map();

  const keys = Object.keys(link.providerTypeFormData);
  for (const key of keys) {
    const value = link.providerTypeFormData[key];
    if (key != "OAuthInfo")
      bindParametersRequestBody.set(key, { value });
  }

  return await providerTypesApi.bindParameters(link.providerTypeId, {
    parameterValues: Object.fromEntries(bindParametersRequestBody),
    providerLinkId: props.providerLinkId,
  });
}

async function performExternalClientSync(provider: SupportedExternalProvider) {
  if (state.performingExternalClientSync)
    return;
  if (provider.linkedExternalLink == null)
    return;
  try {
    state.performingExternalClientSync = true;
    await providerLinksApi.syncClientsFromLinkedProvider(
      props.providerLinkId!,
      provider.linkedExternalLink.providerLinkId,
    );
    toast.success("Success");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to sync external integration clients.",
      details: err,
    });
  }
  finally {
    state.performingExternalClientSync = false;
  }
}

function providerClientListSelectionChanged(val: IGetProviderClientResponse[]) {
  state.selectedClients = val;
}

async function fetchProviderClients() {
  try {
    await providerLinksApi.syncClientsFromProvider(props.providerLinkId!);
    toast.success("Sync triggered");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to sync clients.",
      details: err,
    });
  }
}

async function linkClientsWithExistingTenantsByName() {
  if (props.providerLinkId == null)
    return;
  const { value } = await Swal.fire({
    title: "Are you sure?",
    text: `${clientsMatchingTenantsByName.value?.length} client(s) will be linked to tenants with the same name`,
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: "Yes, I am sure",
    cancelButtonText: "Cancel",
    reverseButtons: true,
  });
  if (!value)
    return;

  try {
    const res: IProviderClientLinkToTenantByExactNameResponse[]
      = await providerLinksApi.linkExactMatchClients(props.providerLinkId);

    if (res.length > 0 && !!state.providerLink) {
      const providerClientTenantMap = new Map(
        res.map(providerClient => [
          providerClient.externalClientId,
          providerClient.linkedToTenantId,
        ]),
      );
      state.providerLink.providerClients?.forEach((client) => {
        if (providerClientTenantMap.has(client.externalClientId)) {
          client.linkedToTenantId = providerClientTenantMap.get(client.externalClientId);
        }
      });
    }
    toast.success("Clients linked");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to link clients to tenants by name.",
      details: err,
    });
  }
}

async function linkUnlinkedWithNoMatchingTenatByName() {
  const clientIds = unlikedClients?.value?.map(unlinkedClient => unlinkedClient.externalClientId) ?? [];
  if (state.syncingClients || clientIds.length == 0)
    return;
  try {
    if (supportsRunningScripts.value) {
      const { value } = await Swal.fire({
        title: "Are you sure?",
        text: `Scripts will start executing on computers for linked clients.`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, I am sure",
        cancelButtonText: "Cancel",
        reverseButtons: true,
      });
      if (!value)
        return;
    }

    if (showNoMspTenantAssignedMessage.value) {
      const { value } = await Swal.fire({
        title: "No client has been linked to the MSP tenant",
        text: `If there are clients in this integration that corresponds to the MSP tenant, link them before you bulk create new tenants for unassigned clients.  Continue anyway?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Continue without linking MSP tenant",
        cancelButtonText: "Cancel",
        reverseButtons: true,
      });
      if (!value)
        return;
    }
    state.syncingClients = true;

    toast.success("Creating links.  Page will reload when complete");
    await providerLinksApi.autoLinkClientsToTenant(props.providerLinkId!, {
      clientIds,
    });
    await tenantsStore.getAllTenants({ force: true });
    toast.success("Finished linking all clients");
    await triggerClientSync();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to link all clients.",
      details: err,
    });
  }
  finally {
    state.syncingClients = false;
  }
}

async function bulkSync(forSelected: boolean) {
  try {
    if (state.syncingClients || isNew.value)
      return;

    // reach into the data grid and grab the current filter set
    // and only bulk create tenants for these.
    const clientsDataGridInstance = clientDatagridIsAccesible();
    if (clientsDataGridInstance == null) {
      return;
    }
    const filter = clientsDataGridInstance.getCombinedFilter();
    const filteredRows = (await clientsDataGridInstance
      .getDataSource()
      .store()
      .load({ filter })) as IGetProviderClientResponse[];

    if (!filteredRows.length) {
      appAlertsStore.addAlert({
        text: "There are zero clients showing in the table.  Change the table filter to include at least one client.",
      });
      return;
    }

    if (supportsRunningScripts.value) {
      const { value } = await Swal.fire({
        title: "Are you sure?",
        text: `Scripts will start executing on computers for linked clients.`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, I am sure",
        cancelButtonText: "Cancel",
        reverseButtons: true,
      });
      if (!value)
        return;
    }

    if (showNoMspTenantAssignedMessage.value) {
      const { value } = await Swal.fire({
        title: "No client has been linked to the MSP tenant",
        text: `If there are clients in this integration that corresponds to the MSP tenant, link them before you bulk create new tenants for unassigned clients.  Continue anyway?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Continue without linking MSP tenant",
        cancelButtonText: "Cancel",
        reverseButtons: true,
      });
      if (!value)
        return;
    }
    state.syncingClients = true;

    let filteredClients: string[] = [];
    if (filteredRows.length !== state.providerLink?.providerClients?.length)
      filteredClients = filteredRows.map(a => a.externalClientId);

    toast.success("Creating links.  Page will reload when complete");
    await providerLinksApi.autoLinkClientsToTenant(props.providerLinkId!, {
      clientIds: filteredClients.length
        ? filteredClients
        : forSelected
          ? state.selectedClients.map(a => a.externalClientId)
          : [],
    });
    await tenantsStore.getAllTenants({ force: true });
    toast.success("Finished linking all clients");
    await triggerClientSync();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to link all clients.",
      details: err,
    });
  }
  finally {
    state.syncingClients = false;
  }
}
async function handleClientUnlinked({ externalClientId }: { externalClientId: string }) {
  if (isNew.value)
    return;
  const providerLink = state.providerLink;
  if (providerLink == null)
    return;
  try {
    await providerLinksStore.unlinkProviderClients(props.providerLinkId!, [externalClientId]);
    const client = providerLink.providerClients?.find(c => c.externalClientId === externalClientId);
    if (client != null)
      Object.assign(client, { linkedToTenantId: null });

    state.providerLink = Object.assign({}, state.providerLink);
    toast.success("Successfully unlinked client");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Error occurred while unlinking the client.",
      details: err,
    });
  }
}
async function handleClientLinked({
  externalClientId,
  linkedToTenantId,
}: {
  externalClientId: string;
  linkedToTenantId: number;
}) {
  if (isNew.value)
    return;
  try {
    if (linkedToTenantId === NEW_TENANT_ID) {
      await providerLinksApi.linkClientToNewTenant(props.providerLinkId!, {
        externalClientId,
        tenantName: undefined,
      });

      toast.success("Successfully created new tenant and linked client");
    }
    else {
      await providerLinksStore.linkProviderClientsToTenant(
        linkedToTenantId,
        props.providerLinkId!,
        [externalClientId],
      );
      const client = state.providerLink?.providerClients?.find(
        c => c.externalClientId === externalClientId,
      );
      if (client != null)
        client.linkedToTenantId = linkedToTenantId;

      state.providerLink = Object.assign({}, state.providerLink);
      toast.success("Successfully linked client");
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Error occurred while linking the client to the tenant.",
      details: err,
    });
  }
}

async function triggerClientSync() {
  if (isNew.value)
    return;
  try {
    const res = await providerLinksApi.getProviderClients(props.providerLinkId!);
    state.providerLink = Object.assign({}, state.providerLink, {
      providerClients: res,
    });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while triggering the client sync.",
      details: err,
    });
  }
}
function goToEditPage() {
  if (state.newLinkId == null)
    return;
  router.push({
    name: "Edit Integration",
    params: {
      id: state.newLinkId.toString(),
    },
  });
}
async function create() {
  if (state.providerLink == null)
    return;
  try {
    authStore.setLoading();
    state.submitting = true;
    toast.success("Creating integration... please wait.");
    let link;
    if (state.providerLinkExternalReferenceData != null) {
      link = await providerLinksStore.createProviderLinkWithExternalProviderReference({
        providerLink: state.providerLink,
        providerLinkExternalReferenceData: {
          providerLinkId: props.providerLinkExternalReference!,
          providerTypeFormData: state.providerLinkExternalReferenceData.providerTypeFormData,
          enableClientExternalLinking:
            state.providerLinkExternalReferenceData.enableClientExternalLinking,
        },
      });
    }
    else {
      const payload: ICreateProviderLinkRequestBody = {
        name: state.providerLink.name ?? "",
        providerTypeId: state.providerLink.providerTypeId ?? "",
      };
      link = await providerLinksStore.createProviderLink(payload);
      state.newLinkId = link.id;
    }
    toast.success("Integration created successfully");

    // if any recommended deployments for this integration, show them in modal
    if (recommendedDeploymentsForIntegration.value.length) {
      state.showRecommendedIntegrationDeploymentModal = true;
      return;
    }

    router.push({
      name: "Edit Integration",
      params: {
        id: link.id.toString(),
      },
    });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while creating the integration.",
      details: err,
    });
  }
  finally {
    authStore.loadingComplete();
    state.submitting = false;
  }
}
async function update(toggleDisabled = false) {
  if (state.providerLink?.id == null || state.providerLink?.name == null)
    return;
  try {
    authStore.setLoading();
    state.submitting = true;

    if (toggleDisabled)
      togglingDisabled.value = true;

    const link: IUpdateProviderLinkPayload = {
      id: state.providerLink.id,
      name: state.providerLink.name,
      disabled: toggleDisabled ? !state.providerLink.disabled : !!state.providerLink.disabled,
      excludedCapabilities: excludedCapabilities.value,
      providerTypeFormData: state.providerLink.providerTypeFormData,
    };

    state.providerLink = await providerLinksStore.updateProviderLink(link);
    originalExcludedCapabilities.value = [...excludedCapabilities.value];
    if (state.providerLink.errorMessage != null) {
      updateErrorMessage.value = state.providerLink.errorMessage;
      state.providerLink.healthStatus = HealthStatus.Degraded;
      state.providerLink.healthStatusMessage = updateErrorMessage.value;
      toast.warning("Integration updated with errors");
    }
    else {
      updateErrorMessage.value = undefined;
      toast.success("Integration updated successfully");
    }
    await triggerClientSync();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while updating the integration.",
      details: err,
    });
  }
  finally {
    authStore.loadingComplete();
    state.submitting = false;
    togglingDisabled.value = null;
  }
}
async function linkToExternalLink(
  supportedExternalProvider: SupportedExternalProvider,
  selectedproviderLinkId: number,
) {
  if (selectedproviderLinkId == null) {
    throw new Error(
      "Cannot link this integration to other integration - no integration link id was provided",
    );
  }
  try {
    const providerLinkExternalReference = await providerLinksApi.createLinkedProviderReference(
      props.providerLinkId!,
      {
        providerLinkId: selectedproviderLinkId,
        isCrossProviderClientExternalLinkingEnabled: false,
      },
    );
    const providersLinkedFromThis = state.providerLink?.providersLinkedFromThisProvider;
    if (providersLinkedFromThis == null)
      return;
    state.providerLink = Object.assign({}, state.providerLink, {
      providersLinkedFromThisProvider: [...providersLinkedFromThis, providerLinkExternalReference],
    });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while linking the external integration.",
      details: err,
    });
  }
}
async function unlinkFromExternalLink(supportedExternalProvider: SupportedExternalProvider) {
  const externalLinkId = supportedExternalProvider.linkedExternalLink?.providerLinkId;
  if (externalLinkId == null) {
    throw new Error(
      "Cannot unlink this integration from the other integration - it is not currently linked",
    );
  }

  const conf = window.confirm("Are you sure you want to remove this linkage?");
  if (!conf)
    return;

  try {
    await providerLinksApi.deleteLinkedProviderReference(props.providerLinkId!, externalLinkId);
    const providersLinkedFromThis = state.providerLink?.providersLinkedFromThisProvider;
    if (providersLinkedFromThis == null)
      return;
    state.providerLink = Object.assign({}, state.providerLink, {
      providersLinkedFromThisProvider: providersLinkedFromThis.filter(
        r => r.providerLinkId !== externalLinkId,
      ),
    });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while unlinking the external integration.",
      details: err,
    });
  }
}
async function triggerExternalProviderSetupProcess(
  supportedExternalProvider: SupportedExternalProvider,
) {
  if (props.providerLinkId == null)
    return;

  // create a new integration for the supplied external provider and navigate to it.
  await onProviderTypeSelected(supportedExternalProvider.providerType);
}

function getStatusText(statusId: string | null | undefined) {
  if (statusId == null)
    return;
  return state.clientStatuses.find(a => a.value == statusId)?.text;
}
function getTypesText(types: string[] | null | undefined) {
  if (!types?.length)
    return "";
  return types
    .map((t) => {
      return state.clientTypes.find(a => a.value == t)?.text;
    })
    .join(", ");
}

async function onProviderTypeSelected(p: IProviderTypeDto) {
  let name = p.displayName;
  const count = allProviderLinks.value.filter(a => a.providerTypeId === p.providerTypeId).length;
  if (count > 0)
    name += ` ${count + 1}`;
  const link = await providerLinksApi.create({
    providerTypeId: p.providerTypeId,
    name,
  });

  router.push({
    name: "Edit Integration",
    params: {
      id: link.id.toString(),
    },
  });
}

function setupEventHubListeners() {
  userHubEventInstance.onEvent(UserHub.Client.ProviderClientSyncProgress, async (ev) => {
    if (props.providerLinkId !== ev.providerLinkId)
      return;
    state.syncingClientsFromProviderProgress = ev;

    if (ev.state === SyncState.Success) {
      try {
        const res = await providerLinksApi.getProviderClients(props.providerLinkId!);

        state.providerLink = Object.assign({}, state.providerLink, {
          providerClients: res,
        });
      }
      catch (err) {
        appAlertsStore.addAlert({
          text: "An error occurred while triggering the client sync.",
          details: err,
        });
      }
    }
  });
  userHubEventInstance.onHubReconnected(async () => {
    if (props.providerLinkId)
      await userHubEventInstance.joinProviderLinkGroup(props.providerLinkId);
  });
}

function initExtraColumns() {
  state.extraColumns = [
    {
      dataType: "number",
      dataField: "status",
      caption: "Status",
      alignment: "center",
      headerFilter: {
        dataSource: state.clientStatuses,
      },
      slotName: "status",
    },
    {
      dataType: "string",
      dataField: "types",
      caption: "Types",
      slotName: "types",
      alignment: "center",
      headerFilter: {
        dataSource: state.clientTypes,
      },
      calculateDisplayValue: (rowInfo: Client) => {
        if (!("types" in rowInfo) || typeof rowInfo.types === "undefined")
          return "";
        return getTypesText(rowInfo.types);
      },
      calculateFilterExpression(
        this: DxDataGridTypes.Column<LinkClient>,
        filterValue: any,
        selectedFilterOperation: string | null,
        target: string,
      ) {
        const column = this;
        if (filterValue) {
          const applyOperation = (arg1: unknown, arg2: unknown, op: string) => {
            if (op === "=")
              return arg1 === arg2;
            if (op === "contains")
              return (arg1 as object[]).includes(arg2 as object);
            if (op === "startswith")
              return (arg1 as string).startsWith(arg2 as string);
            if (op === "endswith")
              return (arg1 as string).endsWith(arg2 as string);
          };
          if (target === "headerFilter") {
            const selector = (data: LinkClient) => {
              const values = column.calculateCellValue?.(data);
              return (
                values
                && !!values.find((v: unknown) =>
                  applyOperation(v, filterValue, selectedFilterOperation ?? ""),
                )
              );
            };
            return [selector, "=", true];
          }
          else if (target === "filterRow") {
            const selector = (data: LinkClient) => {
              if (typeof column.calculateDisplayValue === "function") {
                const values = column.calculateDisplayValue(data)?.toLowerCase() ?? "";
                return applyOperation(
                  values,
                  (filterValue as string).toLowerCase(),
                  selectedFilterOperation ?? "",
                );
              }
            };
            return [selector, "=", true];
          }
          else {
            // eslint-disable-next-line ts/no-non-null-asserted-optional-chain
            return column.defaultCalculateFilterExpression?.(
              filterValue,
              selectedFilterOperation,
              target,
            )!;
          }
        }
        // eslint-disable-next-line ts/no-non-null-asserted-optional-chain
        return column.defaultCalculateFilterExpression?.(
          filterValue,
          selectedFilterOperation,
          target,
        )!;
      },
    },
  ];
}

onBeforeUnmount(async () => {
  if (props.providerLinkId)
    await userHubEventInstance.leaveProviderLinkGroup(props.providerLinkId);

  userHubEventInstance.clearCallbacks();
});

watch(
  () => state.selectedTab,
  (val) => {
    if (val === 1)
      providerClientList.value?.refresh();
  },
);
watch(selectedProviderType, (val) => {
  // if this is a new link, then let's auto set the name to the selected type's name
  // and if there is already a link of the same type then append an index to ensure a unique name
  if (val && !props.providerLinkId && state.providerLink != null) {
    state.providerLink.name = val.displayName;

    const count = allProviderLinks.value.filter(
      a => a.providerTypeId === val.providerTypeId,
    ).length;
    if (count > 0)
      state.providerLink.name += ` ${count + 1}`;
  }
});

async function reloadIntegration() {
  if (!props.providerLinkId)
    return;
  await providerLinksApi.reload(props.providerLinkId);
  toast.success("Integration reloaded");
}

// agent syncing

const syncingAgents = ref(false);
async function syncAgentsForSelectedClients() {
  if (!props.providerLinkId)
    return;
  const selectedClients = state.selectedClients;
  if (syncingAgents.value || !selectedClients.length)
    return;
  try {
    syncingAgents.value = true;
    const clientIds = selectedClients.map(a => a.externalClientId);
    await providerLinksApi.syncAgentsForClients(props.providerLinkId, clientIds);
    toast.success("Agent sync triggered");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while syncing agents.",
      details: err,
    });
  }
  finally {
    syncingAgents.value = false;
  }
}
async function syncAgents() {
  if (!props.providerLinkId || syncingAgents.value)
    return;
  try {
    syncingAgents.value = true;
    await providerLinksApi.syncAgents(props.providerLinkId);
    toast.success("Agent sync triggered");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while syncing agents.",
      details: err,
    });
  }
  finally {
    syncingAgents.value = false;
  }
}
</script>

<style lang="scss" scoped>
#provider-link-tenant-list > .dx-datagrid {
  min-width: auto;
}

.provider-capabilities {
  padding-left: 1rem;
  li {
    line-height: 1.5rem;
  }

  .excluded {
    // add strike-through
    text-decoration: line-through;
  }
}

.tab-header {
  background: gray;
}

.tab-body {
  padding: 2rem;
}

.health-status-message {
  white-space: pre-wrap;
}

.offline-checkbox {
  :deep(.custom-control-label) {
    // always keep checkbox and label on the same line
    min-width: 144px;
  }
}

/*Checked icon override*/
:deep(.dx-checkbox-checked .dx-checkbox-icon::before) {
  content: "\58" !important;
  background-color: var(--radio-checked-bg) !important;
  font-family: "fontawesome" !important;
  color: var(--radio-color) !important;
}

:deep(.dx-list-item) {
  white-space: wrap;
}

:deep(.dx-list-item-content) {
  &:before {
    content: "";
  }
}
.capability-toggle {
  margin-bottom: 0;
  width: 1.75rem !important;
  min-width: 1.75rem !important;
  height: 1rem !important;

  :deep(.slider)::before {
    height: 14px;
    width: 14px;
    left: 1px;
    bottom: 1px;
  }

  :deep(input:checked + .slider):before {
    transform: translateX(12px);
  }
}
</style>
