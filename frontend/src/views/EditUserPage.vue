<template>
  <Page>
    <template #header>
      <page-header :title="title" />
    </template>
    <div class="row">
      <div class="col-6">
        <SimplePanel>
          <template #title>
            Details
          </template>
          <form v-if="person != null" class="person-form font-weight-bold" @submit.prevent="onPersonSubmit">
            <ImmyRow>
              <ImmyCol lg="12">
                <ImmyFormGroup v-if="canManagePerson" label="Tenant" label-for="tenant-select">
                  <v-select
                    id="tenant-select"
                    v-model="selectedTenant"
                    :options="allTenants"
                    label="name"
                  />
                </ImmyFormGroup>
              </ImmyCol>
            </ImmyRow>
            <ImmyRow>
              <ImmyCol lg="6">
                <ImmyFormGroup id="name-input-group" label="First Name" label-for="first-name-input">
                  <ImmyInput
                    id="first-name-input"
                    v-model="person.firstName"
                    data-testid="person-first-name-input"
                    type="text"
                    required
                    placeholder="Enter first name"
                    :disabled="!canManagePerson"
                  />
                </ImmyFormGroup>
              </ImmyCol>
              <ImmyCol lg="6">
                <ImmyFormGroup id="last-name-input-group" label="Last Name" label-for="last-name-input">
                  <ImmyInput
                    id="last-name-input"
                    v-model="person.lastName"
                    data-testid="person-last-name-input"
                    type="text"
                    required
                    placeholder="Enter last name"
                    :disabled="!canManagePerson"
                  />
                </ImmyFormGroup>
              </ImmyCol>
            </ImmyRow>
            <ImmyRow>
              <ImmyCol lg="6">
                <ImmyFormGroup id="email-input-group" label="Email" label-for="email-input">
                  <ImmyInput
                    id="email-input"
                    v-model="person.emailAddress"
                    data-testid="person-email-input"
                    type="text"
                    required
                    placeholder="Enter Email Address"
                    :disabled="!canManagePerson"
                  />
                </ImmyFormGroup>
              </ImmyCol>
              <ImmyCol lg="6">
                <ImmyFormGroup
                  id="ad-external-id-input-group"
                  label="AD External ID"
                  label-for="ad-external-id-input"
                >
                  <ImmyInput
                    id="ad-external-id-input"
                    v-model="person.azurePrincipalId"
                    data-testid="person-azure-id-input"
                    type="text"
                    :state="getPrincipalIdState"
                    placeholder="Enter AD External Identifier"
                    :disabled="!canManagePerson"
                  />
                  <ImmyFormInvalidFeedBack id="input-live-feedback" :state="getPrincipalIdState">
                    Must be a valid GUID.
                    <a
                      target="_BLANK"
                      rel="noopener"
                      href="https://docs.microsoft.com/en-us/azure/marketplace/find-tenant-object-id#find-user-object-id"
                    >Click here for more information</a>
                  </ImmyFormInvalidFeedBack>
                </ImmyFormGroup>
              </ImmyCol>
            </ImmyRow>
            <ImmyFormGroup v-if="person.id" label="Tags" :disabled="!canManagePerson">
              <AddNewTag :entity-ids="[person.id]" :tag-type="personTagType" @tag:added="onTagAdded" />
              <div class="d-flex align-items-center gap-2 mt-2">
                <TagPill
                  v-for="tag in customTags"
                  :key="tag.id"
                  :model-value="tag"
                  :can-remove="true"
                  @click="onCustomTagRemove(tag)"
                />
              </div>
            </ImmyFormGroup>
            <ImmyButton v-if="person.id" type="submit" variant="primary" data-testid="update-person-btn" :disabled="!canManagePerson">
              Update
            </ImmyButton>
            <ImmyButton v-else type="submit" variant="primary" data-testid="create-person-btn" :disabled="!canManagePerson">
              Create
            </ImmyButton>
          </form>
        </SimplePanel>
      </div>
      <div class="col-6">
        <SimplePanel>
          <template #title>
            Permissions
          </template>
          <div class="d-flex align-items-baseline">
            <FormGroupWithHelp
              v-if="canManageRoles && hasUser"
              label="Assign Role"
              description="The highest level or permission. Admins have the power to access almost everything."
              required
            >
              <!-- TODO: fix role options not being loaded -->
              <GroupRolesDropdown v-model="selectedRoleOptions" :options="selectableRoleOptions" :styles="{ width: '27rem' }" />
            </FormGroupWithHelp>
            <div v-else class="field" lg="12">
              <span v-if="selectedRoleOptions.length > 0"> {{ selectedRoleOptions.map(role => role.text).join(", ") }}</span>
              <span v-else-if="!hasUser"> Grant Immy access to assign roles </span>
              <span v-else> No roles assigned </span>
            </div>
            <div>
              <ImmyButton v-if="canManageRoles && hasUser" variant="outline-warning" class="ml-2" :disabled="selectedRoleOptions?.length === 0" @click="clearRoles">
                Clear
              </ImmyButton>
            </div>
          </div>
          <div v-if="hasUser" class="d-flex mb-2">
            <div class="font-weight-bold">
              Is Expired:
            </div>
            <div class="ml-1 mr-3">
              {{ isExpired ? "Yes" : "No" }}
            </div>
            <div class="font-weight-bold">
              Expiration Date:
            </div>
            <div class="ml-1">
              {{ formatDate(person?.expirationDateUTC) }}
            </div>
          </div>
          <FormGroupWithHelp label="Access Expires in">
            <ImmyRadioGroup
              v-model="expirationTime"
              class="mt-1"
              :options="options"
              :disabled="!canManageUsers"
            />
          </FormGroupWithHelp>
          <ImmyButton v-if="props.id" variant="primary" data-testid="update-person-btn" :disabled="!canManagePerson" @click="onSubmitRolesAndExpiration">
            {{ hasUser ? "Update" : "Grant Immy Access" }}
          </ImmyButton>
          <ImmyButton v-if="hasUser" class="ml-2" variant="danger" data-testid="grant-access-btn" @click="revokeAccess">
            Revoke Access
          </ImmyButton>
        </SimplePanel>
      </div>
    </div>

    <SimplePanel v-if="person?.userId" class="mt-4">
      <template #title>
        <i class="fa-solid fa-user mr-2" /> Activity Log
      </template>
      <AuditTable :user-id="id" />
    </SimplePanel>
  </Page>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { ExpirationTime, IGetSimplePersonResponse, IGetTenantResponse, TagType } from "@/api/backend/generated/contracts";
import { TagOption } from "@/components/AddNewTag.vue";
import { IRoleOption } from "@/components/GroupRolesDropdown.vue";
import { IBreadCrumb, useViewBase } from "@/composables/ViewBase";
import { useTagsStore } from "@/store/pinia";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { usePersonsStore } from "@/store/pinia/persons-store";
import { useRolesStore } from "@/store/pinia/roles-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { useUserRolesStore } from "@/store/pinia/user-roles-store";
import { useUsersStore } from "@/store/pinia/users-store";
import { formatDate, isDateInPast, isGuid } from "@/utils/misc";
import { toast } from "@/utils/toast";

const props = defineProps<{
  id?: number;
}>();

// Stores & Services
const authStore = useAuthStore();
const appAlertsStore = useAppAlertsStore();
const tenantsStore = useTenantsStore();
const personsStore = usePersonsStore();
const tagsStore = useTagsStore();
const rolesStore = useRolesStore();
const usersStore = useUsersStore();
const userRolesStore = useUserRolesStore();
const router = useRouter();

// State
const loading = ref(false);
const selectedRoleOptions = ref<IRoleOption[]>([]);
const selectedTenant = ref<IGetTenantResponse | undefined>(undefined);

// Data
const person = ref<IGetSimplePersonResponse | undefined>(undefined);

const expirationTime = ref<ExpirationTime>(ExpirationTime.Indefinite);
const customTags = ref<TagOption[]>([]);

// Permissions
const canManagePerson = computed(() => authStore.hasClaim("persons:manage:allow"));
const canManageUsers = computed(() => authStore.hasClaim("users:manage:allow"));
const canManageRoles = computed(() => authStore.hasClaim("rbac:manage:allow"));

// Computed
const title = computed(() => (props.id ? person.value?.fullName : "New User"));

const selectableRoleOptions = computed(() => rolesStore.roles.map(role => ({
  text: role.name!,
  value: role.id,
})));

const personTagType = computed(() => TagType.Person);

const isExpired = computed(() => isDateInPast(person.value?.expirationDateUTC));
const hasUser = computed(() => person.value?.userId != null);

const getPrincipalIdState = computed(() => {
  if (person.value?.azurePrincipalId)
    return isGuid(person.value.azurePrincipalId);
  return null;
});

const serialized = computed(() => {
  if (!person.value)
    return undefined;

  return {
    id: person.value.id,
    firstName: person.value.firstName ?? "",
    lastName: person.value.lastName ?? "",
    emailAddress: person.value.emailAddress,
    tenantId: person.value.tenantId,
    azurePrincipalId: person.value.azurePrincipalId,
  };
});

const allTenants = computed(() => {
  if (true)
    return tenantsStore.allTenants;
  return [];
});

const breadcrumbs = computed<IBreadCrumb[]>(() => {
  return [
    { text: "Users", to: { name: "Users & People" } },
    {
      text: props.id ? "Edit User" : "Create New",
    },
  ];
});

const options = [
  { value: ExpirationTime.OneHour, text: "One Hour" },
  { value: ExpirationTime.OneDay, text: "One Day" },
  { value: ExpirationTime.ThreeDays, text: "Three Days" },
  { value: ExpirationTime.Indefinite, text: "Never" },
];

// Functions
useViewBase({
  breadcrumbs,
  loading,
  initialize: async () => {
    if (props.id != null) {
      try {
        loading.value = true;
        // START BACK HERE
        person.value = await personsStore.getById(props.id, {
          useCache: true,
        });

        await rolesStore.loadRoles();

        // Grab current user's roles based on all roles
        if (person.value?.roleIds && person.value?.roleIds.length > 0) {
          selectedRoleOptions.value = rolesStore.roles.filter(role => person.value?.roleIds.includes(role.id)).map(role => ({
            text: role.name!,
            value: role.id,
          }));
        }

        // Grab current user's tags
        customTags.value = tagsStore.all.filter(a => person.value?.personTagIds.includes(a.id));

        // Grab current user's expiration time
        if (person.value?.expirationDateUTC) {
          expirationTime.value = customDaysUntil(person.value?.expirationDateUTC);
        }

        // Grab current all tenants and set selected tenant to current user's tenant
        await tenantsStore.getAllTenants();
        if (person.value?.tenantId) {
          selectedTenant.value = allTenants.value.find(t => t.id === person.value?.tenantId);
        }
      }
      catch (err) {
        appAlertsStore.addAlert({
          text: "An error occurred while getting the user.",
          details: err,
        });
      }
      finally {
        loading.value = false;
      }
    }
  },
});

async function updateRole(userId: number | undefined) {
  if (userId == null) {
    return;
  }
  try {
    const roleIds = selectedRoleOptions.value.filter(role => role.value != null).map(role => +role.value!);
    const hasRolesIds = person.value?.roleIds && person.value?.roleIds.length > 0;

    // If the user already has the roles, don't update them
    if (hasRolesIds && person.value?.roleIds.every(roleId => roleIds.includes(roleId))) {
      return;
    }

    await userRolesStore.setUserRoles(userId, roleIds);
    toast.success(`Role ${roleIds.length === 0 ? "cleared" : "assigned"} successfully`);
  }
  catch {
    toast.showError("An error occurred while assigning role");
  }
}

async function onSubmitRolesAndExpiration() {
  try {
    const res = await updateUser();
    if (res && !hasUser.value) {
      // If the user was created, push to the new user page
      toast.success(`Successfully created user.`);
      location.reload();
    }
    else {
      // If the user was updated, continue to update the roles
      await updateRole(person.value?.userId);
      toast.success(`Successfully updated user.`);
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: `An error occurred while updating the person.`,
      details: err,
    });
  }
}

async function onPersonSubmit() {
  try {
    if (!person.value || !serialized.value)
      return;

    // validate principal id
    if (person.value.azurePrincipalId && !isGuid(person.value.azurePrincipalId))
      return;

    if (props.id) {
      await personsStore.update(person.value.id, serialized.value);
    }
    else {
      const res = await personsStore.create(serialized.value);
      await router.push({
        name: "Edit User2",
        params: { id: res.id },
      });
      return;
    }
    toast.success(`Successfully ${props.id ? "saved" : "created"} person.`);
    await router.push({ name: "Edit User2", params: { id: person.value.id } });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: `An error occurred while ${props.id ? "saving" : "creating"} the person.`,
      details: err,
    });
  }
}

async function clearRoles() {
  selectedRoleOptions.value = [];
}

async function revokeAccess() {
  if (!person.value?.userId)
    return;

  const { value } = await Swal.fire({
    title: "Are you sure?",
    html: `This will remove access and all roles assigned to this user.`,
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
    reverseButtons: true,
  });
  if (!value)
    return;

  try {
    await usersStore.remove(person.value.userId);
    toast.success("Access revoked");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while revoking access.",
      details: err,
    });
  }
  finally {
    location.reload();
  }
}

async function onTagAdded(tag: TagOption) {
  if (customTags.value.some(t => t.id === tag.id))
    return;
  customTags.value.push(tag);
}

async function onCustomTagRemove(tag: TagOption) {
  try {
    if (!person.value?.id)
      return;

    await personsStore.removeTag(person.value.id, tag.id);
    const ind = customTags.value.indexOf(tag);
    customTags.value.splice(ind, 1);
    toast.success("Tag removed");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while removing the tag from the person",
      details: err,
    });
  }
}

async function updateUser() {
  const personExpirationDate = customDaysUntil(person.value?.expirationDateUTC ?? null);
  // This creates a user through granting access if needed, but prevents a user from being updated if the expiration date is the same
  if (person.value == null || (person.value?.userId != null && personExpirationDate === expirationTime.value)) {
    return;
  }

  try {
    const res = await personsStore.grantAccessRbac([person.value.id], expirationTime.value);
    return res.success;
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while saving the user.",
      details: err,
    });
  }
}

function customDaysUntil(targetDate: string | null): ExpirationTime {
  if (targetDate == null) {
    return ExpirationTime.Indefinite;
  }

  const now = new Date();
  const target = new Date(targetDate);
  const diffHours = (target.getTime() - now.getTime()) / (1000 * 60 * 60); // Convert milliseconds to hours

  if (diffHours > 0 && diffHours < 1) {
    return ExpirationTime.OneHour;
  }
  else if (diffHours >= 1 && diffHours < 48) {
    return ExpirationTime.OneDay;
  }
  else {
    return ExpirationTime.ThreeDays;
  }
}

// Watchers
watch(selectedTenant, (val) => {
  if (person.value)
    person.value.tenantId = val?.id ?? 0;
});
</script>

<style scoped lang="scss">
.field {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  &:last-child {
    margin-bottom: 0;
  }
  span:first-child {
    margin-bottom: 0.5rem;
  }
}

.person-form {
  max-width: 600px;
}
</style>
