<template>
  <Page no-bg>
    <template #header>
      <page-header title="Media">
        <ImmyRadioGroup
          v-if="common.isImmense && common.isAdmin"
          v-model="selectedDatabaseType"
          class="ml-3"
          buttons
          button-variant="outline-primary"
          :options="filterOptions"
        />
        <template #primary-action>
          <ImmyDropdown id="media-actions" variant="primary" :disabled="!hasSelectedMedia">
            <template #button-content>
              Batch Actions
            </template>
            <ImmyDropdownItem :disabled="batchDeleting" @click="batchDeleteSelected">
              <i class="text-danger fal fa-trash-can" />
              Delete Selected
              <i v-if="batchDeleting" class="fa fa-spin fa-spinner" />
            </ImmyDropdownItem>
          </ImmyDropdown>
        </template>
      </page-header>
      <ImmyMediaUploader
        class="mb-3"
        :show-uploader="true"
        :database-type="uploaderDatabaseType"
        img-class="image-128"
        @update:model-value="onUploaded"
      />
    </template>

    <immy-dx-data-grid
      id="media-list"
      ref="mediaList"
      class="mt-3"
      :data-source="dataSource"
      :columns="columns"
      :hide-column-chooser="true"
      :column-auto-width="true"
      :show-header-filter="false"
      :export-enabled="false"
      :store-state="false"
      show-refresh
      :selection="true"
      :show-load-panel="loading"
      @on-selection-changed="onSelectionChanged"
    >
      <template #file="{ data }">
        <div class="d-flex align-items-center">
          <media-image :media="data" img-class="image-50" class="mr-2" />
          <span>
            <p class="m-0 fw-600">{{ data.name }}</p>
            <p
              v-if="isNameDifferentThanFileName(data)"
              class="m-0 text-muted"
              label="File Name"
              title="File Name"
            >
              {{ data.fileName }}
            </p>
            <p class="m-0 fs-85">{{ getDatabaseTypeText(data.databaseType, "Media") }}</p>
          </span>
        </div>
      </template>
      <template #audit="{ data }">
        <AuditableColumnData
          :updated-by="(data as any).updatedBy"
          :updated-date-utc="data.updatedDateUTC"
          :created-date-utc="data.createdDateUTC"
        />
      </template>
      <template #actions="{ data }">
        <div class="table-actions">
          <ImmyButton
            :to="{
              name: 'Edit Media',
              params: { mediaId: data.id },
              query: { databaseType: data.databaseType },
            }"
            size="sm"
            variant="secondary"
          >
            <template v-if="canEditMedia(data)">
              <i class="fal fa-edit" />
            </template>
            <template v-else>
              <i class="fal fa-eye" />
            </template>
          </ImmyButton>
          <load-button
            v-show="canSeeMedia()"
            :handler="() => onDownloadMedia(data)"
            size="sm"
            variant="secondary"
          >
            Download
          </load-button>
          <load-button
            v-show="canDeleteMedia(data)"
            :handler="() => onDeleteMedia(data)"
            size="sm"
            variant="danger"
          >
            <i class="line-height-1-5 fal fa-trash-can" />
          </load-button>
        </div>
      </template>
    </immy-dx-data-grid>
  </Page>
</template>

<script lang="ts" setup>
import { Column } from "devextreme/ui/data_grid";
import Swal from "sweetalert2";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { DatabaseType, IGlobalMediaResponse, ILocalMediaResponse } from "@/api/backend/generated/contracts";
import { mediaApi } from "@/api/backend/v1";
import { useCommon } from "@/composables/Common";
import {
  canDeleteMedia,
  canEditMedia,
  canSeeMedia,
  isNameDifferentThanFileName,
} from "@/composables/MediaActions";
import { useViewBase } from "@/composables/ViewBase";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { getDatabaseTypeFilterOptions, getDatabaseTypeText } from "@/utils/misc";
import { toast } from "@/utils/toast";

const MEDIA_TABLE_COLUMNS: Column[] = [
  {
    cellTemplate: "actions",
    caption: "Actions",
    width: "auto",
    minWidth: 120,
  },
  {
    cellTemplate: "file",
    dataField: "name",
    caption: "File",
  },
  {
    dataField: "packageHash",
    caption: "MD5 Hash",
    width: "auto",
  },
  {
    dataField: "updatedDateUTC",
    dataType: "datetime",
    caption: "Last Updated",
    cellTemplate: "audit",
    sortOrder: "desc",
    width: "auto",
  },
];

const appAlertsStore = useAppAlertsStore();
const common = useCommon();
const router = useRouter();
useViewBase({
  breadcrumbs: computed(() => []),
});

const loading = ref(false);
const selectedDatabaseType = ref<DatabaseType | null>();
const selectedMedia = ref<(ILocalMediaResponse | IGlobalMediaResponse)[]>([]);
const batchDeleting = ref(false);
const localMedia = ref<ILocalMediaResponse[]>([]);
const globalMedia = ref<IGlobalMediaResponse[]>([]);

const filterOptions = getDatabaseTypeFilterOptions();

const hasSelectedMedia = computed(() => selectedMedia.value.length);
const columns = MEDIA_TABLE_COLUMNS;

const uploaderDatabaseType = computed(() => {
  // only immense admins can upload to both local and global
  if (common.isImmense && common.isAdmin)
    return selectedDatabaseType.value ?? DatabaseType.Local;
  return DatabaseType.Local;
});

const dataSource = computed(() => {
  if (selectedDatabaseType.value === DatabaseType.Local)
    return localMedia.value;
  else if (selectedDatabaseType.value === DatabaseType.Global)
    return globalMedia.value;
  else return [...localMedia.value, ...globalMedia.value];
});

onMounted(async () => {
  await loadMedia();
});

const mediaList = ref();

async function batchDeleteSelected() {
  try {
    batchDeleting.value = true;
    const reqs = selectedMedia.value.map(a => deleteMedia(a));
    await Promise.all(reqs);
    toast.success("Successfully deleted media");
    mediaList.value?.refresh();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to batch delete media",
      details: err,
    });
  }
  finally {
    batchDeleting.value = false;
  }
}

function onSelectionChanged(selection: (ILocalMediaResponse | IGlobalMediaResponse)[]) {
  selectedMedia.value = selection;
}

function onUploaded(media: ILocalMediaResponse | IGlobalMediaResponse) {
  router.push({
    name: "Edit Media",
    params: { mediaId: media.id },
    query: { databaseType: media.databaseType },
  });
}

async function onDownloadMedia(media: ILocalMediaResponse | IGlobalMediaResponse) {
  try {
    let url;
    if (media.databaseType === DatabaseType.Local)
      url = await mediaApi.getLocalDownloadUrl(media.id);
    else if (media.databaseType === DatabaseType.Global)
      url = await mediaApi.getGlobalDownloadUrl(media.id);
    else
      return;

    if (url)
      window.open(url, "_self");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to download the file.",
      details: err,
    });
  }
}

async function onDeleteMedia(media: ILocalMediaResponse | IGlobalMediaResponse) {
  try {
    const { value } = await Swal.fire({
      title: "Are you sure?",
      text: "This media will be permanently deleted.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    });
    if (value) {
      await deleteMedia(media);
      toast.success("Success");
      mediaList.value?.refresh();
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to delete media",
      details: err,
    });
  }
}

async function deleteMedia(media: ILocalMediaResponse | IGlobalMediaResponse) {
  if (media.databaseType === DatabaseType.Local)
    await mediaApi.deleteLocal(media.id);
  else await mediaApi.deleteGlobal(media.id);
}

async function loadMedia() {
  try {
    loading.value = true;
    const [local, global] = await Promise.all([mediaApi.getLocal(), mediaApi.getGlobal()]);
    localMedia.value = (await (local as any)._loadFunc()) ?? [];
    globalMedia.value = (await (global as any)._loadFunc()) ?? [];
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to retrieve media",
      details: err,
    });
  }
  finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped></style>
