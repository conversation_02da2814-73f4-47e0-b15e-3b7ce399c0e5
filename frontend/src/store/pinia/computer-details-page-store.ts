// Pinia Store
import { defineStore } from "pinia";
import { computed, ref } from "vue";
import { InventoryKeys } from "@/api/backend/generated/contracts";
import {
  IGetComputerResponse,
  IGetMaintenanceSessionResponse,
} from "@/api/backend/generated/responses";
import { ISession, IUpdateMaintenanceSessionResource } from "@/api/backend/generated/signalr";
import { computersApi } from "@/api/backend/v1";
import { formatDate, getHumanReadableDate, isDateInPast } from "@/utils/misc";

export const useComputerDetailsPageStore = defineStore("computerDetailsPage", () => {
  const detectionOutdated = ref(false);
  const runningSession = ref<
    IGetMaintenanceSessionResponse | IUpdateMaintenanceSessionResource | ISession | null
  >(null);
  const sessionEnqueued = ref(false);
  const computer = ref<IGetComputerResponse | null>(null);
  const isImmyBotRemoteControlFeatureEnabled = ref(false);
  const updatingInventoryKeys = ref<string[]>([]);
  const inventoryStarted = ref(false);
  const inventoryFinished = ref(false);
  const lastLogActivityName = ref<string>("");

  const runningSessionId = computed(() => {
    return runningSession.value?.id;
  });
  const hasRunningSession = computed(() => {
    return !!runningSession.value || sessionEnqueued.value;
  });
  const inventorying = computed(() => {
    return updatingInventoryKeys.value.length > 0;
  });

  const computerDevLabVmClaimIsExpired = computed(() => {
    if (computer.value == null)
      return false;
    return isDateInPast(computer.value.devLabVmClaimExpirationDateUtc);
  });

  const computerDevLabVmClaimExpirationDateFormattedLocal = computed(() => {
    if (computer.value == null)
      return undefined;
    return formatDate(computer.value.devLabVmClaimExpirationDateUtc);
  });

  const computerDevLabVmClaimExpirationDateFormattedSentence = computed(() => {
    if (computer.value == null)
      return undefined;
    return getHumanReadableDate(computer.value.devLabVmClaimExpirationDateUtc);
  });

  const setDetectionOutdated = (val: boolean) => {
    detectionOutdated.value = val;
  };

  const setComputerName = (val: string) => {
    if (computer.value)
      computer.value.computerName = val;
  };

  const setRunningSession = (
    val: IGetMaintenanceSessionResponse | IUpdateMaintenanceSessionResource | ISession | null,
  ) => {
    runningSession.value = val;
    if (val == null && inventoryFinished.value) {
      inventoryStarted.value = false;
      inventoryFinished.value = false;
    }
  };

  const setSessionEnqueued = (val: boolean) => {
    sessionEnqueued.value = val;
  };

  const resetState = () => {
    detectionOutdated.value = false;
    runningSession.value = null;
    sessionEnqueued.value = false;
    computer.value = null;
    updatingInventoryKeys.value = [];
    inventoryStarted.value = false;
    inventoryFinished.value = false;
  };

  const setComputer = (val: IGetComputerResponse) => {
    computer.value = val;
  };

  const updateComputer = (val: Partial<IGetComputerResponse>) => {
    computer.value = Object.assign({}, computer.value, val);
  };

  const setInventoryStarted = () => {
    inventoryStarted.value = true;
  };

  const setInventoryKeyUpdating = (val: string) => {
    updatingInventoryKeys.value = [...updatingInventoryKeys.value.filter(k => k !== val), val];
  };

  const unsetInventoryKeyUpdating = (val: string) => {
    updatingInventoryKeys.value = updatingInventoryKeys.value.filter(k => k !== val);

    if (inventoryStarted.value && !inventorying.value) {
      inventoryStarted.value = false;
      if (hasRunningSession.value)
        inventoryFinished.value = true;
    }
  };

  const refreshComputerInventoryKey = async (inventoryKey: string) => {
    if (!computer.value)
      return;

    const result = await computersApi.getInventoryScriptResult(computer.value?.id, inventoryKey);
    const currentInventory = computer.value?.inventory;
    const currentMeta = (currentInventory as any)?.[InventoryKeys.InventoryTaskMetaKey];
    const newMeta = Object.assign({}, currentMeta, result[InventoryKeys.InventoryTaskMetaKey]);
    computer.value.inventory = Object.assign({}, currentInventory, result, {
      [InventoryKeys.InventoryTaskMetaKey]: newMeta,
    });
  };

  const updateOnlineStatusForAgent = async ({
    agentId,
    isOnline,
  }: {
    agentId: number;
    isOnline: boolean;
  }) => {
    if (!computer.value)
      return;

    const agent = computer.value.agents.find(a => a.id == agentId);
    if (agent)
      agent.isOnline = isOnline;
  };

  const updatePrimaryUser = async (primaryPersonId: number | undefined) => {
    if (!computer.value)
      return;
    const updatedPerson = await computersApi.updatePrimaryPerson(computer.value.id, {
      primaryPersonId,
    });
    computer.value = Object.assign({}, computer.value, {
      primaryPerson: updatedPerson,
      primaryPersonId: updatedPerson?.id,
    });
  };

  const updatelastLogActivityName = (name: string) => {
    lastLogActivityName.value = name;
  };

  return {
    isImmyBotRemoteControlFeatureEnabled,
    detectionOutdated,
    runningSession,
    sessionEnqueued,
    computer,
    updatingInventoryKeys,
    inventoryStarted,
    inventoryFinished,
    runningSessionId,
    setDetectionOutdated,
    setComputerName,
    setRunningSession,
    setSessionEnqueued,
    resetState,
    setComputer,
    setInventoryStarted,
    setInventoryKeyUpdating,
    unsetInventoryKeyUpdating,
    refreshComputerInventoryKey,
    updateOnlineStatusForAgent,
    updatePrimaryUser,
    updateComputer,
    hasRunningSession,
    inventorying,
    computerDevLabVmClaimIsExpired,
    computerDevLabVmClaimExpirationDateFormattedLocal,
    computerDevLabVmClaimExpirationDateFormattedSentence,
    lastLogActivityName,
    updatelastLogActivityName,
  };
});
