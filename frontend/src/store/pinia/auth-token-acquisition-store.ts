import { computedAsync } from "@vueuse/core";
import { defineStore } from "pinia";
import { ref, watch, WatchStopHandle } from "vue";
import {
  IMissingAccessTokenDetails,
  IOauth2AccessToken,
  IOauthConsentData,
} from "@/api/backend/generated/interfaces";
import { useOauthTokenAcquisition } from "@/composables/OauthTokenAcquisition";
import { useAuthStore } from "./auth-store";
import { useTenantsStore } from "./tenants-store";

export interface IPermissionAcquisitionOptions {
  mainText?: string;
  secondaryText?: string;
  allowIndefiniteAccessType?: boolean;
  tenantIdBeingAccessed?: number;
  allowUserToSelectExistingToken?: boolean;
}

const msAuthEndpoint = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
const msTokenEndpoint = "https://login.microsoftonline.com/organizations/oauth2/v2.0/token";

export const useAuthTokenAcquisitionStore = defineStore("auth-token-acquisition-store", () => {
  const authStore = useAuthStore();
  const tenantsStore = useTenantsStore();

  const acquisitionFn = ref<
    ReturnType<typeof useOauthTokenAcquisition>["triggerAcquisition"] | null
  >(null);
  const cancelAcquisitionFn = ref<
    ReturnType<typeof useOauthTokenAcquisition>["cancelAcquisition"] | null
  >(null);

  const manuallySelectedToken = ref<IOauth2AccessToken | null>(null);
  const consentData = ref<IOauthConsentData | null>(null);
  const isModalShown = ref<boolean>(false);
  const hasBeenShown = ref<boolean>(false);
  const selectedAccessType = ref<"oneTime" | "indefinite">("oneTime");
  const successWatcher = ref<WatchStopHandle | null>(null);
  const failureWatcher = ref<WatchStopHandle | null>(null);
  const acquisitionOptions = ref<IPermissionAcquisitionOptions | null>(null);
  const triggeredAcquisition = ref<boolean>(false);
  const tenantNameBeingAccessed = computedAsync(async () => {
    const tenantId = acquisitionOptions.value?.tenantIdBeingAccessed;
    if (tenantId == null)
      return null;
    const tenant = await tenantsStore.getTenantById(tenantId);
    return tenant?.name ?? `tenant with id=${tenantId}`;
  }, null);

  function setVisibility(val: boolean) {
    isModalShown.value = val;
    // When user closes the modal, cancel the acquisition
    if (!val)
      cancelAcquisitionFn.value?.();
  }

  /**
   * Triggers the acquisition flow, which will show the consent page to the user
   */
  async function triggerAcquisition() {
    if (acquisitionFn.value == null)
      throw new Error("No acquisition function is registered");

    if (authStore.defaultAppRegAppId == null)
      throw new Error("Backend registration app id is missing");

    if (consentData.value == null)
      throw new Error("Consent data is missing");

    const allowSilentRefresh
      = (acquisitionOptions.value?.allowIndefiniteAccessType ?? false)
        && selectedAccessType.value === "indefinite";

    await acquisitionFn.value(consentData.value, allowSilentRefresh);
    triggeredAcquisition.value = true;
  }
  /**
   * This alternative to the triggerAcquisition function allows the user to select
   * an existing token instead of consenting to a new one
   * @param token Token value selected by the user from the list of existing tokens
   */
  function setSelectedToken(token: IOauth2AccessToken) {
    manuallySelectedToken.value = token;
  }

  /**
   * Takes a backend-generated error object and opens the auth token acquisition modal to allow the
   * user to trigger the acquisition flow to obtain the necessary permissions
   * @param missingAccessToken The error returned from the backend when a token is missing
   * @param options Options to configure how the acquisition flow will be shown to the user
   * @returns A Promise that will resolve with the acquired (or user-selected) access token
   */
  function handleMissingAccessToken(
    missingAccessToken: IMissingAccessTokenDetails,
    options?: IPermissionAcquisitionOptions,
  ) {
    if (authStore.defaultAppRegAppId == null)
      throw new Error("Backend registration app id is missing");

    const c: IOauthConsentData = {
      clientId: authStore.defaultAppRegAppId,
      authorizationEndpoint: msAuthEndpoint,
      tokenEndpoint: msTokenEndpoint,
      scopes: missingAccessToken.requiredScopes,
      extraQueryParameters: {},
    };

    const t = missingAccessToken.tenantId;
    if (t != null) {
      c.authorizationEndpoint = c.authorizationEndpoint.replace("common", t);
      c.tokenEndpoint = c.tokenEndpoint.replace("organizations", t);
      const tenantsStore = useTenantsStore();
      (options ??= {}).tenantIdBeingAccessed = tenantsStore.azureTenantsHierarchy.get(t)?.[0]?.id;
    }

    return handleAcquisition(c, options);
  }

  /**
   * Takes a consent data object and opens the auth token acquisition modal to allow the user to
   * trigger the acquisition flow to obtain the permissions defined in the consent data
   * @param oauthConsentData The consent data to use for the acquisition flow
   * @param options Options to configure how the acquisition flow will be shown to the user
   * @returns A Promise that will resolve with the acquired (or user-selected) access token
   */
  async function handleAcquisition(
    oauthConsentData: IOauthConsentData,
    options?: IPermissionAcquisitionOptions,
  ) {
    if (acquisitionFn.value != null)
      throw new Error("Only one access token acquisition can be active at a time");

    const oauthTokenAcquisition = useOauthTokenAcquisition();

    try {
      acquisitionFn.value = oauthTokenAcquisition.triggerAcquisition;
      cancelAcquisitionFn.value = oauthTokenAcquisition.cancelAcquisition;

      consentData.value = oauthConsentData;
      acquisitionOptions.value = options ?? null;

      // Show the acquisition modal
      isModalShown.value = true;
      return await new Promise<IOauth2AccessToken>((resolve, reject) => {
        successWatcher.value = watch(
          () => [oauthTokenAcquisition.success.value, manuallySelectedToken.value],
          ([val, manual]) => {
            if (val != null)
              resolve(val);

            if (manual != null)
              resolve(manual);
          },
        );
        failureWatcher.value = watch(oauthTokenAcquisition.failure, (val) => {
          if (val != null)
            reject(val);
        });
      });
    }
    finally {
      clearState();
      oauthTokenAcquisition.dispose();
    }
  }

  function clearState() {
    successWatcher.value?.();
    successWatcher.value = null;
    failureWatcher.value?.();
    failureWatcher.value = null;
    acquisitionOptions.value = null;
    hasBeenShown.value = false;
    isModalShown.value = false;
    selectedAccessType.value = "oneTime";
    consentData.value = null;
    acquisitionFn.value = null;
    cancelAcquisitionFn.value = null;
    manuallySelectedToken.value = null;
    triggeredAcquisition.value = false;
  }

  return {
    isModalShown,
    hasBeenShown,
    acquisitionOptions,
    selectedAccessType,
    consentData,
    tenantNameBeingAccessed,
    triggeredAcquisition,
    setVisibility,
    triggerAcquisition,
    handleMissingAccessToken,
    handleAcquisition,
    setSelectedToken,
  };
});
