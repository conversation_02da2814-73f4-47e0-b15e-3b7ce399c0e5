import { defineStore } from "pinia";
import { computed, ref } from "vue";
import { PermissionCategory } from "@/api/backend/generated/enums";
import { IPermissionMetadata, ISubjectMetadata } from "@/api/backend/generated/interfaces";
import { useRolesStore } from "@/store/pinia/roles-store";

export function useRoleManagementStore(id: string) {
  return defineStore(`role-management/${id}`, () => {
    const rolesStore = useRolesStore();

    const allViewPermissions = computed(() => {
      return rolesStore.subjects.reduce((agg, cur) => {
        const viewPermissions = cur.permissions.filter(a => a.category === PermissionCategory.View);
        agg.push(...viewPermissions);
        return agg;
      }, [] as IPermissionMetadata[]);
    });

    const grantedPermissionIds = ref<Set<string>>(new Set());

    function addGrantedPermission(permissionId: string) {
      grantedPermissionIds.value.add(permissionId);
    }

    function removeGrantedPermission(permissionId: string) {
      grantedPermissionIds.value.delete(permissionId);
    }

    function managePermission(permissionId: string) {
      if (grantedPermissionIds.value.has(permissionId)) {
        removeGrantedPermission(permissionId);
      }
      else {
        addGrantedPermission(permissionId);
      }
    }

    function setGrantedPermissions(permissions: string[]) {
      grantedPermissionIds.value = new Set(permissions);
    }

    function setGrantedCanViewPermissions(subjects: ISubjectMetadata[]) {
      // clear all "view" permissions from granted permissions
      allViewPermissions.value.forEach(permission => removeGrantedPermission(permission.allowClaim));

      subjects.forEach((subject) => {
        subject.permissions.forEach((permission) => {
          if (permission.category === PermissionCategory.View) {
            addGrantedPermission(permission.allowClaim);
          }
        });
      });
    }

    function clearGrantedPermissions() {
      grantedPermissionIds.value = new Set<string>();
    }

    const totalGrantedPermissions = computed(() => grantedPermissionIds.value.size);
    const grantedPermissions = computed(() => [...grantedPermissionIds.value]);

    return {
      grantedPermissionIds,
      grantedPermissions,
      totalGrantedPermissions,
      addGrantedPermission,
      removeGrantedPermission,
      managePermission,
      setGrantedCanViewPermissions,
      clearGrantedPermissions,
      setGrantedPermissions,
      id,
    };
  })();
}
