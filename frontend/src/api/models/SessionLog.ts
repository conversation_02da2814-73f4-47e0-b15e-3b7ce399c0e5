import {
  DatabaseType,
  IGetMaintenanceActionResponse,
  IGetMaintenanceSessionResponse,
  IGetMaintenanceSessionStageResponse,
  IGetSessionLogResponse,
  MaintenanceActionStatus,
  ScriptLanguage,
  SessionLogType,
} from "../backend/generated/contracts";

export default class SessionLog implements IGetSessionLogResponse {
  constructor(model: IGetSessionLogResponse) {
    this.id = model.id;
    this.progressCorrelationId = model.progressCorrelationId;
    this.sessionPhaseId = model.sessionPhaseId;
    this.message = model.message;
    this.maintenanceSessionId = model.maintenanceSessionId;
    this.maintenanceSessionStageId = model.maintenanceSessionStageId;
    this.maintenanceActionId = model.maintenanceActionId;
    this.maintenanceActionStatus = model.maintenanceActionStatus;
    this.scriptId = model.scriptId;
    this.script = model.script;
    this.scriptOutput = model.scriptOutput;
    this.scriptType = model.scriptType;
    this.scriptLanguage = model.scriptLanguage;
    this.scriptParameters = model.scriptParameters;
    this.paramBlockParameters = model.paramBlockParameters;
    this.time = model.time;
    this.updatedTime = model.updatedTime;
    this.sessionLogType = model.sessionLogType;
    this.isPrimary = model.isPrimary;
    this.progressPercentComplete = model.progressPercentComplete;
    this.progressActivity = model.progressActivity;
    this.progressStatus = model.progressStatus;
    this.progressSecondsRemaining = model.progressSecondsRemaining;
    this.progressCurrentOperation = model.progressCurrentOperation;
    this.progressCompleted = model.progressCompleted;
    this.maintenanceSession = model.maintenanceSession;
    this.maintenanceSessionStage = model.maintenanceSessionStage;
    this.maintenanceAction = model.maintenanceAction;
  }

  maintenanceActionStatus?: MaintenanceActionStatus | undefined;
  scriptId?: number | undefined;
  script?: string | undefined;
  scriptOutput?: string | undefined;
  scriptType?: DatabaseType | undefined;
  scriptLanguage?: ScriptLanguage | undefined;
  scriptParameters: { [key: string]: unknown };
  paramBlockParameters: { [key: string]: unknown };
  updatedTime?: string | undefined;
  sessionLogType: SessionLogType;
  progressPercentComplete?: number | undefined;
  progressActivity?: string | undefined;
  progressStatus?: string | undefined;
  progressSecondsRemaining?: number | undefined;
  progressCurrentOperation?: string | undefined;
  progressCompleted: boolean;
  id: number;
  message?: string | undefined;
  maintenanceSessionId: number;
  maintenanceSessionStageId?: number | undefined;
  maintenanceActionId?: number | undefined;
  time: string;
  isPrimary: boolean;
  progressCorrelationId: string | undefined;
  sessionPhaseId: number | undefined;
  maintenanceSession?: IGetMaintenanceSessionResponse;
  maintenanceSessionStage?: IGetMaintenanceSessionStageResponse;
  maintenanceAction?: IGetMaintenanceActionResponse;
}
