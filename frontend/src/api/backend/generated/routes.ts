//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

export abstract class ApiRoutes
{
  public static Root: string = `api`;
  public static Version: string = `v1`;
  public static Base: string = `api/v1`;
}
export abstract class UserAuthRoutes
{
  public static Base: string = `auth`;
  public static Me: string = `auth/me`;
  public static Refresh: string = `auth/refresh`;
  public static Login: string = `auth/login`;
  public static LoginCallback: string = `auth/login-callback`;
  public static Logout: string = `auth/logout`;
  public static LogoutCallback: string = `auth/logout-callback`;
}
export abstract class AuthApiRoutes
{
  public static Get: string = `api/v1/auth`;
  public static UpdateAzureTenantAuthDetails: string = `api/v1/auth/update-azure-tenant-auth-details`;
  public static DeleteAzureTenantAuthDetails: string = `api/v1/auth/delete-azure-tenant-auth-details`;
  public static GetAzureTenantAuthDetails: string = `api/v1/auth/get-azure-tenant-auth-details/{azureTenantPrincipalId}`;
  public static GetImmybotIPAddresses: string = `api/v1/auth/get-ip-addresses`;
  public static RequestAccess: string = `api/v1/auth/request-access`;
}
export abstract class AzureErrorsApiRoutes
{
  public static GetAllDx: string = `api/v1/azure-errors/dx`;
  public static GetForTenantDx: string = `api/v1/azure-errors/for-tenant/{tenantPrincipalId}/dx`;
}
export abstract class AuditsApiRoutes
{
  public static GetLocalDx: string = `api/v1/audits/local/dx`;
  public static GetGlobalDx: string = `api/v1/audits/global/dx`;
}
export abstract class OauthApiRoutes
{
  public static BeginAuthCodeFlow: string = `api/v1/oauth/begin-auth-code-flow`;
  public static FailAuthCodeFlow: string = `api/v1/oauth/fail-auth-code-flow`;
  public static FinishAuthCodeFlow: string = `api/v1/oauth/finish-auth-code-flow`;
  public static ListOauthAccessTokens: string = `api/v1/oauth/oauth-access-tokens`;
  public static DeleteOauthAccessToken: string = `api/v1/oauth/oauth-access-tokens/{id}`;
  public static GetOauthAccessToken: string = `api/v1/oauth/oauth-access-tokens/{id}/{accessTokenId}`;
  public static RefreshOauthAccessToken: string = `api/v1/oauth/oauth-access-tokens/{id}/refresh`;
}
export abstract class BillingApiRoutes
{
  public static CreateCustomerPortalSession: string = `api/v1/billing/create-customer-portal-session`;
  public static GetSubscriptionDetails: string = `api/v1/billing/subscription-details`;
  public static GetProductCatalogItems: string = `api/v1/billing/product-catalog-items`;
  public static GetBillingPlatformDetails: string = `api/v1/billing/billing-platform-details`;
}
export abstract class ComputerApiRoutes
{
  public static GetAll: string = `api/v1/computers`;
  public static GetAllPaged: string = `api/v1/computers/paged`;
  public static Dx: string = `api/v1/computers/dx`;
  public static Get: string = `api/v1/computers/{computerId}`;
  public static SetExcludedFromUserAffinity: string = `api/v1/computers/{computerId}/exclude-from-user-affinity`;
  public static SetExcludedFromUserAffinityBatch: string = `api/v1/computers/set-excluded-from-user-affinity`;
  public static ExportUserAffinities: string = `api/v1/computers/user-affinities/export`;
  public static GetUserAffinitiesDx: string = `api/v1/computers/user-affinities`;
  public static GetDeviceUpdateFormData: string = `api/v1/computers/{computerId}/device-update-form-data`;
  public static GetInventoryScriptResult: string = `api/v1/computers/{computerId}/inventory-script-results/{inventoryKey}`;
  public static AllDetectedComputerSoftwareDx: string = `api/v1/computers/{computerId}/detected-computer-software`;
  public static GetOnboarding: string = `api/v1/computers/onboarding`;
  public static TriggerSyncJob: string = `api/v1/computers/sync`;
  public static GetComputerOnlineStatus: string = `api/v1/computers/{computerId}/status`;
  public static UpdatePrimaryPerson: string = `api/v1/computers/{computerId}/update-primary-person`;
  public static UpdateAdditionalPersons: string = `api/v1/computers/{computerId}/update-additional-persons`;
  public static Update: string = `api/v1/computers/{computerId}`;
  public static SetToNeedsOnboarding: string = `api/v1/computers/{computerId}/set-to-needs-onboarding`;
  public static SkipOnboarding: string = `api/v1/computers/skip-onboarding`;
  public static GetScreenShareUrl: string = `api/v1/computers/{computerId}/provider-links/{providerLinkId}/screen-share-url`;
  public static ReinventoryComputer: string = `api/v1/computers/{computerId}/reinventory`;
  public static GetMyComputers: string = `api/v1/computers/my`;
  public static SearchAllInventorySoftwareByName: string = `api/v1/computers/all-inventory-software/search-by-name`;
  public static SearchInventorySoftwareByName: string = `api/v1/computers/inventory-software/search-by-name`;
  public static SearchInventorySoftwareByUpgradeCode: string = `api/v1/computers/inventory-software/search-by-upgrade-code`;
  public static GetInventorySoftwareRegexMatchCount: string = `api/v1/computers/inventory-software-regex-match-count`;
  public static DeleteComputers: string = `api/v1/computers/bulk-delete`;
  public static PermanentlyDeleteComputers: string = `api/v1/computers/bulk-delete/permanent`;
  public static GetEphemeralAgentCircuitBreaker: string = `api/v1/computers/{computerId}/ephemeral-agent-circuit-breaker`;
  public static ResetEphemeralAgentCircuitBreaker: string = `api/v1/computers/{computerId}/ephemeral-agent-circuit-breaker/reset`;
  public static GetComputerEvents: string = `api/v1/computers/{computerId}/events`;
  public static ResolveOnboardingOverridableTargetAssignments: string = `api/v1/computers/{computerId}/resolve-onboarding-overridable-target-assignments`;
  public static GetParentTenantInfo: string = `api/v1/computers/{computerId}/parent-tenant-info`;
  public static AddTags: string = `api/v1/computers/add-tags`;
  public static RemoveTags: string = `api/v1/computers/remove-tags`;
  public static ChangeTenant: string = `api/v1/computers/change-tenant`;
  public static ExcludeFromMaintenance: string = `api/v1/computers/{computerId}/exclude-from-maintenance`;
  public static UpdateNotes: string = `api/v1/computers/{computerId}/notes`;
  public static GetEphemeralAgent: string = `api/v1/computers/{computerId}/ephemeral-agent`;
  public static DeleteEphemeralAgent: string = `api/v1/computers/{computerId}/ephemeral-agent`;
  public static ExportComputers: string = `api/v1/computers/export`;
  public static ComputerInventoryDx: string = `api/v1/computers/inventory`;
  public static ExportComputerInventory: string = `api/v1/computers/inventory/export`;
  public static ComputerAgentStatusReport: string = `api/v1/computers/agent-status`;
  public static LaunchEphemeralAgent: string = `api/v1/computers/{computerId}/launch-ephemeral-agent`;
  public static LoadRegistryKeys: string = `api/v1/computers/{computerId}/registry/keys`;
  public static LoadRegistryKeyValues: string = `api/v1/computers/{computerId}/registry/values`;
  public static RestoreComputers: string = `api/v1/computers/restore`;
}
export abstract class PreferenceApiRoutes
{
  public static Get: string = `api/v1/preferences`;
  public static GetTenantPreferences: string = `api/v1/preferences/tenants/{tenantId}`;
  public static UpdateTenantPreferences: string = `api/v1/preferences/tenants/{tenantId}`;
  public static UpdateAppPreferences: string = `api/v1/preferences/application`;
  public static UpdateUserPreferences: string = `api/v1/preferences/my`;
}
export abstract class ScriptApiRoutes
{
  public static Base: string = `api/v1/scripts`;
  public static DxGetAll: string = `api/v1/scripts/dx`;
  public static Search: string = `api/v1/scripts/search`;
  public static GetAllLocalScripts: string = `api/v1/scripts/local`;
  public static GetAllGlobalScripts: string = `api/v1/scripts/global`;
  public static GetAllLocalScriptNames: string = `api/v1/scripts/local/names`;
  public static GetAllGlobalScriptNames: string = `api/v1/scripts/global/names`;
  public static GetLocalScript: string = `api/v1/scripts/local/{scriptId}`;
  public static GetGlobalScript: string = `api/v1/scripts/global/{scriptId}`;
  public static UpdateLocalScript: string = `api/v1/scripts/local/{scriptId}`;
  public static UpdateGlobalScript: string = `api/v1/scripts/global/{scriptId}`;
  public static CreateLocalScript: string = `api/v1/scripts/local`;
  public static CreateGlobalScript: string = `api/v1/scripts/global`;
  public static DeleteLocalScript: string = `api/v1/scripts/local/{scriptId}`;
  public static DeleteGlobalScript: string = `api/v1/scripts/global/{scriptId}`;
  public static ScriptSyntaxCheck: string = `api/v1/scripts/syntax-check`;
  public static GetScriptReferenceCounts: string = `api/v1/scripts/references/count`;
  public static GetGlobalScriptReferences: string = `api/v1/scripts/global/{scriptId}/references`;
  public static GetLocalScriptReferences: string = `api/v1/scripts/local/{scriptId}/references`;
  public static DuplicateScript: string = `api/v1/scripts/duplicate`;
  public static MigrateLocalToGlobalWhatIf: string = `api/v1/scripts/local/{scriptId}/migrate-local-to-global-what-if`;
  public static MigrateLocalToGlobal: string = `api/v1/scripts/local/{scriptId}/migrate-local-to-global`;
  public static RunScript: string = `api/v1/scripts/run`;
  public static EditorServicesBase: string = `api/v1/scripts/language-service`;
  public static StartEditorServices: string = `api/v1/scripts/language-service/start`;
  public static ConnectLanguageService: string = `api/v1/scripts/language-service/{terminalId}/language`;
  public static ConnectDebuggerService: string = `api/v1/scripts/language-service/{terminalId}/debug`;
  public static CancelScript: string = `api/v1/scripts/debug/cancel/{cancellationId}`;
  public static GetScriptVariablesAndParameters: string = `api/v1/scripts/default-variables`;
  public static FindFunctions: string = `api/v1/scripts/functions`;
  public static GetFunctionSyntax: string = `api/v1/scripts/functions/syntax`;
  public static ValidateParamBlockParameters: string = `api/v1/scripts/validate-param-block-parameters`;
  public static DoesScriptHaveParamBlock: string = `api/v1/scripts/does-script-have-param-block`;
  public static GetDisabledPreflightScripts: string = `api/v1/scripts/disabled-preflight-scripts`;
  public static SetPreflightScriptEnablement: string = `api/v1/scripts/set-preflight-script-enablement`;
  public static GetGlobalScriptAudits: string = `api/v1/scripts/global/{scriptId}/audit`;
  public static GetLocalScriptAudits: string = `api/v1/scripts/local/{scriptId}/audit`;
}
export abstract class MaintenanceSessionApiRoutes
{
  public static GetAll: string = `api/v1/maintenance-sessions`;
  public static DxGetAll: string = `api/v1/maintenance-sessions/dx`;
  public static Get: string = `api/v1/maintenance-sessions/{sessionId}`;
  public static GetSessionLogs: string = `api/v1/maintenance-sessions/{sessionId}/logs`;
  public static GetOldSessionLogs: string = `api/v1/maintenance-sessions/{sessionId}/old-logs`;
  public static GetSessionPhases: string = `api/v1/maintenance-sessions/{sessionId}/phases`;
  public static GetLastSessionLog: string = `api/v1/maintenance-sessions/{sessionId}/last-log`;
  public static CancelSession: string = `api/v1/maintenance-sessions/{sessionId}/cancel`;
  public static CancelSessions: string = `api/v1/maintenance-sessions/cancel`;
  public static CancelAllSessions: string = `api/v1/maintenance-sessions/cancel-all`;
  public static CancelSessionsForSchedule: string = `api/v1/maintenance-sessions/cancel-for-schedule/{scheduleId}`;
  public static GetSessionStatusCounts: string = `api/v1/maintenance-sessions/status-counts`;
  public static RerunSession: string = `api/v1/maintenance-sessions/{sessionId}/rerun`;
  public static RerunSessions: string = `api/v1/maintenance-sessions/rerun`;
  public static ResumeSession: string = `api/v1/maintenance-sessions/{sessionId}/resume`;
  public static RerunAction: string = `api/v1/maintenance-sessions/{sessionId}/actions/{actionId}/rerun`;
}
export abstract class MaintenanceActionApiRoutes
{
  public static DxGetAll: string = `api/v1/maintenance-actions/dx`;
  public static DxGetActionsForComputer: string = `api/v1/maintenance-actions/dx-for-computer/{computerId}`;
  public static GetLastLogForAction: string = `api/v1/maintenance-actions/{actionId}/last-log`;
  public static GetActionsForMaintenanceItem: string = `api/v1/maintenance-actions/maintenance-item`;
  public static GetActionsForVersion: string = `api/v1/maintenance-actions/version`;
  public static GetLogsForAction: string = `api/v1/maintenance-actions/{actionId}/logs`;
  public static GetActionsNeedingAttentionForComputer: string = `api/v1/maintenance-actions/computer/{computerId}/needs-attention`;
  public static GetLatestActionsForComputer: string = `api/v1/maintenance-actions/latest-for-computer/{computerId}`;
  public static GetLatestActionForComputers: string = `api/v1/maintenance-actions/latest-action-for-computers`;
  public static GetLatestActionsForTenant: string = `api/v1/maintenance-actions/latest-for-tenant/{tenantId}`;
  public static GetLatestActionForTenants: string = `api/v1/maintenance-actions/latest-action-for-tenants`;
  public static GetLatestNonCompliantMaintenanceActionsForTenant: string = `api/v1/maintenance-actions/latest-non-compliant-actions-for-tenant/{tenantId}`;
}
export abstract class AzureApiRoutes
{
  public static GetPartnerTenantCustomers: string = `api/v1/azure/partner-tenant-customers/{partnerPrincipalId}`;
  public static GetPartnerTenantInfos: string = `api/v1/azure/partner-tenant-infos`;
  public static SyncAzureUsersForTenants: string = `api/v1/azure/sync-users-from-azure-tenants`;
  public static PreconsentCustomerTenants: string = `api/v1/azure/preconsent-customer-tenants`;
  public static SyncAzureDetailsForTenants: string = `api/v1/azure/sync-details-from-azure-tenants`;
  public static HandleTenantConsent: string = `api/v1/azure/tenant-consented`;
  public static DisambiguateAzureTenantType: string = `api/v1/azure/disambiguate-azure-tenant-type`;
}
export abstract class ApplicationLogsApiRoutes
{
  public static GetSourceContexts: string = `api/v1/application-logs/source-contexts`;
  public static UpdateSourceContext: string = `api/v1/application-logs/source-context`;
  public static ClearSourceContext: string = `api/v1/application-logs/source-context/clear`;
  public static ClearAllSourceContexts: string = `api/v1/application-logs/source-context/clear-all`;
  public static ToggleStreaming: string = `api/v1/application-logs/streaming`;
}
export abstract class DevLabApiRoutes
{
  public static UnclaimVm: string = `api/v1/dev-lab/{computerId}/unclaim`;
  public static DownloadRdpFile: string = `api/v1/dev-lab/{computerId}/rdp-info`;
}
export abstract class UserApiRoutes
{
  public static GetAll: string = `api/v1/users`;
  public static Get: string = `api/v1/users/{userId}`;
  public static Update: string = `api/v1/users/{userId}`;
  public static Create: string = `api/v1/users`;
  public static CreateFromPerson: string = `api/v1/users/create-from-person`;
  public static Delete: string = `api/v1/users/{userId}`;
  public static BulkDelete: string = `api/v1/users/bulk-delete`;
  public static SubmitFeedback: string = `api/v1/users/submit-feedback`;
  public static ImpersonateUser: string = `api/v1/users/{userId}/impersonate`;
  public static StopImpersonatingUser: string = `api/v1/users/stop-impersonating`;
  public static GetClaims: string = `api/v1/users/claims`;
}
export abstract class TagApiRoutes
{
  public static GetAll: string = `api/v1/tags`;
  public static Get: string = `api/v1/tags/{tagId}`;
  public static Update: string = `api/v1/tags/{tagId}`;
  public static Create: string = `api/v1/tags`;
  public static Delete: string = `api/v1/tags/{tagId}`;
}
export abstract class SoftwareApiRoutes
{
  public static GetAllLocalSoftware: string = `api/v1/software/local`;
  public static GetLocalSoftware: string = `api/v1/software/local/{softwareIdentifier}`;
  public static CreateLocalSoftware: string = `api/v1/software/local`;
  public static UpdateLocalSoftware: string = `api/v1/software/local/{softwareIdentifier}`;
  public static DeleteLocalSoftware: string = `api/v1/software/local/{softwareIdentifier}`;
  public static GetAllGlobalSoftware: string = `api/v1/software/global`;
  public static GetGlobalSoftware: string = `api/v1/software/global/{softwareIdentifier}`;
  public static CreateGlobalSoftware: string = `api/v1/software/global`;
  public static UpdateGlobalSoftware: string = `api/v1/software/global/{softwareIdentifier}`;
  public static DeleteGlobalSoftware: string = `api/v1/software/global/{softwareIdentifier}`;
  public static GetAllLocalSoftwareVersions: string = `api/v1/software/local/{softwareIdentifier}/versions`;
  public static GetLocalSoftwareVersion: string = `api/v1/software/local/{softwareIdentifier}/versions/{semanticVersion}`;
  public static GetLatestVersionForLocalSoftware: string = `api/v1/software/local/{softwareIdentifier}/latest`;
  public static CreateLocalSoftwareVersion: string = `api/v1/software/local/{softwareIdentifier}/versions`;
  public static FastCreateLocalSoftwareVersion: string = `api/v1/software/local/fast-create`;
  public static UploadLocalSoftwareVersionFile: string = `api/v1/software/local/upload`;
  public static UpdateLocalSoftwareVersion: string = `api/v1/software/local/{softwareIdentifier}/versions/{semanticVersion}`;
  public static DeleteLocalSoftwareVersion: string = `api/v1/software/local/{softwareIdentifier}/versions/{semanticVersion}`;
  public static AnalyzeLocalSoftwarePackage: string = `api/v1/software/local/analyze`;
  public static GetDownloadUrlForLocalSoftwareVersion: string = `api/v1/software/local/{softwareIdentifier}/versions/{semanticVersion}/request-download`;
  public static GetAllGlobalSoftwareVersions: string = `api/v1/software/global/{softwareIdentifier}/versions`;
  public static GetGlobalSoftwareVersion: string = `api/v1/software/global/{softwareIdentifier}/versions/{semanticVersion}`;
  public static GetLatestVersionForGlobalSoftware: string = `api/v1/software/global/{softwareIdentifier}/latest`;
  public static CreateGlobalSoftwareVersion: string = `api/v1/software/global/{softwareIdentifier}/versions`;
  public static FastCreateGlobalSoftwareVersion: string = `api/v1/software/global/fast-create`;
  public static UploadGlobalSoftwareVersionFile: string = `api/v1/software/global/upload`;
  public static UpdateGlobalSoftwareVersion: string = `api/v1/software/global/{softwareIdentifier}/versions/{semanticVersion}`;
  public static DeleteGlobalSoftwareVersion: string = `api/v1/software/global/{softwareIdentifier}/versions/{semanticVersion}`;
  public static AnalyzeGlobalSoftwarePackage: string = `api/v1/software/global/analyze`;
  public static GetDownloadUrlForGlobalSoftwareVersion: string = `api/v1/software/global/{softwareIdentifier}/versions/{semanticVersion}/request-download`;
  public static MigrateLocalToGlobalWhatIf: string = `api/v1/software/local/{softwareIdentifier}/migrate-local-to-global-what-if`;
  public static MigrateLocalToGlobal: string = `api/v1/software/local/{softwareIdentifier}/migrate-local-to-global`;
  public static RunLocalAutoUpdateScript: string = `api/v1/software/local/{softwareIdentifier}/run-auto-update`;
  public static RunGlobalAutoUpdateScript: string = `api/v1/software/global/{softwareIdentifier}/run-auto-update`;
}
export abstract class ChangeRequestApiRoutes
{
  public static DeleteChangeRequest: string = `api/v1/change-requests/{id}`;
  public static ApproveChangeRequest: string = `api/v1/change-requests/{id}/approve`;
  public static DenyChangeRequest: string = `api/v1/change-requests/{id}/deny`;
  public static RequireChanges: string = `api/v1/change-requests/{id}/require-changes`;
  public static CommentOnChangeRequest: string = `api/v1/change-requests/{id}/comment`;
  public static GetAllDx: string = `api/v1/change-requests/dx`;
  public static GetOpenCount: string = `api/v1/change-requests/open-count`;
}
export abstract class TargetAssignmentApiRoutes
{
  public static CalculateTargetedComputers: string = `api/v1/target-assignments/target-preview`;
  public static CalculateTargetedTenants: string = `api/v1/target-assignments/tenant-target-preview`;
  public static CalculateTargetedPersons: string = `api/v1/target-assignments/persons-target-preview`;
  public static GetLocalTargetAssignmentType: string = `api/v1/target-assignments/{id}/type`;
  public static GetAllLocal: string = `api/v1/target-assignments`;
  public static GetDuplicatesLocal: string = `api/v1/target-assignments/duplicates`;
  public static CreateLocal: string = `api/v1/target-assignments`;
  public static UpdateLocal: string = `api/v1/target-assignments/{id}`;
  public static DeleteLocal: string = `api/v1/target-assignments/{id}`;
  public static GetLocal: string = `api/v1/target-assignments/{id}`;
  public static OverrideLocal: string = `api/v1/target-assignments/{id}/override`;
  public static UpdateNotesLocal: string = `api/v1/target-assignments/{id}/notes`;
  public static ResolveVisibilityTargetAssignments: string = `api/v1/target-assignments/visibility`;
  public static BatchUpdateLocal: string = `api/v1/target-assignments/batch-update`;
  public static GetGlobalTargetAssignmentType: string = `api/v1/target-assignments/global/{id}/type`;
  public static GetAllGlobal: string = `api/v1/target-assignments/global`;
  public static CreateGlobal: string = `api/v1/target-assignments/global/create`;
  public static UpdateGlobal: string = `api/v1/target-assignments/global/{id}`;
  public static DeleteGlobal: string = `api/v1/target-assignments/global/{id}`;
  public static GetGlobal: string = `api/v1/target-assignments/global/{id}`;
  public static OverrideGlobal: string = `api/v1/target-assignments/global/{id}/override`;
  public static UpdateNotesGlobal: string = `api/v1/target-assignments/global/{id}/notes`;
  public static GetRecommendedApprovals: string = `api/v1/target-assignments/recommended-approvals`;
  public static UpdateRecommendedApprovals: string = `api/v1/target-assignments/recommended-approvals/update`;
  public static GetAllOptionalTargetAssignmentApprovalsForComputer: string = `api/v1/target-assignments/optional-target-assignment-approvals/computer/{computerId}`;
  public static UpdateOptionalTargetAssignmentApproval: string = `api/v1/target-assignments/optional-target-assignment-approvals/{id}`;
  public static GetMaintenanceItemOrder: string = `api/v1/target-assignments/maintenance-item-orders`;
  public static UpdateMaintenanceItemOrder: string = `api/v1/target-assignments/update-maintenance-item-order`;
  public static Duplicate: string = `api/v1/target-assignments/duplicate`;
  public static MigrateToSupersedingAssignment: string = `api/v1/target-assignments/migrate-to-superseding-assignment`;
  public static MigrateToSupersedingAssignmentWhatIf: string = `api/v1/target-assignments/migrate-to-superseding-assignment-what-if`;
  public static GetChangeRequest: string = `api/v1/target-assignments/change-request/{changeRequestId}`;
  public static GetAllChangeRequests: string = `api/v1/target-assignments/change-requests`;
  public static GetAllChangeRequestsForDeployment: string = `api/v1/target-assignments/{deploymentId}/change-requests`;
  public static CreateChangeRequestForExistingDeployment: string = `api/v1/target-assignments/{deploymentId}/change-request`;
  public static UpdateChangeRequestForExistingDeployment: string = `api/v1/target-assignments/{deploymentId}/change-request/{changeRequestId}`;
  public static CreateChangeRequestForNewDeployment: string = `api/v1/target-assignments/change-request`;
  public static UpdateChangeRequestForNewDeployment: string = `api/v1/target-assignments/change-request/{changeRequestId}`;
  public static GetChangeRequestDiff: string = `api/v1/target-assignments/change-request/{changeRequestId}/diff`;
  public static MigrateDeploymentsToProviderLinks: string = `api/v1/target-assignments/migrate-deployments-to-provider-links`;
}
export abstract class SmtpConfigApiRoutes
{
  public static GetAll: string = `api/v1/smtp-configs`;
  public static Get: string = `api/v1/smtp-configs/{tenantId}`;
  public static Update: string = `api/v1/smtp-configs/{tenantId}`;
  public static Create: string = `api/v1/smtp-configs`;
  public static Delete: string = `api/v1/smtp-configs/{tenantId}`;
  public static SendTestEmail: string = `api/v1/smtp-configs/send-test-email`;
}
export abstract class ScheduleApiRoutes
{
  public static GetAll: string = `api/v1/schedules`;
  public static Get: string = `api/v1/schedules/{scheduleId}`;
  public static Update: string = `api/v1/schedules/{scheduleId}`;
  public static Create: string = `api/v1/schedules`;
  public static Delete: string = `api/v1/schedules/{scheduleId}`;
  public static RunScheduleNow: string = `api/v1/schedules/{scheduleId}/run-now`;
  public static GetRunningScheduleIds: string = `api/v1/schedules/running-schedule-ids`;
  public static Cancel: string = `api/v1/schedules/{scheduleId}/cancel`;
}
export abstract class MetricApiRoutes
{
  public static GetAppMetrics: string = `api/v1/metrics/app`;
  public static GetCircuitBreakers: string = `api/v1/metrics/circuit-breakers`;
  public static IsolateCircuitBreaker: string = `api/v1/metrics/circuit-breakers/isolate`;
  public static ResetCircuitBreaker: string = `api/v1/metrics/circuit-breakers/reset`;
  public static GetAllProviderLinkMetrics: string = `api/v1/metrics/provider-links`;
}
export abstract class BrandingApiRoutes
{
  public static GetAll: string = `api/v1/brandings`;
  public static Get: string = `api/v1/brandings/{id}`;
  public static Update: string = `api/v1/brandings/{id}`;
  public static Create: string = `api/v1/brandings`;
  public static Delete: string = `api/v1/brandings/{id}`;
  public static SetDefaultBranding: string = `api/v1/brandings/global-default/{id}`;
  public static SendTestBrandingEmail: string = `api/v1/brandings/send-test-email`;
  public static ValidateTimeFormat: string = `api/v1/brandings/validate-time-format/{timeFormat}`;
  public static GetSupportBranding: string = `api/v1/brandings/support-branding`;
}
export abstract class EmailApiRoutes
{
  public static Postpone: string = `api/v1/maintenance-emails/{emailGuid}/jobs/postpone`;
  public static RebootNow: string = `api/v1/maintenance-emails/{emailGuid}/jobs/rebootnow`;
  public static UpdateNow: string = `api/v1/maintenance-emails/{emailGuid}/jobs/now`;
}
export abstract class ImmyBotApiRoutes
{
  public static RunImmyService: string = `api/v1/run-immy-service`;
  public static RunImmyServiceNew: string = `api/v1/run-immy-service-new`;
}
export abstract class EphemeralAgentRoutes
{
  public static SessionEndpointBase: string = `api/v1/ephemeral-session/{sessionIdentifier}`;
  public static GetDevelopmentEphemeralBinary: string = `api/v1/ephemeral-session/development/latest-ephemeral-binary`;
}
export abstract class ImmyAgentMetadataRoutes
{
  public static Base: string = `api/v1/immy-agent-metadata`;
  public static AgentHash: string = `api/v1/immy-agent-metadata/agent-hash`;
}
export abstract class ApplicationLocksRoutes
{
  public static EndpointBase: string = `api/v1/application-locks`;
  public static EventStream: string = `api/v1/application-locks/realtime-event-stream`;
  public static RequestCancellation: string = `api/v1/application-locks/request-cancellation`;
}
export abstract class WebHookRoutes
{
  public static EndpointBase: string = `api/v1/webhooks/{id}`;
}
export abstract class PersonApiRoutes
{
  public static Dx: string = `api/v1/persons/dx`;
  public static GetAll: string = `api/v1/persons`;
  public static Get: string = `api/v1/persons/{id}`;
  public static Delete: string = `api/v1/persons/{id}`;
  public static Put: string = `api/v1/persons/{id}`;
  public static Create: string = `api/v1/persons`;
  public static GetPersonsRequestingAccess: string = `api/v1/persons/requesting-access`;
  public static GrantAccess: string = `api/v1/persons/{personId}/grant-access`;
  public static GrantAccessRbac: string = `api/v1/persons/grant-access-rbac`;
  public static DenyAccess: string = `api/v1/persons/{personId}/deny-access`;
  public static GetSelfServiceItems: string = `api/v1/persons/{id}/self-service`;
  public static AddTags: string = `api/v1/persons/add-tags`;
  public static RemoveTags: string = `api/v1/persons/remove-tags`;
}
export abstract class ProviderTypeApiRoutes
{
  public static GetAll: string = `api/v1/provider-types`;
  public static GetDeviceGroups: string = `api/v1/provider-types/device-group-types/{deviceGroupTypeId}/device-groups`;
  public static GetClientGroups: string = `api/v1/provider-types/client-group-types/{clientGroupTypeId}/client-groups`;
  public static BindParameters: string = `api/v1/provider-types/{providerType}/bind-parameters`;
}
export abstract class ProviderLinkApiRoutes
{
  public static ProviderLinkBase: string = `api/v1/provider-links`;
  public static GetAll: string = `api/v1/provider-links`;
  public static Create: string = `api/v1/provider-links`;
  public static CreateWithExternalProviderReference: string = `api/v1/provider-links/create-with-external-provider-reference`;
  public static Get: string = `api/v1/provider-links/{id}`;
  public static Update: string = `api/v1/provider-links/{id}`;
  public static Delete: string = `api/v1/provider-links/{id}`;
  public static VerifyCredentialsWithExternalProviderReference: string = `api/v1/provider-links/verify-with-external-provider-reference`;
  public static PrioritizeScriptRunProviderLinks: string = `api/v1/provider-links/prioritize`;
  public static Reload: string = `api/v1/provider-links/{id}/reload`;
  public static OldRmmLinkBase: string = `api/v1/rmm-links`;
  public static OldGetAll: string = `api/v1/rmm-links`;
  public static OldGet: string = `api/v1/rmm-links/{id}`;
  public static OldUpdate: string = `api/v1/rmm-links/{id}`;
  public static OldCreate: string = `api/v1/rmm-links`;
  public static ProviderLinkXRefBase: string = `api/v1/provider-links/{id}/cross-references`;
  public static GetExternalProviderLinkInitializationInfo: string = `api/v1/provider-links/{id}/cross-references/init-info`;
  public static CreateLinkedProviderReference: string = `api/v1/provider-links/{id}/cross-references/create`;
  public static DeleteLinkedProviderReference: string = `api/v1/provider-links/{id}/cross-references/{externalLinkId}/delete`;
  public static DisableLinkedProviderClientLinking: string = `api/v1/provider-links/{id}/cross-references/{externalLinkId}/disable-client-linking`;
  public static EnableLinkedProviderClientLinking: string = `api/v1/provider-links/{id}/cross-references/{externalLinkId}/enable-client-linking`;
  public static SyncClientsFromLinkedProvider: string = `api/v1/provider-links/{id}/cross-references/{externalLinkId}/sync-clients`;
  public static GetTechnicianPageInfoFromPsaTicket: string = `api/v1/provider-links/{id}/tickets/{ticketId}`;
  public static ProviderLinkClientBase: string = `api/v1/provider-links/{id}/clients`;
  public static GetClients: string = `api/v1/provider-links/{id}/clients`;
  public static LinkClientToNewTenant: string = `api/v1/provider-links/{id}/clients/link-to-new-tenant`;
  public static LinkExactMatchClients: string = `api/v1/provider-links/{id}/clients/link-exact-match-clients`;
  public static LinkClientsToTenant: string = `api/v1/provider-links/{id}/clients/link-to-tenant`;
  public static AutoLinkClientsToTenants: string = `api/v1/provider-links/{id}/clients/auto-link-to-tenants`;
  public static UnlinkClients: string = `api/v1/provider-links/{id}/clients/unlink-from-tenants`;
  public static GetClientStatuses: string = `api/v1/provider-links/{id}/clients/statuses`;
  public static GetClientTypes: string = `api/v1/provider-links/{id}/clients/types`;
  public static FetchClientsFromProvider: string = `api/v1/provider-links/{id}/clients/sync`;
  public static SyncAgentsForClients: string = `api/v1/provider-links/{id}/clients/sync-agents`;
  public static ImmyAgentProviderOldRekeyEndpoint: string = `/installer/agent-rekey/request`;
  public static ProviderLinkAgentBase: string = `api/v1/provider-links/{id}/agents`;
  public static GetAgentExecutableUri: string = `api/v1/provider-links/{id}/agents/executable-uri`;
  public static GetAgentExecutableUriWithOnboardingOptions: string = `api/v1/provider-links/{id}/agents/executable-uri-with-onboarding`;
  public static GetAgentProvisioningPackageUri: string = `api/v1/provider-links/{id}/agents/provisioning-package-uri`;
  public static GetAgentProvisioningPackageUriWithOnboardingOptions: string = `api/v1/provider-links/{id}/agents/provisioning-package-uri-with-onboarding`;
  public static GetAgentPowerShellInstallScript: string = `api/v1/provider-links/{id}/agents/powershell-install-script`;
  public static GetAgentPowerShellInstallScriptWithOnboardingOptions: string = `api/v1/provider-links/{id}/agents/powershell-install-script-with-onboarding`;
  public static GetAgentBashInstallScript: string = `api/v1/provider-links/{id}/agents/bash-install-script`;
  public static GetAgentBashInstallScriptWithOnboardingOptions: string = `api/v1/provider-links/{id}/agents/bash-install-script-with-onboarding`;
  public static InstallAgentOnComputer: string = `api/v1/provider-links/{id}/agents/install-on-computer/{computerId}`;
  public static DeleteOfflineAgentFromComputer: string = `api/v1/provider-links/{id}/agents/{agentId}/delete-offline-agent-from-computer`;
  public static RefreshAgentOnlineStatus: string = `api/v1/provider-links/{id}/agents/{agentId}/refresh-device-online-status`;
  public static GetExternalProviderAgentUrl: string = `api/v1/provider-links/{id}/agents/{computerId}/external-agent-url`;
  public static SyncAgents: string = `api/v1/provider-links/{id}/agents/sync`;
  public static ProviderCustomRoute: string = `plugins/api/v1/{providerLinkId}/{**catchAll}`;
}
export abstract class TenantApiRoutes
{
  public static Base: string = `api/v1/tenants`;
  public static GetAll: string = `api/v1/tenants`;
  public static Create: string = `api/v1/tenants`;
  public static Get: string = `api/v1/tenants/{id}`;
  public static Update: string = `api/v1/tenants/{id}`;
  public static UpdateAzureTenantLink: string = `api/v1/tenants/update-azure-tenant-link`;
  public static BulkCreate: string = `api/v1/tenants/bulk-create`;
  public static BulkDelete: string = `api/v1/tenants/bulk-delete`;
  public static BulkMerge: string = `api/v1/tenants/bulk-merge`;
  public static ActivateTenant: string = `api/v1/tenants/activate/{id}`;
  public static DeactivateTenant: string = `api/v1/tenants/deactivate/{id}`;
  public static SoftwareFromInventory: string = `api/v1/tenants/software-from-inventory/{id}`;
  public static AllSoftwareFromInventoryDx: string = `api/v1/tenants/software-from-inventory/dx`;
  public static ExportSoftware: string = `api/v1/tenants/software-from-inventory/export`;
  public static GetAzureGroupsAtTenant: string = `api/v1/tenants/{id}/azure-groups`;
  public static GetAzureGroupAtTenant: string = `api/v1/tenants/{id}/azure-groups/{groupId}`;
  public static GetMembersOfAzureGroupAtTenant: string = `api/v1/tenants/{id}/azure-groups/{groupId}/members`;
  public static GetProviderLinks: string = `api/v1/tenants/{id}/provider-links`;
  public static GetComputersExcludedFromMaintenance: string = `api/v1/tenants/{id}/computers/excluded-from-maintenance`;
  public static AddTags: string = `api/v1/tenants/add-tags`;
  public static RemoveTags: string = `api/v1/tenants/remove-tags`;
  public static SetParentTenant: string = `api/v1/tenants/set-parent-tenant`;
  public static RemoveParentTenant: string = `api/v1/tenants/remove-parent-tenant`;
  public static ResolveAssignmentsForMaintenanceItem: string = `api/v1/tenants/resolve-assignments-for-maintenance-item`;
  public static GetComputerCounts: string = `api/v1/tenants/computer-counts`;
}
export abstract class LicenseApiRoutes
{
  public static Dx: string = `api/v1/licenses/dx`;
  public static GetAll: string = `api/v1/licenses`;
  public static Get: string = `api/v1/licenses/{licenseId}`;
  public static Update: string = `api/v1/licenses/{licenseId}`;
  public static Create: string = `api/v1/licenses`;
  public static Delete: string = `api/v1/licenses/{licenseId}`;
  public static Upload: string = `api/v1/licenses/upload`;
  public static GetDownloadUrl: string = `api/v1/licenses/{licenseId}/request-download`;
}
export abstract class DevInstanceManagementApiRoutes
{
  public static StartHangfireServer: string = `api/v1/dev-instance-management/start-hangfire-server`;
  public static StopHangfireServer: string = `api/v1/dev-instance-management/stop-hangfire-server`;
}
export abstract class ProviderAgentApiRoutes
{
  public static Base: string = `api/v1/provider-agents`;
  public static GetDx: string = `api/v1/provider-agents/dx`;
  public static GetPendingDx: string = `api/v1/provider-agents/pending-dx`;
  public static GetPending: string = `api/v1/provider-agents/pending`;
  public static BulkDeletePendingAgents: string = `api/v1/provider-agents/bulk-delete-pending`;
  public static GetPendingCounts: string = `api/v1/provider-agents/pending-counts`;
  public static ResolveFailuresForAgents: string = `api/v1/provider-agents/resolve-failures`;
  public static ResolveFailuresForAgent: string = `api/v1/provider-agents/{agentId}/resolve-failures`;
  public static ResolveFailure: string = `api/v1/provider-agents/resolve-failure/{failureId}`;
  public static GetPendingAgentConflictsForComputer: string = `api/v1/provider-agents/{computerId}/pending-conflicts`;
  public static GetIdentificationLogs: string = `api/v1/provider-agents/{agentId}/identification-logs`;
  public static IdentifyAgents: string = `api/v1/provider-agents/identify`;
  public static RestoreAgentComputers: string = `api/v1/provider-agents/restore-agents-computers`;
}
export abstract class MaintenanceTaskApiRoutes
{
  public static Search: string = `api/v1/maintenance-tasks/search`;
  public static GetAllLocalMaintenanceTasks: string = `api/v1/maintenance-tasks/local`;
  public static GetAllGlobalMaintenanceTasks: string = `api/v1/maintenance-tasks/global`;
  public static GetLocalMaintenanceTask: string = `api/v1/maintenance-tasks/local/{id}`;
  public static GetGlobalMaintenanceTask: string = `api/v1/maintenance-tasks/global/{id}`;
  public static CreateGlobalMaintenanceTask: string = `api/v1/maintenance-tasks/global`;
  public static CreateLocalMaintenanceTask: string = `api/v1/maintenance-tasks/local`;
  public static UpdateGlobalMaintenanceTask: string = `api/v1/maintenance-tasks/global/{id}`;
  public static UpdateLocalMaintenanceTask: string = `api/v1/maintenance-tasks/local/{id}`;
  public static DeleteGlobalMaintenanceTask: string = `api/v1/maintenance-tasks/global/{id}`;
  public static DeleteLocalMaintenanceTask: string = `api/v1/maintenance-tasks/local/{id}`;
  public static GetReferenceCount: string = `api/v1/maintenance-tasks/reference-count`;
  public static GetGlobalMaintenanceTaskReferences: string = `api/v1/maintenance-tasks/global/{id}/references`;
  public static DuplicateMaintenanceTask: string = `api/v1/maintenance-tasks/duplicate`;
  public static ValidateParamBlockParameters: string = `api/v1/maintenance-tasks/validate-param-block-parameters`;
  public static GetParamBlockFromLocalMaintenanceTaskParameters: string = `api/v1/maintenance-tasks/local/{id}/param-block-from-parameters`;
  public static GetParamBlockFromGlobalMaintenanceTaskParameters: string = `api/v1/maintenance-tasks/global/{id}/param-block-from-parameters`;
  public static MigrateLocalToGlobalWhatIf: string = `api/v1/maintenance-tasks/local/{id}/migrate-local-to-global-what-if`;
  public static MigrateLocalToGlobal: string = `api/v1/maintenance-tasks/local/{id}/migrate-local-to-global`;
}
export abstract class InventoryTaskApiRoutes
{
  public static GetAll: string = `api/v1/inventory-tasks`;
  public static CreateLocal: string = `api/v1/inventory-tasks/local`;
  public static UpdateLocal: string = `api/v1/inventory-tasks/local/{id}`;
  public static DeleteLocal: string = `api/v1/inventory-tasks/local/{id}`;
  public static AddScriptToLocalInventoryTask: string = `api/v1/inventory-tasks/local/{id}/scripts`;
  public static DeleteScriptFromLocalInventoryTask: string = `api/v1/inventory-tasks/local/{taskId}/scripts/{inventoryKey}`;
}
export abstract class ChocolateyApiRoutes
{
  public static Search: string = `api/v1/chocolatey/search`;
  public static FindPackagesById: string = `api/v1/chocolatey/find-packages-by-id`;
}
export abstract class NotificationApiRoutes
{
  public static GetDx: string = `api/v1/notifications/dx`;
  public static Acknowledge: string = `api/v1/notifications/acknowledge`;
  public static GetUnacknowledgedNotifications: string = `api/v1/notifications/unacknowledged`;
  public static SilenceNotification: string = `api/v1/notifications/{type}/silence`;
  public static GetSilencedNotificationsForUser: string = `api/v1/notifications`;
  public static RemoveSilencedNotification: string = `api/v1/notifications/{id}/unsilence`;
}
export abstract class SystemApiRoutes
{
  public static GetReleases: string = `api/v1/system/releases`;
  public static PullUpdate: string = `api/v1/system/pull-update`;
  public static GetTimezones: string = `api/v1/system/timezones`;
  public static RestartBackend: string = `api/v1/system/restart-backend`;
  public static GetImmySupportAccessGrantDetails: string = `api/v1/system/immy-support-access-grant-details`;
  public static IsImmySupportAccessGranted: string = `api/v1/system/is-immy-support-access-granted`;
  public static RequestSessionSupport: string = `api/v1/system/request-session-support`;
  public static RequestFormSupport: string = `api/v1/system/request-form-support`;
  public static EnableImmySupportAccess: string = `api/v1/system/enable-immy-support-access`;
  public static DisableImmySupportAccess: string = `api/v1/system/disable-immy-support-access`;
  public static EnqueuePendoTrackEvent: string = `api/v1/system/enqueue-pendo-track-event`;
  public static UpdateReleaseChannel: string = `api/v1/system/update-release-channel`;
  public static Reset: string = `api/v1/system/reset`;
}
export abstract class MediaApiRoutes
{
  public static Search: string = `api/v1/media/search`;
  public static GetLocal: string = `api/v1/media/local`;
  public static GetLocalById: string = `api/v1/media/local/{id}`;
  public static UpdateLocal: string = `api/v1/media/local/{id}`;
  public static DeleteLocal: string = `api/v1/media/local/{id}`;
  public static CreateLocal: string = `api/v1/media/local`;
  public static GetGlobal: string = `api/v1/media/global`;
  public static GetGlobalById: string = `api/v1/media/global/{id}`;
  public static UpdateGlobal: string = `api/v1/media/global/{id}`;
  public static DeleteGlobal: string = `api/v1/media/global/{id}`;
  public static CreateGlobal: string = `api/v1/media/global`;
  public static RequestFileDownloadUrl: string = `api/v1/media/requestFileDownloadUrl`;
  public static GetLocalDownloadUrl: string = `api/v1/media/local/{id}/download-url`;
  public static GetGlobalDownloadUrl: string = `api/v1/media/global/{id}/download-url`;
  public static UploadGlobalMedia: string = `api/v1/media/global/upload`;
  public static UploadLocalMedia: string = `api/v1/media/local/upload`;
  public static UploadSupportMedia: string = `api/v1/media/support/upload`;
}
export abstract class SyncsApiRoutes
{
  public static TriggerUserAffinitySync: string = `api/v1/syncs/trigger-user-affinity-sync`;
  public static TriggerAzureUserSync: string = `api/v1/syncs/azure-user-sync`;
}
export abstract class DynamicIntegrationTypeRoutes
{
  public static GetAll: string = `api/v1/dynamic-provider-types`;
  public static CreateLocal: string = `api/v1/dynamic-provider-types/local;`;
  public static CreateGlobal: string = `api/v1/dynamic-provider-types/global`;
  public static GetLocal: string = `api/v1/dynamic-provider-types/local/{id}`;
  public static GetGlobal: string = `api/v1/dynamic-provider-types/global/{id}`;
  public static UpdateLocal: string = `api/v1/dynamic-provider-types/local/{id}`;
  public static UpdateGlobal: string = `api/v1/dynamic-provider-types/global/{id}`;
  public static DeleteLocal: string = `api/v1/dynamic-provider-types/local/{id}`;
  public static DeleteGlobal: string = `api/v1/dynamic-provider-types/global/{id}`;
  public static Reload: string = `api/v1/dynamic-provider-types/reload`;
  public static ReloadByGlobalId: string = `api/v1/dynamic-provider-types/global/{id}/reload`;
  public static ReloadByLocalId: string = `api/v1/dynamic-provider-types/local/{id}/reload`;
  public static SetupTestIntegration: string = `api/v1/dynamic-provider-types/test-environment/{terminalId}`;
  public static RemoveTestIntegration: string = `api/v1/dynamic-provider-types/test-environment/{terminalId}`;
  public static TestIntegrationBindConfigurationForm: string = `api/v1/dynamic-provider-types/test-environment/{terminalId}/bind-configuration-form`;
  public static TestIntegrationMethod: string = `api/v1/dynamic-provider-types/test-environment/{terminalId}/execute-method/{method}`;
}
export abstract class ProviderAuditLogRoutes
{
  public static Base: string = `api/v1/provider-audit-logs`;
  public static GetAllPaged: string = `api/v1/provider-audit-logs/paged`;
  public static Get: string = `api/v1/provider-audit-logs/{id}`;
}
export abstract class GettingStartedApiRoutes
{
  public static Base: string = `api/v1/getting-started`;
  public static Checklist: string = `api/v1/getting-started/checklist`;
  public static ResetChecklist: string = `api/v1/getting-started/checklist/reset`;
  public static CompleteChecklist: string = `api/v1/getting-started/checklist/complete`;
}
export abstract class RoleApiRoutes
{
  public static GetRoles: string = `api/v1/roles`;
  public static GetRole: string = `api/v1/roles/{roleId}`;
  public static CreateRole: string = `api/v1/roles`;
  public static UpdateRole: string = `api/v1/roles/{roleId}`;
  public static DeleteRole: string = `api/v1/roles/{roleId}`;
  public static CloneRole: string = `api/v1/roles/{roleId}/clone`;
  public static GetPermissions: string = `api/v1/roles/permissions`;
}
export abstract class UserRoleApiRoutes
{
  public static GetUserRoles: string = `api/v1/user-roles/get/user/{userId}/roles`;
  public static AssignRoleToUser: string = `api/v1/user-roles/assign/user/{userId}/role/{roleId}`;
  public static RemoveRoleFromUser: string = `api/v1/user-roles/remove/user/{userId}/role/{roleId}`;
  public static GetUsersInRole: string = `api/v1/user-roles/get/role/{roleId}/users`;
  public static BulkAssignRolesToPeople: string = `api/v1/user-roles/assign/bulk-assign-roles`;
  public static BulkRemoveRolesFromPeople: string = `api/v1/user-roles/remove/bulk-remove-roles`;
  public static InvalidateCache: string = `api/v1/user-roles/invalidate-cache/{userId}`;
  public static SetUserRoles: string = `api/v1/user-roles/set/user/{userId}/roles`;
}
export abstract class PersonUserApiRoutes
{
  public static GetAll: string = `api/v1/person-user/get`;
}
