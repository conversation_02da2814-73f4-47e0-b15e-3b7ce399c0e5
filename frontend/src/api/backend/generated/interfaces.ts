//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.


export const ProviderCapabilities = {
  ['IHasExtraDataAccessibleInMetascripts']: { DisplayName: 'Extra data accessible in Metascripts', Description: 'Extra data that can be accessed via the ProviderInfo.ExtraData property in Metascripts' },
  ['IRunScriptProvider']: { DisplayName: 'Running scripts', Description: 'Running scripts on the device' },
  ['ISupportsAgentBashInstallScript']: { DisplayName: 'Agent Install (Bash)', Description: 'Installing the agent from a Bash script' },
  ['ISupportsAgentBashInstallScriptWithOnboardingOptions']: { DisplayName: 'Agent Install (Bash) With Auto Onboarding', Description: 'Installing the agent from a Bash script and specifying onboarding options with the installer' },
  ['ISupportsAgentExecutableUri']: { DisplayName: 'Agent Install (EXE)', Description: 'Installing the agent from executable' },
  ['ISupportsAgentExecutableUriWithOnboardingOptions']: { DisplayName: 'Agent Install (EXE) With Auto Onboarding', Description: 'Installing the agent from an executable and specifying onboarding options with the installer' },
  ['ISupportsAgentPowerShellInstallScript']: { DisplayName: 'Agent Install (PowerShell)', Description: 'Installing the agent from a PowerShell script' },
  ['ISupportsAgentPowerShellInstallScriptWithOnboardingOptions']: { DisplayName: 'Agent Install (PowerShell) With Auto Onboarding', Description: 'Installing the agent from a PowerShell script and specifying onboarding options with the installer' },
  ['ISupportsAgentProvisioningPackageUri']: { DisplayName: 'Agent OOBE Installation', Description: 'Installing the agent from PPKG' },
  ['ISupportsAgentProvisioningPackageUriWithOnboardingOptions']: { DisplayName: 'Agent OOBE Installation With Auto Onboarding', Description: 'Installing the agent from PPKG and specifying onboarding options with the installer' },
  ['ISupportsAgentUninstallToken']: { DisplayName: 'Agent Uninstall Token', Description: 'Getting an uninstall token for an agent' },
  ['ISupportsAgentUpdatesStageResolution']: { DisplayName: 'Agent Updates Stage Resolution', Description: 'Resolving agent updates during the agent updates stage of maintenance sessions' },
  ['ISupportsApplyingWindowsPatches']: { DisplayName: 'Windows Patching', Description: 'Installing Windows updates' },
  ['ISupportsAuthenticatedDownload']: { DisplayName: 'Authenticated Download', Description: 'Generating an authentication header for downloading files' },
  ['ISupportsClientGrouping`1']: { DisplayName: 'Client Grouping', Description: 'Retrieving client groups and clients in groups' },
  ['ISupportsCrossProviderClientLinking']: { DisplayName: 'Cross-Integration Client Linking', Description: 'Syncing linked clients with certain other integrations' },
  ['ISupportsCrossProviderInitialization']: { DisplayName: 'Cross-Integration Initialization', Description: 'Initializing certain other integrations' },
  ['ISupportsDeletingOfflineAgent']: { DisplayName: 'Delete Offline Agents', Description: 'Manually removing offline agents from within ImmyBot' },
  ['ISupportsDeviceGrouping`1']: { DisplayName: 'Device Grouping', Description: 'Retrieving device groups and devices in groups' },
  ['ISupportsDynamicVersions']: { DisplayName: 'Dynamic Versions', Description: 'Retrieving agent installers dynamically from a URL' },
  ['ISupportsExternalProviderAgentUrl']: { DisplayName: 'External Provider Agent URL', Description: 'Getting the URL for an agent at the external provider\'s site' },
  ['ISupportsGetLatestAgentVersion']: { DisplayName: 'Get Latest Agent Version', Description: 'Getting the latest version of the agent installer' },
  ['ISupportsGetLatestAgentVersionDownloadUrl']: { DisplayName: 'Get Latest Agent Version Download URL', Description: 'Getting the URL for the latest version of the agent installer' },
  ['ISupportsHttpRequest']: { DisplayName: 'HTTP Request Handling', Description: 'Handling incoming HTTP requests' },
  ['ISupportsInventoryIdentification']: { DisplayName: 'Inventory Identification', Description: 'Agent identification by adding an inventory script to be run against all of your endpoints' },
  ['ISupportsListingAgents']: { DisplayName: 'Agent Import', Description: 'Importing agents from linked clients into ImmyBot' },
  ['ISupportsListingClients']: { DisplayName: 'Client Import', Description: 'Mapping clients from the integration to tenants in ImmyBot' },
  ['ISupportsMaintenanceMode']: { DisplayName: 'Maintenance Mode', Description: 'Setting and removing maintenance mode for an agent at the external provider\'s site' },
  ['ISupportsProviderClientStatus']: { DisplayName: 'Client Status', Description: 'Getting the status of clients from the provider' },
  ['ISupportsProviderClientType']: { DisplayName: 'Client Type', Description: 'Getting the types of clients from the provider' },
  ['ISupportsPsaTicketDetails']: { DisplayName: 'Fetch Ticket Details', Description: 'Getting the ticket details from the provider' },
  ['ISupportsRefreshAgentOnlineStatus']: { DisplayName: 'Agent Connection Status Check', Description: 'Refreshing the agents\' connection status on-demand' },
  ['ISupportsScreenShare']: { DisplayName: 'Screen sharing', Description: 'Opening a remote screen sharing session with a device' },
  ['ISupportsSupportTicketDetailOverride']: { DisplayName: 'Support Ticket Override', Description: 'Override the default support ticket creation process with a custom implementation' },
  ['ISupportsSyncingAgentsOnDemand']: { DisplayName: 'Sync Agents On Demand', Description: 'Syncing agents from the provider into immy.bot on demand' },
  ['ISupportsTenantInstallToken']: { DisplayName: 'Client Install Token', Description: 'Getting an install token for a specific client' },
  ['ISupportsTenantUninstallToken']: { DisplayName: 'Tenant Uninstall Token', Description: 'Getting a client uninstall token' },
  ['ISupportsUpdatingAgents']: { DisplayName: 'Agent Relocation', Description: 'Updating agent details' },
  ['ISupportsUpdatingClientForAgents']: { DisplayName: 'Updating Client For Agents', Description: 'Updating the external client to which an agent is assigned' },
  ['ISupportsUpgradingAgentVersion']: { DisplayName: 'Agent Update', Description: 'Upgrading the agent version from within immy.bot' },
} as const;

import { DatabaseType } from './enums';
import { ScriptCategory } from './enums';
import { ScriptExecutionContext } from './enums';
import { TargetEnforcement } from './enums';
import { MaintenanceType } from './enums';
import { DesiredSoftwareState } from './enums';
import { SoftwareProviderType } from './enums';
import { MaintenanceTaskMode } from './enums';
import { TargetType } from './enums';
import { TargetCategory } from './enums';
import { TargetGroupFilter } from './enums';
import { HealthStatus } from './enums';
import { IGetProviderClientResponse } from './responses';
import { IGetComputerResponse } from './responses';
import { IGetScheduleResponse } from './responses';
import { AgentIdentificationManualResolutionDecision } from './enums';
import { ComputerOnboardingStatus } from './enums';
import { TimelineEventType } from './enums';
import { SyncState } from './enums';
import { IntegrationTag } from './enums';
import { MediaCategory } from './enums';
import { TargetAssignmentApprovalStatus } from './enums';
import { Relationship } from './enums';
import { MaintenanceItemOrderLocation } from './enums';
import { TargetVisibility } from './enums';
import { ScriptLanguage } from './enums';
import { ScriptOutputType } from './enums';
import { MaintenanceTaskCategory } from './enums';
import { MaintenanceTaskParameterType } from './enums';
import { SoftwareType } from './enums';
import { ChangeRequestObjectType } from './enums';
import { ChangeRequestState } from './enums';
import { AzurePermissionLevel2 } from './enums';
import { AppRegistrationType } from './enums';
import { AccessTokenSource } from './enums';
import { SessionStatus } from './enums';
import { NotificationType } from './notification-types';
import { NotificationAcknowledgement } from './enums';
import { NotificationSeverity } from './enums';
import { RebootPreference } from './enums';
import { PromptTimeoutAction } from './enums';
import { ComputerOfflineMaintenanceSessionBehavior } from './enums';
import { EnqueuedSessionType } from './enums';
import { SubjectQualifier } from './enums';
import { Condition } from './enums';
import { ActionToPerform } from './enums';
import { TimelineObjectType } from './enums';
import { AgentIdentificationLogType } from './enums';
import { MaintenanceActionType } from './enums';
import { MaintenanceActionStatus } from './enums';
import { MaintenanceActionReason } from './enums';
import { MaintenanceActionResult } from './enums';
import { MaintenanceActionResultReason } from './enums';
import { LicenseType } from './enums';
import { UpdateActionType } from './enums';
import { SoftwareLicenseRequirement } from './enums';
import { DetectionMethod } from './enums';
import { SoftwareTableNameSearchMode } from './enums';
import { RepairActionType } from './enums';
import { PackageType } from './enums';
import { SoftwareVersionInstallerType } from './enums';
import { ExpirationTime } from './enums';
import { SessionStageType } from './enums';
import { SessionLogType } from './enums';
import { ActionProgressPhaseName } from './enums';
import { SessionPhaseStatus } from './enums';
import { SoftwareRegistryContext } from './enums';
import { AzTenantType } from './enums';
import { InventoryTaskFrequency } from './enums';
import { Platform } from './enums';
import { ClaimCode } from './enums';
import { ReleaseChannel } from './enums';
import { TargetScope } from './enums';
import { ProviderTypeSource } from './enums';
import { FeatureEntitlementType } from './enums';
import { FeatureResetInterval } from './enums';
import { SubscriptionFeatureStatus } from './enums';
import { SubscriptionFeatureType } from './enums';
import { SubscriptionItemType } from './enums';
import { ScriptReferenceType } from './enums';
import { LogLevel } from './enums';
import { WindowsSessionType } from './enums';
import { RegistryValueKindDto } from './enums';
import { RegistrySearchTargets } from './enums';
import { RegistrySearchResultType } from './enums';
import { PermissionCategory } from './enums';

export class StartEditorServicesRequest implements IStartEditorServicesRequest
{
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public terminalId: string;
  public scriptId?: number;
  public scriptType?: DatabaseType;
  constructor (obj: IStartEditorServicesRequest)
  {
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.terminalId = obj.terminalId;
    this.scriptId = obj.scriptId;
    this.scriptType = obj.scriptType;
  }
}
export interface IStartEditorServicesRequest
{
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  terminalId: string;
  scriptId?: number;
  scriptType?: DatabaseType;
}
export interface IAgentStatusDto
{
  agentId: number;
  providerLinkId: number;
  isConnected: boolean;
  version?: string;
  lastUpdatedUtc: string;
}
export interface IComputerAgentStatusDto
{
  id: number;
  tenantId: number;
  computerName?: string;
  tenantName?: string;
  allAgentsConnected: boolean;
  someConnected: boolean;
  agents: IAgentStatusDto[];
}
export interface IComputerUserAffinityResponse
{
  project: any;
  id: number;
  personId: number;
  date: string;
  personName: string;
  computerId: number;
  computerName: string;
  tenantId: number;
  tenantName: string;
}
export interface ITargetAssignmentResource
{
  id: number;
  databaseType: DatabaseType;
  isCore: boolean;
  excluded: boolean;
  targetEnforcement: TargetEnforcement;
  notes?: string;
  notesUpdatedByUserName?: string;
  notesUpdatedUtc?: string;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
  targetName?: string;
  softwareSemanticVersion?: string;
  softwareSemanticVersionString?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  licenseId?: number;
  licenseName?: string;
  updatedDateUTC: string;
  updatedBy?: string;
  createdDateUTC: string;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  sortOrder: number;
  integrationTypeId?: string;
  integrationPrompt?: string;
  providerLinkIdForMaintenanceItem?: number;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  targetText?: string;
  targetTypeName: string;
  targetScopeName: string;
  targetMissing: boolean;
  tenantMissing: boolean;
  targetTypeMissing: boolean;
  targetTypeDescription: string;
  onboardingOnly: boolean;
  targetGroupFilterName: string;
}
export interface ILinkedExternalLink
{
  providerLinkId: number;
  providerLinkName: string;
  providerTypeId: string;
  isExternalClientLinkingEnabled: boolean;
  isExternalProviderInitializedFromThisProvider: boolean;
}
export interface IGetProviderLinkResponse
{
  id: number;
  disabled: boolean;
  errorMessage?: string;
  healthStatus: HealthStatus;
  healthStatusMessage?: string;
  name: string;
  runScriptPriority: number;
  providerTypeFormData: any;
  inputsWithStoredPasswords?: string[];
  ownerTenantId: number;
  providerTypeId: string;
  providerPluginBaseUrl?: string;
  updatedBy?: number;
  createdBy?: number;
  updatedDateUTC: string;
  createdDateUTC: string;
  includeClientsFailedMessage?: string;
  getFormSchemaFailedMessage?: string;
  getProviderFailedMessage?: string;
  updatedByName?: string;
  latestAgentVersion?: string;
  getLatestAgentVersionFailedMessage?: string;
  supportsDeviceUpdating: boolean;
  providerCapabilities: (keyof typeof ProviderCapabilities)[];
  excludedCapabilities?: (keyof typeof ProviderCapabilities)[];
  supportedCrossProviderClientLinkages: ISupportedCrossProviderLinkage[];
  supportedCrossProviderInitializationLinkages: ISupportedCrossProviderLinkage[];
  providerClients?: IGetProviderClientResponse[];
  computers?: IGetComputerResponse[];
  schedules?: IGetScheduleResponse[];
  providersLinkedFromThisProvider?: ILinkedExternalLink[];
  linkedFromProviders?: ILinkedExternalLink[];
}
export interface IAccessRequestAcknowledgedEvent
{
  acknowledgedByUserId: number;
  accessRequestByPersonId: number;
  description: string;
  newUserId?: number;
  accessGranted: boolean;
}
export interface IAccessRequestedEvent
{
  data: IAccessRequestResponse;
}
export interface IAgentConnectedEvent
{
}
export interface IAgentConnectedSupportingScriptExecutionEvent
{
  computerId?: number;
}
export interface IAgentCreatedEvent
{
  providerAgent: IProviderAgent;
}
export interface IAgentDeletedEvent
{
  id: number;
  computerId?: number;
}
export interface IAgentDisconnectedEvent
{
}
export interface IAgentFinishedIdentificationEvent
{
  providerAgent: IProviderAgent;
  success: boolean;
}
export interface IAgentFinishedResolutionEvent
{
  providerAgent: IProviderAgent;
}
export interface IAgentIdentificationLogAddedEvent
{
  log: IAgentIdentificationLog;
  deviceName: string;
}
export interface IAgentIdentificationResolutionEvent
{
  computerId: number;
  existingAgentId: number;
  decision: AgentIdentificationManualResolutionDecision;
}
export interface IReplaceExistingAgentIdentificationResolutionEvent extends IAgentIdentificationResolutionEvent
{
}
export interface IReplaceExistingComputerIdentificationResolutionEvent extends IAgentIdentificationResolutionEvent
{
  newComputerId: number;
}
export interface IKeepBothAgentsIdentificationResolutionEvent extends IAgentIdentificationResolutionEvent
{
  newComputerId: number;
}
export interface IAgentIdentificationResolutionFailedEvent
{
  computerId: number;
  existingAgentId: number;
  errorMessage: string;
}
export interface IAzureCustomerPreconsentProgressMessageAddedEvent
{
  partnerTenantPrincipalId: string;
  customerTenantPrincipalId: string;
  message: IAzureMessage;
  messageIndex: number;
}
export interface IAzureCustomerPreconsentStartedEvent
{
  partnerTenantPrincipalId: string;
  customerTenantPrincipalId: string;
}
export interface IAzureCustomerPreconsentFinishedEvent
{
  partnerTenantPrincipalId: string;
  customerTenantPrincipalId: string;
  result?: IAzureCustomerPreconsentResult;
}
export interface IAzureMultiCustomerPreconsentFailedEvent
{
  partnerTenantPrincipalId: string;
  error: (IAzureErrorLogItem | string);
}
export interface IAzureMultiCustomerPreconsentFinishedEvent
{
  partnerTenantPrincipalId: string;
}
export interface IAzureErrorEvent
{
  azureErrorLogItem: IAzureErrorLogItem;
  emitNotificationEvent: boolean;
}
export interface IAzureTenantProblemsClearedEvent
{
  azureTenantPrincipalId: string;
}
export interface IAzureTenantProblemsDetectedEvent
{
  azureTenantPrincipalId: string;
  azureTenantProblems: IAzureTenantProblem[];
  hasAzureErrors: boolean;
}
export interface IChangeRequestAcknowledgedEvent
{
  user: any;
  changeRequest: IChangeRequest;
}
export interface IChangeRequestCreatedOrUpdatedEvent
{
  user: any;
  changeRequest: IChangeRequest;
}
export interface IComputerInventoryKeyUpdatedEvent
{
  computerId: number;
  inventoryKey: string;
}
export interface IComputerInventoryKeyUpdateFailedEvent
{
  computerId: number;
  inventoryKey: string;
}
export interface IComputerInventoryKeyUpdatingEvent
{
  computerId: number;
  inventoryKey: string;
}
export interface IComputerOnboardingStatusChangedEvent
{
  computerId: number;
  onboardingStatus: ComputerOnboardingStatus;
}
export interface ICorrelatedOnboardingEvent
{
  onboardingCorrelationId: string;
}
export interface ICorrelatedOnboardingPendingAgentCreatedEvent extends ICorrelatedOnboardingEvent
{
  agent: IProviderAgent;
  providerLinkName: string;
  externalClientName?: string;
}
export interface ICorrelatedOnboardingSessionCreatedEvent extends ICorrelatedOnboardingEvent
{
  sessionId: number;
  computerId: number;
}
export interface ICorrelatedOnboardingComputerCreatedEvent extends ICorrelatedOnboardingEvent
{
  computerId: number;
}
export interface ICorrelatedOnboardingExistingComputerDeterminedEvent extends ICorrelatedOnboardingEvent
{
  computerId: number;
}
export interface ICorrelatedOnboardingAgentIdentificationFailedEvent extends ICorrelatedOnboardingEvent
{
  agentIdentificationFailure: IAgentIdentificationFailure;
}
export interface IEphemeralAgentCircuitBrokenEvent
{
  computerId: number;
}
export interface IEphemeralAgentEvent
{
  computerId: number;
  message: string;
  timelineEventType: TimelineEventType;
}
export interface IFeatureUsageExceededEvent
{
  featureUsage: IFeatureUsage;
}
export interface IImmyBotUpdateRequestedEvent
{
  userId?: number;
  userDisplayName?: string;
  updatingToVersion: string;
}
export interface IIntegrationUnhealthyEvent
{
  providerLinkId: number;
  message: string;
  totalHealthChecksWhileUnhealthy: number;
}
export interface IIntegrationHealthyEvent
{
  providerLinkId: number;
}
export interface IIntegrationRecommendationEvent
{
  migratableDeploymentResults: IMigratableDeploymentResultDto[];
}
export interface ILargeScriptOutputEvent
{
  scriptId?: number;
  scriptType?: DatabaseType;
  scriptName: string;
  totalBytes: number;
  computerId?: number;
}
export interface IMaintenanceActionCompletedEvent extends IMaintenanceActionEvent
{
}
export interface IMaintenanceActionEvent
{
  maintenanceAction: IMaintenanceAction;
}
export interface IMaintenanceActionStartedEvent extends IMaintenanceActionEvent
{
}
export interface IMaintenanceSessionCancelledEvent
{
  sessionId: number;
}
export interface IMaintenanceSessionCompletedEvent
{
  sessionId: number;
}
export interface IProviderAgentsRequiringManualDecisionEvent
{
  count: number;
}
export interface IProviderAuditLogAddedEvent
{
  log: IProviderAuditLog;
}
export interface IProviderClientSyncProgressEvent
{
  providerLinkId: number;
  state: SyncState;
  message?: string;
}
export interface IProviderInitializedEvent
{
  providerLinkId: number;
  provider: IProvider;
}
export interface IRedisReconnectedEvent
{
}
export interface IRetryEphemeralAgentConnectedEvent
{
  computerId: number;
  sessionId: number;
}
export interface IScheduleFailedEvent
{
  scheduleId: number;
  exception: unknown;
}
export interface ITimelineAppEvent
{
  timelineEvent: ITimelineEvent;
}
export interface ITimelineAppEventForComputer extends ITimelineAppEvent
{
  computerId: number;
}
export interface IUnacknowledgedDeploymentsEvent
{
  count: number;
}
export interface IWebHookTriggeredEvent
{
  webHookId: string;
  queryParameters?: { [key:string]: any };
  body?: any;
}
export interface ISessionGroupData
{
  sessionId?: number;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  detectionOnly: boolean;
  errorMessage?: string;
}
export interface IChangeTenantsPayload
{
  targetTenantId: number;
  computersToMove: number[];
}
export interface IDynamicIntegrationTypePayload
{
  name: string;
  enabled: boolean;
  scriptId: number;
  logoId: number;
  docsUrl?: string;
  tag: IntegrationTag;
}
export interface ICreateDynamicIntegrationTypePayload extends IDynamicIntegrationTypePayload
{
  integrationTypeId: string;
}
export interface IUpdateDynamicIntegrationTypePayload extends IDynamicIntegrationTypePayload
{
  id: number;
}
export interface ICreateGlobalMediaPayload extends IMediaPayloadBase
{
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category?: MediaCategory;
}
export interface ICreateLocalMediaPayload extends IMediaPayloadBase
{
  tenants: ITenantMedia[];
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category?: MediaCategory;
}
export interface ICreatePersonPayload extends IPersonPayloadBase
{
  tenantId: number;
  azurePrincipalId?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  onPremisesSecurityIdentifier?: string;
}
export interface ICreatePersonWithRequestedAccessPayload extends IPersonPayloadBase
{
  tenantId: number;
  azurePrincipalId?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
}
export interface IDeleteGlobalSoftwareVersionPayload
{
  softwareId: number;
  semanticVersion: string;
}
export interface IDeleteLocalSoftwareVersionPayload
{
  softwareId: number;
  semanticVersion: string;
}
export interface IDeleteTenantsCmdResponse
{
  success: boolean;
  tenantResults: IDeleteTenantResult[];
  message?: string;
}
export interface IDeleteTenantResult
{
  tenantId: number;
  success: boolean;
  failedReason?: string;
}
export interface IGetSessionStatusCountsPayload
{
  counts: { [key:number]: number };
  total: number;
}
export interface ICreateBrandingPayload
{
}
export interface ICreateGlobalSoftwarePayload
{
  softwarePrerequisites: ISoftwarePrerequisite[];
}
export interface ICreateGlobalSoftwareVersionPayload
{
  softwareId: number;
}
export interface ICreateLocalSoftwarePayload
{
  ownerTenantId?: number;
  tenantSoftware: number[];
  softwarePrerequisites: ISoftwarePrerequisite[];
  detectionScriptType?: DatabaseType;
  autoUpdateScriptType?: DatabaseType;
  maintenanceTaskType?: DatabaseType;
  dynamicVersionsScriptType?: DatabaseType;
  downloadInstallerScriptType?: DatabaseType;
}
export interface ICreateLocalSoftwareVersionPayload
{
  softwareId: number;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
}
export interface ICreateSchedulePayload
{
}
export interface IMediaPayloadBase
{
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category?: MediaCategory;
}
export interface IInventoryDeviceCmdPayload
{
  computerDeviceId: string;
  runOnlyOutdated: boolean;
  inventoryTasks?: [string,DatabaseType][];
  inventoryKeys?: string[];
  strict: boolean;
  degreeOfParallelism: number;
}
export interface IInventoryDeviceCmdResponse
{
  success: boolean;
  failureReason?: string;
  output?: { [key:string]: { [key:string]: string } };
}
export interface IPrioritizeProviderLinkPayload
{
  id: number;
  priority: number;
}
export interface IUpdateBrandingPayload
{
  id: number;
}
export interface IUpdateGlobalSoftwarePayload
{
  softwareId: number;
  softwarePrerequisites: ISoftwarePrerequisite[];
}
export interface IUpdateGlobalSoftwareVersionPayload
{
  softwareId: number;
  currentSemanticVersion?: string;
}
export interface IUpdateLocalSoftwarePayload
{
  softwareId: number;
  ownerTenantId?: number;
  tenantSoftware?: number[];
  softwarePrerequisites?: ISoftwarePrerequisite[];
  autoUpdateScriptType?: DatabaseType;
  detectionScriptType?: DatabaseType;
  maintenanceTaskType?: DatabaseType;
  dynamicVersionsScriptType?: DatabaseType;
  downloadInstallerScriptType?: DatabaseType;
}
export interface IUpdateLocalSoftwareVersionPayload
{
  softwareId: number;
  currentSemanticVersion?: string;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
}
export interface IUpdateProviderLinkPayload
{
  id: number;
  name: string;
  disabled: boolean;
  providerTypeFormData: any;
  excludedCapabilities: string[];
  updatedBy?: number;
}
export interface IUpdateSchedulePayload
{
  id: number;
}
export interface IMergeTenantsPayload
{
  targetTenantId: number;
  tenantsToMerge: number[];
}
export interface IMigrationPreviewResponse
{
  software: IMigratorDataDto[];
  softwareVersions: IMigratorDataDto[];
  maintenanceTasks: IMigratorDataDto[];
  scripts: IMigratorDataDto[];
  softwareHasLocalPrerequisites: boolean;
}
export interface IMigratorDataDto
{
  id: number;
  name: string;
}
export interface IOptionalTargetAssignmentApprovalPayload
{
  targetAssignmentId: number;
  approved: TargetAssignmentApprovalStatus;
  targetType: TargetType;
  target?: string;
  maintenanceType: MaintenanceType;
}
export interface ICreateOptionalTargetAssignmentApprovalPayload extends IOptionalTargetAssignmentApprovalPayload
{
}
export interface IUpdateOptionalTargetAssignmentApprovalPayload extends IOptionalTargetAssignmentApprovalPayload
{
}
export interface IPersonPayloadBase
{
  tenantId: number;
  azurePrincipalId?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
}
export interface IRunScheduleCmdResponse
{
  tenantSessionResults?: { [key:number]: [boolean,string] };
  computerSessionResults?: { [key:number]: [boolean,string] };
}
export interface ICreateTenantTagAuthorizationPayload
{
  tenantId: number;
  relationship: Relationship;
}
export interface ICreateTagPayload
{
  name: string;
  description?: string;
  color?: string;
  tenantTagAuthorizations: ICreateTenantTagAuthorizationPayload[];
}
export interface IUpdateTagPayload
{
  id: number;
  name: string;
  description?: string;
  color?: string;
  tenantTagAuthorizations: ICreateTenantTagAuthorizationPayload[];
}
export interface IUnlinkProviderClientCmdResponse
{
  success: boolean;
  message?: string;
}
export interface IUpdateGlobalMediaPayload extends IMediaPayloadBase
{
  id: number;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category?: MediaCategory;
}
export interface IUpdateLocalMediaPayload extends IMediaPayloadBase
{
  id: number;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category?: MediaCategory;
  tenants: ITenantMedia[];
}
export interface IUpdateMaintenanceItemOrderPayload
{
  id: number;
  toIndex?: number;
  location: MaintenanceItemOrderLocation;
}
export interface IUpdatePersonPayload extends IPersonPayloadBase
{
  id: number;
  tenantId: number;
  azurePrincipalId?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
}
export interface IUpdateRecommendedApprovalPayload
{
  globalTargetAssignmentId: number;
  approved: boolean;
}
export interface IUpdateTenantPayload
{
  id: number;
  name: string;
  slug?: string;
  parentTenantId?: number;
  isMsp?: boolean;
}
export interface IUpdateUserPayload
{
  id: number;
  isAdmin: boolean;
  tenantId: number;
  canManageCrossTenantDeployments: boolean;
  hasManagementAccess: boolean;
}
export interface IBatchUpdateAssignmentRequest
{
  targetAssignmentIds: number[];
  updateArray: IUpdateTargetVisibilityPayload[];
}
export interface ICreateGlobalTargetAssignmentPayload extends ITargetAssignmentDetailsBase
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  softwareSemanticVersion?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  targetGroupFilter: TargetGroupFilter;
  softwareProviderType?: SoftwareProviderType;
  excluded: boolean;
  onboardingOnly: boolean;
  targetEnforcement: TargetEnforcement;
}
export interface ICreateLocalTargetAssignmentPayload extends ILocalTargetAssignmentDetailsBase, ITargetAssignmentDetailsBase, ILocalTargetAssignmentPayload
{
}
export interface IDuplicateTargetAssignmentPayload extends ITargetAssignmentDetailsBase
{
  id?: number;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  maintenanceTaskMode?: MaintenanceTaskMode;
  target?: string;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  targetType: TargetType;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  targetEnforcement: TargetEnforcement;
  softwareSemanticVersion?: string;
  softwareProviderType?: SoftwareProviderType;
  desiredSoftwareState?: DesiredSoftwareState;
  providerLinkId?: number;
  providerLinkIdForMaintenanceItem?: number;
  providerDeviceGroupType?: string;
  providerClientGroupType?: string;
  onboardingOnly: boolean;
  notes?: string;
  excluded: boolean;
}
export interface ILocalTargetAssignmentDetailsBase extends ITargetAssignmentDetailsBase
{
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  licenseId?: number;
  providerLinkIdForMaintenanceItem?: number;
  providerDeviceGroupType?: string;
  providerClientGroupType?: string;
  visibility?: ITargetAssignmentVisibilityPayload;
}
export interface ITargetAssignmentDetailsBase
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  targetEnforcement: TargetEnforcement;
  targetType: TargetType;
  targetCategory: TargetCategory;
  target?: string;
  softwareSemanticVersion?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  targetGroupFilter: TargetGroupFilter;
  excluded: boolean;
  onboardingOnly: boolean;
}
export interface ILocalTargetAssignmentPayload extends ILocalTargetAssignmentDetailsBase, ITargetAssignmentDetailsBase
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  licenseId?: number;
  softwareSemanticVersion?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  targetGroupFilter: TargetGroupFilter;
  providerDeviceGroupType?: string;
  providerClientGroupType?: string;
  softwareProviderType?: SoftwareProviderType;
  excluded: boolean;
  targetEnforcement: TargetEnforcement;
  onboardingOnly: boolean;
  providerLinkIdForMaintenanceItem?: number;
  visibility?: ITargetAssignmentVisibilityPayload;
}
export interface ITargetAssignmentVisibilityPayload
{
  selfService: boolean;
  technicianPod: boolean;
}
export interface IUpdateGlobalTargetAssignmentPayload extends ITargetAssignmentDetailsBase
{
  id: number;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  softwareSemanticVersion?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  targetGroupFilter: TargetGroupFilter;
  excluded: boolean;
  onboardingOnly: boolean;
  targetEnforcement: TargetEnforcement;
}
export interface IUpdateLocalTargetAssignmentPayload extends ILocalTargetAssignmentDetailsBase, ITargetAssignmentDetailsBase, ILocalTargetAssignmentPayload
{
  id: number;
}
export interface IUpdateTargetVisibilityPayload
{
  targetVisibility: TargetVisibility;
  updateStatus?: boolean;
}
export interface ICreateGlobalScriptPayload extends IScriptDetailsBase
{
}
export interface ICreateLocalScriptPayload extends IScriptDetailsBase
{
  tenants: ITenantScript[];
}
export interface IScriptDetailsBase
{
  action: string;
  name: string;
  scriptLanguage: ScriptLanguage;
  scriptExecutionContext: ScriptExecutionContext;
  scriptType: DatabaseType;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  timeout?: number;
}
export interface IUpdateGlobalScriptPayload extends IScriptDetailsBase
{
}
export interface IUpdateLocalScriptPayload extends IScriptDetailsBase
{
  tenants: ITenantScript[];
}
export interface ICreateGlobalMaintenanceTaskPayload extends IMaintenanceTaskBaseWithParameters
{
  name: string;
  databaseType: DatabaseType;
  onboardingOnly: boolean;
  ignoreDuringAutomaticOnboarding: boolean;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  recommended: boolean;
  isConfigurationTask: boolean;
  parameters: IMaintenanceTaskParameterPayload[];
  iconMediaId?: number;
  executeSerially: boolean;
  notes?: string;
  useScriptParamBlock: boolean;
  supersededByTaskId?: number;
  supersededByTaskType?: DatabaseType;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
}
export interface ICreateLocalMaintenanceTaskPayload extends IMaintenanceTaskBaseWithParameters
{
  tenants: ILocalMaintenanceTaskTenantPayload[];
  name: string;
  databaseType: DatabaseType;
  ignoreDuringAutomaticOnboarding: boolean;
  onboardingOnly: boolean;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  recommended: boolean;
  isConfigurationTask: boolean;
  parameters: IMaintenanceTaskParameterPayload[];
  maintenanceTaskCategory: MaintenanceTaskCategory;
  iconMediaId?: number;
  executeSerially: boolean;
  notes?: string;
  useScriptParamBlock: boolean;
  supersededByTaskId?: number;
  supersededByTaskType?: DatabaseType;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
}
export interface IMaintenanceTaskBaseWithParameters
{
  parameters: IMaintenanceTaskParameterPayload[];
}
export interface IMaintenanceTaskParameterPayload
{
  id: number;
  maintenanceTaskId: number;
  name: string;
  dataType: MaintenanceTaskParameterType;
  required: boolean;
  selectableValues: string[];
  notes?: string;
  defaultValue?: string;
  hidden: boolean;
  order: number;
  defaultMediaId?: number;
  defaultMediaDatabaseType?: DatabaseType;
}
export interface IUpdateGlobalMaintenanceTaskPayload extends IMaintenanceTaskBaseWithParameters
{
  id: number;
  name: string;
  databaseType: DatabaseType;
  onboardingOnly: boolean;
  ignoreDuringAutomaticOnboarding: boolean;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  recommended: boolean;
  isConfigurationTask: boolean;
  parameters: IMaintenanceTaskParameterPayload[];
  iconMediaId?: number;
  executeSerially: boolean;
  notes?: string;
  useScriptParamBlock: boolean;
  supersededByTaskId?: number;
  supersededByTaskType?: DatabaseType;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
}
export interface ILocalMaintenanceTaskTenantPayload
{
  tenantId: number;
  relationship: Relationship;
}
export interface IUpdateLocalMaintenanceTaskPayload extends IMaintenanceTaskBaseWithParameters
{
  id: number;
  name: string;
  databaseType: DatabaseType;
  onboardingOnly: boolean;
  ignoreDuringAutomaticOnboarding: boolean;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  recommended: boolean;
  isConfigurationTask: boolean;
  parameters: IMaintenanceTaskParameterPayload[];
  tenants: ILocalMaintenanceTaskTenantPayload[];
  iconMediaId?: number;
  executeSerially: boolean;
  notes?: string;
  useScriptParamBlock: boolean;
  supersededByTaskId?: number;
  supersededByTaskType?: DatabaseType;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
}
export interface ICreateLicensePayload extends ILicensePayloadBase
{
}
export interface ILicensePayloadBase
{
  name: string;
  licenseValue: string;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  softwareName: string;
  semanticVersion?: string;
  tenantId?: number;
  restrictToMajorVersion: boolean;
}
export interface IUpdateLicensePayload extends ILicensePayloadBase
{
  id: number;
}
export interface IAccessRequestedNotificationInput
{
  accessRequest: IAccessRequestResponse;
}
export interface IAzureCustomerPreconsentFailedNotificationInput
{
  partnerTenantPrincipalId: string;
  preconsentResult: IAzureCustomerPreconsentResultNotificationInput;
}
export interface IAzureCustomerPreconsentResultNotificationInput
{
  customerTenantPrincipalId: string;
  messages: IAzureMessageNotificationInput[];
}
export interface IAzureMessageNotificationInput
{
  message: string;
  azureErrorId?: string;
  isErrorNonFatal: boolean;
}
export interface IAzureMultiCustomerPreconsentFailedNotificationInput
{
  partnerTenantPrincipalId: string;
  azureErrorId?: string;
  nonAzureErrorMessage?: string;
}
export interface IAzureTenantProblemsNotificationInput
{
  azureTenantPrincipalId: string;
  azureTenantProblems: IAzureTenantProblem[];
  hasAzureErrors: boolean;
}
export interface IChangeRequestAcknowledgedNotificationInput
{
  changeRequestId: number;
  objectType: ChangeRequestObjectType;
  objectId?: number;
  databaseType: DatabaseType;
  state: ChangeRequestState;
}
export interface IChangeRequestCreatedOrUpdatedNotificationInput
{
  changeRequestId: number;
  objectType: ChangeRequestObjectType;
  objectId?: number;
  databaseType: DatabaseType;
}
export interface IFeatureUsageExceededNotificationInput
{
  featureId: string;
  featureTrackStartDateUtc: string;
}
export interface IIntegrationRecommendationNotificationInput
{
  integrationId: string;
  providerLinkId: number;
  softwareName: string;
  targetAssignmentIds: number[];
}
export interface IIntegrationUnhealthyNotificationInput
{
  unhealthyCount: number;
}
export interface ILargeScriptOutputNotificationInput
{
  scriptId?: number;
  scriptType?: DatabaseType;
  totalBytes: number;
  computerId?: number;
}
export interface IProviderAgentsRequiringManualDecisionNotificationInput
{
  count: number;
}
export interface IScheduleFailedNotificationInput
{
}
export interface ITenantDeletionNotificationInput
{
  tenantId: number;
}
export interface IUnacknowledgedDeploymentNotificationInput
{
  count: number;
}
export interface IUpdateInstanceNotificationInput
{
  updatingToVersion: string;
}
export interface IAccessRequestResponse
{
  id: number;
  personId: number;
  emailAddress?: string;
  azurePrincipalId?: string;
  fullName?: string;
  dateRequestedUTC: string;
}
export interface IApiResponseErrorContent
{
  httpMethod?: string;
  uri?: string;
  statusCode: number;
  content: string;
}
export interface IAudit
{
  id: number;
  userId?: number;
  userAzureId?: string;
  userDisplayName?: string;
  type: string;
  objectType: string;
  objectName?: string;
  dateTimeUtc: string;
  oldValues?: string;
  newValues?: string;
  affectedProperties?: string;
  primaryKey?: string;
  message?: string;
}
export interface IAzureCustomerPreconsentResult
{
  customerPrincipalId: string;
  messages: IAzureMessage[];
}
export interface IAzureError
{
  message: string;
  oDataError?: IODataErrorDetails;
  msalError?: IMsalErrorDetails;
  missingAccessToken?: IMissingAccessTokenDetails;
  apiResponseContent?: IApiResponseErrorContent;
  partnerCenterApiResponseDetails?: IApiResponseErrorContent;
  oauthAccessTokenErrorResponse?: IOauth2AccessTokenErrorResponse;
  credentialDetails?: IAzureTenantTokenCredentialDetails;
  formattedErrorMessage: string;
}
export interface IAzureErrorLogItem
{
  id: string;
  azureError: IAzureError;
  sourceMessage: string;
  tenantPrincipalId?: string;
  oauth2AccessTokenId?: number;
  createdDateUtc: string;
}
export interface IAzureMessage
{
  message: string;
  error?: IAzureErrorLogItem;
  isErrorNonFatal: boolean;
}
export interface IAzureSyncResult
{
  attemptDateUtc: string;
  attemptFailedErrorId?: string;
}
export interface IAzureTenantAuthDetails
{
  azureTenantPrincipalId: string;
  selectedPermissionLevel: AzurePermissionLevel2;
  customAppRegAppId?: string;
  customAppRegSecret?: string;
}
export interface IAzureTenantConsentDetails
{
  consentedWith?: AppRegistrationType;
  consentDateUtc?: string;
}
export interface IAzureTenantInfo
{
  tenantName: string;
  defaultDomainName?: string;
  domainNames?: string[];
}
export interface IAzureTenantLinkDomainFilter
{
  immyTenantId: number;
  azTenantId: string;
  domainName: string;
}
export interface IAzureTenantProblem
{
  message: string;
  description?: string;
  azureErrorId?: string;
}
export interface IAzureTenantTokenCredentialDetails
{
  tenantPrincipalId: string;
  partnerPrincipalId?: string;
  gotAccessTokenFrom?: AccessTokenSource;
  resolvedClientId?: string;
  onlyUsePartnerCenterRefresh: boolean;
  tenantPreferredAzureClientId?: string;
  tenantAzurePermissionLevel?: AzurePermissionLevel2;
}
export interface IComputerListViewModel
{
  createdDate: string;
  id: number;
  deletedAt?: string;
  isOnline?: boolean;
  primaryUserFirstName?: string;
  primaryUserLastName?: string;
  primaryUserEmail?: string;
  tenantName?: string;
  tenantId: number;
  primaryPersonId?: number;
  providerLinkIds?: number[];
  computerTagIds?: number[];
  deviceId: string;
  onboardingStatus: ComputerOnboardingStatus;
  activeSessionStatus?: SessionStatus;
  activeSessionId?: number;
  computerName?: string;
  chassisTypes?: number[];
  operatingSystem?: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  domainRole?: number;
  hasPendingReboot?: boolean;
  domain?: string;
  lastBootTimeUtc?: string;
  internalIpAddress?: string;
  externalIpAddress?: string;
  lastLoggedOnUser?: string;
  isSandbox: boolean;
  excludeFromMaintenance: boolean;
  notes?: string;
  devLabVmClaimExpirationDateUtc?: string;
  devLabVmUnclaimed: boolean;
  devLabVmName?: string;
  isDevLab: boolean;
  lastProviderAgentEventDateUtc?: string;
  licensed?: boolean;
}
export interface IDeploymentParameterValue
{
  allowOverride?: boolean;
  requiresOverride?: boolean;
  value?: any;
}
export interface IDeviceDetails
{
  operatingSystemName?: string;
  manufacturer?: string;
  deviceName?: string;
  serialNumber?: string;
  deviceId?: string;
  osInstallDateUTC?: string;
  machineId?: string;
  isSandbox: boolean;
  domain?: string;
  azureTenantId?: string;
  chassisTypes?: number[];
}
export interface IMaintenanceSpecifier
{
  maintenanceName?: string;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
}
export interface IMaintenanceTaskParameter
{
  id: number;
  maintenanceTaskId: number;
  name: string;
  dataType: MaintenanceTaskParameterType;
  required: boolean;
  selectableValues?: string[];
  notes?: string;
  defaultValue?: string;
  hidden: boolean;
  order: number;
  defaultMediaId?: number;
  defaultMediaDatabaseType?: DatabaseType;
  defaultMedia?: IMedia;
  maintenanceTask?: IMaintenanceTask;
}
export interface IMedia
{
  id: number;
  databaseType: DatabaseType;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  category: MediaCategory;
  tenants: ITenantMedia[];
  localSoftware: ILocalSoftware[];
  globalSoftware: IGlobalSoftware[];
  iconForMaintenanceTasks: IMaintenanceTask[];
  createdByUser?: any;
  updatedByUser?: any;
  dynamicIntegrationTypes: IDynamicIntegrationType[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IMissingAccessTokenDetails
{
  tenantId?: string;
  requiredScopes: string;
}
export interface IMsalErrorDetails
{
  error: string;
  errorDescription: string;
  errorCodes: number[];
  timestamp: string;
  traceId: string;
  correlationId: string;
}
export interface INotification
{
  id: string;
  type: NotificationType;
  objectId?: string;
  title: string;
  description: string;
  inputData?: any;
  outputData?: any;
  acknowledgement: NotificationAcknowledgement;
  acknowledgedByUserId?: number;
  acknowledgedByUserAzureId?: string;
  acknowledgedByUserDisplayName?: string;
  tenantId?: number;
  adminOnly: boolean;
  onlyForUserId?: number;
  triggeredByUserId?: number;
  severity: NotificationSeverity;
  resolved: boolean;
  createdDate: string;
  updatedDate: string;
}
export interface IODataErrorDetails
{
  code: string;
  date?: string;
  requestId: string;
  clientRequestId: string;
  odataType: string;
}
export interface IOauth2AccessToken
{
  id: number;
  consentData: IOauthConsentData;
  allowSilentRefresh: boolean;
  accessTokenId: string;
  tokenType: string;
  accessTokenExpiresAtUtc: string;
  tenantPrincipalId: string;
  refreshTokenId?: string;
  refreshTokenExpiresAtUtc?: string;
  identityTokenId?: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IOauthConsentData
{
  authorizationEndpoint: string;
  tokenEndpoint: string;
  resource?: string;
  scopes?: string;
  clientId: string;
  extraQueryParameters: { [key:string]: string };
}
export interface IOauthConsentParameterValue
{
  accessTokenId: string;
  accessToken: string;
  tokenType: string;
  idToken?: string;
  expiresAtUtc: string;
}
export interface IOauthConsentParameterValueIdentifier
{
  id: number;
  accessTokenId: string;
}
export interface IParameterValue
{
  value?: any;
}
export interface IProviderAuditLog
{
  providerLinkId: number;
  providerLinkName: string;
  methodName: string;
  errorMessage?: string;
  input?: any;
  output?: any;
  correlationId?: string;
  id: string;
  timeUtc: string;
}
export interface ISessionJobArgs
{
  sessionGroupId?: string;
  manuallyTriggeredByUserId?: number;
  manuallyResumedByPersonId?: number;
  manuallyResumed: boolean;
  scheduledId?: number;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  installWindowsUpdates: boolean;
  cacheOnly: boolean;
  rebootPreference: RebootPreference;
  promptTimeoutAction: PromptTimeoutAction;
  autoConsentToReboots: boolean;
  promptTimeoutMinutes: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  maintenanceSchedulingConfiguration?: IMaintenanceSchedulingConfiguration;
  maintenanceOnboardingConfiguration?: IMaintenanceOnboardingConfiguration;
  maintenanceEmailConfiguration?: IMaintenanceEmailConfiguration;
  maintenanceAgentUpdatesConfiguration?: IMaintenanceAgentUpdatesConfiguration;
  maintenanceItem?: IMaintenanceItem;
  deploymentId?: number;
  deploymentType?: DatabaseType;
  cacheGroupId?: string;
  rerunningAction: boolean;
  actionIdToRerun?: number;
  terminalId?: string;
  detectionOnly: boolean;
  runInventoryInDetection: boolean;
  resolutionOnly: boolean;
  inventoryOnly: boolean;
  repair: boolean;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  suppressRebootsDuringBusinessHours: boolean;
  appliedOnConnect: boolean;
  useWinningDeployment: boolean;
  hasSessionFeatureBeenTracked: boolean;
  rerunFromScheduleId?: number;
  enqueuedSessionType?: EnqueuedSessionType;
}
export interface ISoftwarePrerequisite
{
  softwaresForCondition: ISoftwareSpecifier[];
  softwaresToPerformActionOn: ISoftwareSpecifier[];
  subjectQualifier: SubjectQualifier;
  condition: Condition;
  actionToPerform: ActionToPerform;
  softwareSpecifiers: ISoftwareSpecifier[];
}
export interface ISoftwareSpecifier
{
  softwareType: SoftwareType;
  softwareIdentifier: string;
  maintenanceSpecifier: IMaintenanceSpecifier;
}
export interface ITimelineEvent
{
  id: number;
  dateUTC: string;
  eventType: TimelineEventType;
  objectType: TimelineObjectType;
  objectId: string;
  message: string;
  data?: string;
  identifier: string;
}
export interface IMaintenanceItemOrder
{
  id: number;
  maintenanceIdentifier?: string;
  maintenanceType: MaintenanceType;
  sortOrder: number;
  location: MaintenanceItemOrderLocation;
}
export interface ITargetedPerson
{
  personId: number;
  tenantId: number;
  name: string;
  email: string;
}
export interface IComputerInventoryAllSoftware
{
  computerId: number;
  tenantId: number;
  computer?: string;
  tenant?: string;
  softwareName?: string;
  softwareVersion?: string;
}
export interface IComputerInventorySoftware
{
  displayName?: string;
  displayVersion?: string;
  upgradeCode?: string;
  computers: number;
}
export interface IMaintenanceSession
{
  id: number;
  jobId?: string;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  scheduledId?: number;
  sessionStatus: SessionStatus;
  onboarding: boolean;
  scheduledExecutionDate?: string;
  duration?: string;
  usingActiveHours: boolean;
  fullMaintenance: boolean;
  jobArgs: ISessionJobArgs;
  activeSession?: IActiveSession;
  maintenanceActions: IMaintenanceAction[];
  stages: IMaintenanceSessionStage[];
  logs: ISessionLog[];
  phases: ISessionPhase[];
  activities: IMaintenanceActionActivity[];
  computer?: IComputer;
  person?: IPerson;
  tenant?: ITenant;
  createdByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ITargetAssignment
{
  id: number;
  databaseType: DatabaseType;
  isCore: boolean;
  autoApprove: boolean;
  integrationTypeId?: string;
  integrationPrompt?: string;
  excluded: boolean;
  sortOrder: number;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  onboardingOnly: boolean;
  targetEnforcement: TargetEnforcement;
  targetType: TargetType;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  target?: string;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerDeviceGroupType?: string;
  providerClientGroupType?: string;
  providerLinkIdForMaintenanceItem?: number;
  targetName?: string;
  providerLink?: any;
  notes?: ITargetAssignmentNotes;
  visibility?: ITargetAssignmentVisibility;
  softwareSemanticVersion?: string;
  softwareSemanticVersionString?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  licenseId?: number;
  license?: ILicense;
  maintenanceTaskMode?: MaintenanceTaskMode;
  maintenanceTaskParameterValues: IMaintenanceTaskParameterValue[];
  changeRequests: IChangeRequest[];
  parameterValueMigrationErrors?: string;
  softwareSpecifier?: ISoftwareSpecifier;
  maintenanceSpecifier: IMaintenanceSpecifier;
  createdByUser?: any;
  updatedByUser?: any;
  softwareId?: number;
  softwareVersionId: number;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ITenantTagAuthorization
{
  tagId: number;
  tenantId: number;
  relationship: Relationship;
  tenant?: ITenant;
  tag?: ITag;
}
export interface ITenantMedia
{
  tenantId: number;
  mediaId: number;
  relationship: Relationship;
  tenant?: ITenant;
  media?: IMedia;
}
export interface ITenantMaintenanceTask
{
  tenantId: number;
  maintenanceTaskId: number;
  relationship: Relationship;
  tenant?: ITenant;
  maintenanceTask?: IMaintenanceTask;
}
export interface IScriptContextParameters
{
  software?: ISoftware;
  softwareVersion?: ISoftwareVersion;
  targetAssignment?: ITargetAssignment;
  maintenanceTask?: IMaintenanceTask;
  tenant?: ITenant;
  computer?: IComputer;
  user?: any;
  session?: IMaintenanceSession;
  action?: IMaintenanceAction;
  maintenanceTaskParameters: { [key:string]: any };
}
export interface ITenantScript
{
  tenantId: number;
  scriptId: number;
  relationship: Relationship;
  tenant?: ITenant;
  script?: IIDomainScript;
}
export interface IAgentIdentificationLog
{
  id: number;
  providerAgentId: number;
  message: string;
  logType: AgentIdentificationLogType;
  timeUtc: string;
  providerAgent?: IProviderAgent;
}
export interface IChangeRequest
{
  id: number;
  objectType: ChangeRequestObjectType;
  targetAssignmentId?: number;
  scriptId?: number;
  newValuesJson: any;
  state: ChangeRequestState;
  acknowledgedByUserId?: number;
  acknowledgedByUserName?: string;
  comments: IChangeRequestComment[];
  createdByUser?: any;
  updatedByUser?: any;
  targetAssignment?: ITargetAssignment;
  script?: IIDomainScript;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IMaintenanceAction
{
  id: number;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  maintenanceSessionId: number;
  assignmentId?: number;
  assignmentType?: DatabaseType;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
  maintenanceDisplayName?: string;
  actionType: MaintenanceActionType;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskMode?: MaintenanceTaskMode;
  detectedVersion?: string;
  detectedVersionString?: string;
  hasDeterminedDetectedVersion: boolean;
  desiredVersion?: string;
  desiredVersionString?: string;
  hasDeterminedDesiredVersion: boolean;
  maintenanceTaskGetResult?: string;
  hasDeterminedTaskGetResult: boolean;
  taskTestResult?: boolean;
  hasDeterminedTaskTestResult: boolean;
  softwareActionIdForConfigurationTask?: number;
  softwareProviderType: SoftwareProviderType;
  actionStatus: MaintenanceActionStatus;
  actionReason: MaintenanceActionReason;
  actionResult: MaintenanceActionResult;
  actionResultReason?: MaintenanceActionResultReason;
  policyDescription?: string;
  actionResultReasonMessage?: string;
  postMaintenanceTest?: string;
  postMaintenanceTestType?: number;
  postMaintenanceTestResult?: boolean;
  postMaintenanceTestResultMessage?: string;
  softwareType?: SoftwareType;
  maintenanceTaskType?: DatabaseType;
  scriptType: DatabaseType;
  startTime: string;
  endTime: string;
  softwareTableRegexString?: string;
  description: string;
  softwareActionIdForConfigurationTaskOrId: number;
  isStarted: boolean;
  isComplete: boolean;
  maintenanceSession?: IMaintenanceSession;
  logs: ISessionLog[];
  phases: ISessionPhase[];
  dependsOn: IMaintenanceActionDependency[];
  dependents: IMaintenanceActionDependency[];
  dependsOnActions: IMaintenanceAction[];
  activities: IMaintenanceActionActivity[];
  lastAction?: string;
  lastActionResult?: string;
  lastActionType: ScriptLanguage;
  lastActionRmmComputerId?: string;
  completedDetectionStatuses: MaintenanceActionStatus[];
  parameters?: string;
  parentId?: number;
  usesManualProgressControl: boolean;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ILocalSoftware
{
  id: number;
  ownerTenantId?: number;
  softwareType: SoftwareType;
  identifier: string;
  tenantSoftware: ITenantSoftware[];
  softwareVersions: ILocalSoftwareVersion[];
  softwarePrerequisites: ISoftwarePrerequisite[];
  softwareIcon?: IMedia;
  createdByUser?: any;
  updatedByUser?: any;
  customAuditProperties: any[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
  installScript?: IIDomainScript;
  testScript?: IIDomainScript;
  upgradeScript?: IIDomainScript;
  uninstallScript?: IIDomainScript;
  postInstallScript?: IIDomainScript;
  postUninstallScript?: IIDomainScript;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  installScriptId?: number;
  testScriptId?: number;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  name: string;
  licenseRequirement: SoftwareLicenseRequirement;
  installOrder: number;
  hidden: boolean;
  softwareTableName?: string;
  detectionMethod: DetectionMethod;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  agentIntegrationTypeId?: string;
  detectionScript?: IIDomainScript;
  detectionScriptId?: number;
  detectionScriptType?: DatabaseType;
  downloadInstallerScript?: IIDomainScript;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
  autoUpdateScript?: IIDomainScript;
  autoUpdateScriptId?: number;
  autoUpdateScriptType?: DatabaseType;
  upgradeCode?: string;
  maintenanceTask?: IMaintenanceTask;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  notes?: string;
  rebootNeeded: boolean;
  repairType: RepairActionType;
  repairScript?: IIDomainScript;
  repairScriptId?: number;
  repairScriptType?: DatabaseType;
  softwareIconMediaId?: number;
  recommended: boolean;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  maintenanceType: MaintenanceType;
  useSoftwareTableDetection: boolean;
  licenseDescription?: string;
  useDynamicVersions: boolean;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  dynamicVersionsScript?: IIDomainScript;
  relativeCacheSourcePath: string;
  maintenanceSpecifier: IMaintenanceSpecifier;
}
export interface IGlobalSoftware
{
  id: number;
  softwareIcon?: IMedia;
  softwareVersions: IGlobalSoftwareVersion[];
  softwareType: SoftwareType;
  identifier: string;
  maintenanceTaskType?: DatabaseType;
  softwarePrerequisites: ISoftwarePrerequisite[];
  customAuditProperties: any[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
  installScript?: IIDomainScript;
  testScript?: IIDomainScript;
  upgradeScript?: IIDomainScript;
  uninstallScript?: IIDomainScript;
  postInstallScript?: IIDomainScript;
  postUninstallScript?: IIDomainScript;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  installScriptId?: number;
  testScriptId?: number;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  name: string;
  licenseRequirement: SoftwareLicenseRequirement;
  installOrder: number;
  hidden: boolean;
  softwareTableName?: string;
  detectionMethod: DetectionMethod;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  agentIntegrationTypeId?: string;
  detectionScript?: IIDomainScript;
  detectionScriptId?: number;
  detectionScriptType?: DatabaseType;
  downloadInstallerScript?: IIDomainScript;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
  autoUpdateScript?: IIDomainScript;
  autoUpdateScriptId?: number;
  autoUpdateScriptType?: DatabaseType;
  upgradeCode?: string;
  maintenanceTask?: IMaintenanceTask;
  maintenanceTaskId?: number;
  notes?: string;
  rebootNeeded: boolean;
  repairType: RepairActionType;
  repairScript?: IIDomainScript;
  repairScriptId?: number;
  repairScriptType?: DatabaseType;
  softwareIconMediaId?: number;
  recommended: boolean;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  maintenanceType: MaintenanceType;
  useSoftwareTableDetection: boolean;
  licenseDescription?: string;
  useDynamicVersions: boolean;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  dynamicVersionsScript?: IIDomainScript;
  relativeCacheSourcePath: string;
  maintenanceSpecifier: IMaintenanceSpecifier;
}
export interface IDynamicIntegrationType
{
  id: number;
  databaseType: DatabaseType;
  integrationTypeId: string;
  name: string;
  logoId: number;
  enabled: boolean;
  scriptId: number;
  script?: IIDomainScript;
  creationErrorMessage?: string;
  docsUrl?: string;
  tag: IntegrationTag;
  logo?: IMedia;
  secrets?: any;
  createdDate: string;
  updatedDate: string;
}
export interface IPerson
{
  id: number;
  tenantId: number;
  azurePrincipalId?: string;
  firstName?: string;
  lastName?: string;
  emailAddress: string;
  displayName: string;
  primaryComputers: IComputer[];
  additionalComputers: IComputerPerson[];
  detectedComputerSoftware: IDetectedComputerSoftware[];
  personSessions: IMaintenanceSession[];
  user?: any;
  tenant?: ITenant;
  userAffinities: IUserAffinity[];
  tags: ITag[];
  personTags: IPersonTag[];
  onPremisesSecurityIdentifier?: string;
  createdByUser?: any;
  updatedByUser?: any;
  accessRequests: IAccessRequest[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ITenant
{
  id: number;
  name: string;
  slug?: string;
  parentTenantId?: number;
  ownerTenantId?: number;
  active: boolean;
  isMsp: boolean;
  azureTenantLink?: IAzureTenantLink;
  ownerTenant?: ITenant;
  smtpConfig?: ISmtpConfig;
  tenantPreferences?: ITenantPreferences;
  ownedTenants: ITenant[];
  users: any[];
  providerClients: IProviderClient[];
  ownedProviderLinks: any[];
  tenantSoftware: ITenantSoftware[];
  detectedComputerSoftware: IDetectedComputerSoftware[];
  scripts: ITenantScript[];
  media: ITenantMedia[];
  maintenanceTasks: ITenantMaintenanceTask[];
  schedules: ISchedule[];
  computers: IComputer[];
  persons: IPerson[];
  brandings: IBranding[];
  licenses: ILicense[];
  maintenanceSessions: IMaintenanceSession[];
  tags: ITag[];
  tenantTagAuthorizations: ITenantTagAuthorization[];
  tenantTags: ITenantTag[];
  markedForDeletionAtUtc?: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ILocalSoftwareVersion
{
  deprecatedIdField?: number;
  softwareId: number;
  software?: ILocalSoftware;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
  installScript?: IIDomainScript;
  testScript?: IIDomainScript;
  upgradeScript?: IIDomainScript;
  uninstallScript?: IIDomainScript;
  postInstallScript?: IIDomainScript;
  postUninstallScript?: IIDomainScript;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  installScriptId?: number;
  testScriptId?: number;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  url?: string;
  blobName?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  packageHash?: string;
  productCode?: string;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  maintenanceType: MaintenanceType;
  numActionSuccesses: number;
  numActionFailures: number;
  lastResult?: MaintenanceActionResult;
  notes?: string;
  architecture?: number;
  dependsOnSemanticVersion?: string;
}
export interface ILicense
{
  id: number;
  name: string;
  licenseValue: string;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  softwareName: string;
  semanticVersion?: string;
  restrictToMajorVersion: boolean;
  tenantId?: number;
  tenant?: ITenant;
  targetAssignments: ITargetAssignment[];
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ISchedule
{
  id?: number;
  target?: string;
  targetName?: string;
  targetType: TargetType;
  targetGroupFilter: TargetGroupFilter;
  providerDeviceGroupType?: string;
  providerClientGroupType?: string;
  providerLinkId?: number;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  targetCategory: TargetCategory;
  maintenanceType?: MaintenanceType;
  maintenanceIdentifier?: string;
  disabled: boolean;
  day?: number;
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showRunNowButton: boolean;
  showPostponeButton: boolean;
  rebootPreference: RebootPreference;
  promptTimeoutAction: PromptTimeoutAction;
  autoConsentToReboots: boolean;
  promptTimeoutMinutes: number;
  applyWindowsUpdates: boolean;
  showMaintenanceActions: boolean;
  allowAccessToMSPResources: boolean;
  timeZoneInfoId?: string;
  time?: string;
  maintenanceTime?: string;
  customCronExpression?: string;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  suppressRebootsDuringBusinessHours: boolean;
  useComputersTimezoneForExecution: boolean;
  scheduleExecutionAfterActiveHours: boolean;
  createdByUser?: any;
  updatedByUser?: any;
  tenant?: ITenant;
  providerLink?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IBranding
{
  id: number;
  tenantId?: number;
  fromAddress: string;
  startDate?: string;
  endDate?: string;
  ignoreYear?: boolean;
  timeFormat?: string;
  mascotImgUri?: string;
  mascotName?: string;
  logoUri?: string;
  logoAltText?: string;
  backgroundColor: string;
  foregroundColor: string;
  tableHeaderColor: string;
  tableHeaderTextColor: string;
  textColor: string;
  description: string;
  tenant?: ITenant;
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ITag
{
  id: number;
  name: string;
  description?: string;
  color?: string;
  createdByUser?: any;
  updatedByUser?: any;
  computers: IComputer[];
  computerTags: IComputerTag[];
  tenantTags: ITenantTag[];
  tenants: ITenant[];
  persons: IPerson[];
  personTags: IPersonTag[];
  tenantTagAuthorizations: ITenantTagAuthorization[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IRecommendedTargetAssignmentApproval
{
  globalTargetAssignmentId: number;
  approved: boolean;
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IAccessRequest
{
  id: number;
  personId: number;
  dateRequestedUTC: string;
  dateAcknowledgedUTC?: string;
  acknowledgedByUserId?: number;
  granted: boolean;
  isAdmin?: boolean;
  expirationTime?: ExpirationTime;
  message?: string;
  person?: IPerson;
  acknowledgedByUser?: any;
}
export interface IUserSilencedNotification
{
  id: number;
  userId: number;
  notificationType: NotificationType;
  notificationObjectId?: string;
  user?: any;
  createdDate: string;
  updatedDate: string;
}
export interface IChangeRequestComment
{
  id: number;
  comment: string;
  commentedByUsername: string;
  changeRequestId: number;
  changeRequest?: IChangeRequest;
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IUserImpersonation
{
  id: number;
  impersonatorUserId: number;
  impersonatingUserId: number;
  expiresAtUtc: string;
  impersonatorUser?: any;
  impersonatingUser?: any;
}
export interface IActiveSession
{
  maintenanceSessionId: number;
  sessionStatus: SessionStatus;
  maintenanceSession?: IMaintenanceSession;
}
export interface IMaintenanceSessionStage
{
  id: number;
  maintenanceSessionId: number;
  stageStatus: SessionStatus;
  type: SessionStageType;
  jobId?: string;
  maintenanceSession?: IMaintenanceSession;
  logs: ISessionLog[];
  phases: ISessionPhase[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IProviderAgent
{
  id: number;
  providerLinkId: number;
  externalClientId: string;
  externalAgentId: string;
  computerId?: number;
  isOnline: boolean;
  supportsOnlineStatus: boolean;
  supportsRunningScripts: boolean;
  agentVersion?: string;
  dateAddedUTC: string;
  lastUpdatedUTC: string;
  internalData?: any;
  deviceDetails: IDeviceDetails;
  onboardingOptions: IAgentOnboardingOptions;
  requireManualIdentification: boolean;
  providerLink?: any;
  computer?: IComputer;
  providerClient?: IProviderClient;
  identificationFailures: IAgentIdentificationFailure[];
  identificationLogs: IAgentIdentificationLog[];
  isMemberOfInitialDeviceSync: boolean;
  deletedAt?: string;
  deletedReason?: string;
}
export interface IAgentIdentificationFailure
{
  id: number;
  pendingAgentId: number;
  computerId?: number;
  existingAgentId?: number;
  message?: string;
  resolved: boolean;
  manualResolutionDecision?: AgentIdentificationManualResolutionDecision;
  requiresManualResolution: boolean;
  unsupportedDeviceType: boolean;
  featureUsageExceeded: boolean;
  createdDateUTC: string;
  pendingAgent?: IProviderAgent;
  computer?: IComputer;
}
export interface IFeatureUsage
{
  featureId: string;
  featureTrackStartDateUtc: string;
  enabled: boolean;
  count: number;
  maxCount?: number;
  maxCountPerItem?: number;
  items: IFeatureUsageItem[];
  usageExceededEventNotifiedDateUtc?: string;
}
export interface IMaintenanceTask
{
  id: number;
  name: string;
  onboardingOnly: boolean;
  ignoreDuringAutomaticOnboarding: boolean;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  databaseType: DatabaseType;
  executeSerially: boolean;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  recommended: boolean;
  isConfigurationTask: boolean;
  iconMediaId?: number;
  notes?: string;
  useScriptParamBlock: boolean;
  supersededByTaskId?: number;
  supersededByTaskType?: DatabaseType;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
  parameters: IMaintenanceTaskParameter[];
  tenants: ITenantMaintenanceTask[];
  setScript?: IIDomainScript;
  getScript?: IIDomainScript;
  testScript?: IIDomainScript;
  icon?: IMedia;
  maintenanceType: MaintenanceType;
  createdByUser?: any;
  updatedByUser?: any;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface ISessionLog
{
  id: number;
  maintenanceSessionId: number;
  maintenanceSessionStageId?: number;
  maintenanceActionId?: number;
  sessionPhaseId?: number;
  maintenanceActionStatus?: MaintenanceActionStatus;
  sessionLogType: SessionLogType;
  message?: string;
  progressCorrelationId?: string;
  progressPercentComplete?: number;
  progressActivity?: string;
  progressStatus?: string;
  progressSecondsRemaining?: number;
  progressCurrentOperation?: string;
  progressCompleted: boolean;
  time: string;
  updatedTime?: string;
  scriptId?: number;
  script?: string;
  scriptOutput?: string;
  scriptType?: DatabaseType;
  scriptLanguage?: ScriptLanguage;
  scriptExecutionContext?: ScriptExecutionContext;
  immediateUser?: boolean;
  scriptParameters: { [key:string]: any };
  paramBlockParameters: { [key:string]: any };
  scriptTimeout?: number;
  isPrimary: boolean;
  maintenanceSession?: IMaintenanceSession;
  maintenanceSessionStage?: IMaintenanceSessionStage;
  maintenanceAction?: IMaintenanceAction;
  sessionPhase?: ISessionPhase;
}
export interface IComputer
{
  id: number;
  deviceId: string;
  tenantId: number;
  primaryPersonId?: number;
  onboardingStatus: ComputerOnboardingStatus;
  onboardedDateUtc?: string;
  inventoryStartedDate?: string;
  detectionOutdated: boolean;
  computerName?: string;
  chassisTypes?: number[];
  operatingSystem?: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  domainRole?: number;
  hasPendingReboot?: boolean;
  domain?: string;
  lastBootTimeUtc?: string;
  internalIpAddress?: string;
  externalIpAddress?: string;
  lastLoggedOnUser?: string;
  osInstallDate?: string;
  isSandbox: boolean;
  excludedFromUserAffinity: boolean;
  devLabVmClaimExpirationDateUtc?: string;
  devLabVmUnclaimed: boolean;
  devLabVmName?: string;
  excludeFromMaintenance: boolean;
  tenant?: ITenant;
  primaryPerson?: IPerson;
  latestProviderEvent?: IComputerLatestProviderEvent;
  computerNote?: string;
  sessions: IMaintenanceSession[];
  additionalPersons: IComputerPerson[];
  schedules: ISchedule[];
  agents: IProviderAgent[];
  latestInventoryScriptResults: IComputerInventoryTaskScriptResult[];
  agentIdentificationFailures: IAgentIdentificationFailure[];
  detectedSoftware: IDetectedComputerSoftware[];
  tags: ITag[];
  remoteControlRecordings: IRemoteControlRecording[];
  userAffinities: IUserAffinity[];
  computerTags: IComputerTag[];
  deletedAt?: string;
  deletedReason?: string;
  successorComputerId?: number;
  successorComputer?: IComputer;
  predecessorComputers: IComputer[];
  createdDate: string;
  updatedDate: string;
}
export interface ISessionPhase
{
  id: number;
  phaseName: string;
  actionProgressPhaseName?: ActionProgressPhaseName;
  maintenanceSessionId: number;
  maintenanceSessionStageId: number;
  maintenanceActionId?: number;
  status: SessionPhaseStatus;
  progressPercentComplete?: number;
  progressStatus?: string;
  progressCompleted: boolean;
  maintenanceSession?: IMaintenanceSession;
  maintenanceSessionStage?: IMaintenanceSessionStage;
  maintenanceAction?: IMaintenanceAction;
  dateStartedUtc?: string;
  dateCompletedUtc?: string;
  logs: ISessionLog[];
}
export interface ITargetAssignmentNotes
{
  id: number;
  targetAssignmentId: number;
  updatedByName: string;
  notes: string;
  targetAssignment?: ITargetAssignment;
  createdDate: string;
  updatedDate: string;
}
export interface ITargetAssignmentVisibility
{
  targetAssignmentId: number;
  targetAssignment?: ITargetAssignment;
  selfService: boolean;
  technicianPod: boolean;
}
export interface IMaintenanceTaskParameterValue
{
  id: number;
  value?: string;
  deploymentId: number;
  maintenanceTaskType: DatabaseType;
  maintenanceTaskId: number;
  maintenanceTaskParameterId: number;
  mediaId?: number;
  mediaDatabaseType?: DatabaseType;
  deployment?: ITargetAssignment;
  maintenanceTask?: IMaintenanceTask;
  maintenanceTaskParameter?: IMaintenanceTaskParameter;
  media?: IMedia;
  allowOverrideFromComputerOnboarding: boolean;
  parameterName?: string;
  parameterType?: MaintenanceTaskParameterType;
}
export interface IProviderClient
{
  providerLinkId: number;
  externalClientId: string;
  externalClientName: string;
  status?: string;
  internalData?: any;
  linkedToTenantId?: number;
  linkedToTenant?: ITenant;
  providerLink?: any;
  hasCompletedInitialAgentSync: boolean;
  types?: string[];
  providerAgents: IProviderAgent[];
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IProviderLinkCrossReference
{
  isExternalClientLinkingEnabled: boolean;
  isProviderLink2InitializedFromProviderLink1: boolean;
  providerLink1Id: number;
  providerLink2Id: number;
  providerLink1?: any;
  providerLink2?: any;
}
export interface IProviderLinkInternalData
{
  providerLinkId: number;
  internalData?: any;
  providerLink?: any;
}
export interface ISoftware
{
  identifier: string;
  softwareType: SoftwareType;
  name: string;
  licenseRequirement: SoftwareLicenseRequirement;
  installOrder: number;
  hidden: boolean;
  softwareTableName?: string;
  detectionMethod: DetectionMethod;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  agentIntegrationTypeId?: string;
  detectionScript?: IIDomainScript;
  detectionScriptId?: number;
  detectionScriptType?: DatabaseType;
  downloadInstallerScript?: IIDomainScript;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
  autoUpdateScript?: IIDomainScript;
  autoUpdateScriptId?: number;
  autoUpdateScriptType?: DatabaseType;
  upgradeCode?: string;
  maintenanceTask?: IMaintenanceTask;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  notes?: string;
  rebootNeeded: boolean;
  repairType: RepairActionType;
  repairScript?: IIDomainScript;
  repairScriptId?: number;
  repairScriptType?: DatabaseType;
  softwareIconMediaId?: number;
  recommended: boolean;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  softwarePrerequisites: ISoftwarePrerequisite[];
  maintenanceType: MaintenanceType;
  useSoftwareTableDetection: boolean;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  installScript?: IIDomainScript;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  testScript?: IIDomainScript;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
  upgradeScript?: IIDomainScript;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  uninstallScript?: IIDomainScript;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postInstallScript?: IIDomainScript;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  postUninstallScript?: IIDomainScript;
  testRequired: boolean;
  testFailedError?: string;
  upgradeStrategy: UpdateActionType;
  licenseType: LicenseType;
  licenseDescription?: string;
  useDynamicVersions: boolean;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  dynamicVersionsScript?: IIDomainScript;
  relativeCacheSourcePath: string;
  maintenanceSpecifier: IMaintenanceSpecifier;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IMaintenanceActionDependency
{
  dependentId: number;
  dependsOnId: number;
  dependent?: IMaintenanceAction;
  dependsOn?: IMaintenanceAction;
}
export interface ITenantSoftware
{
  tenantId: number;
  softwareId: number;
  tenant?: ITenant;
  software?: ILocalSoftware;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IComputerPerson
{
  computerId: number;
  personId: number;
  computer?: IComputer;
  person?: IPerson;
}
export interface IDetectedComputerSoftware
{
  id: number;
  context: SoftwareRegistryContext;
  displayIcon?: string;
  displayName: string;
  displayVersion?: string;
  globalSoftwareId?: number;
  globalSoftwareName?: string;
  installDate?: string;
  platform?: string;
  productCode?: string;
  quietUninstallString?: string;
  systemComponent?: number;
  uninstallString?: string;
  upgradeCode?: string;
  userName?: string;
  userSid?: string;
  detectedAt: string;
  computer?: IComputer;
  computerId: number;
  tenant?: ITenant;
  tenantId: number;
  primaryPerson?: IPerson;
  primaryPersonId?: number;
  registryPath?: string;
  installLocation?: string;
  originalKey: string;
}
export interface IUserAffinity
{
  id: number;
  computerId: number;
  computer?: IComputer;
  person?: IPerson;
  personId: number;
  date: string;
}
export interface IPersonTag
{
  entityId: number;
  tagId: number;
  person?: IPerson;
  tag?: ITag;
}
export interface IComputerTag
{
  entityId: number;
  tagId: number;
  computer?: IComputer;
  tag?: ITag;
}
export interface IDynamicIntegrationTypeProperties
{
  providerTypeId: string;
  databaseType: DatabaseType;
  name: string;
  logoMediaId: number;
  secrets?: { [key: string]: any };
  docsUrl?: string;
}
export interface IInventoryTaskScript
{
  inventoryTaskId: number;
  inventoryKey: string;
  scriptId: number;
  fromProvider: boolean;
  saveInformationStream: boolean;
  saveWarningStream: boolean;
  saveVerboseStream: boolean;
  saveDebugStream: boolean;
  inventoryTask?: IInventoryTask;
  script?: IIDomainScript;
  createdDate: string;
  updatedDate: string;
}
export interface IMaintenanceTaskParameterValueDetails
{
  id: number;
  value?: string;
  deploymentId: number;
  maintenanceTaskType: DatabaseType;
  maintenanceTaskId: number;
  maintenanceTaskParameterId: number;
  mediaId?: number;
  mediaDatabaseType?: DatabaseType;
  allowOverrideFromComputerOnboarding: boolean;
  parameterName?: string;
  parameterType?: MaintenanceTaskParameterType;
}
export interface ISoftwareVersion
{
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  testRequired: boolean;
  url?: string;
  blobName?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  testFailedError?: string;
  packageHash?: string;
  productCode?: string;
  upgradeStrategy: UpdateActionType;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  maintenanceType: MaintenanceType;
  numActionSuccesses: number;
  numActionFailures: number;
  lastResult?: MaintenanceActionResult;
  notes?: string;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  installScript?: IIDomainScript;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  testScript?: IIDomainScript;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
  upgradeScript?: IIDomainScript;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  uninstallScript?: IIDomainScript;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postInstallScript?: IIDomainScript;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  postUninstallScript?: IIDomainScript;
  licenseType: LicenseType;
  architecture?: number;
  dependsOnSemanticVersion?: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IGlobalSoftwareVersion
{
  deprecatedIdField?: number;
  softwareId: number;
  software?: IGlobalSoftware;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
  installScript?: IIDomainScript;
  testScript?: IIDomainScript;
  upgradeScript?: IIDomainScript;
  uninstallScript?: IIDomainScript;
  postInstallScript?: IIDomainScript;
  postUninstallScript?: IIDomainScript;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  installScriptId?: number;
  testScriptId?: number;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  url?: string;
  blobName?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  packageHash?: string;
  productCode?: string;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  maintenanceType: MaintenanceType;
  numActionSuccesses: number;
  numActionFailures: number;
  lastResult?: MaintenanceActionResult;
  notes?: string;
  architecture?: number;
  dependsOnSemanticVersion?: string;
}
export interface IAzureTenantLink
{
  immyTenantId: number;
  azTenantId: string;
  shouldLimitDomains: boolean;
  azureTenant?: IAzureTenant;
  tenant?: ITenant;
  limitToDomains: IAzureTenantLinkDomainFilter[];
}
export interface ISmtpConfig
{
  tenantId: number;
  port: number;
  host: string;
  enableSSL: boolean;
  timeout: number;
  username?: string;
  passwordHash?: string;
  enabled: boolean;
  useAuthentication: boolean;
  tenant?: ITenant;
}
export interface ITenantTag
{
  entityId: number;
  tagId: number;
  tenant?: ITenant;
  tag?: ITag;
}
export interface IFeatureUsageItem
{
  value: string;
  count: number;
}
export interface IComputerLatestProviderEvent
{
  computerId: number;
  lastProviderAgentEventDateUtc: string;
  computer?: IComputer;
}
export interface IAzureTenant
{
  principalId: string;
  consentDetails: IAzureTenantConsentDetails;
  azureTenantType: AzTenantType;
  partnerPrincipalId?: string;
  infoSyncedFromAzure?: IAzureTenantInfo;
  lastGetTenantInfoSyncResult?: IAzureSyncResult;
  lastGetUsersSyncResult?: IAzureSyncResult;
  parentPartner?: IAzureTenant;
  azureTenantLinks: IAzureTenantLink[];
}
export interface IInventoryTask
{
  id: number;
  inventoryTaskType: DatabaseType;
  fromProvider: boolean;
  name: string;
  frequency: InventoryTaskFrequency;
  specifiedNumMinutes?: number;
  scripts: IInventoryTaskScript[];
  frequencyTimespan: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IRemoteControlRecording
{
  id: number;
  mimeContentType: string;
  blobName: string;
  computer?: IComputer;
  computerId: number;
  tenant?: ITenant;
  tenantId: number;
  user?: any;
  userId: number;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
}
export interface IComputerInventoryTaskScriptResult
{
  timestamp: string;
  computerId: number;
  inventoryKey: string;
  latestSuccessResult?: any;
  latestErrorResult?: any;
  latestResultIsError: boolean;
  computer?: IComputer;
}
export interface IUserAuthInfo
{
  userEmailAddress?: string;
  userDisplayName?: string;
  expirationSecondsSinceEpoch: number;
  expirationDate: string;
}
export interface IIDomainScript
{
  name: string;
  id: number;
  action: string;
  scriptLanguage: ScriptLanguage;
  timeout?: number;
  scriptExecutionContext: ScriptExecutionContext;
  scriptType: DatabaseType;
  readOnly: boolean;
  identifier: string;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  publicStorageDownloadUrl?: string;
  scriptHash?: string;
  scriptCacheName?: string;
  variables: { [key:string]: any };
  parameters: { [key:string]: any };
  parameterOverrides: { [key:string]: any };
  dynamicProviderStoreId?: string;
  dynamicIntegrationTypeProperties?: IDynamicIntegrationTypeProperties;
  providerLinkIdForMaintenanceItem?: number;
  tenants: ITenantScript[];
  changeRequests: IChangeRequest[];
  createdByUser?: any;
  updatedByUser?: any;
  hidden: boolean;
  errorActionPreference: number;
  skipPreflight: boolean;
  skipBusinessHoursCheck: boolean;
  dynamicIntegrationType?: IDynamicIntegrationType;
  inventoryTaskScript?: IInventoryTaskScript;
}
export interface IAgentOnboardingOptions
{
  automaticallyOnboard: boolean;
  onboardingCorrelationId?: string;
  onboardingSessionRebootPreference?: RebootPreference;
  onboardingSessionPromptTimeoutAction?: PromptTimeoutAction;
  onboardingSessionAutoConsentToReboots: boolean;
  onboardingSessionPromptTimeoutMinutes: number;
  onboardingSessionSendFollowUpEmail: boolean;
  primaryPersonId?: number;
  additionalPersonIds?: number[];
  tags?: number[];
  isDevLab: boolean;
}
export interface IAgentUpdateFormSchema
{
  inputs: IAgentUpdateFormSchemaInput[];
}
export interface IAgentUpdateFormSchemaInput
{
  name: string;
  type: string;
  required: boolean;
  label: string;
  description: string;
  default: any;
  deviceGroupTypeId?: string;
}
export interface IGetBashInstallScriptParameters
{
  platform: Platform;
  targetExternalClientId: string;
}
export interface IGetBashInstallScriptParametersWithOnboardingOptions
{
  platform: Platform;
  targetExternalClientId: string;
  onboardingOptions: IAgentOnboardingOptions;
}
export interface IGetExecutableUriParameters
{
  targetExternalClientId: string;
}
export interface IGetExecutableUriParametersWithOnboardingOptions
{
  targetExternalClientId: string;
  onboardingOptions: IAgentOnboardingOptions;
}
export interface IGetPowerShellInstallScriptParameters
{
  platform: Platform;
  targetExternalClientId: string;
}
export interface IGetPowerShellInstallScriptParametersWithOnboardingOptions
{
  platform: Platform;
  targetExternalClientId: string;
  onboardingOptions: IAgentOnboardingOptions;
}
export interface IGetProvisioningPackageUriParameters
{
  targetExternalClientId: string;
  ppkgOptions: IProvisioningPackageOptions;
}
export interface IGetProvisioningPackageUriParametersWithOnboardingOptions
{
  targetExternalClientId: string;
  ppkgOptions: IProvisioningPackageOptions;
  onboardingOptions: IAgentOnboardingOptions;
}
export interface IClientGroup
{
  clientGroupDisplayName: string;
  clientGroupId: string;
}
export interface IClientStatus
{
  clientStatusDisplayName: string;
  clientStatusId: string;
}
export interface IClientType
{
  clientTypeDisplayName: string;
  clientTypeId: string;
}
export interface IDeviceGroup
{
  deviceGroupDisplayName: string;
  deviceGroupId: string;
}
export interface IProviderAgentDetails
{
  agentVersion?: string;
  externalClientId: string;
  externalAgentId: string;
  internalData?: any;
  isOnline: boolean;
  lastUpdatedUTC: string;
  supportsRunningScripts: boolean;
  deviceDetails: IDeviceDetails;
  onboardingOptions: IAgentOnboardingOptions;
  requireManualIdentification: boolean;
}
export interface IScript
{
  scriptLanguage: ScriptLanguage;
  script: string;
  timeout: number;
}
export interface IProviderTypeFormSchema
{
  inputs: IProviderTypeFormSchemaInput[];
}
export interface IProviderTypeFormSchemaInput
{
  name: string;
  type: string;
  required: boolean;
  label: string;
  description: string;
  default?: any;
  validationRegexes: { [key:string]: string };
}
export interface IProvisioningPackageOptions
{
  encryptPackage: boolean;
  packagePassword?: string;
  downloadISO: boolean;
  wirelessOpen?: boolean;
  wirelessSSID?: string;
  wirelessKey?: string;
  setupAdmin: boolean;
  localAdminUsername?: string;
  localAdminPassword?: string;
  hideAdminAccount: boolean;
  enableCleanPC: boolean;
  disableHibernation: boolean;
  setupWireless: boolean;
}
export interface ISupportedCrossProviderLinkage
{
  providerTypeId: string;
  description: string;
}
export interface IVerifyProviderResult
{
  wasSuccessful: boolean;
  message: string;
}
export interface IApdexMetric
{
  frustrating: number;
  sampleSize: number;
  satisfied: number;
  score: number;
  tolerating: number;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface ICounterMetric
{
  count: number;
  items: ISetItem[];
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface ISetItem
{
  count: number;
  item: string;
  percent: number;
}
export interface IGaugeMetric
{
  value?: number;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface IHistogramMetric
{
  count: number;
  sum: number;
  lastUserValue: string;
  lastValue: number;
  max: number;
  maxUserValue: string;
  mean: number;
  median: number;
  min: number;
  minUserValue: string;
  percentile75: number;
  percentile95: number;
  percentile98: number;
  percentile99: number;
  percentile999: number;
  sampleSize: number;
  stdDev: number;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface IMeterMetric
{
  count: number;
  fifteenMinuteRate: number;
  fiveMinuteRate: number;
  items: ISetItem[];
  meanRate: number;
  oneMinuteRate: number;
  rateUnit: string;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface ISetItem
{
  count: number;
  fifteenMinuteRate: number;
  fiveMinuteRate: number;
  item: string;
  meanRate: number;
  oneMinuteRate: number;
  percent: number;
}
export interface IMetricData
{
  contexts: IMetricsContext[];
  timestamp: string;
}
export interface IMetricsContext
{
  apdexScores: IApdexMetric[];
  context: string;
  counters: ICounterMetric[];
  gauges: IGaugeMetric[];
  histograms: IHistogramMetric[];
  bucketHistograms: IBucketHistogramMetric[];
  meters: IMeterMetric[];
  timers: ITimerMetric[];
  bucketTimers: IBucketTimerMetric[];
}
export interface ITimerMetric
{
  activeSessions: number;
  count: number;
  durationUnit: string;
  histogram: IHistogramData;
  rate: IRateData;
  rateUnit: string;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface IHistogramData
{
  lastUserValue: string;
  lastValue: number;
  max: number;
  maxUserValue: string;
  mean: number;
  median: number;
  min: number;
  minUserValue: string;
  percentile75: number;
  percentile95: number;
  percentile98: number;
  percentile99: number;
  percentile999: number;
  sampleSize: number;
  stdDev: number;
  sum: number;
}
export interface IRateData
{
  fifteenMinuteRate: number;
  fiveMinuteRate: number;
  meanRate: number;
  oneMinuteRate: number;
}
export interface IBucketHistogramMetric
{
  count: number;
  sum: number;
  buckets: { [key:number]: number };
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface IBucketTimerMetric
{
  activeSessions: number;
  count: number;
  durationUnit: string;
  histogram: IBucketHistogramData;
  rate: IRateData;
  rateUnit: string;
  name: string;
  tags: { [key:string]: string };
  unit: string;
}
export interface IBucketHistogramData
{
  buckets: { [key:number]: number };
  sum: number;
}
export interface IRateData
{
  fifteenMinuteRate: number;
  fiveMinuteRate: number;
  meanRate: number;
  oneMinuteRate: number;
}
export interface IApplicationPreferences
{
  id: number;
  enableOnboarding: boolean;
  enableAzureUserSync: boolean;
  defaultEmailBccList?: IDefaultEmailBccList;
  enableNiniteIntegration: boolean;
  defaultBrandingId?: number;
  enableUserAffinitySync: boolean;
  enableSessionEmails: boolean;
  defaultBranding?: IBranding;
  defaultScriptTimeouts: IDefaultScriptTimeouts;
  useImmyBotChocolateyFeed: boolean;
  hideChocolateyPackages: boolean;
  overwriteExistingDeviceIfOSIsNew: boolean;
  enableNonEssentialDeviceInventory: boolean;
  requireConsentForExternalSessionProviders: boolean;
  allowNonAdminsToManageAssignments: boolean;
  showGettingStartedWizard: boolean;
  enableHistoricalInventory: boolean;
  defaultTimeZone?: string;
  immyScriptPath?: string;
  enableRequestAccess: boolean;
  enableEphemeralAgentDebugMode: boolean;
  staleComputersLastAgentConnectionAgeDays: number;
  allowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts: boolean;
  enablePreflightScripts: boolean;
  enableImmyBotRemoteControl: boolean;
  enableImmyBotRemoteControlRecording: boolean;
  enableProviderAuditLogging: boolean;
  mspNonAdminsRequireChangeRequestsForCrossTenantDeployments: boolean;
  enableMaintenanceActionActivities: boolean;
  enableAutomaticImmyBotReleaseUpdates: boolean;
  automaticImmyBotReleaseUpdateHour: number;
  daysToWaitBeforeAutomaticImmyBotUpdate: number;
  runScheduledInventoryAsMaintenanceSessions: boolean;
  enableBetaDynamicIntegrationMigrations: boolean;
  enableUserImpersonation: boolean;
  disconnectLeastActiveEditorServiceWhenLimitReached: boolean;
}
export interface IDefaultScriptTimeouts
{
  detection: number;
  action: number;
  install: number;
  uninstall: number;
  upgrade: number;
  autoUpdateJob: number;
  dynamicVersions: number;
}
export interface ITenantPreferences
{
  id: number;
  tenantId: number;
  enableOnboarding: boolean;
  enableSessionEmails: boolean;
  enableOnboardingPatching: boolean;
  tenant?: ITenant;
  defaultEmailBccList?: IDefaultEmailBccList;
  overwriteExistingDeviceIfOSIsNew: boolean;
  requireConsentForExternalSessionProviders?: boolean;
  businessHoursStart?: string;
  businessHoursEnd?: string;
  timeZoneInfoId?: string;
  excludeFromCrossTenantDeploymentsAndSchedules: boolean;
  enableUserAffinitySync: boolean;
  enableImmyBotRemoteControl?: boolean;
  enableImmyBotRemoteControlRecording?: boolean;
}
export interface IUserPreferences
{
  id: number;
  userId: number;
  maskPiiData: boolean;
}
export interface IDefaultEmailBccList
{
  emails: string[];
}
export interface IAzureCustomerCheckAccessResult
{
  customerPrincipalId: string;
  expectedRoleAssignments: IExpectedRoleAssignment[];
}
export interface IAzureTenantCustomer
{
  tenantId: string;
  displayName: string;
  domain: string;
  gdapRelationships: IAzureGdapRelationshipDetails[];
}
export interface IAzureTenantCustomersResult
{
  partnerPrincipalId: string;
  result: (IAzureTenantCustomer[] | IAzureError);
}
export interface IAzureTenantDetailsSyncResult
{
  principalId: string;
  wasSuccessful: boolean;
  failedReason?: IAzureError;
  tenantInformationSynced?: IAzureTenantInfo;
}
export interface IAzureTenantUserSyncResult
{
  principalId: string;
  wasSuccessful: boolean;
  failedReason?: IAzureError;
  numUsersSynced: number;
  immyTenantsSyncedUsers: { [key:number]: number };
}
export interface IGetPartnerCenterOrgResult
{
  result: (IPartnerOrganizationProfile | IAzureError);
}
export interface IMultiCustomerPreconsentResult
{
  partnerPrincipalId: string;
  result: (IAzureCustomerPreconsentResult[] | IAzureError);
}
export interface ITenantInfoResult
{
  principalId: string;
  result: (IAzureTenantInfo | IAzureError);
}
export interface IAzureGdapRelationshipDetails
{
  id: string;
  displayName: string;
  status: string;
  durationNumDays?: number;
  createdDateTimeUtc?: string;
  endDateTimeUtc?: string;
  autoExtendDuration?: string;
  roles: IGdapRelationshipRole[];
  accessAssignments: IGdapRelationshipAccessAssignment[];
}
export interface IGdapRelationshipRole
{
  roleDefinitionId: string;
  roleDefinitionName?: string;
}
export interface IGdapRelationshipAccessAssignment
{
  accessContainerId: string;
  accessContainerType: string;
}
export interface IBillingPlatformDetails
{
  siteName: string;
}
export interface IClaimDevLabVmResponse
{
  devLabVmName?: string;
  claimExpirationDateUtc?: string;
  code: ClaimCode;
  message?: string;
}
export interface IGetIpAddressesResponse
{
  success: boolean;
  message: string;
  ipAddresses: string[];
}
export interface IGetProductCatalogItemsResponse
{
  plans: ISubscriptionItemBasicInfo[];
  addons: ISubscriptionItemBasicInfo[];
  features: ISubscriptionFeatureBasicInfo[];
}
export interface IGetRdpInfoResponse
{
  rdpFileName: string;
  rdpFileContent: string;
  rdpPassword: string;
}
export interface IUpdateReleaseChannelRequest
{
  releaseChannel: ReleaseChannel;
}
export interface IChangeRequestDiff
{
  oldDeploymentDescription?: string;
  newDeploymentDescription?: string;
  parameterDiffs: IChangeRequestParameterDiff[];
}
export interface IChangeRequestParameterDiff
{
  name: string;
  oldValue?: IDeploymentParameterValue;
  newValue?: IDeploymentParameterValue;
}
export interface IChocoPackageVersion
{
  id: string;
  version: string;
  title: string;
  semanticVersion?: INuGetVersion;
}
export interface IChocoSearchResult
{
  id: string;
  title: string;
}
export interface IClientGroupTypeDto
{
  clientGroupTypeId: string;
  providerTypeId: string;
  displayName: string;
  description: string;
}
export interface IDeviceGroupTypeDto
{
  deviceGroupTypeId: string;
  providerTypeId: string;
  displayName: string;
  description: string;
  targetScope: TargetScope;
}
export interface IGetCommandResult
{
  commandType: number;
  name: string;
}
export interface IMigratableDeploymentResultDto
{
  providerTypeId: string;
  providerLinkId: number;
  softwareName: string;
  targetAssignmentIds: number[];
}
export interface IProviderTypeDto
{
  providerTypeId: string;
  source: ProviderTypeSource;
  isDynamic: boolean;
  tag: string;
  displayName: string;
  configurationForm?: IDynamicFormBindResultWithConvertedParameters;
  logoSrc?: string;
  docsUrl?: string;
  screenShareLogoSrc?: string;
  canManage: boolean;
  deviceGroupTypes: IDeviceGroupTypeDto[];
  clientGroupTypes: IClientGroupTypeDto[];
  supportsDeviceUpdating: boolean;
  providerCapabilities: (keyof typeof ProviderCapabilities)[];
  agentUpdateFormSchema?: IAgentUpdateFormSchema;
  supportedCrossProviderClientLinkages: ISupportedCrossProviderLinkage[];
  supportedCrossProviderInitializationLinkages: ISupportedCrossProviderLinkage[];
}
export interface ICommandResult
{
  success: boolean;
  message?: string;
}
export interface IComputerDto
{
  computerId: number;
  deployments: IComputerInDeploymentDto[];
}
export interface IComputerInDeploymentDto
{
  deploymentId: number;
  databaseType: DatabaseType;
}
export interface IResolveAssignmentsForMaintenanceItemResult
{
  computers: IComputerDto[];
  items: IResolveAssignmentsForMaintenanceItemResultItem[];
}
export interface IResolveAssignmentsForMaintenanceItemResultItem
{
  deploymentId: number;
  databaseType: DatabaseType;
  deploymentPhrase: string;
}
export interface IComputerSearch
{
  id: number;
  name: string;
  tenant: string;
  tenantId: number;
  online: boolean;
  updatedDate: string;
  excludeFromMaintenance: boolean;
}
export interface IDeleteAzureTenantAuthDetailsCmdPayload
{
  azureTenantPrincipalId: string;
}
export interface IDeleteAzureTenantAuthDetailsCmdResult
{
  defaultAppRegistrationUpdated: boolean;
}
export interface IMigrateToSupersedingAssignmentRequest
{
  oldAssignmentId: number;
  newTaskId: number;
  newTaskType: DatabaseType;
}
export interface IMigrateToSupersedingAssignmentResponse
{
  targetAssignment: ITargetAssignment;
}
export interface IUpdateAzureTenantAuthDetailsCmdPayload
{
  authDetails: IAzureTenantAuthDetails;
}
export interface IUpdateAzureTenantAuthDetailsCmdResult
{
  authDetails: IAzureTenantAuthDetails;
  defaultAppRegistrationUpdated: boolean;
}
export interface IDynamicFormBindResult
{
  bindErrors: { [key:string]: string };
  showCommandInfo: IShowCommandInfo;
  parameterSetName: string;
  hasErrors: boolean;
}
export interface IDynamicFormBindResultWithConvertedParameters
{
  convertedParameters: { [key:string]: any };
  bindErrors: { [key:string]: string };
  showCommandInfo: IShowCommandInfo;
  parameterSetName: string;
  hasErrors: boolean;
}
export interface IParameter
{
  name: string;
  displayName?: string;
  helpText?: string;
  showRawHelpText: boolean;
  onlyForHelpText: boolean;
  defaultValue?: any;
  defaultValueText?: string;
  hidden: boolean;
  isMandatory: boolean;
  valueFromPipeline: boolean;
  isValueStripped: boolean;
  position: number;
  allowNull: boolean;
  parameterType: IParameterType;
  hasParameterSet: boolean;
  validParamSetValues: string[];
  hasDropdownParameterSet: boolean;
  validDropdownValues: IParameterDropdownTextValue[];
  showDropdownAsRadioButtons: boolean;
  oauthConsentParameterData?: IOauthConsentData;
}
export interface IParameterSet
{
  name: string;
  isDefault: boolean;
  parameters: IParameter[];
}
export interface IParameterType
{
  fullName: string;
  name: string;
  isEnum: boolean;
  isArray: boolean;
  enumValues?: any;
  hasFlagAttribute: boolean;
  elementType?: any;
  implementsDictionary: boolean;
}
export interface IShowCommandInfo
{
  definition: string;
  parameterSets: IParameterSet[];
}
export interface IParameterDropdownTextValue
{
  text: string;
  value: string;
}
export interface IExpectedRoleAssignment
{
  roleDefinitionId: string;
  roleName: string;
  result: (IRoleAssignment | IAzureError);
}
export interface IPartnerOrganizationProfile
{
  companyName: string;
  domain: string;
  tenantId: string;
  profileType: string;
}
export interface IFeature
{
  enabled: boolean;
  name: string;
}
export interface IFeatureEnabledFromSubscription
{
  featureId: string;
  featureEntitlementType: FeatureEntitlementType;
  isUsageBased: boolean;
  featureUsage?: IFeatureUsageDetails;
  featureCustomValue?: string;
}
export interface IFeatureUsageDetails
{
  maxCount?: number;
  maxCountPerItem?: number;
  resetInterval?: FeatureResetInterval;
  featureTrackStartDateUtc?: string;
  itemCount?: number;
}
export interface IPopulatedTargetGroup
{
  targetText?: string;
  targetTypeName: string;
  targetScopeName: string;
  targetMissing: boolean;
  tenantMissing: boolean;
  targetTypeMissing: boolean;
  targetTypeDescription: string;
  targetType: TargetType;
  onboardingOnly: boolean;
  target?: string;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  targetGroupFilterName: string;
}
export interface IGetCustomerPortalSessionResult
{
  portalSessionId: string;
  token: string;
  accessUrl: string;
  status: number;
  createdAt: string;
  expiresAt?: string;
  customerId: string;
}
export interface IMspInstanceImmySupportAccessGrantDetails
{
  isEnabled: boolean;
  enabledByName: string;
  enabledByEmail: string;
  enabledDateUtc: string;
  disabledByName?: string;
  disabledByEmail?: string;
  disabledDateUtc?: string;
}
export interface IReleaseDetails
{
  tag: string;
  releaseNotes?: string;
  releaseChannel: ReleaseChannel;
  immyAgentInstallerVersion?: string;
  immyAgentInstallerFileVersion?: string;
  publishedDate?: string;
}
export interface ISubscriptionFeatureBasicInfo
{
  featureId: string;
  featureName: string;
  featureDescription: string;
  status: SubscriptionFeatureStatus;
  type: SubscriptionFeatureType;
  unit: string;
  levels: ISubscriptionFeatureLevelInfo[];
}
export interface ISubscriptionFeatureLevelInfo
{
  name: string;
  value: string;
  level: number;
  isUnlimited: boolean;
}
export interface ISubscriptionItemBasicInfo
{
  itemId: string;
  itemName: string;
  itemDescription: string;
  itemType: SubscriptionItemType;
}
export interface ISubscriptionAddonDto
{
  addonId: string;
  quantity?: number;
  price?: number;
  trialEndUtc?: string;
}
export interface IHttpProblem
{
  type: string;
  title: string;
  status?: number;
  detail: string;
  instance: string;
  extras: { [key:string]: any };
}
export interface IMaintenanceSchedulingConfiguration
{
  time?: string;
  maintenanceTime?: string;
  timeZoneInfoId?: string;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  useComputersTimezoneForExecution: boolean;
  scheduleExecutionAfterActiveHours: boolean;
}
export interface IMaintenanceOnboardingConfiguration
{
  onboardingOnlyParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
  onboardingOnlyMaintenanceTaskParameterValueOverrides?: IMaintenanceTaskParameterValueDetails[];
  automaticOnboarding: boolean;
}
export interface IMaintenanceEmailConfiguration
{
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showRunNowButton: boolean;
  showPostponeButton: boolean;
  showMaintenanceActions: boolean;
}
export interface IMaintenanceAgentUpdatesConfiguration
{
  providerLinkId?: number;
}
export interface IMaintenanceItem
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  providerLinkIdForMaintenanceItem?: number;
}
export interface IOauth2AccessTokenErrorResponse
{
  error: string;
  errorDescription: string;
  errorCodes: number[];
  timestamp: string;
  traceId: string;
  correlationId: string;
}
export interface IOauth2AccessTokenResponse
{
  accessTokenId: string;
  accessToken: string;
  tokenType: string;
  refreshToken: string;
  idToken: string;
  expiresIn: number;
  clientInfo: string;
}
export interface IOauth2AuthCodeErrorResponse
{
  errorDescription2: string;
  error: string;
  errorDescription: string;
}
export interface IOauthHook
{
  expiration: string;
  id: string;
  oauthConsentData?: IOauthConsentData;
  oauthAccessToken?: IOauth2AccessToken;
  oauthAccessTokenErrorResponse?: IOauth2AccessTokenErrorResponse;
  oauthAuthCodeErrorResponse?: IOauth2AuthCodeErrorResponse;
  oauthException?: string;
  allowSilentRefresh: boolean;
}
export interface IOauthHookFailedEvent
{
  hook: IOauthHook;
  dateUTC: string;
}
export interface IOauthHookSucceededEvent
{
  hook: IOauthHook;
  dateUTC: string;
}
export interface IOperation<TModel>
{
  value: any;
  operationType: number;
  path: string;
  op: string;
  from: string;
}
export interface IOpResult
{
  exception?: unknown;
  hadException: boolean;
  isSuccess: boolean;
  reason: string;
  dateCreatedUtc: string;
}
export interface IOpResult2<T>
{
  exception?: unknown;
  hadException: boolean;
  isSuccess: boolean;
  reason: string;
  value?: T;
  dateCreatedUtc: string;
}
export interface IScriptReference
{
  id: number;
  name: string;
  databaseType: DatabaseType;
  scriptReferenceType: ScriptReferenceType;
  softwareVersion?: string;
}
export interface ITechnicianPageInfoFromPsaTicket
{
  personId?: number;
  personName?: string;
  tenantId?: number;
  tenantName?: string;
  computers: IUnifiedComputerInfo[];
  ticketEmailAddress: string;
  ticketClientId: string;
}
export interface IUnifiedComputerInfo
{
  computerId: number;
  computerName: string;
  isOnline: boolean;
}
export interface ISoftwareFileUploadData
{
  installerType: SoftwareVersionInstallerType;
  packageType: PackageType;
  installerFile?: string;
  relativeCacheSourcePath: string;
  fileBlobName?: string;
  md5Hash?: string;
}
export interface IScriptReferenceCounts
{
  localSoftwareCount: number;
  localSoftwareVersionCount: number;
  localMaintenanceTaskCount: number;
  globalSoftwareCount: number;
  globalSoftwareVersionCount: number;
  globalMaintenanceTaskCount: number;
  total: number;
}
export interface ISourceContextLogLevel
{
  sourceContext: string;
  logLevel: LogLevel;
}
export interface IWindowsSession
{
  id: number;
  name: string;
  type: WindowsSessionType;
  username: string;
}
export interface IRegistryKeyDto
{
  displayName: string;
  fullPath: string;
  subKeyCount: number;
  valueCount: number;
}
export interface IRegistryValueDto
{
  binaryValue?: string;
  dWordValue?: number;
  kind: RegistryValueKindDto;
  multiStringValue?: string[];
  parentKeyPath: string;
  qWordValue?: number;
  stringValue?: string;
  valueName: string;
}
export interface IRegistrySearchRequestDto
{
  computerId: number;
  searchPattern: string;
  startKeyPath: string;
  targets: RegistrySearchTargets;
  matchWholeWord: boolean;
  userHubConnectionId: string;
}
export interface IRegistrySearchResultDto
{
  fullKeyPath: string;
  type: RegistrySearchResultType;
  valueName?: string;
}
export interface IGettingStartedChecklist
{
  enableGettingStartedWizard: boolean;
  activatedTrial: boolean;
  installedImmyAgent: boolean;
  ranOnboardingSession: boolean;
}
export interface ISyntaxCheckerResult
{
  isSyntaxValid: boolean;
  errors: string[];
}
export interface IRoleAssignment
{
  description?: string;
  displayName?: string;
  resourceScopes?: string[];
  roleDefinition?: IRoleDefinition;
  additionalData: { [key:string]: any };
  backingStore: IBackingStore;
  id?: string;
  odataType?: string;
}
export interface IRoleDefinition
{
  description?: string;
  displayName?: string;
  isBuiltIn?: boolean;
  roleAssignments?: IRoleAssignment[];
  rolePermissions?: any[];
  additionalData: { [key:string]: any };
  backingStore: IBackingStore;
  id?: string;
  odataType?: string;
}
export interface IResourceAction
{
  additionalData: { [key:string]: any };
  allowedResourceActions?: string[];
  backingStore: IBackingStore;
  notAllowedResourceActions?: string[];
  odataType?: string;
}
export interface IProvider
{
}
export interface IMaintenanceActionActivity
{
  id: number;
  maintenanceSessionId: number;
  maintenanceActionId: number;
  scriptName?: string;
  activity: string;
  currentOperation?: string;
  activityId?: string;
  parentId?: string;
  percentComplete?: number;
  secondsRemaining?: number;
  sourceId?: string;
  status?: string;
  completed?: boolean;
  dateUtc: string;
  maintenanceSession: IMaintenanceSession;
  maintenanceAction: IMaintenanceAction;
}
export interface INuGetVersion
{
  version: IVersion;
  isLegacyVersion: boolean;
  revision: number;
  isSemVer2: boolean;
  originalVersion?: string;
  major: number;
  minor: number;
  patch: number;
  releaseLabels: string[];
  release: string;
  isPrerelease: boolean;
  hasMetadata: boolean;
  metadata?: string;
}
export interface IBackingStore
{
  initializationCompleted: boolean;
  returnOnlyChangedValues: boolean;
}
export interface IVersion
{
  major: number;
  minor: number;
  build: number;
  revision: number;
  majorRevision: number;
  minorRevision: number;
}
export interface IGetRoleResponse
{
  id: number;
  name?: string;
  description?: string;
  roleTypeId: number;
  roleTypeName?: string;
  userCount: number;
  updatedDate?: string;
  createdDate?: string;
  grantedPermissionIds: string[];
  grantedPermissionCount: number;
  updatedBy?: string;
}
export interface IRolePermissionsResponse
{
  subjects: ISubjectMetadata[];
  grantedPermissionIds: string[];
}
export interface ICreateOrUpdateRoleRequest
{
  name: string;
  description?: string;
  grantedPermissionIds: string[];
}
export interface ICloneRoleRequest
{
  newName: string;
}
export interface IRolePermissionRequest
{
  permissionIdsToGrant: string[];
}
export interface ISetUserRolesRequest
{
  roleIds: number[];
}
export interface ISubjectMetadata
{
  id: string;
  name: string;
  displayName: string;
  description: string;
  sortOrder: number;
  permissions: IPermissionMetadata[];
}
export interface IPermissionMetadata
{
  id: string;
  permissionName: string;
  displayName: string;
  description: string;
  category: PermissionCategory;
  sortOrder: number;
  hasGreatPower: boolean;
  dependencies: IPermissionMetadata[];
  subject: ISubjectMetadata;
  claim: string;
  allowClaim: string;
  denyClaim: string;
}
export interface IParentTenantInfo
{
  id: number;
  name: string;
}
