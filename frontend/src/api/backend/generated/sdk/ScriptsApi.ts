//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IStartEditorServicesRequest } from '../interfaces';
import { IRunScriptRequestBody } from '../requests';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { DatabaseType } from '../enums';
import { IGetScriptNameResponse } from '../responses';
import { ScriptCategory } from '../enums';
import { IScriptSearchResult } from '../responses';
import { IGetLocalScriptResponse } from '../responses';
import { IGetGlobalScriptResponse } from '../responses';
import { IUpdateLocalScriptRequestBody } from '../requests';
import { IUpdateGlobalScriptRequestBody } from '../requests';
import { ICreateLocalScriptRequestBody } from '../requests';
import { ICreateGlobalScriptRequestBody } from '../requests';
import { ISyntaxCheckerResult } from '../interfaces';
import { IScriptSyntaxCheckRequestBody } from '../requests';
import { IScriptReferenceCounts } from '../interfaces';
import { IScriptReference } from '../interfaces';
import { IDuplicateScriptRequestBody } from '../requests';
import { IMigrationPreviewResponse } from '../interfaces';
import { IGetScriptVariablesAndParametersResponse } from '../responses';
import { IGetScriptVariablesAndParametersRequest } from '../requests';
import { IGetCommandResult } from '../interfaces';
import { IGetFunctionSyntaxRequest } from '../requests';
import { IDynamicFormBindResult } from '../interfaces';
import { IValidateParamBlockParametersFromScriptRequest } from '../requests';
import { IDoesScriptHaveParamBlockRequest } from '../requests';
import { IDisabledPreflightScriptResponse } from '../responses';
import { IOpResult } from '../interfaces';
import { ISetPreflightScriptEnablementRequest } from '../requests';
import { IScriptActionAuditResult } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ScriptsController */
export class ScriptsApi
{
  public async cancelScript(cancellationId: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/debug/cancel/${cancellationId}`
    await $post(route, undefined, {cancelToken});
  }
  public async startEditorServices(payload: IStartEditorServicesRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/language-service/start`
    await $post(route, payload, {cancelToken});
  }
  public async connectLanguageService(terminalId: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/language-service/${terminalId}/language`
    await $get(route, {cancelToken});
  }
  public async connectDebuggerService(terminalId: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/language-service/${terminalId}/debug`
    await $get(route, {cancelToken});
  }
  public async runScript(body: IRunScriptRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/run`
    await $post(route, body, {cancelToken});
  }
  public dxGetAll(queries: {databaseType?: DatabaseType} = {}) : CustomStore
  {
    const route = `/api/v1/scripts/dx`
    return createStore(route, { queries });
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.searchFilter - Defaults to null in the controller
  * @param {ScriptCategory} queries.scriptCategory - Defaults to null in the controller
  * @param {string} queries.searchType - Defaults to Name and Content in the controller
  */
  public async getAllGlobalScriptNames(queries: {searchFilter?: string, scriptCategory?: ScriptCategory, searchType?: string} = {}, cancelToken?: CancelToken) : Promise<IGetScriptNameResponse[]>
  {
    const route = `/api/v1/scripts/global/names`
    const res = await $get<IGetScriptNameResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.searchFilter - Defaults to null in the controller
  * @param {ScriptCategory} queries.scriptCategory - Defaults to null in the controller
  * @param {string} queries.searchType - Defaults to Name and Content in the controller
  */
  public async getAllLocalScriptNames(queries: {searchFilter?: string, scriptCategory?: ScriptCategory, searchType?: string} = {}, cancelToken?: CancelToken) : Promise<IGetScriptNameResponse[]>
  {
    const route = `/api/v1/scripts/local/names`
    const res = await $get<IGetScriptNameResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.globalOnly - Defaults to True in the controller
  * @param {boolean} queries.localOnly - Defaults to False in the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async search(queries: {globalOnly?: boolean, localOnly?: boolean, filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IScriptSearchResult[]>
  {
    const route = `/api/v1/scripts/search`
    const res = await $get<IScriptSearchResult[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAllLocalScripts(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetLocalScriptResponse[]>
  {
    const route = `/api/v1/scripts/local`
    const res = await $get<IGetLocalScriptResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAllGlobalScripts(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetGlobalScriptResponse[]>
  {
    const route = `/api/v1/scripts/global`
    const res = await $get<IGetGlobalScriptResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getLocalScript(scriptId: number, cancelToken?: CancelToken) : Promise<IGetLocalScriptResponse>
  {
    const route = `/api/v1/scripts/local/${scriptId}`
    const res = await $get<IGetLocalScriptResponse>(route, {cancelToken});
    return res.data;
  }
  public async getGlobalScript(scriptId: number, cancelToken?: CancelToken) : Promise<IGetGlobalScriptResponse>
  {
    const route = `/api/v1/scripts/global/${scriptId}`
    const res = await $get<IGetGlobalScriptResponse>(route, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.ignoreSyntaxErrors - Defaults to False in the controller
  */
  public async updateLocalScript(scriptId: number, body: IUpdateLocalScriptRequestBody, queries: {ignoreSyntaxErrors?: boolean} = {}, cancelToken?: CancelToken) : Promise<IGetLocalScriptResponse>
  {
    const route = `/api/v1/scripts/local/${scriptId}`
    const res = await $post<IGetLocalScriptResponse>(route, body, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.ignoreSyntaxErrors - Defaults to False in the controller
  */
  public async updateGlobalScript(scriptId: number, body: IUpdateGlobalScriptRequestBody, queries: {ignoreSyntaxErrors?: boolean} = {}, cancelToken?: CancelToken) : Promise<IGetGlobalScriptResponse>
  {
    const route = `/api/v1/scripts/global/${scriptId}`
    const res = await $post<IGetGlobalScriptResponse>(route, body, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.ignoreSyntaxErrors - Defaults to False in the controller
  */
  public async createLocalScript(body: ICreateLocalScriptRequestBody, queries: {ignoreSyntaxErrors?: boolean} = {}, cancelToken?: CancelToken) : Promise<IGetLocalScriptResponse>
  {
    const route = `/api/v1/scripts/local`
    const res = await $post<IGetLocalScriptResponse>(route, body, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.ignoreSyntaxErrors - Defaults to False in the controller
  */
  public async createGlobalScript(body: ICreateGlobalScriptRequestBody, queries: {ignoreSyntaxErrors?: boolean} = {}, cancelToken?: CancelToken) : Promise<IGetGlobalScriptResponse>
  {
    const route = `/api/v1/scripts/global`
    const res = await $post<IGetGlobalScriptResponse>(route, body, {params: queries, cancelToken});
    return res.data;
  }
  public async deleteLocalScript(scriptId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/local/${scriptId}`
    await $delete(route, {cancelToken});
  }
  public async deleteGlobalScript(scriptId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/scripts/global/${scriptId}`
    await $delete(route, {cancelToken});
  }
  public async syntaxCheck(body: IScriptSyntaxCheckRequestBody, cancelToken?: CancelToken) : Promise<ISyntaxCheckerResult>
  {
    const route = `/api/v1/scripts/syntax-check`
    const res = await $post<ISyntaxCheckerResult>(route, body, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {DatabaseType} queries.scriptType
  * @param {number} queries.id
  */
  public async getScriptReferenceCounts(queries: {scriptType: DatabaseType, id: number}, cancelToken?: CancelToken) : Promise<IScriptReferenceCounts>
  {
    const route = `/api/v1/scripts/references/count`
    const res = await $get<IScriptReferenceCounts>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getGlobalScriptReferences(scriptId: number, cancelToken?: CancelToken) : Promise<IScriptReference[]>
  {
    const route = `/api/v1/scripts/global/${scriptId}/references`
    const res = await $get<IScriptReference[]>(route, {cancelToken});
    return res.data;
  }
  public async getLocalScriptReferences(scriptId: number, cancelToken?: CancelToken) : Promise<IScriptReference[]>
  {
    const route = `/api/v1/scripts/local/${scriptId}/references`
    const res = await $get<IScriptReference[]>(route, {cancelToken});
    return res.data;
  }
  public async duplicateScript(body: IDuplicateScriptRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalScriptResponse>
  {
    const route = `/api/v1/scripts/duplicate`
    const res = await $post<IGetLocalScriptResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async migrateLocalToGlobalWhatIf(scriptId: number, cancelToken?: CancelToken) : Promise<IMigrationPreviewResponse>
  {
    const route = `/api/v1/scripts/local/${scriptId}/migrate-local-to-global-what-if`
    const res = await $get<IMigrationPreviewResponse>(route, {cancelToken});
    return res.data;
  }
  public async migrateLocalToGlobal(scriptId: number, cancelToken?: CancelToken) : Promise<number>
  {
    const route = `/api/v1/scripts/local/${scriptId}/migrate-local-to-global`
    const res = await $post<number>(route, undefined, {cancelToken});
    return res.data;
  }
  public async getScriptVariablesAndParameters(request: IGetScriptVariablesAndParametersRequest, cancelToken?: CancelToken) : Promise<IGetScriptVariablesAndParametersResponse>
  {
    const route = `/api/v1/scripts/default-variables`
    const res = await $post<IGetScriptVariablesAndParametersResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async findFunctions(cancelToken?: CancelToken) : Promise<IGetCommandResult[]>
  {
    const route = `/api/v1/scripts/functions`
    const res = await $get<IGetCommandResult[]>(route, {cancelToken});
    return res.data;
  }
  public async getFunctionSyntax(req: IGetFunctionSyntaxRequest, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/scripts/functions/syntax`
    const res = await $post<string>(route, req, {cancelToken});
    return res.data;
  }
  public async validateParamBlockParameters(request: IValidateParamBlockParametersFromScriptRequest, cancelToken?: CancelToken) : Promise<IDynamicFormBindResult>
  {
    const route = `/api/v1/scripts/validate-param-block-parameters`
    const res = await $post<IDynamicFormBindResult>(route, request, {cancelToken});
    return res.data;
  }
  public async doesScriptHaveParamBlock(request: IDoesScriptHaveParamBlockRequest, cancelToken?: CancelToken) : Promise<boolean>
  {
    const route = `/api/v1/scripts/does-script-have-param-block`
    const res = await $post<boolean>(route, request, {cancelToken});
    return res.data;
  }
  public async getDisabledPreflightScripts(cancelToken?: CancelToken) : Promise<IDisabledPreflightScriptResponse[]>
  {
    const route = `/api/v1/scripts/disabled-preflight-scripts`
    const res = await $get<IDisabledPreflightScriptResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async setPreflightScriptEnablement(request: ISetPreflightScriptEnablementRequest, cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/scripts/set-preflight-script-enablement`
    const res = await $post<IOpResult>(route, request, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.skip - Defaults to 0 in the controller
  */
  public async getLocalScriptAudits(scriptId: number, queries: {skip?: number} = {}, cancelToken?: CancelToken) : Promise<IScriptActionAuditResult>
  {
    const route = `/api/v1/scripts/local/${scriptId}/audit`
    const res = await $get<IScriptActionAuditResult>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.skip - Defaults to 0 in the controller
  */
  public async getGlobalScriptAudits(scriptId: number, queries: {skip?: number} = {}, cancelToken?: CancelToken) : Promise<IScriptActionAuditResult>
  {
    const route = `/api/v1/scripts/global/${scriptId}/audit`
    const res = await $get<IScriptActionAuditResult>(route, {params: queries, cancelToken});
    return res.data;
  }
}
