//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetTenantResponse } from '../responses';
import { IGetTenantSoftwareFromInventoryResponse } from '../responses';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IDetectedComputerSoftwareResponse } from '../responses';
import { IUpdateTenantPayload } from '../interfaces';
import { IUpdateAzureTenantLinkRequest } from '../requests';
import { ICreateTenantRequestBody } from '../requests';
import { IBulkCreateTenantRequestBody } from '../requests';
import { IDeleteTenantsCmdResponse } from '../interfaces';
import { IBulkDeleteRequest } from '../requests';
import { ICommandResult } from '../interfaces';
import { IMergeTenantsPayload } from '../interfaces';
import { IGetAzureGroupResponse } from '../responses';
import { ITenantProviderLinksResponseItem } from '../responses';
import { IComputerNameResponse } from '../responses';
import { IAddTagsRequest } from '../requests';
import { IRemoveTagsRequest } from '../requests';
import { ISetParentTenantRequest } from '../requests';
import { IRemoveParentTenantRequest } from '../requests';
import { IResolveAssignmentsForMaintenanceItemResult } from '../interfaces';
import { IResolveAssignmentsForMaintenanceItemRequest } from '../requests';
import { ITenantComputerCountResponse } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.TenantsController */
export class TenantsApi
{
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getTenants(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetTenantResponse[]>
  {
    const route = `/api/v1/tenants`
    const res = await $get<IGetTenantResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getSoftwareFromInventory(id: number, cancelToken?: CancelToken) : Promise<IGetTenantSoftwareFromInventoryResponse[]>
  {
    const route = `/api/v1/tenants/software-from-inventory/${id}`
    const res = await $get<IGetTenantSoftwareFromInventoryResponse[]>(route, {cancelToken});
    return res.data;
  }
  public exportSoftware(queries: {tenantId?: number} = {}) : CustomStore
  {
    const route = `/api/v1/tenants/software-from-inventory/export`
    return createStore(route, { queries });
  }
  public getSoftwareFromInventoryDx(queries: {tenantId?: number} = {}) : CustomStore<IDetectedComputerSoftwareResponse>
  {
    const route = `/api/v1/tenants/software-from-inventory/dx`
    return createStore(route, { queries });
  }
  public async getTenant(id: number, cancelToken?: CancelToken) : Promise<IGetTenantResponse>
  {
    const route = `/api/v1/tenants/${id}`
    const res = await $get<IGetTenantResponse>(route, {cancelToken});
    return res.data;
  }
  public async putTenant(id: number, body: IUpdateTenantPayload, cancelToken?: CancelToken) : Promise<IGetTenantResponse>
  {
    const route = `/api/v1/tenants/${id}`
    const res = await $put<IGetTenantResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async updateAzureTenantLink(body: IUpdateAzureTenantLinkRequest, cancelToken?: CancelToken) : Promise<IGetTenantResponse>
  {
    const route = `/api/v1/tenants/update-azure-tenant-link`
    const res = await $post<IGetTenantResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async postTenant(body: ICreateTenantRequestBody, cancelToken?: CancelToken) : Promise<IGetTenantResponse>
  {
    const route = `/api/v1/tenants`
    const res = await $post<IGetTenantResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async bulkCreate(body: IBulkCreateTenantRequestBody, cancelToken?: CancelToken) : Promise<IGetTenantResponse[]>
  {
    const route = `/api/v1/tenants/bulk-create`
    const res = await $post<IGetTenantResponse[]>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteTenants(req: IBulkDeleteRequest, cancelToken?: CancelToken) : Promise<IDeleteTenantsCmdResponse>
  {
    const route = `/api/v1/tenants/bulk-delete`
    const res = await $post<IDeleteTenantsCmdResponse>(route, req, {cancelToken});
    return res.data;
  }
  public async mergeTenants(req: IMergeTenantsPayload, cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/tenants/bulk-merge`
    const res = await $post<ICommandResult>(route, req, {cancelToken});
    return res.data;
  }
  public async activateTenant(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/activate/${id}`
    await $patch(route, undefined, {cancelToken});
  }
  public async deactivateTenant(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/deactivate/${id}`
    await $patch(route, undefined, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.search
  */
  public async getAzureGroupsAtTenant(id: number, queries: {search: string}, cancelToken?: CancelToken) : Promise<IGetAzureGroupResponse[]>
  {
    const route = `/api/v1/tenants/${id}/azure-groups`
    const res = await $get<IGetAzureGroupResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getAzureGroupAtTenant(groupId: string, id: number, cancelToken?: CancelToken) : Promise<IGetAzureGroupResponse>
  {
    const route = `/api/v1/tenants/${id}/azure-groups/${groupId}`
    const res = await $get<IGetAzureGroupResponse>(route, {cancelToken});
    return res.data;
  }
  public async getProviderLinks(id: number, cancelToken?: CancelToken) : Promise<ITenantProviderLinksResponseItem[]>
  {
    const route = `/api/v1/tenants/${id}/provider-links`
    const res = await $get<ITenantProviderLinksResponseItem[]>(route, {cancelToken});
    return res.data;
  }
  public async getComputersExcludedFromMaintenance(id: number, cancelToken?: CancelToken) : Promise<IComputerNameResponse[]>
  {
    const route = `/api/v1/tenants/${id}/computers/excluded-from-maintenance`
    const res = await $get<IComputerNameResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async addTags(request: IAddTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/add-tags`
    await $post(route, request, {cancelToken});
  }
  public async removeTags(request: IRemoveTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/remove-tags`
    await $post(route, request, {cancelToken});
  }
  public async setParentTenant(request: ISetParentTenantRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/set-parent-tenant`
    await $post(route, request, {cancelToken});
  }
  public async removeParentTenant(request: IRemoveParentTenantRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/tenants/remove-parent-tenant`
    await $post(route, request, {cancelToken});
  }
  public async resolveAssignmentsForMaintenanceItem(req: IResolveAssignmentsForMaintenanceItemRequest, cancelToken?: CancelToken) : Promise<IResolveAssignmentsForMaintenanceItemResult>
  {
    const route = `/api/v1/tenants/resolve-assignments-for-maintenance-item`
    const res = await $post<IResolveAssignmentsForMaintenanceItemResult>(route, req, {cancelToken});
    return res.data;
  }
  public async getTenantComputerCounts(cancelToken?: CancelToken) : Promise<ITenantComputerCountResponse[]>
  {
    const route = `/api/v1/tenants/computer-counts`
    const res = await $get<ITenantComputerCountResponse[]>(route, {cancelToken});
    return res.data;
  }
}
