import { Directive } from "vue";

const ImmyToggleDirective: Directive = {
  mounted(el, binding) {
    setTimeout(() => {
      const targetElement = document.getElementById(binding.arg ?? "");

      // Check if the target element exists
      if (!targetElement)
        return;

      // Toggle visibility on click
      el.addEventListener("click", () => {
        if (targetElement.classList.contains("show")) {
          targetElement.classList.remove("show");
        }
        else {
          targetElement.classList.add("show");
          const accordionId = targetElement.getAttribute("accordion");
          if (accordionId) {
            document.querySelectorAll(`div[accordion="${accordionId}"]`).forEach((element) => {
              if (element.id != binding.arg)
                element.classList.remove("show");
            });
          }
        }
      });
    }, 500);
  },
};

export default ImmyToggleDirective;
