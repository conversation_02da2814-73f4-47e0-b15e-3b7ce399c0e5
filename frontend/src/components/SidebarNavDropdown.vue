<template>
  <div class="im-sidebar-nav-item-dropdown">
    <li
      :id="`sidebar-nav-item-${item.id}`"
      :data-testid="`menu-item-${item.id}`"
      class="im-sidebar-nav-item"
      :class="{ 'dropdown-active': isActive }"
      @click="expanded = !expanded"
    >
      <div class="d-flex align-items-center justify-content-between">
        <span>
          <i v-if="item.icon" :class="item.icon" class="im-nav-icon" />
          {{ item.name }}
        </span>
        <i v-if="!expanded" class="fal fa-angle-down" />
        <i v-else class="fal fa-angle-up" />
      </div>
    </li>
    <template v-if="item.children && expanded">
      <SidebarNavItem
        v-for="child in item.children"
        :key="`${child.name}-${child.url}`"
        class="im-sidebar-nav-dropdown-item"
        :item="child"
        :parent-id="item.id"
        :data-testid="`submenu-item-${item.id}-${child.id}`"
      />
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import { INavItem } from "@/nav";

export default defineComponent({
  props: {
    item: {
      type: Object as PropType<INavItem>,
      required: true,
    },
  },
  data() {
    return {
      expanded: false,
    };
  },
  computed: {
    isActive(): boolean {
      return !!this.item.children?.find(child => this.$route.path.startsWith(child.url));
    },
  },
  watch: {
    isActive: {
      handler(val: boolean) {
        this.expanded = val;
      },
      immediate: true,
    },
  },
});
</script>

<style lang="scss" scoped>
.im-sidebar-nav-item-dropdown {
  .im-sidebar-nav-item:not(.im-sidebar-nav-dropdown-item) {
    padding: 0.5rem;
  }
}
</style>
