<template>
  <form @submit.prevent.stop>
    <template v-if="supportsOnboardingOptions">
      <ImmyButton variant="primary" :disabled="submitting" @click="submitForm">
        Get Install Script
      </ImmyButton>
    </template>
    <template v-if="submitting">
      <i class="mx-1 fa fa-spinner fa-spin" />
    </template>
    <template v-if="installScript != null">
      <p>Copy and run the following script to install the agent:</p>
      <CopyToClipboardInput v-model="installScript" />
    </template>
  </form>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import {
  IAgentOnboardingOptions,
  IGetProviderLinkResponse,
} from "@/api/backend/generated/contracts";
import { Platform } from "@/api/backend/generated/enums";
import { providerLinksApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useProviderTypesStore } from "@/store/pinia/provider-types-store";

interface SubmitEventParam {
  onboardingOptions: IAgentOnboardingOptions;
}

interface IProps {
  providerLink: IGetProviderLinkResponse;
  tenantId: number;
  onboardingOptions: IAgentOnboardingOptions;
}

const props = defineProps<IProps>();
const emit = defineEmits<{
  (e: "submitted", val: SubmitEventParam): void;
}>();
const providerTypesStore = useProviderTypesStore();
const appAlertsStore = useAppAlertsStore();

const providerTypeCapabilities = computed(() =>
  providerTypesStore.providerTypesCapabilityCheckers.get(props.providerLink.providerTypeId),
);

const submitting = ref<boolean>(false);
const installScript = ref<string | null>(null);
const supportsOnboardingOptions = computed(
  () => providerTypeCapabilities.value?.supportsAgentBashInstallScriptWithOnboardingOptions && !props.providerLink.excludedCapabilities?.includes("ISupportsAgentBashInstallScriptWithOnboardingOptions"),
);

onMounted(() => {
  if (!supportsOnboardingOptions.value) {
    // if no onboarding options, just submit the form immediately since there's nothing the
    // user can actually configure
    submitForm();
  }
});

async function submitForm() {
  if (submitting.value)
    return;
  try {
    let script;
    if (supportsOnboardingOptions.value) {
      script = await providerLinksApi.getAgentBashInstallScriptWithOnboardingOptions(
        props.providerLink.id,
        {
          platform: Platform.Windows,
          targetExternalClientId: props.tenantId?.toString(),
          onboardingOptions: props.onboardingOptions,
        },
      );
    }
    else {
      script = await providerLinksApi.getAgentBashInstallScript(props.providerLink.id, {
        platform: Platform.Windows,
        targetExternalClientId: props.tenantId?.toString(),
      });
    }
    installScript.value = script.script;
    emit("submitted", { onboardingOptions: props.onboardingOptions });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to get agent bash install script.",
      details: err,
    });
  }
  finally {
    submitting.value = false;
  }
}
</script>

<style></style>
