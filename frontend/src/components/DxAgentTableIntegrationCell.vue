<template>
  <i v-if="agent?.isConnected === undefined" />
  <i v-else-if="agent?.isConnected" class="fas fa-bolt-lightning text-success" />
  <i v-else class="fas fa-bolt-lightning text-danger" />
</template>

<script setup lang="ts">
import { IAgentStatusDto } from "@/api/backend/generated/interfaces";

defineProps<{
  agent: IAgentStatusDto | undefined;
}>();
</script>

<style scoped lang="scss">

</style>
