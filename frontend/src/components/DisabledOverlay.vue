<template>
  <div class="disabled-overlay" />
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "DisabledOverlay",
});
</script>

<style lang="scss" scoped>
.disabled-overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: black;
  opacity: 0.1;
  z-index: 1;
  &:hover {
    cursor: not-allowed;
  }
}
</style>
