<template>
  <SimplePanel
    class="p-3 computer-onboarding-dynamic-form mb-3"
    sticky-header
    header-bg-color="var(--assignment-with-task-section-bg-color)"
  >
    <template #title>
      <span>{{ action.maintenanceDisplayName }}</span><i v-if="loading" class="fs-85 fal fa-spinner fa-spin ml-2" />
    </template>
    <template #subtitle>
      {{
        action.softwareItem
          ? `Configuration task for ${action.softwareItem.name}`
          : "Maintenance Task"
      }}
    </template>
    <template #actions>
      <div class="d-flex align-items-center gap-2">
        <ImmyButton
          size="sm"
          @click="dynamicFormStore.showSimpleView = !dynamicFormStore.showSimpleView"
        >
          <template v-if="dynamicFormStore.showSimpleView">
            Show Form View
          </template><template v-else>
            Show Value View
          </template>
        </ImmyButton>
        <LoadButton size="sm" :disabled="loading" :handler="validateParams">
          Refresh
        </LoadButton>
        <template v-if="isMsp">
          <ImmyLink
            class="btn btn-secondary btn-sm"
            rel="noopener"
            target="_blank"
            :to="{
              name: 'Edit Deployment',
              query: { databaseType: action.assignment.databaseType.toString() },
              params: { assignmentId: action.assignment.id.toString() },
            }"
          >
            <div class="d-flex align-items-center">
              Deployment <i class="pl-2 fal fa-external-link-alt" />
            </div>
          </ImmyLink>
          <ImmyLink
            v-if="action.softwareItem?.id != null"
            class="btn btn-secondary btn-sm"
            rel="noopener"
            target="_blank"
            :to="{
              name: 'Edit Software',
              query: { softwareType: action.softwareItem.softwareType.toString() },
              params: { softwareId: action.softwareItem.id.toString() },
            }"
          >
            <div class="d-flex align-items-center">
              Software <i class="pl-2 fal fa-external-link-alt" />
            </div>
          </ImmyLink>
          <ImmyLink
            class="btn btn-secondary btn-sm"
            rel="noopener"
            target="_blank"
            :to="{
              name: 'Edit Task',
              query: { maintenanceTaskType: action.maintenanceTask.databaseType.toString() },
              params: { maintenanceTaskId: action.maintenanceTask.id.toString() },
            }"
          >
            <div class="d-flex align-items-center">
              {{ action.softwareItem != null ? "Config Task" : "Task" }}
              <i class="pl-2 fal fa-external-link-alt" />
            </div>
          </ImmyLink>
        </template>
      </div>
    </template>
    <ImmyAlert v-if="action.nextHighestPriorityAssignment != null" show variant="warning">
      There is a
      <ImmyLink
        rel="noopener"
        target="_blank"
        :to="{
          name: 'Edit Deployment',
          query: { databaseType: action.nextHighestPriorityAssignment.databaseType.toString() },
          params: { assignmentId: action.nextHighestPriorityAssignment.id.toString() },
        }"
      >
        <div class="d-inline-flex align-items-center">
          different deployment <i class="pl-2 fal fa-external-link-alt" />
        </div>
      </ImmyLink>
      that will take precedence after the initial onboarding session. Any value you override here
      will not persist. Consider creating a new deployment if you need these values to persist after
      the onboarding session.
    </ImmyAlert>
    <BaseDynamicForm
      :id="`${action.assignmentId}-${action.assignmentType}`"
      :panel-title="action.maintenanceDisplayName"
      :subtitle="
        action.softwareItem
          ? `Configuration task for ${action.softwareItem.name}`
          : 'Maintenance Task'
      "
      :form-id="formId"
    >
      <template #overrideLink="{ parameter, fieldRef }">
        <DynamicFormFieldOverrideLink
          v-if="fieldRef"
          :parameter="parameter"
          :field-ref="fieldRef"
          :form-id="formId"
          :deployment-specified-value="getDeploymentSpecifiedValue(parameter)"
        />
      </template>
      <template #defaultValue="{ parameter }">
        <DynamicFormFieldOverridableDefaultValue
          :parameter="parameter"
          :form-id="formId"
          :deployment-specified-value="getDeploymentSpecifiedValue(parameter)"
        />
      </template>
    </BaseDynamicForm>
  </SimplePanel>
</template>

<script setup lang="ts">
import debounce from "lodash.debounce";
import { getActivePinia } from "pinia";
import equals from "ramda/src/equals";
import { onMounted, onUnmounted, ref, watch } from "vue";
import {
  IDeploymentParameterValue,
  IParameter,
  IParameterValue,
} from "@/api/backend/generated/interfaces";
import { maintenanceTasksApi } from "@/api/backend/v1";
import DynamicFormFieldOverridableDefaultValue from "@/components/DynamicFormFieldOverridableDefaultValue.vue";

import DynamicFormFieldOverrideLink from "@/components/DynamicFormFieldOverrideLink.vue";
import { useCommon } from "@/composables/Common";
import { ParameterOverridePolicy } from "@/composables/DynamicFormFieldOverridableHelpText";
import { useDynamicFormStore } from "@/store/pinia/dynamic-form-store";
import { makeUid } from "@/utils/misc";
import { IOverridableAssignmentWithTask } from "./ComputerOnboardingOverridableTasksForm.vue";

const props = defineProps<IProps>();
const emit = defineEmits<{
  (e: "update:parameterValues", val: Map<string, IDeploymentParameterValue>): void;
}>();
// setup & tear down dynamic form store
const formId = makeUid();
const dynamicFormStore = useDynamicFormStore(formId);
onUnmounted(() => {
  dynamicFormStore.$dispose();
  const activePinia = getActivePinia();
  delete activePinia?.state.value[dynamicFormStore.$id];
});

interface IProps {
  personId?: number;
  computerId?: number;
  tenantId?: number;
  action: IOverridableAssignmentWithTask;
}
const { isMsp } = useCommon();
const newMap = new Map<string, IDeploymentParameterValue>(new Map());
let oldMap = new Map(new Map());

const deploymentSpecifiedValues = props.action.initialParameterValues ?? new Map();

function getDeploymentSpecifiedValue(parameter: IParameter) {
  const parameterValue = deploymentSpecifiedValues.get(parameter.name);
  return parameterValue?.value;
}

const parameterOverridePolicyMap = new Map<string, ParameterOverridePolicy>();

function calculateShowInput(
  parameter: IParameter,
  parameterValue: IParameterValue,
  overriding: boolean,
) {
  const policy = parameterOverridePolicyMap.get(parameter.name);
  if (policy === ParameterOverridePolicy.NotAllowed)
    return false;

  if (
    parameter.isMandatory
    // if the parameter didn't have an override policy specified, then it's likely a dynamic parameter that the deployment didn't know about.
    // In this case, we should show the input if the parameter is mandatory
    && (policy === ParameterOverridePolicy.Require || policy === undefined)
  )
    return true;
  return overriding;
}

function calculateShowField(parameter: IParameter) {
  const policy = parameterOverridePolicyMap.get(parameter.name);
  return policy !== ParameterOverridePolicy.NotAllowed;
}

function calculateCanOverride(parameter: IParameter) {
  const policy = parameterOverridePolicyMap.get(parameter.name);
  if (policy === ParameterOverridePolicy.NotAllowed)
    return false;
  return !(parameter.isMandatory
    // if the parameter didn't have an override policy specified, then it's likely a dynamic parameter that the deployment didn't know about.
    // In this case, we should disable override if the parameter is mandatory since we will be showing the input
    && (policy === ParameterOverridePolicy.Require || policy === undefined));
}

function calculateHasOverride(parameter: IParameter, parameterValue: IParameterValue) {
  return parameterValue.value != null;
}

function getParameterOverridePolicy(parameter: IDeploymentParameterValue) {
  if (parameter.requiresOverride)
    return ParameterOverridePolicy.Require;
  if (parameter.allowOverride)
    return ParameterOverridePolicy.Allow;
  return ParameterOverridePolicy.NotAllowed;
}

const debounceBindParameters = debounce(async () => {
  await validateParams();
}, 500);

const loading = ref(false);
async function validateParams() {
  try {
    loading.value = true;
    await dynamicFormStore.load(newMap, async () => {
      // fetch dynamic form data from task and software
      const res = await maintenanceTasksApi.validateParamBlockParameter({
        maintenanceTaskId: props.action.maintenanceTask.id,
        databaseType: props.action.maintenanceTask.databaseType,
        forceRebind: false,
        computerId: props.computerId,
        personId: props.personId,
        tenantId: props.tenantId,
        parameterValues: Object.fromEntries(newMap),
        deploymentId: props.action.assignmentId,
        deploymentDatabaseType: props.action.assignmentType,
      });

      return {
        dynamicFormBindResult: res,
        initialParameterValues: newMap,
        showInputFn: calculateShowInput,
        showField: calculateShowField,
        canOverrideValueFn: calculateCanOverride,
        hasOverrideValueFn: calculateHasOverride,
      };
    });
  }
  finally {
    loading.value = false;
  }
}

onMounted(async () => {
  dynamicFormStore.tenantId = props.tenantId ?? null;
  // set onboarding options map
  props.action.initialParameterValues?.forEach((value, key) => {
    parameterOverridePolicyMap.set(key, getParameterOverridePolicy(value));
  });
  debounceBindParameters();
});

function emitParameterValuesChangedAndRefreshIfNecessary() {
  emit("update:parameterValues", newMap);

  if (!equals(oldMap, newMap))
    debounceBindParameters();

  oldMap = new Map<string, IDeploymentParameterValue>(newMap);
}

dynamicFormStore.setOnValueRemoved(async (parameterName) => {
  newMap.delete(parameterName);
  emitParameterValuesChangedAndRefreshIfNecessary();
});

watch(
  () => dynamicFormStore.parameterValueUpdate,
  (val) => {
    if (!val)
      return;
    const policy = parameterOverridePolicyMap.get(val.parameter.name);
    newMap.set(val.parameter.name, {
      value: val.value?.value,
      allowOverride: policy === ParameterOverridePolicy.Allow,
      requiresOverride: policy === ParameterOverridePolicy.Require,
    });
    emitParameterValuesChangedAndRefreshIfNecessary();
  },
);
</script>

<style lang="scss" scoped>
.computer-onboarding-dynamic-form {
  background: var(--assignment-with-task-section-bg-color);
  border: var(--assignment-with-task-section-border);
}
</style>
