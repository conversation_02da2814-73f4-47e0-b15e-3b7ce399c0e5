<template>
  <div v-if="searching">
    <i class="fal fa-spinner fa-spin ml-3 mr-2 mb-3" /> Searching...
  </div>
  <div v-else-if="props.searchFilter?.length" class="script-editor-search-results">
    <div class="script-results-view w-100">
      <ScriptEditorSearchResultsWrapper
        v-show="showingMyScripts"
        :default-show="true"
        :hide-empty-categories="true"
        :results="localScriptSearchResults"
        :database-type="DatabaseType.Local"
        :search-filter="searchFilter ?? undefined"
        :show-matches="showMatches"
      >
        <template #title>
          My Scripts
        </template>
      </ScriptEditorSearchResultsWrapper>
    </div>
    <div class="script-results-view w-100">
      <ScriptEditorSearchResultsWrapper
        v-show="showingGlobalScripts"
        :default-show="true"
        :hide-empty-categories="true"
        :results="globalScriptSearchResults"
        :database-type="DatabaseType.Global"
        :search-filter="searchFilter ?? undefined"
        :show-matches="showMatches"
      >
        <template #title>
          Global Scripts
        </template>
      </ScriptEditorSearchResultsWrapper>
    </div>
  </div>
</template>

<script setup lang="ts">
import Axios, { CancelTokenSource } from "axios";
import debounce from "lodash.debounce";
import { computed, onMounted, ref, watch } from "vue";
import { DatabaseType, ScriptCategory } from "@/api/backend/generated/contracts";

import { scriptsApi } from "@/api/backend/v1";
import useSearch, { ScriptSearchType, SortDirection, SortOption } from "@/composables/ScriptEditor/search";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

const props = withDefaults(defineProps<IProps>(), {
  searchType: "Name and Content",
  searchFilter: undefined,
  scriptCategory: undefined,
});

const appAlertsStore = useAppAlertsStore();

interface IProps {
  searchFilter?: string;
  searchType?: ScriptSearchType;
  scriptCategory?: ScriptCategory;
  sort: SortOption;
  sortDir: SortDirection;
}

const { localScriptSearchResults, globalScriptSearchResults } = useSearch();

const showingMyScripts = ref(true);
const showingGlobalScripts = ref(true);

const searchDebounced = debounce(fetchScriptNames, 350);

const searching = ref(false);

let cancelTokenSource: CancelTokenSource | null = null;

const showMatches = computed(() => props.searchType !== "Name" && !!props.searchFilter?.length);

async function fetchScriptNames() {
  try {
    searching.value = true;
    cancelTokenSource = Axios.CancelToken.source();

    if (!props.searchFilter?.length) {
      localScriptSearchResults.value = [];
      globalScriptSearchResults.value = [];
      return;
    }

    const localScriptsReq = scriptsApi.getAllLocalScriptNames(props, cancelTokenSource.token);
    const globalScriptsReq = scriptsApi.getAllGlobalScriptNames(props, cancelTokenSource.token);

    const [localScriptNamesResponse, globalScriptNamesResponse] = await Promise.all([
      localScriptsReq,
      globalScriptsReq,
    ]);

    localScriptSearchResults.value = localScriptNamesResponse;
    globalScriptSearchResults.value = globalScriptNamesResponse;
  }
  catch (err) {
    if (Axios.isCancel(err))
      return;

    appAlertsStore.addAlert({
      text: "Failed to retrieve scripts",
      details: err,
    });
  }
  finally {
    searching.value = false;
  }
}

defineExpose({
  search: searchDebounced,
});

watch(
  () => props.searchFilter,
  async (val, oldVal) => {
    if (val != oldVal)
      await searchDebounced();
  },
);

watch(
  () => props.searchType,
  async () => {
    if (props.searchFilter != null)
      await searchDebounced();
  },
);

watch(
  () => props.scriptCategory,
  async () => {
    await searchDebounced();
  },
);

onMounted(async () => {
  await searchDebounced();
});
</script>

<style lang="scss" scoped>
.script-editor-search-results {
  overflow: auto;
  @extend .immy-scrollbar;
}
.script-results-type-collapse {
  color: var(--script-editor-primary-sidebar-color);
  text-decoration: none;
  font-size: 0.75rem;
  text-transform: uppercase;
}
.script-results {
  list-style: none;
  margin: 0;
  padding: 0;
}
</style>
