<template>
  <Teleport to="body">
    <div class="position-absolute h-100 w-100 box-container" data-testid="machine-onboarding">
      <div class="h-100 w-100 box-container-background">
        <div ref="headerRef" class="header d-flex align-items-center justify-content-between">
          <img id="immense-logo-mini" src="/img/brand/logo_light.svg" alt="immense logo">

          <span id="skip-setup-link" data-testid="skip-setup-link" @click="dismiss(false)">Skip Setup <i class="fa-duotone fa-forward" /></span>
        </div>

        <div class="d-flex justify-content-center">
          <div class="content">
            <MachineOnboardingSteps
              v-if="step <= OnboardingStep.InternetConfiguration"
              :step="step"
              @go-back="goBack($event)"
            />

            <div class="d-flex justify-content-center">
              <p class="setup-info">
                {{ currentOnboardingStepDetails.setupInfo }}
              </p>
            </div>

            <div
              v-if="step <= OnboardingStep.InternetConfiguration"
              class="offering-info d-flex justify-content-center"
              v-html="markdownToHtml(currentOnboardingStepDetails.offeringInfo)"
            />

            <MachineOnboardingSetup
              v-if="
                step <= OnboardingStep.OfferingType
                  && currentOnboardingStepDetails.rightCard
                  && currentOnboardingStepDetails.leftCard
              "
              id="machine-onboarding-setup-container"
              :active-card="activecard"
              :left-card="currentOnboardingStepDetails.leftCard"
              :right-card="currentOnboardingStepDetails.rightCard"
              @set-active-card="setActiveCard($event)"
            />

            <MachineOnboardingDownload
              v-if="step >= OnboardingStep.InternetConfiguration"
              id="machine-onboarding-state-container"
              :button-text="downloadStepTexts.buttonText"
              :step="step"
              :machine-type="machineType"
              :offering-type="offeringType"
              @url-obtained="urlObtained"
              @dismissed="dismiss(true)"
            />

            <div
              v-if="step <= OnboardingStep.OfferingType"
              class="d-flex justify-content-center"
              @click="goNextStep"
            >
              <button
                :id="continueBtnId"
                class="offerButton"
                type="button"
                :class="{ passive: activecard == null }"
                title="Choose an option above to continue"
              >
                Continue <i class="fa-regular fa-arrow-right" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { CheckListState } from "@/helpers/enums/CheckList";
import { OnboardingCardType, OnboardingStep } from "@/helpers/enums/OnboardingStatusExtended";
import { useCheckListStore } from "@/store/pinia/checklist-store";
import { markdownToHtml } from "@/utils/htmlSanitization";

const emit = defineEmits<{
  (e: "dismissed"): void;
}>();

const router = useRouter();
const checkListStore = useCheckListStore();

const headerRef = ref<HTMLElement | null>(null);
const activecard = ref<OnboardingCardType | null>(null);
const step = ref<OnboardingStep>(OnboardingStep.MachineType);
const machineType = ref<OnboardingCardType | null>(null);
const offeringType = ref<OnboardingCardType | null>(null);

function setActiveCard(cardType: OnboardingCardType) {
  activecard.value = cardType;
}

const continueBtnId = computed(() => {
  if (step.value === OnboardingStep.MachineType) {
    return "machine-onboarding-continue-to-offering-btn";
  }
  else return "machine-onboarding-continue-to-internet-btn";
});

function goNextStep() {
  if (activecard.value === null)
    return;
  if (step.value === OnboardingStep.MachineType) {
    machineType.value = activecard.value;
  }
  if (step.value === OnboardingStep.OfferingType) {
    offeringType.value = activecard.value;
  }
  step.value += 1;
  activecard.value = null;
  moveToTop();
}

function goBack(selectedStep: number) {
  step.value = selectedStep;
}

function moveToTop() {
  if (window.innerWidth <= 470 && headerRef.value) {
    headerRef.value.scrollIntoView({ behavior: "smooth" });
  }
}

function urlObtained() {
  step.value = OnboardingStep.Download;
}

const currentOnboardingStepDetails = computed(() => {
  switch (step.value) {
    case OnboardingStep.OfferingType:
      return {
        setupInfo: "What is the state of your computer?",
        offeringInfo:
          "<p style='max-width: 768px'>The machine’s state will determine how the ImmyBot Agent can be installed.</p>",
        leftCard: {
          title: "Out of Box Experience",
          description:
            "Painless OOBE setup that configures Windows, applies the latest manufacturer BIOS, updates drivers, and deploys your software automatically.",
          type: OnboardingCardType.OutOfBox,
        },
        rightCard: {
          title: "Already logged in to Windows",
          description:
            "Have a computer already logged into Windows? You’ll miss the out-of-box experience skip, but can still test software deployments to see how easy it is.",
          type: OnboardingCardType.Logged,
        },
      };
    case OnboardingStep.InternetConfiguration:
      return {
        setupInfo: "Last step!",
        offeringInfo: downloadStepTexts.value.headerText,
      };
    case OnboardingStep.Download:
      return {
        setupInfo: `Let’s setup your first computer with that ${downloadStepTexts.value.setupInfoText}`,
        offeringInfo: "",
      };
      break;

    default:
      return {
        setupInfo: "Our robot overlord requires your computers. Let’s setup your first one now.",
        offeringInfo:
          "<p style='max-width: 768px;'>ImmyBot can’t free up more time for naps, or Warhammer, or whatever you biological lifeforms do these days without <i>moar computers!</i> <span style='color: white'>Choose an option below<span>.</p>",
        leftCard: {
          title: "Physical Machine",
          description:
            "For the full ImmyBot experience, use a real computer. Grab any Dell, HP, Lenovo, or Surface laying around.",
          type: OnboardingCardType.Physical,
          features: [
            "Hands-free OOBE setup",
            "Applies latest manufacturer BIOS",
            "Updates drivers",
            "Deploy software and settings",
            "Schedule maintenance sessions",
          ],
        },
        rightCard: {
          title: "Virtual Machine",
          description:
            "Not ready to commit a physical device? That’s ok! You can still explore the tool and test software deployments on a disposable VM.",
          type: OnboardingCardType.Virtual,
          features: ["Deploy software and settings", "Schedule maintenance sessions"],
        },
      };
  }
});

const downloadStepTexts = computed(() => {
  if (
    machineType.value == OnboardingCardType.Physical
    && offeringType.value == OnboardingCardType.OutOfBox
  ) {
    return {
      headerText:
        "<p style='max-width: 768px;'>Configure an admin account and internet access, then download the provisioning package for your USB flash drive.</p>",
      setupInfoText: "provisioning package",
      buttonText: "Download provisioning package",
    };
  }
  else if (
    machineType.value == OnboardingCardType.Virtual
    && offeringType.value == OnboardingCardType.OutOfBox
  ) {
    return {
      headerText:
        "<p style='max-width: 768px;'>Configure an admin account and internet access, then download the ISO file for your USB flash drive.</p>",
      setupInfoText: "ISO",
      buttonText: "Download ISO file",
    };
  }
  else {
    return {
      headerText:
        "<p style='max-width: 768px;'>Download the executable file for your USB flash drive.</p>",
      setupInfoText: "EXE",
      buttonText: "Download EXE file",
    };
  }
});

function dismiss(goToComputers: boolean = false) {
  if (goToComputers) {
    if (router.currentRoute.value.name === "Computer List")
      // If we're already on the computer list page, then reload the whole page so the table is updated.
      // No need to emit dismissed since we're reloading the page.
      location.reload();
    else if (router.currentRoute.value.name === "Checklist") {
      checkListStore.updateActualStep(CheckListState.OnboardFirst);
      emit("dismissed");
    }
    else {
      // otherwise just navigate to the computer list page
      router.push("/");
      emit("dismissed");
    }
  }
  else {
    emit("dismissed");
  }
}
</script>

<style lang="scss" scoped>
.box-container {
  background: var(--mo-background);

  .header {
    height: 100px;
    padding: 0 85px;

    img {
      width: 123px;
      height: 22px;
    }

    span {
      color: var(--mo-header-color);
      font-family: "Roboto Mono";
      font-size: var(--font-size-10);
      font-style: normal;
      font-weight: 700;
      line-height: 12px;
      letter-spacing: 1px;
      text-transform: uppercase;
      margin-top: 2rem;
      cursor: pointer;
    }
  }

  .content {
    width: 1024px;

    .setup-info {
      width: 573.317px;
      color: var(--mo-setup-info-color);
      text-align: center;
      font-family: Lato;
      font-size: var(--font-size-28);
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      margin: 0 0 32px 0;
      max-width: 768px;
    }

    .offering-info {
      margin: 0 0 40px 0;
      color: var(--mo-offering-color);
      text-align: center;
      font-family: Lato;
      font-size: var(--font-size-20);
      font-style: normal;
      font-weight: 400;
      line-height: 30px;

      & > i {
        display: none;
      }
    }

    .offerButton {
      margin-top: 32px;
      padding: 12px 24px;
      color: var(--mo-background);
      font-family: "Roboto Mono";
      font-size: var(--font-size-12);
      font-weight: 700;
      line-height: 14px;
      letter-spacing: 0.6px;
      text-transform: uppercase;
      background: var(--mo-offer-button-background);
      border: none;

      &.passive {
        background: var(--mo-offer-button-passive-background);
        color: var(--mo-offer-button-passive-color);
        cursor: not-allowed;
      }
    }
  }

  .marginless {
    margin: 0 !important;
  }
}

.box-container-background {
  background: url("/img/background.svg");
  background-size: cover;
  -webkit-background-origin: border;
  background-origin: border;
}

@media (max-width: 1000px) {
  .box-container {
    overflow-y: scroll;

    .header {
      padding: 0 10px !important;
      height: 50px;
      span {
        margin-top: 0;
      }
    }

    .content {
      width: unset;

      .setup-info {
        width: unset;
      }

      .offerButton {
        margin: 16px 0;
      }
    }
  }
}
</style>
