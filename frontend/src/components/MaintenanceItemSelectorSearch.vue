<template>
  <div>
    <template v-if="supersedingTask != null && showDeprecationAlert">
      <ImmyAlert show variant="danger">
        <h4>Deprecation Notice</h4>
        <hr>
        <em>{{ model?.maintenanceName ?? "The selected maintenance item" }}</em> is no longer
        supported and has been superseded by <em>{{ supersedingTask.name }}</em>. New deployments for deprecated tasks are not allowed. Existing deployments can be
        migrated.
      </ImmyAlert>
      <div class="migration-area">
        <h3 v-if="showDeprecationAlertMigrateButton">
          Migrate to&nbsp;<em>{{ supersedingTask.name }}</em>
        </h3>
        <LoadButton
          v-if="showDeprecationAlertMigrateButton && !showMigrateToSupersedingDeployment"
          class="mt-2"
          variant="primary"
          :handler="migrateDeploymentToSupersedingTask"
        >
          Review Migration Changes
        </LoadButton>
        <MigrateToSupersedingDeployment
          v-if="
            showMigrateToSupersedingDeployment && supersedingTask != null && assignmentId != null
          "
          :deprecated-assignment-id="assignmentId"
          :superseded-by-task-id="supersedingTask.id"
          :superseded-by-task-type="supersedingTask.databaseType"
          @close="showMigrateToSupersedingDeployment = false"
        />
      </div>
      <hr>
    </template>
    <ImmyAlert v-if="selectionDoesNotExist" show variant="warning">
      The selected maintenance item no longer exists.
    </ImmyAlert>
    <FormGroupWithHelp
      :label="searchLabel"
      :description="searchDescription"
      :show="showItemNotes"
      alert-button-position="before-label"
    >
      <template v-if="showItemNotes && model?.notes" #help-alert-content>
        <span class="item-notes">{{ model.notes }}</span>
      </template>
      <div class="d-flex justify-content-between flex-wrap search-wrapper">
        <v-select
          key="maintenance-item-selector-search"
          ref="selector"
          v-model="model"
          data-testid="maintenance-item-selector-search"
          :disabled="disabled"
          class="w-100"
          :class="[itemValid === false ? 'invalid' : '']"
          :options="selectableMaintenanceItems"
          label="maintenanceName"
          :placeholder="searchPlaceholderText"
          :select-on-tab="true"
          :filterable="false"
          @close="onDropdownClose"
          @search="onSearch"
          @update:model-value="onSelected"
        >
          <template #no-options>
            No matching maintenance items
          </template>
          <template #option="option">
            <div :id="`maintenance-name-option-${option.maintenanceName}`" class="option">
              <div class="d-flex align-items-center">
                <media-image
                  :media="option.icon"
                  img-class="image-30"
                  class="mr-3"
                  :recommended="option.recommended"
                  :fallback-icon="option.fallbackIcon"
                />
                <span>
                  <p class="type">{{ option.maintenanceTypeName }}</p>
                  <p class="name">{{ option.maintenanceName }}</p>
                </span>
              </div>
            </div>
          </template>
          <template #selected-option="option">
            <template v-if="option.maintenanceName">
              <div class="d-flex align-items-center">
                <media-image
                  :media="option.icon"
                  img-class="image-20"
                  class="mr-3"
                  :recommended="option.recommended"
                  :fallback-icon="option.fallbackIcon"
                />
                <span>{{ option.maintenanceName }}</span>
                <span class="text-muted pl-1">({{ option.maintenanceTypeName }})</span>
              </div>
            </template>
          </template>
        </v-select>
        <ImmyLink
          v-if="isGlobalOrLocalSoftwareSelected && model?.maintenanceIdentifier != null"
          class="btn btn-secondary ml-2"
          rel="noopener"
          target="_blank"
          :to="{
            name: 'Edit Software',
            params: { softwareId: model?.maintenanceIdentifier },
            query: { softwareType: selectedSoftwareType?.toString() },
          }"
        >
          <div class="d-flex align-items-center">
            View <i class="pl-2 fal fa-external-link-alt" />
          </div>
        </ImmyLink>
        <ImmyLink
          v-if="isMaintenanceTaskSelected && model?.maintenanceIdentifier != null"
          class="btn btn-secondary ml-2"
          rel="noopener"
          target="_blank"
          :to="{
            name: 'Edit Task',
            params: { maintenanceTaskId: model?.maintenanceIdentifier },
            query: { maintenanceTaskType: selectedMaintenanceTaskType?.toString() },
          }"
        >
          <div class="d-flex align-items-center">
            View <i class="pl-2 fal fa-external-link-alt" />
          </div>
        </ImmyLink>
      </div>
    </FormGroupWithHelp>
    <div v-if="itemValid === false" class="invalid-feedback-message">
      {{ invalidDescription }}
    </div>
  </div>
</template>

<script lang="ts">
import debounce from "lodash.debounce";
import { defineComponent, PropType } from "vue";
import {
  DatabaseType,
  IGetGlobalMaintenanceTaskResponse,
  IGetLocalMaintenanceTaskResponse,
  IMediaResponseBase,
  MaintenanceTaskCategory,
  MaintenanceType,
  SoftwareLicenseRequirement,
  SoftwareType,
} from "@/api/backend/generated/contracts";
import { chocolateyApi } from "@/api/backend/v1";
import NiniteSoftwareList from "@/assets/js/niniteSoftwareList";

import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { useMaintenanceTasksStore } from "@/store/pinia/maintenance-tasks-store";

import { useSoftwareStore } from "@/store/pinia/software-store";
import {
  databaseTypeFromMaintenanceType,
  getMaintenanceTaskTypeText,
  SoftwareMaintenanceTypes,
  softwareTypeFromMaintenanceType,
  TaskMaintenanceTypes,
} from "@/utils/misc";
import { toast } from "@/utils/toast";

export interface IExcludedSoftware {
  id: number;
  softwareType: SoftwareType;
}

export interface IMaintenanceItemSelectorSearchValue {
  maintenanceIdentifier: string;
  maintenanceType: TaskMaintenanceTypes | SoftwareMaintenanceTypes;
  maintenanceName: string | null;
  agentIntegrationTypeId: string | undefined;
}

export interface ITaskMaintenanceItemSelectorSearchValue
  extends IMaintenanceItemSelectorSearchValue {
  maintenanceType: TaskMaintenanceTypes;
}

export interface ISoftwareMaintenanceItemSelectorSearchValue
  extends IMaintenanceItemSelectorSearchValue {
  maintenanceType: SoftwareMaintenanceTypes;
  configurationTaskId: number | undefined;
  configurationTaskType: DatabaseType | undefined;
}

export interface IMigrateToSupercededTaskRequest {
  supersededByTaskId: number;
  supersededByTaskType: DatabaseType;
}

type ModelType = ISelectableTask | ISelectableSoftware;

interface ISelectableItemBase {
  maintenanceTypeName: string;
  icon: IMediaResponseBase | null;
  recommended: boolean;
  fallbackIcon: string;
  maintenanceType: TaskMaintenanceTypes | SoftwareMaintenanceTypes;
  maintenanceIdentifier: string;
  maintenanceName: string;
  updatedDate: Date | null;
}

interface ISelectableTask extends ISelectableItemBase {
  maintenanceType: TaskMaintenanceTypes;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  supersededByTaskId: number | undefined;
  supersededByTaskType: DatabaseType | undefined;
  notes: string | null;
  agentIntegrationTypeId: string | undefined;
}

interface ISelectableSoftware extends ISelectableItemBase {
  maintenanceType: SoftwareMaintenanceTypes;
  notes: string | null;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  configurationTaskId: number | undefined;
  configurationTaskType?: DatabaseType;
  agentIntegrationTypeId: string | undefined;
}

function modelsEqual(obj1: { maintenanceType: MaintenanceType; maintenanceIdentifier: string } | null, obj2: { maintenanceType: MaintenanceType; maintenanceIdentifier: string } | null) {
  if (!obj1)
    obj1 = {} as { maintenanceType: MaintenanceType; maintenanceIdentifier: string };
  if (!obj2)
    obj2 = {} as { maintenanceType: MaintenanceType; maintenanceIdentifier: string };
  return (
    obj1.maintenanceIdentifier == obj2.maintenanceIdentifier
    && obj1.maintenanceType === obj2.maintenanceType
  );
}

const SOFTWARE_FALLBACK_ICON = "fa-box";
const MAINTENANCE_TASK_FALLBACK_ICON = "fa-tasks";

const Events = {
  INPUT: "update:modelValue",
  INITIALIZED: "initialized",
  IS_VALID: "isValid",
};

function makeTaskModel(
  task: IGetGlobalMaintenanceTaskResponse | IGetLocalMaintenanceTaskResponse,
  maintenanceType: TaskMaintenanceTypes,
): ISelectableTask {
  return {
    maintenanceType,
    maintenanceTypeName: getMaintenanceTaskTypeText(
      task.databaseType,
      task.maintenanceTaskCategory,
      task.isConfigurationTask,
    ),
    maintenanceTaskCategory: task.maintenanceTaskCategory,
    maintenanceIdentifier: task.id.toString(),
    maintenanceName: task.name,
    updatedDate: new Date(task.updatedDateUTC),
    recommended: task.recommended,
    icon: task.icon ?? null,
    fallbackIcon: MAINTENANCE_TASK_FALLBACK_ICON,
    notes: task.notes ?? null,
    supersededByTaskId: task.supersededByTaskId,
    supersededByTaskType: task.supersededByTaskType,
    agentIntegrationTypeId: undefined,
  };
}

function filterItems(items: ModelType[], filter: string | null | undefined) {
  const lowerFilter = filter?.toLowerCase();
  if (lowerFilter != null)
    items = items.filter(a => a?.maintenanceName?.toLowerCase()?.includes(lowerFilter));

  // push the recommended items (ordered alphabetically) to the top
  // and then sort by updated date for the rest
  const recommended = items
    .filter(a => a.recommended)
    .sort((a, b) => {
      const nameA = a?.maintenanceName?.toUpperCase()?.trim(); // ignore upper and lowercase
      const nameB = b?.maintenanceName?.toUpperCase()?.trim(); // ignore upper and lowercase
      if (nameA < nameB)
        return -1;
      if (nameA > nameB)
        return 1;
      // names must be equal
      return 0;
    });

  const notRecommended = items
    .filter(a => !a.recommended)
    .sort((a, b) => {
      if (b.updatedDate && a.updatedDate) {
        return b.updatedDate.valueOf() - a.updatedDate.valueOf();
      }
      else if (b.updatedDate) {
        return 1;
      }
      else if (a.updatedDate) {
        return -1;
      }
      else {
        if (
          b.maintenanceType === MaintenanceType.ChocolateySoftware
          && a.maintenanceType === MaintenanceType.ChocolateySoftware
        )
          return b.maintenanceName > a.maintenanceName ? 1 : -1;
        else if (
          b.maintenanceType === MaintenanceType.NiniteSoftware
          && a.maintenanceType === MaintenanceType.NiniteSoftware
        )
          return b.maintenanceName > a.maintenanceName ? 1 : -1;
        else if (
          b.maintenanceType === MaintenanceType.ChocolateySoftware
          && a.maintenanceType === MaintenanceType.NiniteSoftware
        )
          return -1;
        else if (
          b.maintenanceType === MaintenanceType.NiniteSoftware
          && a.maintenanceType === MaintenanceType.ChocolateySoftware
        )
          return 1;
        else return 0;
      }
    });

  return [...recommended, ...notRecommended];
}

export default defineComponent({
  name: "MaintenanceItemSelectorSearch",
  props: {
    modelValue: {
      type: Object as PropType<IMaintenanceItemSelectorSearchValue | null>,
      required: false,
      default: null,
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    onlySoftwareThatSupportLicenses: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreGlobal: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreLocal: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreChocolatey: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreNinite: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreMaintenanceTasks: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreComputerMaintenanceTasks: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    ignoreCloudTasks: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    limitToCloudTasks: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    searchLabel: {
      type: String as PropType<string>,
      default: "Software / Task",
    },
    searchPlaceholderText: {
      type: String as PropType<string>,
      default: "Search maintenance items",
    },
    searchDescription: {
      type: String as PropType<string>,
      default: "",
    },
    invalidDescription: {
      type: String as PropType<string>,
      default: "Search and select a maintenance item",
    },
    showInvalidDescription: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    clearSelection: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    includeConfigurationTasks: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: true,
    },
    removeProviderLinkedSoftware: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: true,
    },
    globalOnly: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    showItemNotes: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    showDeprecationAlert: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showDeprecationAlertMigrateButton: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    assignmentId: {
      type: Number as PropType<number | null>,
      required: false,
      default: null,
    },
    excludedSoftware: {
      type: Object as PropType<IExcludedSoftware | IExcludedSoftware[] | null>,
      required: false,
      default: null,
    },
  },
  emits: [Events.INPUT, Events.IS_VALID, Events.INITIALIZED, "update:modelValue"],
  setup() {
    return {
      maintenanceTasksStore: useMaintenanceTasksStore(),
      softwareStore: useSoftwareStore(),
    };
  },
  data() {
    return {
      model: null as ModelType | null,
      initialized: false as boolean,
      loadingMaintenanceItems: false as boolean,
      chocolateySearchResults: [] as ISelectableSoftware[],
      filter: "" as string,
      selectionDoesNotExist: false as boolean,
      showMigrateToSupersedingDeployment: false as boolean,
    };
  },
  computed: {
    serialize():
      | ISoftwareMaintenanceItemSelectorSearchValue
      | ITaskMaintenanceItemSelectorSearchValue
      | null {
      if (!this.model)
        return null;
      const base = {
        maintenanceIdentifier: this.model.maintenanceIdentifier,
        maintenanceType: this.model.maintenanceType,
        maintenanceName: this.model.maintenanceName,
        agentIntegrationTypeId: this.model.agentIntegrationTypeId,
      };

      if ("configurationTaskId" in this.model) {
        return {
          ...base,
          configurationTaskId: this.model.configurationTaskId,
          configurationTaskType: this.model.configurationTaskType,
        };
      }
      return base as ITaskMaintenanceItemSelectorSearchValue;
    },
    itemValid(): boolean {
      if (!this.showInvalidDescription)
        return true;
      return this.model?.maintenanceIdentifier != null;
    },
    isSelected(): boolean {
      return this.model != null;
    },
    isGlobalOrLocalSoftwareSelected(): boolean {
      return (
        this.isSelected
        && (this.model?.maintenanceType === MaintenanceType.GlobalSoftware
          || this.model?.maintenanceType === MaintenanceType.LocalSoftware)
      );
    },
    isMaintenanceTaskSelected(): boolean {
      return (
        this.isSelected
        && (this.model?.maintenanceType === MaintenanceType.GlobalMaintenanceTask
          || this.model?.maintenanceType === MaintenanceType.LocalMaintenanceTask)
      );
    },
    supersedingTask(): IGetLocalMaintenanceTaskResponse | IGetGlobalMaintenanceTaskResponse | null {
      if (!this.model)
        return null;
      if (!("supersededByTaskId" in this.model))
        return null;
      if (this.model.supersededByTaskId == null || this.model.supersededByTaskType == null)
        return null;

      const taskId = this.model.supersededByTaskId;
      const taskType = this.model.supersededByTaskType;
      if (this.model.supersededByTaskType === DatabaseType.Global)
        return this.globalTasks.find(i => i.id === taskId && i.databaseType === taskType) ?? null;
      else return this.localTasks.find(i => i.id === taskId && i.databaseType === taskType) ?? null;
    },
    selectedSoftwareType(): SoftwareType | undefined {
      if (!this.model)
        return;
      return softwareTypeFromMaintenanceType(this.model.maintenanceType);
    },
    selectedMaintenanceTaskType(): DatabaseType | undefined {
      if (!this.model)
        return;
      return databaseTypeFromMaintenanceType(this.model.maintenanceType);
    },
    localTasks(): IGetLocalMaintenanceTaskResponse[] {
      return this.maintenanceTasksStore.local;
    },
    globalTasks(): IGetGlobalMaintenanceTaskResponse[] {
      return this.maintenanceTasksStore.global;
    },
    chocoProviderIdSet(): Set<string> {
      if (!this.removeProviderLinkedSoftware)
        return new Set();

      const chocoProviderIdSetFromLocal = this.selectableLocalSoftware.reduce((set, s) => {
        const id = s.chocoProviderSoftwareId;
        if (id != null)
          set.add(id);
        return set;
      }, new Set<string>());
      const chocoProviderIdSetFromGlobal = this.selectableGlobalSoftware.reduce((set, s) => {
        const id = s.chocoProviderSoftwareId;
        if (id != null)
          set.add(id);
        return set;
      }, new Set<string>());

      return new Set([...chocoProviderIdSetFromLocal, ...chocoProviderIdSetFromGlobal]);
    },
    niniteProviderIdSet(): Set<string> {
      if (!this.removeProviderLinkedSoftware)
        return new Set();

      const niniteProviderIdSetFromLocal = this.selectableLocalSoftware.reduce((set, s) => {
        const id = s.niniteProviderSoftwareId;
        if (id != null)
          set.add(id);
        return set;
      }, new Set<string>());
      const niniteProviderIdSetFromGlobal = this.selectableGlobalSoftware.reduce((set, s) => {
        const id = s.niniteProviderSoftwareId;
        if (id != null)
          set.add(id);
        return set;
      }, new Set<string>());

      return new Set([...niniteProviderIdSetFromLocal, ...niniteProviderIdSetFromGlobal]);
    },
    selectableMaintenanceItems(): ModelType[] {
      let items: (ISelectableTask | ISelectableSoftware)[] = [];
      const excludedSoftware: IExcludedSoftware[] | null = Array.isArray(this.excludedSoftware)
        ? [...this.excludedSoftware]
        : null;

      const filter = this.filter?.toLowerCase();

      if (this.limitToCloudTasks) {
        items.push(...this.selectableLocalMaintenanceTasks);
        items.push(...this.selectableGlobalMaintenanceTasks);
        return filterItems(items, filter);
      }

      if (!this.ignoreGlobal) {
        items.push(...this.selectableGlobalSoftware);
        if (!this.ignoreMaintenanceTasks)
          items.push(...this.selectableGlobalMaintenanceTasks);
      }

      if (!this.globalOnly) {
        if (!this.ignoreLocal)
          items.push(...this.selectableLocalSoftware);

        if (!this.ignoreMaintenanceTasks)
          items.push(...this.selectableLocalMaintenanceTasks);

        if (!this.ignoreChocolatey) {
          // exclude chocolatey software that are linked as a provider for a local or global software
          const choco = this.selectableChocolateySoftware.filter(
            c =>
              c.maintenanceIdentifier == null
              || !this.chocoProviderIdSet.has(c.maintenanceIdentifier),
          );
          items.push(...choco);
        }

        if (!this.ignoreNinite) {
          // exclude ninite software that are linked as a provider for a local or global software
          const ninite = this.selectableNiniteSoftware.filter(
            c =>
              c.maintenanceIdentifier == null
              || !this.niniteProviderIdSet.has(c.maintenanceIdentifier),
          );
          items.push(...ninite);
        }
      }

      if (excludedSoftware != null) {
        items = items.filter((a) => {
          const softwareType = softwareTypeFromMaintenanceType(a.maintenanceType);
          if (softwareType === undefined)
            return false;

          return !excludedSoftware?.find(
            e => e.id === +a.maintenanceIdentifier && e.softwareType === softwareType,
          );
        });

        return filterItems(items, filter);
      }

      return filterItems(items, filter);
    },
    selectableLocalSoftware(): ISelectableSoftware[] {
      const maintenanceType = MaintenanceType.LocalSoftware;
      const maintenanceTypeName = EnumTextHelpers.MaintenanceType.GetTextByValue(maintenanceType);
      let software = this.softwareStore.local;

      if (this.onlySoftwareThatSupportLicenses)
        software = software.filter(a => a.licenseRequirement !== SoftwareLicenseRequirement.None);

      return software.map(a => ({
        maintenanceType,
        maintenanceTypeName,
        maintenanceIdentifier: a.id?.toString() ?? "",
        maintenanceName: a.name,
        updatedDate: new Date(a.updatedDateUTC),
        configurationTaskType: a.maintenanceTaskType,
        configurationTaskId: a.maintenanceTaskId,
        icon: a.softwareIcon ?? null,
        recommended: a.recommended,
        chocoProviderSoftwareId: a.chocoProviderSoftwareId,
        niniteProviderSoftwareId: a.niniteProviderSoftwareId,
        fallbackIcon: SOFTWARE_FALLBACK_ICON,
        notes: a.notes ?? null,
        agentIntegrationTypeId: a.agentIntegrationTypeId,
      }));
    },
    selectableGlobalSoftware(): ISelectableSoftware[] {
      const maintenanceType = MaintenanceType.GlobalSoftware;
      const maintenanceTypeName = EnumTextHelpers.MaintenanceType.GetTextByValue(maintenanceType);

      let software = this.softwareStore.global;

      if (this.onlySoftwareThatSupportLicenses)
        software = software.filter(a => a.licenseRequirement !== SoftwareLicenseRequirement.None);

      return software.map(a => ({
        maintenanceType,
        maintenanceTypeName,
        maintenanceIdentifier: a.id?.toString() ?? "",
        maintenanceName: a.name,
        updatedDate: new Date(a.updatedDateUTC),
        configurationTaskType: a.maintenanceTaskType,
        configurationTaskId: a.maintenanceTaskId,
        icon: a.softwareIcon ?? null,
        recommended: a.recommended,
        chocoProviderSoftwareId: a.chocoProviderSoftwareId,
        niniteProviderSoftwareId: a.niniteProviderSoftwareId,
        fallbackIcon: SOFTWARE_FALLBACK_ICON,
        notes: a.notes ?? null,
        agentIntegrationTypeId: a.agentIntegrationTypeId,
      }));
    },
    selectableNiniteSoftware(): ISelectableSoftware[] {
      const maintenanceType = MaintenanceType.NiniteSoftware;
      const maintenanceTypeName = EnumTextHelpers.MaintenanceType.GetTextByValue(maintenanceType);
      return NiniteSoftwareList.map((a) => {
        return {
          maintenanceType,
          maintenanceTypeName,
          maintenanceIdentifier: a,
          maintenanceName: a,
          fallbackIcon: SOFTWARE_FALLBACK_ICON,
          recommended: false,
          icon: null,
          updatedDate: null,
          notes: null,
          configurationTaskId: undefined,
          agentIntegrationTypeId: undefined,
        };
      });
    },
    selectableChocolateySoftware(): ISelectableSoftware[] {
      return this.chocolateySearchResults;
    },
    selectableLocalMaintenanceTasks(): ISelectableTask[] {
      const maintenanceType = MaintenanceType.LocalMaintenanceTask;

      let tasks = this.localTasks;

      if (this.limitToCloudTasks) {
        tasks = tasks.filter(a => a.maintenanceTaskCategory === MaintenanceTaskCategory.Tenant);
        return tasks.map(a => makeTaskModel(a, maintenanceType));
      }

      if (this.ignoreCloudTasks)
        tasks = tasks.filter(a => a.maintenanceTaskCategory !== MaintenanceTaskCategory.Tenant);

      if (this.ignoreComputerMaintenanceTasks)
        tasks = tasks.filter(a => a.maintenanceTaskCategory !== MaintenanceTaskCategory.Computer);

      if (!this.includeConfigurationTasks)
        tasks = tasks.filter(a => !a.isConfigurationTask);

      return tasks.map(a => makeTaskModel(a, maintenanceType));
    },
    selectableGlobalMaintenanceTasks(): ISelectableTask[] {
      const maintenanceType = MaintenanceType.GlobalMaintenanceTask;

      let tasks = this.globalTasks;

      if (this.limitToCloudTasks) {
        tasks = tasks.filter(a => a.maintenanceTaskCategory === MaintenanceTaskCategory.Tenant);
        return tasks.map(a => makeTaskModel(a, maintenanceType));
      }

      if (this.ignoreCloudTasks)
        tasks = tasks.filter(a => a.maintenanceTaskCategory !== MaintenanceTaskCategory.Tenant);

      if (this.ignoreComputerMaintenanceTasks)
        tasks = tasks.filter(a => a.maintenanceTaskCategory !== MaintenanceTaskCategory.Computer);

      if (!this.includeConfigurationTasks)
        tasks = tasks.filter(a => !a.isConfigurationTask);

      return tasks.map(a => makeTaskModel(a, maintenanceType));
    },
  },
  watch: {
    selectionDoesNotExist(val) {
      if (val)
        this.$emit("update:modelValue", null);
    },
    serialize: {
      async handler(
        newVal:
          | ISoftwareMaintenanceItemSelectorSearchValue
          | ITaskMaintenanceItemSelectorSearchValue
          | null,
        oldVal:
          | ISoftwareMaintenanceItemSelectorSearchValue
          | ITaskMaintenanceItemSelectorSearchValue
          | null,
      ) {
        if (modelsEqual(newVal, oldVal))
          return;
        this.$emit("update:modelValue", newVal ?? null);
      },
    },
    modelValue: {
      handler(
        newVal: IMaintenanceItemSelectorSearchValue | null,
        oldVal: IMaintenanceItemSelectorSearchValue | null,
      ) {
        if (!this.initialized)
          return;
        if (modelsEqual(newVal, oldVal) || modelsEqual(newVal, this.model))
          return;
        if (newVal) {
          this.model
            = this.selectableMaintenanceItems.find(
              i =>
                i.maintenanceType === newVal.maintenanceType
                && i.maintenanceIdentifier === newVal.maintenanceIdentifier,
            ) ?? null;
        }
        else {
          this.model = null;
        }
      },
      immediate: true,
    },
  },
  async mounted() {
    try {
      this.loadingMaintenanceItems = true;
      await Promise.all([this.maintenanceTasksStore.loadAll(), this.softwareStore.loadAll()]);
    }
    catch (err) {
      toast.showError("An error occurred while fetching software versions.", err);
    }
    finally {
      this.loadingMaintenanceItems = false;
    }
    await this.$nextTick();
    const val = this.modelValue;
    if (val) {
      // if we are initializing a chocolatey software, then just create the model
      // since we don't have the item in the selectable list
      if (val.maintenanceType === MaintenanceType.ChocolateySoftware) {
        // have to declare type like this instead of { } "as ISelectableSoftware"
        // because typescript did not complain when using "as ISelectableSoftware"
        const model: ISelectableSoftware = {
          maintenanceType: MaintenanceType.ChocolateySoftware,
          maintenanceTypeName: EnumTextHelpers.MaintenanceType.GetTextByValue(
            MaintenanceType.ChocolateySoftware,
          ),
          maintenanceIdentifier: val.maintenanceIdentifier,
          maintenanceName: val.maintenanceIdentifier,
          fallbackIcon: SOFTWARE_FALLBACK_ICON,
          configurationTaskId: undefined,
          notes: null,
          icon: null,
          recommended: false,
          updatedDate: null,
          agentIntegrationTypeId: undefined,
        };
        this.model = model;
      }
      else {
        this.model
          = this.selectableMaintenanceItems.find(
            i =>
              i.maintenanceType === val.maintenanceType
              && i.maintenanceIdentifier === val.maintenanceIdentifier,
          ) ?? null;
      }
    }
    await this.$nextTick();
    this.initialized = true;
    this.$emit(Events.INITIALIZED);
  },
  methods: {
    migrateDeploymentToSupersedingTask() {
      this.showMigrateToSupersedingDeployment = true;
    },
    onSelected(val: unknown) {
      if (val != null) {
        this.$nextTick(() => {
          if (this.clearSelection) {
            (this.$refs.selector as { clearSelection: () => void } | undefined)?.clearSelection();
          }
        });
      }
    },
    onDropdownClose() {
      this.filter = "";
    },
    async onSearch(search: string, loading: boolean) {
      if (search)
        this.search(loading, search, this);
    },
    search: debounce(
      async (
        loading: boolean,
        search: string,
        vm: {
          filter: string;
          ignoreChocolatey: boolean;
          chocolateySearchResults: ISelectableSoftware[];
        },
      ) => {
        if (!search) {
          vm.chocolateySearchResults = [];
          vm.filter = "";
          return;
        }
        try {
          vm.filter = search;
          if (!vm.ignoreChocolatey) {
            const packages = await chocolateyApi.search(search);
            vm.chocolateySearchResults = packages.map((p) => {
              const s: ISelectableSoftware = {
                maintenanceType: MaintenanceType.ChocolateySoftware,
                maintenanceTypeName: EnumTextHelpers.MaintenanceType.GetTextByValue(
                  MaintenanceType.ChocolateySoftware,
                ),
                maintenanceIdentifier: p.id,
                maintenanceName: p.title,
                fallbackIcon: SOFTWARE_FALLBACK_ICON,
                configurationTaskId: undefined,
                notes: null,
                icon: null,
                recommended: false,
                updatedDate: null,
                agentIntegrationTypeId: undefined,
              };
              return s;
            });
          }
        }
        catch (err) {
          // eslint-disable-next-line no-console
          console.log(err);
          // we don't want to display an error to the user
          // but we want to log the error to App Insights
        }
      },
      350,
    ),
  },
});
</script>

<style lang="scss" scoped>
.vs__dropdown-option--highlight {
  .option {
    .type {
      color: white;
    }
  }
}

:deep(.vs__selected) {
  .media-image {
    .recommended-icon {
      font-size: 12px !important;
      top: -3px;
    }
  }
}

.option {
  margin: 2px 0px;
  p {
    margin: 0px;
  }

  .type {
    font-size: 11px;
    color: grey;
  }
}

.item-notes {
  white-space: pre-line;
  word-break: break-word;
}

.search-wrapper {
  row-gap: 0.5rem;
}

.migration-area {
  padding: 1rem;
  background-color: var(--bg-default3);
}
</style>
