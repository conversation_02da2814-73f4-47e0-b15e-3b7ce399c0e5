<template>
  <div>
    <form-group-with-help label="License" alert-button-position="before-label">
      <template #help-alert-content>
        <p>
          Licenses shown are the ones that are applicable to the current software, version, and
          tenant.
        </p>

        <p>
          e.g. If a license is restricted to major version 10.x, and you are targeting a specific
          version of 9.4.0, then this license will be filtered out since it does not apply.
        </p>

        <p>
          e.g. If a license is restricted to a specific version 9.4.0, and you are targeting
          "latest" or "any" version, then this license will be filtered out since it does not apply.
        </p>

        <p>
          e.g. If a license is restricted to Company A, but you are targeting Company B, then this
          license will be filtered out since it does not apply.
        </p>
        <div v-if="props.helpText">
          <h3>License Description</h3>
          <p class="license-description">
            {{ props.helpText }}
          </p>
        </div>
      </template>
      <template v-if="state.loading">
        <i class="fa fa-spinner fa-spin" />
        Loading licenses
      </template>
      <template v-else>
        <div class="d-flex justify-content-between">
          <v-select
            v-model="state.selectedLicense"
            class="w-100"
            :options="licensesForDropdown"
            label="name"
            placeholder="Select a license"
          >
            <template #option="option">
              <div class="d-flex align-items-center justify-content-between">
                <span>{{ option.name }}</span>
                <span v-if="option.tenantName">
                  <span class="ml-3">{{ option.tenantName }}</span>
                </span>
              </div>
            </template>
          </v-select>
          <div class="license-actions">
            <ImmyLink
              v-if="state.selectedLicense"
              class="btn btn-secondary ml-2"
              rel="noopener"
              target="_blank"
              :to="{
                name: 'Edit License',
                params: { licenseId: state.selectedLicense.id.toString() },
              }"
            >
              <div class="d-flex align-items-center">
                View <i class="pl-2 fal fa-external-link-alt" />
              </div>
            </ImmyLink>
            <ImmyLink
              class="btn btn-secondary ml-2"
              rel="noopener"
              target="_blank"
              :to="{
                name: 'New License',
                query: { maintenanceIdentifier, maintenanceType: maintenanceType?.toString() },
              }"
            >
              <div class="d-flex align-items-center">
                Add <i class="pl-1 fas fa-plus" />
              </div>
            </ImmyLink>
          </div>
        </div>
        <ImmyAlert v-if="state.showTenantMismatchAlert" class="mt-3" show variant="warning">
          The selected license is assigned to a different tenant. Please select a different license.
        </ImmyAlert>
      </template>
    </form-group-with-help>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from "vue";
import { IGetLicenseResponse, SoftwareType } from "@/api/backend/generated/contracts";

import { licensesApi } from "@/api/backend/v1";
import { getMajorFromSemVer, maintenanceTypeFromSoftwareType } from "@/utils/misc";

const props = withDefaults(defineProps<IProps>(), {
  filter: null,
  modelValue: null,
  helpText: null,
});

const emit = defineEmits<{
  (e: "update:modelValue", val: IGetLicenseResponse | null): void;
}>();

function getApplicableLicensesFromFilter(filter: ILicenseSelectorFilter | null, licenses: IGetLicenseResponse[]) {
  const tenantId = filter?.tenantId;
  const semanticVersion = filter?.semanticVersion;

  const ret = licenses.filter((l) => {
    // if we are targeting a specific tenant,
    // then omit licenses where tenant id is not null and not equal to the filtered tenant
    if (tenantId && l.tenantId != null && l.tenantId != tenantId)
      return false;

    if (semanticVersion) {
      // return all licenses applicable for the version from the filter

      // if license doesn't have a version then it can be used
      if (!l.semanticVersion)
        return true;

      // check if license is restricted to major version
      // and check if it is equal to the version from the filter
      if (l.restrictToMajorVersion)
        return getMajorFromSemVer(semanticVersion) == getMajorFromSemVer(l.semanticVersion);

      // check if license's version matches exactly the version from the filter
      return l.semanticVersion === semanticVersion;
    }
    else {
      // If we are not filtering on a specific version, e.g. latest, any, or update if found,
      // then return all licenses without a specific version.
      // Licenses restricted to major versions are allowed.
      return !l.semanticVersion || l.restrictToMajorVersion;
    }
  });

  return ret;
}

interface IProps {
  filter?: ILicenseSelectorFilter | null;
  modelValue?: { id: number } | null;
  helpText?: string | null;
}

interface IState {
  licenses: IGetLicenseResponse[];
  selectedLicense: IGetLicenseResponse | null;
  loading: boolean;
  showTenantMismatchAlert: boolean;
}

interface ILicenseSelectorFilter {
  softwareType: SoftwareType;
  softwareIdentifier: string;
  tenantId: number | null;
  semanticVersion: string | null;
}

const state: IState = reactive({
  licenses: [],
  selectedLicense: null,
  loading: false,
  showTenantMismatchAlert: false,
});

const maintenanceType = computed(() => {
  if (props.filter?.softwareType != null)
    return maintenanceTypeFromSoftwareType(props.filter.softwareType);
  return undefined;
});
const maintenanceIdentifier = computed(() => {
  return props.filter?.softwareIdentifier;
});
const licensesForDropdown = computed(() => {
  return getApplicableLicensesFromFilter(props.filter, state.licenses);
});

async function fetchLicenses(softwareIdentifier: string, softwareType: SoftwareType) {
  try {
    state.loading = true;
    const typeFilter = `SoftwareType==${softwareType}`;
    const idFilter = `SoftwareIdentifier==${softwareIdentifier}`;
    const filters = `${typeFilter},${idFilter}`;
    state.licenses = await licensesApi.getAll({ filters });
  }
  finally {
    state.loading = false;
  }
}

watch(
  () => props.filter,
  async (newValue, oldValue) => {
    if (!newValue)
      return;

    const { softwareType, softwareIdentifier, tenantId } = newValue;

    // if we have a tenantId filter and we have a selected license that does not match the tenantId
    // then we should clear it and show an alert
    const selectedLicenseTenantId = state.selectedLicense?.tenantId;
    if (
      tenantId != null
      && selectedLicenseTenantId != null
      && selectedLicenseTenantId !== tenantId
    ) {
      state.selectedLicense = null;
      state.showTenantMismatchAlert = true;
    }

    const oldSoftwareIdentifier = oldValue?.softwareIdentifier;
    if (softwareIdentifier === oldSoftwareIdentifier)
      return;

    await fetchLicenses(softwareIdentifier, softwareType);

    // set selected license
    if (props.modelValue != null) {
      state.selectedLicense
        = licensesForDropdown.value.find(d => d.id === props.modelValue?.id) ?? null;
    }
  },
  { immediate: true },
);

watch(
  () => state.selectedLicense,
  (val) => {
    if (val !== null)
      state.showTenantMismatchAlert = false;
    emit("update:modelValue", val);
  },
);
</script>

<style lang="scss" scoped>
.license-description {
  white-space: pre-line;
}
</style>
