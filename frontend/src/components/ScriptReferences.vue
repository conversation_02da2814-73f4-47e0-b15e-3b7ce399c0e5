<template>
  <div class="script-references">
    <div v-if="loading">
      <i class="fas fa-spinner fa-spin" />
      Loading reference data
    </div>
    <div v-else>
      <p class="m-0">
        This script is referenced in
        <ImmyLink @click="showingSoftware = !showingSoftware">
          {{ software.length }} Software
          <i v-if="showingSoftware" class="fas fa-caret-up" />
          <i v-else class="fas fa-caret-down" />
        </ImmyLink>
      </p>
      <ImmyDxDataGrid
        v-if="software && software.length"
        v-show="showingSoftware"
        id="software-reference-list"
        :data-source="software"
        :columns="softwareColumns"
        :store-state="false"
      >
        <template #template="{ data }">
          <ImmyButton
            variant="link"
            :to="{
              name: 'Edit Software',
              params: { softwareId: data.id },
              query: { softwareType: data.softwareType },
            }"
          >
            {{ data.name }}
            <i class="text-muted"> ({{ softwareType.GetTextByValue(data.softwareType) }}) </i>
          </ImmyButton>
        </template>
      </ImmyDxDataGrid>
      <p class="m-0">
        This script is referenced in
        <ImmyLink @click="showingVersions = !showingVersions">
          {{ versions.length }} Software Version{{ versions.length > 1 ? "s" : "" }}
          <i v-if="showingVersions" class="fas fa-caret-up" />
          <i v-else class="fas fa-caret-down" />
        </ImmyLink>
      </p>
      <ImmyDxDataGrid
        v-if="versions && versions.length"
        v-show="showingVersions"
        id="versions-reference-list"
        :data-source="versions"
        :columns="versionColumns"
        :store-state="false"
      >
        <template #template="{ data }">
          <ImmyButton
            variant="link"
            :to="{
              name: 'Edit Software Version',
              params: { softwareId: data.softwareIdentifier },
              query: {
                softwareType: data.softwareType,
                semanticVersion: data.semanticVersionString,
              },
            }"
          >
            {{ data.displayName }} - {{ data.semanticVersionString }}
            <i class="text-muted"> ({{ softwareType.GetTextByValue(data.softwareType) }}) </i>
          </ImmyButton>
        </template>
      </ImmyDxDataGrid>
      <p class="m-0">
        This script is referenced in
        <ImmyLink @click="showingMaintenanceTasks = !showingMaintenanceTasks">
          {{ maintenanceTasks.length }} Task{{ maintenanceTasks.length > 1 ? "s" : "" }}
          <i v-if="showingMaintenanceTasks" class="fas fa-caret-up" />
          <i v-else class="fas fa-caret-down" />
        </ImmyLink>
      </p>
      <ImmyDxDataGrid
        v-if="maintenanceTasks && maintenanceTasks.length"
        v-show="showingMaintenanceTasks"
        id="task-reference-list"
        :data-source="maintenanceTasks"
        :columns="maintenanceTaskColumns"
        :store-state="false"
      >
        <template #template="{ data }">
          <ImmyButton
            variant="link"
            :to="{
              name: 'Edit Task',
              params: { maintenanceTaskId: data.id },
              query: {
                maintenanceTaskType: data.databaseType,
              },
            }"
          >
            {{ data.name }}
            <i class="text-muted"> ({{ databaseType.GetTextByValue(data.databaseType) }}) </i>
          </ImmyButton>
        </template>
      </ImmyDxDataGrid>
    </div>
  </div>
</template>

<script>
import { DatabaseType } from "@/api/backend/generated/enums";
import { scriptsApi } from "@/api/backend/v1";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";

const softwareColumns = [
  {
    dataField: "name",
    caption: "Software",
    cellTemplate: "template",
  },
];

const versionColumns = [
  {
    dataField: "name",
    caption: "Software Versions",
    cellTemplate: "template",
    calculateCellValue(rowData) {
      return JSON.stringify({
        rowData,
      });
    },
  },
];

const maintenanceTaskColumns = [
  {
    dataField: "name",
    caption: "Tasks",
    cellTemplate: "template",
  },
];

export default {
  name: "ScriptReferences",
  props: {
    scriptId: {
      type: Number,
      required: true,
    },
    scriptType: {
      type: Number,
      required: true,
    },
    showReferences: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      showingSoftware: this.showReferences,
      showingVersions: this.showReferences,
      showingMaintenanceTasks: this.showReferences,
      software: [],
      versions: [],
      maintenanceTasks: [],
      softwareColumns,
      versionColumns,
      maintenanceTaskColumns,
    };
  },
  computed: {
    softwareType() {
      return EnumTextHelpers.SoftwareType;
    },
    databaseType() {
      return EnumTextHelpers.DatabaseType;
    },
  },
  watch: {
    scriptId: {
      async handler(newVal, oldVal) {
        if (newVal === oldVal)
          return;
        try {
          this.loading = true;
          const references
            = this.scriptType === DatabaseType.Global
              ? await scriptsApi.getGlobalScriptReferences(newVal)
              : await scriptsApi.getLocalScriptReferences(newVal);
          this.software = references.localSoftware.concat(references.globalSoftware);
          this.versions = references.localSoftwareVersions.concat(
            references.globalSoftwareVersions,
          );
          this.maintenanceTasks = references.localMaintenanceTasks.concat(
            references.globalMaintenanceTasks,
          );
        }
        finally {
          this.loading = false;
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped></style>
