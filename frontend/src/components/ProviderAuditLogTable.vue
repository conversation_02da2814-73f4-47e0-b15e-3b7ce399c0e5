<template>
  <div class="p-3">
    <template v-if="!integrationAuditLoggingEnabled">
      <ImmyAlert class="m-0" show variant="warning">
        Audit log streaming is currently disabled. You can enable it under
        <ImmyLink :to="{ name: 'Preferences', query: { search: 'integration audit logging' } }">
          System Preferences
        </ImmyLink>.
      </ImmyAlert>
    </template>
    <template v-else>
      <div class="d-flex align-items-center gap-3 mb-3">
        <p class="text-muted m-0">
          Audit logs for this integration will be streamed as they occur.
        </p>
        <LoadButton size="sm" :variant="streaming ? 'danger' : 'success'" :handler="toggleStreaming">
          <i v-if="streaming" class="fa fa-spinner fa-spin mr-2" /><i v-else class="fa fa-play mr-2" />
          {{ streaming ? "Stop" : "Start" }} streaming
        </LoadButton>
        <LoadButton size="sm" :handler="clearLogs" title="Clear table">
          <span>Clear logs</span>
        </LoadButton>
      </div>
      <ImmyList :items="logs" :paged="false">
        <template #no-results>
          No audit logs have been streamed yet
        </template>
        <template #item="{ data }">
          <div class="w-100">
            <div class="d-flex align-items-center gap-3 justify-content-between">
              <div class="d-flex align-items-center gap-2">
                <i v-if="!data.log.correlationId" class="fal fa-spin fa-spinner text-running" />
                <i v-else-if="data.log.errorMessage" class="fal fa-times-circle text-danger" />
                <i v-else class="fal fa-check-circle text-success" />
                <span>{{ data.log.methodName }}</span>
                <template v-if="data.duration">
                  <span>-</span>
                  <span class="text-muted" title="Total Execution Time">{{ data.duration }}</span>
                </template>
              </div>
              <div class="d-flex align-items-center gap-3">
                <ImmyButton @click="showDetails(data.log)">
                  Details
                </ImmyButton>
                <span>{{ getFormattedDate(data.log) }}</span>
              </div>
              <span v-if="data.log?.errorMessage" class="text-danger">
                {{ data.log?.errorMessage }}
              </span>
            </div>
          </div>
        </template>
      </ImmyList>
      <ProviderAuditLogModal v-if="showingDetailsForAuditLog" :log="showingDetailsForAuditLog" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import { IProviderAuditLog } from "@/api/backend/generated/interfaces";
import UserHub from "@/api/backend/signalr-hubs/user-hub";
import UserHubEventInstance from "@/api/backend/signalr-hubs/UserHubEventInstance";
import { usePreferencesStore } from "@/store/pinia/preferences-store";
import { formatDate } from "@/utils/misc";

interface ICorrelatedProviderAuditLog {
  id: string;
  log: IProviderAuditLog;
  duration?: string;
}

const props = defineProps<{
  providerLinkId: number;
}>();
const userHubEventInstance = new UserHubEventInstance();
const preferencesStore = usePreferencesStore();

const logs = ref<ICorrelatedProviderAuditLog[]>([]);

function clearLogs() {
  logs.value = [];
}

function getFormattedDate(log: IProviderAuditLog) {
  return formatDate(log.timeUtc, "mmm d, yyyy h:MM:ss.ltt Z");
}

const streaming = ref(true);
function toggleStreaming() {
  streaming.value = !streaming.value;

  if (streaming.value)
    startListening();
  else
    stopListening();
}

function sanitizeLog(log: IProviderAuditLog) {
  // handle special case for inbound http request body parsing to json
  if (log.methodName.startsWith("Inbound HTTP Request") && log.input != null) {
    const input = Object.assign({}, log.input);
    try {
      input.request_body = JSON.parse(input.request_body);
    }
    catch {
      // ignore
    }
    log.input = input;
  }
}

function startListening() {
  userHubEventInstance.onEvent(UserHub.Client.ProviderAuditLogAdded, (event) => {
    if (event.log.providerLinkId != props.providerLinkId)
      return;
    const existing = logs.value.find(a => a.id === event.log.correlationId);

    sanitizeLog(event.log);

    if (existing) {
      const ms = Math.abs(
        new Date(event.log.timeUtc).valueOf() - new Date(existing.log.timeUtc).valueOf(),
      );
      existing.duration = `${ms.toString()} ms`;
      existing.log = event.log;
      logs.value = [...logs.value];
      return;
    }
    const newVal: ICorrelatedProviderAuditLog = {
      id: event.log.id,
      log: event.log,
    };

    logs.value = [newVal, ...logs.value];
  });
}

function stopListening() {
  userHubEventInstance.clearCallbacks();
}

onMounted(() => {
  startListening();
});

// show details

const showingDetailsForAuditLog = ref<IProviderAuditLog | null>(null);
function showDetails(log: IProviderAuditLog) {
  showingDetailsForAuditLog.value = null;
  nextTick(() => {
    showingDetailsForAuditLog.value = log;
  });
}

// audit logging enabled
const integrationAuditLoggingEnabled = computed(() => {
  return !!preferencesStore.appPreferences?.enableProviderAuditLogging;
});

onUnmounted(() => {
  stopListening();
});
</script>

<style scoped></style>
