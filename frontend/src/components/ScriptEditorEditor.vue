<template>
  <div v-if="state" ref="root" class="script-editor-editor d-flex flex-column">
    <ScriptEditorOpenTabActionsToolbar />
    <ScriptEditorDebugToolbar
      v-if="isDebugAvailable"
      :is-debugging="isDebugging"
      :is-running="debugState.isRunning"
      :is-stopped="debugState.isStopped"
      :can-start-debug="canStartDebug"
      @start-debug="startDebugging"
      @stop-debug="stopDebugging"
      @continue="continueExecution"
      @step-over="stepOver"
      @step-into="stepInto"
      @step-out="stepOut"
    />
    <div ref="monacoContainer" class="position-relative monaco-container h-100 overflow-hidden">
      <Monaco
        ref="monacoEditor"
        class="monaco-editor"
        language="powershell"
        theme="vs-dark"
        :model-value="state.script.action"
        :options="monacoOptions"
        :identifier="editorIdentifier"
        @change="onChange"
        @editorDidMount="onEditorMounted"
      />
    </div>
    <ScriptEditorDebugPanel
      v-if="isDebugging"
      :variables="debugState.variables"
      :scopes="debugState.scopes"
      :call-stack="debugState.callStack"
      :breakpoints="debugState.breakpoints"
      :current-frame="currentFrameId"
      @frame-selected="onFrameSelected"
      @remove-breakpoint="removeBreakpoint"
    />
  </div>
</template>

<script setup lang="ts">
import { debounce, throttle } from "lodash";

import * as monaco from "monaco-editor";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { DatabaseType, ScriptLanguage } from "@/api/backend/generated/contracts";
import Monaco from "@/components/MonacoVue.vue";
import ScriptEditorDebugToolbar from "@/components/ScriptEditorDebugToolbar.vue";
import ScriptEditorDebugPanel from "@/components/ScriptEditorDebugPanel.vue";
import { useSyntaxCheck } from "@/composables/ScriptEditor/syntax-check";
import { useDebugClient } from "@/composables/useDebugClient";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useGlobalScriptEditor } from "@/store/pinia/global-script-editor";

const globalScriptEditorStore = useGlobalScriptEditor();
const authStore = useAuthStore();
const { updateStateScript, updateState } = globalScriptEditorStore;
const { activeState: state, selectedPanel } = storeToRefs(globalScriptEditorStore);
const { checkScriptSyntax } = useSyntaxCheck();

// Debug client setup
const {
  debugState,
  isDebugging,
  connect,
  disconnect,
  startDebugging: startDebugSession,
  toggleBreakpoint,
  continueExecution,
  stepOver,
  stepInto,
  stepOut,
  getVariables
} = useDebugClient();

const monacoEditor = ref<InstanceType<typeof Monaco> | null>(null);
const monacoContainer = ref<HTMLDivElement | null>(null);
const resizeInterval = ref<number | null>(null);
const resizeAttempts = ref(0);

// Debug-related refs
const currentFrameId = ref<number>(0);
const breakpointDecorations = ref<string[]>([]);
const monacoOptions = computed((): monaco.editor.IStandaloneEditorConstructionOptions => {
  return {
    "automaticLayout": true,
    "readOnly":
      state.value?.props.readonly
      // mark readonly when the script is global and the user is not an immense user
      || (!authStore.isImmense && state.value?.script.scriptType === DatabaseType.Global),
    "fontFamily": "immy-mono",
    "semanticHighlighting.enabled": true,
    "glyphMargin": true,
    "lineNumbers": "on",
    "glyphMarginWidth": 20, // Make glyph margin wider for breakpoints
    "lineNumbersMinChars": 3,
  };
});

const editorIdentifier = computed(() => {
  if (!!state.value?.script.id && state.value?.script.scriptType != null) {
    const databaseTypeText = EnumTextHelpers.DatabaseType.GetTextByValue(
      state.value?.script.scriptType,
    );
    return `${databaseTypeText}/${state.value?.script.id}`;
  }
  return state.value?.id;
});

const canRunSyntaxCheck = computed(
  () => state.value?.script.scriptLanguage === ScriptLanguage.PowerShell,
);

// Debug-related computed properties
const isDebugAvailable = computed(() => {
  return state.value?.script.scriptLanguage === ScriptLanguage.PowerShell;
});

const canStartDebug = computed(() => {
  return !!(isDebugAvailable.value && state.value?.script.action && state.value.script.action.trim().length > 0);
});

function onChange(data: string) {
  if (!state.value)
    return;
  updateStateScript(state.value.id, { action: data });
  if (state.value.ephemeral)
    updateState(state.value.id, { ephemeral: false });

  if (canRunSyntaxCheck.value)
    autoSyntaxCheck();
}

const autoSyntaxCheck = debounce(async () => {
  await checkScriptSyntax();
}, 400);

// Debug functions
const onEditorMounted = (editor: monaco.editor.IEditor) => {
  if ('onMouseDown' in editor) {
    setupBreakpointHandling(editor as monaco.editor.ICodeEditor);
    console.log('Debug breakpoint handling setup complete');
  }
};

const setupBreakpointHandling = (editor: monaco.editor.ICodeEditor) => {
  // Handle gutter clicks for breakpoint toggling
  editor.onMouseDown((e) => {
    // Check for clicks in both glyph margin and line numbers area
    if (e.target.type === monaco.editor.MouseTargetType.GUTTER_GLYPH_MARGIN ||
        e.target.type === monaco.editor.MouseTargetType.GUTTER_LINE_NUMBERS) {
      const lineNumber = e.target.position?.lineNumber;
      if (lineNumber && state.value?.script.action) {
        console.log('Toggling breakpoint at line:', lineNumber);
        toggleBreakpointAtLine(lineNumber);
      }
    }
  });
};

const toggleBreakpointAtLine = async (lineNumber: number) => {
  if (!state.value?.script.action) return;

  console.log('Toggling breakpoint at line:', lineNumber);
  const scriptPath = getScriptPath();
  try {
    const breakpoints = await toggleBreakpoint(scriptPath, lineNumber);
    console.log('Breakpoints after toggle:', breakpoints);
    updateBreakpointDecorations(breakpoints);
  } catch (error) {
    console.error('Error toggling breakpoint:', error);
  }
};

const updateBreakpointDecorations = (breakpoints: any[]) => {
  const editor = getMonacoEditor();
  if (!editor) return;

  const decorations = breakpoints.map(bp => ({
    range: new monaco.Range(bp.line, 1, bp.line, 1),
    options: {
      isWholeLine: false,
      glyphMarginClassName: bp.verified ? 'debug-breakpoint-verified' : 'debug-breakpoint-unverified'
    }
  }));

  breakpointDecorations.value = editor.deltaDecorations(breakpointDecorations.value, decorations);
};

const getScriptPath = () => {
  if (state.value?.script.id) {
    return `script-${state.value.script.id}`;
  }
  return 'untitled-script';
};

const startDebugging = async () => {
  console.log('Start debugging clicked');
  if (!state.value?.script.action) {
    console.log('No script action available');
    return;
  }

  try {
    const scriptPath = getScriptPath();
    console.log('Starting debug session for script:', scriptPath);
    await connect(scriptPath);
    console.log('Connected to debug adapter');
    await startDebugSession(scriptPath, state.value.script.action);
    console.log('Debug session started');
  } catch (error) {
    console.error('Failed to start debugging:', error);
    // Show user-friendly error
    alert(`Failed to start debugging: ${error.message}`);
  }
};

const stopDebugging = async () => {
  await disconnect();
  // Clear breakpoint decorations
  const editor = getMonacoEditor();
  if (editor) {
    breakpointDecorations.value = editor.deltaDecorations(breakpointDecorations.value, []);
  }
};

const onFrameSelected = (frame: any) => {
  currentFrameId.value = frame.id;
  getVariables(frame.id);
};

const removeBreakpoint = async (file: string, line: number) => {
  const breakpoints = await toggleBreakpoint(file, line);
  updateBreakpointDecorations(breakpoints);
};

function getMonacoEditor() {
  return monacoEditor.value?.getEditor();
}

function handleMountedSizing() {
  resizeAttempts.value = 0;
  resizeInterval.value = window.setInterval(() => {
    resizeAttempts.value++;
    if (resizeAttempts.value > 10) {
      if (resizeInterval.value)
        window.clearInterval(resizeInterval.value);
    }

    resizeMonacoThrottled();
  }, 100);
}

onMounted(async () => {
  // initialize the sizes
  handleMountedSizing();
});

/** Resize this components height whenever the panel is changed */

const root = ref<HTMLElement>();
watch(selectedPanel, (val) => {
  if (!root.value)
    return;
  if (val != null)
    root.value.style.height = "75%";
  else
    root.value.style.height = "100%";
});

/**
 * Resize the monaco editor whenever its container has resized.
 *  We can leverage the Resize Observer to accomplish this easily
 */

let monacoContainerResizeObserver: ResizeObserver | null = null;

function resizeEditor() {
  const editor = getMonacoEditor();
  if (editor != null) {
    const el = monacoContainer.value;
    const bounds = el?.getBoundingClientRect();
    editor.layout({
      width: bounds?.width,
      height: bounds?.height,
    });
  }
}

const resizeMonacoThrottled = throttle(() => {
  resizeEditor();
}, 350);

function onMonacoContainerResized() {
  resizeMonacoThrottled();
}

onMounted(() => {
  if (monacoContainer.value) {
    monacoContainerResizeObserver = new ResizeObserver(onMonacoContainerResized);
    monacoContainerResizeObserver.observe(monacoContainer.value);
  }
});

onUnmounted(() => {
  if (monacoContainer.value && monacoContainerResizeObserver)
    monacoContainerResizeObserver.unobserve(monacoContainer.value);
});
</script>

<style lang="scss" scoped>
.script-editor-editor {
  flex: 0 0 auto;
  background-color: #1e1e1e;
  min-height: 64px;
  height: 64vh;
  overflow: hidden;
}
</style>

<style>
/* Global styles for Monaco Editor breakpoints */
.debug-breakpoint-verified {
  background: #e51400 !important;
  border-radius: 50% !important;
  width: 12px !important;
  height: 12px !important;
  margin-left: 4px !important;
  margin-top: 2px !important;
  border: 1px solid #fff !important;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5) !important;
}

.debug-breakpoint-unverified {
  background: #848484 !important;
  border-radius: 50% !important;
  width: 12px !important;
  height: 12px !important;
  margin-left: 4px !important;
  margin-top: 2px !important;
  border: 1px solid #fff !important;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5) !important;
}

.debug-current-line {
  background-color: rgba(255, 255, 0, 0.2) !important;
}

/* Make glyph margin more visible for debugging */
.monaco-editor .margin-view-overlays .glyph-margin {
  background-color: rgba(255, 255, 255, 0.05) !important;
}
</style>
