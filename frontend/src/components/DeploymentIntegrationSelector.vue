<template>
  <FormGroupWithHelp
    v-if="linkedProviderType"
    class="mt-3 mb-0 justify-content-between flex-wrap align-items-center"
    label="Integration to use"
    alert-button-position="before-label"
  >
    <template #help-alert-content>
      The integration for which this deployment should be connected to
    </template>
    <em v-if="providerLinksForLinkedProviderType.length === 0">
      There are no integrations for the selected software. Please add an integration for this
      software in order to use it in a deployment.
    </em>
    <span v-else-if="providerLinksForLinkedProviderType.length === 1">{{
      providerLinksForLinkedProviderType[0].name
    }}</span>
    <ProviderLinkDropdown
      v-else
      v-model="selectedProviderLinkId"
      :provider-type-id="linkedProviderType.providerTypeId"
    />
  </FormGroupWithHelp>
  <ImmyFormInvalidFeedBack v-if="selectedProviderLinkId == null" class="invalid-feedback d-block">
    An integration is required to create a deployment for this software.
  </ImmyFormInvalidFeedBack>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";
import { useProviderTypesStore } from "@/store/pinia/provider-types-store";

const props = defineProps<{
  integrationTypeId: string;
  value?: number | null;
}>();

const emit = defineEmits<{
  (event: "update:modelValue", value: number | null | undefined): void;
}>();

const providerTypesStore = useProviderTypesStore();
const providerLinksStore = useProviderLinksStore();

const selectedProviderLinkId = ref(props.value);

const linkedProviderType = computed(() => {
  return providerTypesStore.providerTypes.find(x => x.providerTypeId === props.integrationTypeId);
});

const providerLinksForLinkedProviderType = computed(() => {
  return providerLinksStore.allProviderLinks.filter(
    a => a.providerTypeId === props.integrationTypeId,
  );
});

watch(
  selectedProviderLinkId,
  (val, oldVal) => {
    if (val === oldVal)
      return;
    emit("update:modelValue", selectedProviderLinkId.value);
  },
  {
    immediate: true,
  },
);

watch(
  () => props.value,
  (val) => {
    if (val === selectedProviderLinkId.value)
      return;
    selectedProviderLinkId.value = val;
  },
);

onMounted(() => {
  if (providerLinksForLinkedProviderType.value.length === 1)
    selectedProviderLinkId.value = providerLinksForLinkedProviderType.value[0].id;
});
</script>

<style scoped></style>
