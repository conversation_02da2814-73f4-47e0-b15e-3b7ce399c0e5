<template>
  <pre>{{ details }}</pre>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { computersApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

const props = defineProps<{
  computerId: number;
  inventoryKey: string;
  errored: boolean;
}>();

const appAlertsStore = useAppAlertsStore();

const loading = ref(false);

const details = ref<string>();
onMounted(async () => {
  if (loading.value)
    return;
  try {
    loading.value = true;
    const inventoryData = await computersApi.getInventoryScriptResult(
      props.computerId,
      props.inventoryKey,
    );
    if (props.errored) {
      const errKey = `${props.inventoryKey}_ErrorResult`;
      details.value = inventoryData[errKey];
    }
    else {
      details.value = inventoryData[props.inventoryKey]?.Output;
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to load inventory details",
      details: err,
    });
  }
  finally {
    loading.value = false;
  }
});
</script>

<style scoped></style>
