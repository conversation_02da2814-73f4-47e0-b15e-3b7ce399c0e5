<template>
  <immy-dx-data-grid
    :id="tableId"
    show-refresh
    class="agent-table"
    :data-source="dataSource"
    repaint-changes-only
    remote-operations
    :columns="columns"
  >
    <template #computer="{ data }">
      <immy-link
        :to="{
          name: 'Computer Details',
          params: { computerId: data.id.toString() },
        }"
      >
        {{ data.computerName }}
      </immy-link>
    </template>
    <template #tenant="{ data }">
      <immy-link
        :to="{
          name: 'Tenant Details',
          params: { tenantId: data.tenantId.toString() },
        }"
      >
        {{ data.tenantName }}
      </immy-link>
    </template>
    <template #allAgentsConnected="{ data }">
      <i v-if="data.allAgentsConnected" class="fa fa-check-circle text-success" />
      <i v-else class="fa fa-question-circle text-warning" />
    </template>
    <template #someConnected="{ data }">
      <i v-if="data.someConnected" class="fa fa-question-circle text-warning" />
    </template>
    <template v-for="link in providerLinksStore.allProviderLinks" :key="link.id" #[`${link.id}-status`]="{ data }">
      <DxAgentTableIntegrationCell :agent="getAgent(data, link.id)" />
    </template>
  </immy-dx-data-grid>
</template>

<script lang="ts" setup>
import { DxDataGridTypes } from "devextreme-vue/cjs/data-grid";
import { Options } from "devextreme/data/data_source";
import { computed, onMounted } from "vue";
import { HealthStatus } from "@/api/backend/generated/enums";
import { IComputerAgentStatusDto } from "@/api/backend/generated/interfaces";
import { ComputerApiRoutes } from "@/api/backend/generated/routes";
import DxAgentTableIntegrationCell from "@/components/DxAgentTableIntegrationCell.vue";
import { createStore } from "@/composables/DxServerDataSource";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";

const props = defineProps<{
  tenantId?: number;
}>();

const dataSource: Options = {
  store: createStore(`/${ComputerApiRoutes.ComputerAgentStatusReport}${props.tenantId ? `?tenantId=${props.tenantId}` : ""}`),
};

const providerLinksStore = useProviderLinksStore();

const columns = computed<DxDataGridTypes.Column<IComputerAgentStatusDto, number>[]>(() => {
  const linkColumns = providerLinksStore.allProviderLinks
    .filter(a => a.healthStatus == HealthStatus.Healthy && a.providerCapabilities.some(b => b == "ISupportsListingAgents" || b == "IRunScriptProvider"))
    .map((link) => {
      const col: DxDataGridTypes.Column<IComputerAgentStatusDto, number> = {
        caption: link.name,
        alignment: "center",
        cellTemplate: `${link.id}-status`,
      };
      return col;
    });

  return [
    {
      dataType: "string",
      dataField: "tenantName",
      cellTemplate: "tenant",
      caption: "Tenant",
      alignment: "center",
      visible: props.tenantId == null,
    },
    {
      dataType: "string",
      dataField: "computerName",
      cellTemplate: "computer",
      caption: "Name",
      alignment: "center",
    },
    {
      dataType: "boolean",
      dataField: "someConnected",
      cellTemplate: "someConnected",
      caption: "Some Connected",
      alignment: "center",
    },
    {
      dataType: "boolean",
      dataField: "allAgentsConnected",
      cellTemplate: "allAgentsConnected",
      caption: "All Connected",
      alignment: "center",
    },
    ...linkColumns,
  ];
});

const tableId = computed(() => {
  if (props.tenantId == null)
    return "dx-agent-table";
  return "dx-tenant-agent-table";
});

onMounted(async () => {
  await providerLinksStore.getAllProviderLinks();
});

function getAgent(computer: IComputerAgentStatusDto, providerLinkId: number) {
  return computer.agents.find(a => a.providerLinkId === providerLinkId);
}
</script>
