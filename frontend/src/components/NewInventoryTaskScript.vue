<template>
  <ImmyButton v-if="!addingScript" variant="primary" @click="addingScript = true">
    Add Script
  </ImmyButton>
  <form v-if="addingScript" @submit.prevent.stop>
    <ImmyFormGroup label="Inventory Key">
      <ImmyInput v-model="inventoryKey" placeholder="Enter Inventory Key" />
    </ImmyFormGroup>
    <ImmyFormGroup label="Script">
      <EmbeddedScriptSelector
        v-model:script-id="scriptId"
        :local-only="inventoryTaskType === DatabaseType.Local"
        :global-only="inventoryTaskType === DatabaseType.Global"
        :script-category="ScriptCategory.DeviceInventory"
      />
    </ImmyFormGroup>
    <div class="d-flex align-items-center gap-2">
      <LoadButton type="submit" variant="primary" :handler="addScript">
        Submit
      </LoadButton>
      <ImmyButton variant="warning" @click="addingScript = false">
        Cancel
      </ImmyButton>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { DatabaseType, ScriptCategory } from "@/api/backend/generated/enums";
import { IInventoryTaskScriptResource } from "@/api/backend/generated/responses";
import { inventoryTasksApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { toast } from "@/utils/toast";

const props = defineProps<{
  inventoryTaskId: number;
  inventoryTaskType: DatabaseType;
}>();

const emit = defineEmits<{
  (e: "add", val: IInventoryTaskScriptResource): void;
}>();

const appAlertsStore = useAppAlertsStore();

const addingScript = ref(false);
const inventoryKey = ref<string>();
const scriptId = ref<number>();

function isValid() {
  return !!inventoryKey.value && !!scriptId.value;
}

async function addScript() {
  if (!isValid()) {
    toast.showError("Please fill out all fields");
    return;
  }

  try {
    const res = await inventoryTasksApi.addScriptToLocalInventoryTask(props.inventoryTaskId, {
      inventoryKey: inventoryKey.value!,
      inventoryTaskId: props.inventoryTaskId,
      scriptId: scriptId.value!,
    });
    toast.success("Success");
    emit("add", res);
    addingScript.value = false;
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while adding the script",
      details: err,
    });
  }
}
</script>

<style scoped></style>
