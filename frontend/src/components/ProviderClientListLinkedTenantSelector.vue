<template>
  <TenantSelectBox
    v-model="modelValue"
    include-new-option
    :full-width="tenantSelectorFullWidth"
    :selectable-tenants="selectableTenants"
    :placeholder="props.tenantSelectorPlaceholder"
  >
    <template #option="{ option }">
      <template v-if="option.id === NEW_TENANT_ID">
        <span class="text-primary">
          <i class="fa fa-plus" />
          Create a new tenant
        </span>
      </template>
      <template v-else>
        <div class="d-flex justify-content-between">
          <div class="w-100">
            {{ option.name }}
            <span v-if="option.isMsp">
              <i class="fa fa-universal-access" aria-hidden="true" /> (MSP)
            </span>
            <span
              v-if="option.azureTenantLink?.azureTenant?.azureTenantType === AzTenantType.Partner"
            >&nbsp;(Azure Partner)</span>
          </div>
          <div v-if="!hideLinkedTenants">
            {{
              clientLinks[option.id] === 0
                ? "unlinked"
                : `linked to ${clientLinks[option.id] ?? 0} other client${
                  clientLinks[option.id] !== 1 ? "s" : ""
                }`
            }}
          </div>
        </div>
      </template>
    </template>
  </TenantSelectBox>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { AzTenantType } from "@/api/backend/generated/enums";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { NEW_TENANT_ID } from "@/utils/constants";
import TenantSelectBox from "./TenantSelectBox.vue";

export interface IClientLink {
  linkedToTenantId?: number | null;
}

const props = withDefaults(
  defineProps<{
    existingLinks: IClientLink[];
    hidePartnerTenants?: boolean;
    tenantSelectorPlaceholder?: string;
    hideLinkedTenants?: boolean;
    tenantSelectorFullWidth?: boolean;
  }>(),
  {
    hidePartnerTenants: false,
    hideLinkedTenants: false,
    tenantSelectorFullWidth: true,
    tenantSelectorPlaceholder: "Select a tenant",
  },
);

const tenantsStore = useTenantsStore();

const modelValue = defineModel<number | null>("modelValue", { required: true });

const allTenants = computed(() => {
  const tenants = tenantsStore.allTenants;

  return tenants;
});

const clientLinks = computed(() => {
  return props.existingLinks.reduce((agg, client) => {
    if (client.linkedToTenantId == null)
      return agg;
    const c = agg[client.linkedToTenantId];
    if (c == null)
      agg[client.linkedToTenantId] = 1;
    else
      agg[client.linkedToTenantId] = c + 1;

    return agg;
  }, {} as { [x: number]: number | undefined });
});

const selectableTenants = computed(() => {
  let tenants = allTenants.value;
  if (props.hidePartnerTenants) {
    tenants = allTenants.value.filter(
      a => a.azureTenantLink?.azureTenant.azureTenantType !== AzTenantType.Partner,
    );
  }
  if (props.hideLinkedTenants)
    tenants = tenants.filter(a => !clientLinks.value[a.id]);

  return tenants;
});
</script>
