/**
 * PII MAsker - Finds elements with specific class names and prefixes their text with a marker
 */
export class PIIMasker {
  private readonly targetClasses: string[];
  private readonly piiPrefix: string;
  private readonly numCharsToKeepInFirstWord: number;
  private readonly numCharsToKeepOtherWords: number;
  private observer: MutationObserver | null;
  private isRunning: boolean;

  /**
   * Creates a new PII MAsker instance
   *
   * @param {string[]} targetClasses - Class names to scan for
   * @param {string} piiPrefix - Prefix to add to marked elements
   * @param {number} numCharsToKeepInFirstWord - Number of characters to keep un-obscured in the first word
   * @param {number} numCharsToKeepOtherWords - Number of characters to keep un-obscured in other words
   */
  constructor(
    targetClasses: string[] = ["pii"],
    piiPrefix: string = "(PII) ",
    numCharsToKeepInFirstWord: number = 2,
    numCharsToKeepOtherWords: number = 1,
  ) {
    this.targetClasses = targetClasses;
    this.piiPrefix = piiPrefix;
    this.numCharsToKeepInFirstWord = numCharsToKeepInFirstWord;
    this.numCharsToKeepOtherWords = numCharsToKeepOtherWords;
    this.observer = null;
    this.isRunning = false;
  }

  /**
   * Obscures text by showing only the first few characters of each word and replacing the rest with asterisks.
   * Words with numCharsToKeep or fewer characters remain unchanged.
   *
   * @param {string} text - The original text to obscure
   * @returns {string} The obscured text with words joined by spaces
   *
   * @example
   * // Returns "He**** W****"
   * generateMaskedText("Hello World");
   *
   * @example
   * // Returns "The c** i* v******"
   * generateMaskedText("The cat is verbose");
   */
  private generateMaskedText(text: string): string {
    // break the original text into an array of words
    const words = text.split(" ");
    const obscuredWords: string[] = [];

    // for every word, take the first few characters and obscure the rest with asterisks
    for (const [index, word] of words.entries()) {
      const numCharsToKeep = index === 0 ? this.numCharsToKeepInFirstWord : this.numCharsToKeepOtherWords;

      if (word.length <= numCharsToKeep) {
        obscuredWords.push(word);
        continue;
      }

      // obscure the word
      const firstChars = word.slice(0, numCharsToKeep);
      const numberOfAsterisks = word.length - numCharsToKeep; // Fixed: using word.length instead of text.length
      let asterisks = "";
      for (let i = 0; i < numberOfAsterisks; i++) {
        asterisks += "*";
      }
      obscuredWords.push(firstChars + asterisks);
    }

    return obscuredWords.join(" ");
  }

  /**
   * Scans the DOM for elements with specific class names and prefixes their text content
   */
  private scanAndPrefixText(): void {
    // Create a selector to find elements with any of the target classes
    const selector = this.targetClasses.map(className => `.${className}`).join(", ");

    // Find all elements matching our selector
    const elements = document.querySelectorAll(selector);

    // eslint-disable-next-line no-console
    console.debug(`Found ${elements.length} elements with target classes`);

    // Iterate through each element and update its text content
    elements.forEach((element) => {
      const originalText = element.textContent?.trim() || "";

      // Only add prefix if there's text and it doesn't already have the prefix
      if (originalText && !originalText.startsWith(this.piiPrefix)) {
        // Store the class name for logging
        const className = Array.from(element.classList)
          .find(cls => this.targetClasses.includes(cls));

        const obscuredText = this.generateMaskedText(originalText);
        // Update the element's text content with the prefix and obscured text
        element.textContent = `${this.piiPrefix}${obscuredText}`;

        // eslint-disable-next-line no-console
        console.debug(`Updated element with class "${className}": "${originalText}" -> "${this.piiPrefix}${obscuredText}"`);
      }
    });
  }

  /**
   * Sets up a MutationObserver to detect and handle dynamically added elements
   */
  private setupMutationObserver(): void {
    // Create a new observer
    this.observer = new MutationObserver((mutations) => {
      let shouldScan = false;

      // Check if any of the mutations might have added elements with our target classes
      for (const mutation of mutations) {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          shouldScan = true;
          break;
        }
      }

      // If relevant mutations were detected, scan the DOM again
      if (shouldScan) {
        this.scanAndPrefixText();
      }
    });

    // Start observing the document with the configured parameters
    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // eslint-disable-next-line no-console
    console.debug("Mutation observer started - will detect dynamically added elements");
  }

  /**
   * Starts the PII masker
   */
  public start(): void {
    if (this.isRunning) {
      // eslint-disable-next-line no-console
      console.debug("PII Masker is already running");
      return;
    }

    // eslint-disable-next-line no-console
    console.debug("PII Masker started");
    this.scanAndPrefixText();
    this.setupMutationObserver();
    this.isRunning = true;
  }

  /**
   * Stops the PII masker by disconnecting the mutation observer
   */
  public stop(): void {
    if (!this.isRunning) {
      // eslint-disable-next-line no-console
      console.debug("PII Masker is not running");
      return;
    }

    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
      this.isRunning = false;
      // eslint-disable-next-line no-console
      console.debug("PII Masker stopped");
    }
  }

  /**
   * Returns whether the masker is currently running
   *
   * @returns {boolean} True if the masker is running, false otherwise
   */
  public isActive(): boolean {
    return this.isRunning;
  }
}

// Example usage:
// const masker = new PIIMasker();
// masker.start();  // Start masking
// masker.stop();   // Stop masking
