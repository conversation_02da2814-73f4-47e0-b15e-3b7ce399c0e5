import { ref, computed, reactive } from 'vue'
import { toSocket, WebSocketMessageReader, WebSocketMessageWriter } from 'vscode-ws-jsonrpc'
import { DebugProtocol } from 'vscode-debugprotocol'
import { GetConstants } from '@/utils/constants'

export interface DebugState {
  isConnected: boolean
  isRunning: boolean
  isStopped: boolean
  breakpoints: Map<string, DebugProtocol.Breakpoint[]>
  variables: DebugProtocol.Variable[]
  scopes: DebugProtocol.Scope[]
  callStack: DebugProtocol.StackFrame[]
  currentLine: number
  currentFile: string
  requestId: number
}

export function useDebugClient() {
  const debugState = reactive<DebugState>({
    isConnected: false,
    isRunning: false,
    isStopped: false,
    breakpoints: new Map<string, DebugProtocol.Breakpoint[]>(),
    variables: [] as DebugProtocol.Variable[],
    scopes: [] as DebugProtocol.Scope[],
    callStack: [] as DebugProtocol.StackFrame[],
    currentLine: -1,
    currentFile: '',
    requestId: 0
  })

  const isDebugging = computed(() => debugState.isConnected)
  
  let socket: WebSocket | null = null
  let reader: WebSocketMessageReader | null = null
  let writer: WebSocketMessageWriter | null = null
  
  // Generate unique request IDs
  const nextRequestId = () => {
    return ++debugState.requestId
  }
  
  // Send a request to the debug adapter
  const sendRequest = (
    command: string,
    args: any
  ): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!writer) {
        reject(new Error('Debug adapter not connected'))
        return
      }

      const request = {
        seq: nextRequestId(),
        type: 'request',
        command: command,
        arguments: args
      }

      const responseHandler = (message: any) => {
        if (message.type === 'response' && message.request_seq === request.seq) {
          // Remove listener (simplified approach)
          if (message.success) {
            resolve(message)
          } else {
            reject(new Error(message.message))
          }
        }
      }

      // Store response handler for cleanup
      // Send as JSON-RPC message
      const jsonRpcRequest = {
        jsonrpc: '2.0',
        id: request.seq,
        method: request.command,
        params: request.arguments
      }
      writer.write(jsonRpcRequest)
    })
  }
  
  // Connect to the debug adapter (simplified version for now)
  const connect = async (terminalId: string): Promise<void> => {
    console.log('Setting up local debug session for terminal:', terminalId)

    // For now, we'll simulate a connection since the backend doesn't have a separate debug endpoint
    // This allows breakpoints to work visually while we develop the full DAP integration
    debugState.isConnected = true

    return Promise.resolve()
      
        socket.onopen = () => {
          console.log('WebSocket connection opened')
          clearTimeout(connectionTimeout)

          const socketWrapper = toSocket(socket as WebSocket)
          reader = new WebSocketMessageReader(socketWrapper)
          writer = new WebSocketMessageWriter(socketWrapper)

          // Set up event handlers
          reader.listen((message: any) => {
            console.log('Received message:', message)
            if (message.type === 'event') {
              handleDebugEvent(message)
            }
          })

          // Initialize debug session and resolve promise
          initializeDebugSession().then(() => {
            resolve()
          }).catch((error) => {
            reject(error)
          })
        }

        socket.onerror = (error) => {
          console.error('Debug WebSocket error:', error)
          clearTimeout(connectionTimeout)
          reject(new Error('WebSocket connection failed'))
        }

        socket.onclose = (event) => {
          console.log('WebSocket connection closed:', event.code, event.reason)
          clearTimeout(connectionTimeout)
          debugState.isConnected = false
          debugState.isRunning = false
          debugState.isStopped = false

          if (event.code !== 1000) { // Not a normal closure
            reject(new Error(`WebSocket closed with code ${event.code}: ${event.reason}`))
          }
        }
      } catch (error) {
        console.error('Failed to create WebSocket:', error)
        reject(error)
      }
    })
  }
  
  // Initialize the debug session
  const initializeDebugSession = async () => {
    try {
      // Send initialize request
      await sendRequest('initialize', {
        clientID: 'monaco-powershell-editor',
        adapterID: 'powershell',
        pathFormat: 'path',
        linesStartAt1: true,
        columnsStartAt1: true,
        supportsVariableType: true,
        supportsVariablePaging: true,
        supportsRunInTerminalRequest: false
      })
      
      debugState.isConnected = true
    } catch (error) {
      console.error('Failed to initialize debug session:', error)
    }
  }
  
  // Handle debug events from the adapter
  const handleDebugEvent = (event: any) => {
    switch (event.event) {
      case 'stopped':
        debugState.isStopped = true
        debugState.isRunning = false

        // Get stack trace when stopped
        getStackTrace().then(() => {
          if (debugState.callStack.length > 0) {
            const topFrame = debugState.callStack[0]
            debugState.currentLine = topFrame.line || 0
            debugState.currentFile = topFrame.source?.path || ''

            // Get variables for top stack frame
            getVariables(topFrame.id)
          }
        })
        break

      case 'continued':
        debugState.isStopped = false
        debugState.isRunning = true
        break

      case 'terminated':
        disconnect()
        break
    }
  }
  
  // Start debugging a script
  const startDebugging = async (scriptPath: string, scriptContent: string) => {
    if (!debugState.isConnected) {
      throw new Error('Debug adapter not connected')
    }
    
    try {
      // Launch the script
      await sendRequest('launch', {
        name: 'PowerShell Script',
        type: 'PowerShell',
        request: 'launch',
        script: scriptPath,
        cwd: '${workspaceFolder}',
        createTemporaryIntegratedConsole: true,
        internalConsoleOptions: 'neverOpen',
        __sessionId: scriptPath
      })
      
      // Set initial breakpoints
      await setBreakpoints(scriptPath)
      
      // Start running
      debugState.isRunning = true
      
      // Configure done
      await sendRequest('configurationDone', {})
    } catch (error) {
      console.error('Failed to start debugging:', error)
      throw error
    }
  }
  
  // Set breakpoints in a file
  const setBreakpoints = async (source: string, breakpoints?: { line: number }[]) => {
    if (!debugState.isConnected) {
      return
    }
    
    try {
      const bps = breakpoints || Array.from(debugState.breakpoints.get(source) || [])
        .map(bp => ({ line: bp.line || 0 }))
      
      const response = await sendRequest('setBreakpoints', {
        source: { path: source },
        breakpoints: bps,
        sourceModified: false
      })
      
      // Store verified breakpoints
      if (response.body?.breakpoints) {
        debugState.breakpoints.set(source, response.body.breakpoints)
      }
    } catch (error) {
      console.error('Failed to set breakpoints:', error)
    }
  }
  
  // Get the current call stack
  const getStackTrace = async () => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      const response = await sendRequest('stackTrace', {
        threadId: 1
      })
      
      if (response.body?.stackFrames) {
        debugState.callStack = response.body.stackFrames
      }
    } catch (error) {
      console.error('Failed to get stack trace:', error)
    }
  }
  
  // Get variables for a stack frame
  const getVariables = async (frameId: number) => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      // Clear previous variables
      debugState.variables = []
      
      // Get scopes for the frame
      const scopesResponse = await sendRequest('scopes', {
        frameId
      })
      
      if (scopesResponse.body?.scopes) {
        debugState.scopes = scopesResponse.body.scopes
        
        // Get variables for each scope
        for (const scope of debugState.scopes) {
          const variablesResponse = await sendRequest('variables', {
            variablesReference: scope.variablesReference
          })
          
          if (variablesResponse.body?.variables) {
            // Add scope info to each variable
            const scopedVariables = variablesResponse.body.variables.map((v: any) => ({
              ...v,
              scope: scope.name
            }))

            // Add to variables array
            debugState.variables.push(...scopedVariables)
          }
        }
      }
    } catch (error) {
      console.error('Failed to get variables:', error)
    }
  }
  
  // Continue execution
  const continueExecution = async () => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      await sendRequest('continue', {
        threadId: 1
      })
      
      debugState.isStopped = false
      debugState.isRunning = true
    } catch (error) {
      console.error('Failed to continue execution:', error)
    }
  }
  
  // Step over
  const stepOver = async () => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      await sendRequest('next', {
        threadId: 1
      })
    } catch (error) {
      console.error('Failed to step over:', error)
    }
  }
  
  // Step into
  const stepInto = async () => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      await sendRequest('stepIn', {
        threadId: 1
      })
    } catch (error) {
      console.error('Failed to step into:', error)
    }
  }
  
  // Step out
  const stepOut = async () => {
    if (!debugState.isConnected || !debugState.isStopped) {
      return
    }
    
    try {
      await sendRequest('stepOut', {
        threadId: 1
      })
    } catch (error) {
      console.error('Failed to step out:', error)
    }
  }
  
  // Disconnect from the debug adapter
  const disconnect = async () => {
    if (debugState.isConnected) {
      try {
        await sendRequest('disconnect', {
          restart: false,
          terminateDebuggee: true
        })
      } catch (error) {
        console.error('Error disconnecting from debug adapter:', error)
      }
    }
    
    if (socket) {
      socket.close()
      socket = null
    }
    
    reader = null
    writer = null
    
    debugState.isConnected = false
    debugState.isRunning = false
    debugState.isStopped = false
    debugState.variables = []
    debugState.scopes = []
    debugState.callStack = []
    debugState.currentLine = -1
    debugState.currentFile = ''
  }
  
  // Toggle a breakpoint
  const toggleBreakpoint = async (source: string, line: number) => {
    const sourceBreakpoints = debugState.breakpoints.get(source) || []
    const existingBpIndex = sourceBreakpoints.findIndex(bp => bp.line === line)
    
    if (existingBpIndex >= 0) {
      // Remove breakpoint
      sourceBreakpoints.splice(existingBpIndex, 1)
    } else {
      // Add breakpoint
      sourceBreakpoints.push({ id: 0, verified: false, line, source: { path: source } })
    }
    
    debugState.breakpoints.set(source, sourceBreakpoints)
    
    // Update breakpoints in debug adapter if connected
    if (debugState.isConnected) {
      await setBreakpoints(source)
    }
    
    return Array.from(debugState.breakpoints.get(source) || [])
  }
  
  return {
    debugState,
    isDebugging,
    connect,
    disconnect,
    startDebugging,
    setBreakpoints,
    toggleBreakpoint,
    continueExecution,
    stepOver,
    stepInto,
    stepOut,
    getVariables
  }
}
