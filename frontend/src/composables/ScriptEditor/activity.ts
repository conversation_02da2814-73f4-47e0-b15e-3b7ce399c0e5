import { ref } from "vue";

export enum ScriptEditorActivityType {
  Target = 1,
  Explorer = 2,
  Search = 3,
  ScriptDetails = 4,
  Commands = 5,
}

const selectedActivity = ref<ScriptEditorActivityType | null>(ScriptEditorActivityType.Explorer);

export default function useActivity() {
  /**
   * The currently selected activity - e.g. Open Editors, Search, Target.
   * The activity is tightly coupled with what is shown inside of the primary sidebar
   */

  /** Method that can be used to toggle between activities */
  function toggleActivity(activityType: ScriptEditorActivityType) {
    if (selectedActivity.value === activityType)
      selectedActivity.value = null;
    else
      selectedActivity.value = activityType;
  }

  /** Method that can be used to focus a specific activity */
  function focusActivity(activityType: ScriptEditorActivityType) {
    selectedActivity.value = activityType;
  }

  function unsetActivity() {
    selectedActivity.value = null;
  }

  return {
    selectedActivity,
    toggleActivity,
    focusActivity,
    unsetActivity,
  };
}
