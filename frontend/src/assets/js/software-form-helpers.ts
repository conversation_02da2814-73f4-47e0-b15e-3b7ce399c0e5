import { DatabaseType, UpdateActionType } from "@/api/backend/generated/contracts";

export interface ISharedSoftwareProperties {
  installScriptId: number | null;
  installScriptType: DatabaseType | null;
  uninstallScriptId: number | null;
  uninstallScriptType: DatabaseType | null;
  upgradeScriptId: number | null;
  upgradeScriptType: DatabaseType | null;
  upgradeStrategy: UpdateActionType;
}

export interface ISharedSoftwareVersionProperties {
  installScriptId: number | null;
  installScriptType: DatabaseType | null;
  uninstallScriptId: number | null;
  uninstallScriptType: DatabaseType | null;
  upgradeScriptId: number | null;
  upgradeScriptType: DatabaseType | null;
  upgradeStrategy: UpdateActionType;
}

export function initSharedSoftwarePropertyModel(model?: ISharedSoftwareProperties | undefined): ISharedSoftwareProperties {
  return {
    installScriptId: model?.installScriptId ?? null,
    installScriptType: model?.installScriptType ?? null,
    uninstallScriptId: model?.uninstallScriptId ?? null,
    uninstallScriptType: model?.uninstallScriptType ?? null,
    upgradeScriptId: model?.upgradeScriptId ?? null,
    upgradeScriptType: model?.upgradeScriptType ?? null,
    upgradeStrategy: model?.upgradeStrategy ?? UpdateActionType.InstallOver,
  };
}

export function initSharedSoftwareVersionPropertyModel(model: ISharedSoftwareVersionProperties): ISharedSoftwareVersionProperties {
  return {
    installScriptId: model?.installScriptId ?? null,
    installScriptType: model?.installScriptType ?? null,
    uninstallScriptId: model?.uninstallScriptId ?? null,
    uninstallScriptType: model?.uninstallScriptType ?? null,
    upgradeScriptId: model?.upgradeScriptId ?? null,
    upgradeScriptType: model?.upgradeScriptType ?? null,
    upgradeStrategy: model?.upgradeStrategy ?? UpdateActionType.None,
  };
}

export const defaultDynamicVersionsScript = `# --- Dynamic Versions Script ---
# The script MUST return an array of applicable version objects.
# Each object is required to provide a "Version" and "Url" property.


# use the New-DynamicVersion function to build out valid versions and assign them to $Response.Versions

# you must explicitly return the data in the following structure
$Response = New-Object PSObject -Property @{
    Versions = @()
}

return $Response;`;
