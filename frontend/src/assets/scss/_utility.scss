.overflow-hidden {
  overflow: hidden;
}

.strike-through {
  text-decoration: line-through;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-prewrap {
  white-space: pre-wrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}
.text-smaller {
  font-size: smaller;
}

.badge-prereq {
  background-color: #ffbf005e;
  padding: 3px;
}

.font-weight-600,
.fw-600 {
  font-weight: 600;
}

.fw-lighter {
  font-weight: lighter;
}

.fs-75 {
  font-size: 0.75rem;
}

.fs-85 {
  font-size: 85%;
}

.fs-1 {
  font-size: 1rem;
}

.fs-2 {
  font-size: 2em;
}

.fs-inherit {
  font-size: inherit !important;
}

.cursor-help {
  cursor: help !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.text-underline {
  text-decoration: underline;
}

.file-icon {
  font-size: 30px;
}

.image-18 {
  max-width: 18px;
  max-height: 18px;
  &.img-svg {
    width: 18px;
  }
}

.image-128 {
  max-width: 128px;
  max-height: 128px;
  font-size: 50px;
  &.img-svg {
    width: 128px;
  }
}

.image-50 {
  max-width: 50px;
  max-height: 50px;
  font-size: 50px;

  &.img-svg {
    width: 50px;
  }
}

.image-40 {
  max-width: 40px;
  max-height: 40px;
  font-size: 40px;
  &.img-svg {
    width: 40px;
  }
}

.image-30 {
  max-width: 30px;
  max-height: 30px;
  font-size: 30px;
  &.img-svg {
    width: 30px;
  }
}

.image-20 {
  max-width: 20px;
  max-height: 20px;
  font-size: 20px;
  &.img-svg {
    width: 20px;
  }
}

.image-25 {
  max-width: 25px;
  max-height: 25px;
  font-size: 25px;

  &.img-svg {
    width: 25px;
  }
}

.row-gap-1 {
  row-gap: 0.25rem !important;
}

.row-gap-2 {
  row-gap: 0.5rem !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 2rem !important;
}

.info-label {
  color: grey;
  margin-bottom: 0;
}
.info-value {
  overflow-wrap: break-word;
}

.tab-data {
  background: white;
  border: 1px solid #c8ced3;
  padding: 1rem;
  padding-bottom: none;
}

.details-page {
  .tab-content {
    border: none;
  }
}

.max-width {
  @include size-modifiers($attribute: "max-width");
}

.max-width-medium {
  max-width: 768px;
}

.line-height-1-5 {
  line-height: 1.5rem;
}

.line-height-1 {
  line-height: 1rem;
}

.line-height-0 {
  line-height: 0rem;
}

.line-height-0-5 {
  line-height: 0.5rem;
}

.min-width-30 {
  min-width: 30px;
}

.text-success-light {
  color: lighten(#4dbd74, 20%) !important;
}

.text-danger-light {
  color: lighten(#f86c6b, 20%) !important;
}

.bg-danger-light {
  background: #fee2e1 linear-gradient(180deg, #fae2e2, #fee2e1) repeat-x;
}

.bg-warning-light {
  background: #fff3cd linear-gradient(180deg, #fbf1d1, #fff3cd) repeat-x;
}

.dot-separate {
  > * {
    &:not(:last-child) {
      &:after {
        content: "\2022";
        padding: 0 8px;
        color: var(--icon-default);
      }
    }
  }
  & > :empty + :not(:empty)::before {
    padding: 0px;
    content: "";
  }
}

.border-width-2 {
  &,
  &.border-top,
  &.border-left,
  &.border-right,
  &.border-bottom,
  &.border {
    border-width: 2px !important;
  }
}

.text-default {
  color: var(--text-default) !important;
}

.break-word {
  word-break: break-word;
}

.z-index-1052 {
  z-index: 1052 !important;
}

@media (prefers-reduced-motion: reduce) {
  .no-transition-if-reduced-motion {
    transition: none;
  }
}

.overflow-visible {
  overflow: visible !important;
}

.icon-sm {
  font-size: 1rem;
}

.icon-lg {
  font-size: 1.5rem;
}
