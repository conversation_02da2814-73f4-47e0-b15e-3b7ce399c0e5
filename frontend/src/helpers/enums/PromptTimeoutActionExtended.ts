import { PromptTimeoutAction } from "@/api/backend/generated/contracts";
import { ExtendedEnum } from "@/assets/js/enums/EnumExtensions";

export default new ExtendedEnum<typeof PromptTimeoutAction, PromptTimeoutAction>({
  Reboot: {
    text: "Reboot",
    value: 0,
  },
  Suppress: {
    text: "Suppress",
    value: 1,
  },
  FailSession: {
    text: "Fail Session",
    value: 2,
  },
  FailAction: {
    text: "Fail Action",
    value: 3,
  },
});
