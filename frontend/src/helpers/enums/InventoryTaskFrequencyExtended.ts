import { InventoryTaskFrequency } from "@/api/backend/generated/contracts";
import { ExtendedEnum, IEnumExtensionOptions } from "@/assets/js/enums/EnumExtensions";

export interface InventoryTaskFrequencyMetadata
  extends IEnumExtensionOptions<InventoryTaskFrequency> {
  description: string;
}

export default new ExtendedEnum<
  typeof InventoryTaskFrequency,
  InventoryTaskFrequency,
  InventoryTaskFrequencyMetadata
>({
  SpecifiedNumMinutes: {
    text: "Specified number of minutes",
    value: InventoryTaskFrequency.SpecifiedNumMinutes,
    description:
      "Indicates that the inventory task should be run every N minutes, where N is the number specified on the inventory task",
  },
  EveryMinute: {
    text: "Every minute",
    value: InventoryTaskFrequency.EveryMinute,
    description: "Indicates that the inventory task should be run every minute",
  },
  Hourly: {
    text: "Every hour",
    value: InventoryTaskFrequency.Hourly,
    description: "Indicates that the inventory task should be run every hour",
  },
  Daily: {
    text: "Every day",
    value: InventoryTaskFrequency.Daily,
    description: "Indicates that the inventory task should be run every day",
  },
  Weekly: {
    text: "Every week",
    value: InventoryTaskFrequency.Weekly,
    description: "Indicates that the inventory task should be run every week",
  },
});
