import { ScriptCategory } from "@/api/backend/generated/contracts";
import { ExtendedEnum, IEnumExtensionOptions } from "@/assets/js/enums/EnumExtensions";

export interface ScriptCategoryMetadata extends IEnumExtensionOptions<ScriptCategory> {
  description: string;
  icon: string;
}

const scriptCategoryEnum = new ExtendedEnum<
  typeof ScriptCategory,
  ScriptCategory,
  ScriptCategoryMetadata
>({
  SoftwareDetection: {
    text: "Software Detection",
    value: ScriptCategory.SoftwareDetection,
    description: "Allows for this script to utilize software detection variables",
    icon: "fal fa-file-magnifying-glass",
  },
  SoftwareAutoUpdate: {
    text: "Software Auto Update",
    value: ScriptCategory.SoftwareAutoUpdate,
    description: "Allows for this script to utilize software auto update variables",
    icon: "fal fa-download",
  },
  SoftwareVersionAction: {
    text: "Software Version Action",
    value: ScriptCategory.SoftwareVersionAction,
    description: "Allows for this script to utilize software version action variables",
    icon: "fal fa-box-circle-check",
  },
  MaintenanceTaskSetter: {
    text: "Task",
    value: ScriptCategory.MaintenanceTaskSetter,
    description: "Allows for this script to utilize task variables",
    icon: "fal fa-cubes",
  },
  MetascriptDeploymentTarget: {
    text: "Metascript Deployment Target",
    value: ScriptCategory.MetascriptDeploymentTarget,
    description: "Allows for this script to be selected as a 'metascript' deployment target type",
    icon: "fal fa-webhook",
  },
  FilterScriptDeploymentTarget: {
    text: "Filter Script Deployment Target",
    value: ScriptCategory.FilterScriptDeploymentTarget,
    description:
      "Allows for this script to be selected as a 'filter script' deployment target type",
    icon: "fal fa-filter",
  },
  DeviceInventory: {
    text: "Device Inventory",
    value: ScriptCategory.DeviceInventory,
    description: "Allows this script to be added to a device inventory schedule",
    icon: "fal fa-clipboard-list",
  },
  Function: {
    text: "Function",
    value: ScriptCategory.Function,
    description: "Allows for this script to be used by other scripts",
    icon: "fal fa-cube",
  },
  ImmySystem: {
    text: "Immy System",
    value: ScriptCategory.ImmySystem,
    description: "Reserved for scripts that are required by ImmyBot",
    icon: "",
  },
  DynamicVersions: {
    text: "Dynamic Versions",
    value: ScriptCategory.DynamicVersions,
    description: "This script is used to fetch installation info for a software",
    icon: "fal fa-sparkle",
  },
  DownloadInstaller: {
    text: "Download Installer",
    value: ScriptCategory.DownloadInstaller,
    description: "This script is used to download installers instead of the default download",
    icon: "fal fa-download",
  },
  Module: {
    text: "Module",
    value: ScriptCategory.Module,
    description: "Define multiple functions in a module script and import",
    icon: "fal fa-cubes-stacked",
  },
  Preflight: {
    text: "Preflight",
    value: ScriptCategory.Preflight,
    description: "This script is ran before any other scripts on a computer",
    icon: "fal fa-list-check",
  },
  Integration: {
    text: "Integration",
    icon: "fal fa-cloud-binary",
    value: ScriptCategory.Integration,
    description: "This script is used to setup an integration with a third party",
  },
});

export default scriptCategoryEnum;
