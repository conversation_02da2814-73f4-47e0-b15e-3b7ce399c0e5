import { NotificationAcknowledgement } from "@/api/backend/generated/contracts";
import { ExtendedEnum } from "@/assets/js/enums/EnumExtensions";

export default new ExtendedEnum<typeof NotificationAcknowledgement, NotificationAcknowledgement>({
  Unacknowledged: {
    text: "Unacknowledged",
    value: 1,
  },
  Acknowledged: {
    text: "Acknowledged",
    value: 2,
  },
  Dismissed: {
    text: "Dismissed",
    value: 3,
  },
});
