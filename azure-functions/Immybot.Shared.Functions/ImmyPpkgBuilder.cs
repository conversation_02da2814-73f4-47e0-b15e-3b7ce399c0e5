using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.IO.Compression;
using System.Xml;
using CliWrap;
using CliWrap.Buffered;
using CliWrap.Builders;
using DiscUtils.Iso9660;
using Microsoft.Azure.Functions.Worker;

namespace Immybot.Shared.Functions
{
  public class ImmyPpkgBuilder
    {
    private static readonly string _baseHomeStorageDirectory = Path.Combine(Environment.GetEnvironmentVariable("HOME") ?? Path.GetTempPath(), "data", "ImmyBot");
    private static readonly string _ICDToolDirectory = Path.Combine(_baseHomeStorageDirectory, "ICDTool", "ICD");
    private readonly ILogger<ImmyPpkgBuilder> _logger;

    public ImmyPpkgBuilder(ILogger<ImmyPpkgBuilder> logger)
    {
      _logger = logger;
    }

    [Function("ImmyPpkgBuilder")]
        public async Task<IActionResult> Run(
          [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequest req)
        {
            _logger.LogInformation($"{nameof(ImmyPpkgBuilder)} endpoint hit");

            EnsureICDToolsExist();

            string payloadString = await new StreamReader(req.Body).ReadToEndAsync();
            PpkgBundleParams bundleData = null;

            try
            {
                bundleData = JsonConvert.DeserializeObject<PpkgBundleParams>(string.IsNullOrEmpty(payloadString) ? req.Query["json"].ToString() : payloadString);
            }
            catch (MissingRequiredPropertiesException)
            {
                _logger.LogInformation("Received request is missing required properties. Canceling request.");
                return new BadRequestObjectResult("Error: Request is missing some required properties.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occured when trying to Parse Bundle Parameters!");
                return new BadRequestObjectResult("Error: Internal Error. Please check your request.");
            }

            _logger.LogInformation("Parsed PPKG bundle request. Generating Customization XML document...");
            var baseDocument = HelpersAndConstants.ReturnBasePPKGDocumentXml(bundleData.PackageName);

            foreach (dynamic module in bundleData.Modules)
            {
                // Check if module contains any direct document patches, and preforms them.
                if (module.GetType().GetMethod("patchBaseDocument") != null)
                {
                    baseDocument = module.patchBaseDocument(baseDocument);
                }

                // Go through every Powershell-based module parts
                if (module.GetType().GetProperty("XmlCommand") != null)
                {
                    if (module.XmlCommand is XmlNode[])
                    {
                        foreach (var command in module.XmlCommand)
                        {
                            var importedCommand = baseDocument.ImportNode(command, true);
                            baseDocument.GetElementsByTagName("PrimaryContext").Item(0).AppendChild(importedCommand);
                        }
                    }
                    else
                    {
                        var importedCommand = baseDocument.ImportNode(module.XmlCommand, true);
                        baseDocument.GetElementsByTagName("PrimaryContext").Item(0).AppendChild(importedCommand);
                    }
                }
            }

            var customizationsTmpFile = Path.Combine(
              Path.GetTempPath(),
              Path.ChangeExtension(Path.GetRandomFileName(), ".xml")
            );

            var ppkgTmpFile = Path.Combine(
              Path.GetTempPath(),
              Path.ChangeExtension(Path.GetRandomFileName(), ".ppkg")
            );

            // We have built the bundle customizations, so we now need to save it for ICD
            using (FileStream fs = File.OpenWrite(customizationsTmpFile))
            {
                baseDocument.Save(fs);
            }

            _logger.LogInformation("Created customization XML and temp PPKG output file. Attempting to run ICD Tool...");
            try
            {
                if (!await ExecuteICDToolAsync(customizationsTmpFile, ppkgTmpFile, bundleData.EncryptionPassword))
                {
                    // Either ExitCode indicated unsuccessful return or output says we failed.
                    _logger.LogError("Error occured when trying to use ICD Tool!");
                    RemoveTempFilesIfExist(ppkgTmpFile, customizationsTmpFile);
                    return new BadRequestObjectResult("Error: Internal Error attempting to generate PPKG.");
                }
                // Looks like we cant sign packages that are encrypted with a password, at least with Signtool.exe the usual way
                // I have gone as far as to decompile ICD tool, to see how they use signtool inside, and it's nearly identical code to what we currently do.
                // so I'm unsure what it is we are doing different.
                if (string.IsNullOrWhiteSpace(bundleData.EncryptionPassword))
                {
                    _logger.LogInformation("ICD Tool successfully ran. Attempting to sign package...");
                    try
                    {
                        if (await FileEVSigner.SignFileAsync(ppkgTmpFile))
                        {
                            _logger.LogInformation("Successfully signed bundle!");
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "Failed to run SignTool on generated PPKG! Returning an Unsigned binary!");
                    }
                    _logger.LogInformation("Reading PPKG file into memory...");
                }
                // Wrapping the memoryStream in a using block causes it to be disposed before returning
                // to client. I have verified the memoryStream does get GC'd after returning.
                MemoryStream memoryBuffer = new MemoryStream();

                using (FileStream ppkgFs = File.OpenRead(ppkgTmpFile))
                {
                    await ppkgFs.CopyToAsync(memoryBuffer);
                    _logger.LogInformation("Read PPKG into memory [{MemBufferKilobytes}kB]",
                      memoryBuffer.Length / 1024);
                }

                RemoveTempFilesIfExist(ppkgTmpFile, customizationsTmpFile);
                _logger.LogInformation($"Deleted temp files.");

                memoryBuffer.Position = 0;

                if (bundleData.DownloadISO)
                {
                    _logger.LogInformation($"Building ISO container of PPKG...");
                    MemoryStream isoMemoryBuffer = new MemoryStream();

                    CDBuilder isoBuilder = new CDBuilder
                    {
                      UseJoliet = true,
                      VolumeIdentifier = "IMMY_AGENT_INSTALLER"
                    };
                    isoBuilder.AddFile("ImmyAgentPPKG.ppkg", memoryBuffer);
                    isoBuilder.Build(isoMemoryBuffer);

                    await memoryBuffer.DisposeAsync();
                    isoMemoryBuffer.Position = 0;

                    _logger.LogInformation($"ISO file generated. Returning ISO binary to client.");

                    return new FileStreamResult(isoMemoryBuffer, "application/octet-stream")
                    { FileDownloadName = bundleData.PackageName + ".iso" };
                }

                _logger.LogInformation($"Returning PPKG binary to client.");

                return new FileStreamResult(memoryBuffer, "application/octet-stream")
                { FileDownloadName = bundleData.PackageName + ".ppkg" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occured when trying to generate PPKG!");
                RemoveTempFilesIfExist(ppkgTmpFile, customizationsTmpFile);

                return new BadRequestObjectResult("Error: Internal Error attempting to generate PPKG.");
            }
        }

        // Run the ICD tool to generate packages
        private static async Task<bool> ExecuteICDToolAsync(string customizationsTmpFile, string ppkgTmpFile, string packagePassword = null)
        {
            var args = new ArgumentsBuilder()
                      .Add("/Build-ProvisioningPackage")
                      .Add($"/CustomizationXML:{customizationsTmpFile}", true)
                      .Add($"/PackagePath:{ppkgTmpFile}", true)
                      .Add($"/StoreFile:{Path.Combine(_ICDToolDirectory, "Microsoft-Desktop-Provisioning.dat")}", true);


            if (!string.IsNullOrWhiteSpace(packagePassword))
            {
                args = args.Add($"/EncryptionPassword:{packagePassword}");
            }
            var command = await Cli.Wrap(Path.Combine(_ICDToolDirectory, "ICD.exe"))
              .WithArguments(args.Build())
              .WithValidation(CommandResultValidation.None)
              .ExecuteBufferedAsync();
            return (command.ExitCode == 0 && command.StandardOutput.ToLower().Contains("successfully built"));
        }

        // Delete temporary files that are used during the bundling process
        private static void RemoveTempFilesIfExist(params string[] files)
        {
            foreach (var file in files)
            {
                File.Delete(file);
            }
        }

        /// <summary>
        /// Will ensure ICD tools Dir is not empty, and if it is, will extract the _ICD.zip archive to directory
        /// </summary>
        public static void EnsureICDToolsExist()
        {
            var extractionPath = new DirectoryInfo(_ICDToolDirectory).Parent;
            Directory.CreateDirectory(extractionPath.FullName);
            // Tools don't exist yet. We need to extract them.
            if (!Directory.Exists(_ICDToolDirectory) || Directory.GetFiles(_ICDToolDirectory).Length == 0)
            {
                using (ZipArchive archive = new ZipArchive(new MemoryStream(Properties.Resources._ICD), ZipArchiveMode.Read))
                {
                    archive.ExtractToDirectory(extractionPath.FullName);
                }
            }
        }
    }
}
