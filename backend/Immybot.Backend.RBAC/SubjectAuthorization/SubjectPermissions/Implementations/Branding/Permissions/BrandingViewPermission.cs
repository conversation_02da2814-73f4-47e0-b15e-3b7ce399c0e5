using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Branding.Permissions;

/// <summary>
/// Permission to view branding.
/// </summary>
public class BrandingViewPermission : PermissionMetadata, IBrandingViewPermission
{
  /// <summary>
  /// Initializes a new instance of the <see cref="BrandingViewPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the branding subject.</param>
  public BrandingViewPermission(SubjectFactory<BrandingSubject> subjectFactory)
      : base(subjectFactory)
  {
  }

  /// <inheritdoc/>
  public override string PermissionName => "view";

  /// <inheritdoc/>
  public override string DisplayName => "View Branding";

  /// <inheritdoc/>
  public override string Description => "View branding settings";
}
