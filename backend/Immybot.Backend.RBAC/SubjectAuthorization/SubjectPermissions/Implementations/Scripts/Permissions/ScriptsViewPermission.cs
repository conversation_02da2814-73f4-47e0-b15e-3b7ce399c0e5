using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;

/// <summary>
/// Permission to view scripts.
/// </summary>
public class ScriptsViewPermission : PermissionMetadata, IScriptsViewPermission
{
  /// <summary>
  /// Initializes a new instance of the <see cref="ScriptsViewPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the scripts subject.</param>
  public ScriptsViewPermission(
    SubjectFactory<ScriptsSubject> subjectFactory)
    : base(subjectFactory)
  {
  }

  /// <inheritdoc/>
  public override string PermissionName => "view";

  /// <inheritdoc/>
  public override string DisplayName => "View Scripts";

  /// <inheritdoc/>
  public override string Description => "View scripts";

  /// <inheritdoc/>
  public override IEnumerable<IPermissionMetadata> Dependencies => [];
}
