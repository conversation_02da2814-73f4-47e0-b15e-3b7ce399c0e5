using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to not need submit change requests for  cross-tenant deployments (create, edit, and delete).
/// </summary>
public class ChangeRequestNotRequiredForCrossTenantDeploymentPermission : PermissionMetadata,
  IChangeRequestNotRequiredForCrossTenantDeploymentPermission
{
  private readonly DeploymentsManageCrossTenantPermission _crossTenantManagePermission;

  /// <summary>
  /// Initializes a new instance of the <see cref="ChangeRequestNotRequiredForCrossTenantDeploymentPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the deployments subject.</param>
  /// <param name="managePermission">The manage cross-tenant permission.</param>
  public ChangeRequestNotRequiredForCrossTenantDeploymentPermission(
    SubjectFactory<DeploymentsSubject> subjectFactory,
    DeploymentsManageCrossTenantPermission managePermission)
    : base(subjectFactory)
  {
    _crossTenantManagePermission = managePermission;
  }

  /// <inheritdoc/>
  public override string PermissionName => "no_change_requests_for_cross_tenant_deployment";

  /// <inheritdoc/>
  public override string DisplayName => "No cross-tenant change requested";

  /// <inheritdoc/>
  public override string Description =>
    "Gives permission to not need to submit change requests for cross-tenant deployments.";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.Advanced;

  /// <inheritdoc/>
  public override IEnumerable<IPermissionMetadata> Dependencies => [_crossTenantManagePermission];
}
