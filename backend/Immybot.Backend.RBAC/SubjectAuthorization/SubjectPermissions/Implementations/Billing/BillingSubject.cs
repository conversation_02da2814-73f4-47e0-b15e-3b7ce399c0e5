using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Billing;

/// <summary>
/// Subject definition for Billing.
/// </summary>
public class BillingSubject : SubjectMetadata<IBillingSubjectPermission>, IBillingSubject
{
  /// <summary>
  /// Initializes a new instance of the <see cref="BillingSubject"/> class.
  /// </summary>
  /// <param name="permissionFactory">Factory delegate that creates the permissions for this subject.</param>
  public BillingSubject(PermissionFactory<IBillingSubjectPermission> permissionFactory)
      : base(permissionFactory)
  { }

  /// <inheritdoc/>
  public override string Name => "billing";

  /// <inheritdoc/>
  public override string DisplayName => "Billing";

  /// <inheritdoc/>
  public override string Description => "Manage billing and subscription information";

  /// <inheritdoc/>
  public override int SortOrder => 130;
}
