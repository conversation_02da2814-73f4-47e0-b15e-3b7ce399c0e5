using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Users.Permissions;

/// <summary>
/// Permission to impersonate users.
/// </summary>
public class UsersImpersonatePermission : PermissionMetadata, IUsersImpersonatePermission
{
  private readonly UsersViewPermission _viewPermission;

  /// <summary>
  /// Initializes a new instance of the <see cref="UsersImpersonatePermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the users subject.</param>
  /// <param name="viewPermission">The view permission dependency.</param>
  public UsersImpersonatePermission(SubjectFactory<UsersSubject> subjectFactory, UsersViewPermission viewPermission)
      : base(subjectFactory)
  {
    _viewPermission = viewPermission;
  }

  /// <inheritdoc/>
  public override string PermissionName => "impersonate";

  /// <inheritdoc/>
  public override string DisplayName => "Impersonate Users";

  /// <inheritdoc/>
  public override string Description => "Impersonate other users";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.Advanced;

  /// <inheritdoc/>
  public override bool HasGreatPower => true;

  /// <inheritdoc/>
  public override IEnumerable<IPermissionMetadata> Dependencies => new IPermissionMetadata[] { _viewPermission };
}
