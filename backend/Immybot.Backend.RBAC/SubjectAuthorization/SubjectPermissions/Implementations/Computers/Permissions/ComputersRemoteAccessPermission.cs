using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;

/// <summary>
/// Permission to remotely access computers.
/// </summary>
public class ComputersRemoteAccessPermission : PermissionMetadata, IComputersRemoteAccessPermission
{
  /// <summary>
  /// Initializes a new instance of the <see cref="ComputersRemoteAccessPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the computer subject.</param>
  public ComputersRemoteAccessPermission(SubjectFactory<ComputersSubject> subjectFactory)
    : base(subjectFactory)
  {
  }

  /// <inheritdoc/>
  public override string PermissionName => "remote_access";

  /// <inheritdoc/>
  public override string DisplayName => "Remote Access";

  /// <inheritdoc/>
  public override string Description => "Remotely access and control computers";
}
