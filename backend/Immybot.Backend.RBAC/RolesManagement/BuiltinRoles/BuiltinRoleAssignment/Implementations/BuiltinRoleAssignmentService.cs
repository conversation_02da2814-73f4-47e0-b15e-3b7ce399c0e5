using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Interfaces;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Implementations;

/// <summary>
/// This service is responsible for watching user events and assigning built-in roles to users.
/// It can be removed once the rbac system is fully implemented.
/// </summary>
public class BuiltinRoleAssignmentService : IHostedService
{
  private readonly IBuiltinRoleAssignmentActions _builtinRoleAssignmentActions;
  private readonly IDisposable _userCreatedHandle;
  private readonly IDisposable _userUpdatedHandle;
  private readonly IDisposable _permissionPreferenceUpdatedHandle;
  private CancellationToken? _cancellationToken;

  public BuiltinRoleAssignmentService(
    IDomainEventReceiver domainEventReceiver,
    IBuiltinRoleAssignmentActions builtinRoleAssignmentActions)
  {
    _builtinRoleAssignmentActions = builtinRoleAssignmentActions;
    _userCreatedHandle = domainEventReceiver.Subscribe<UserCreatedEvent>(AssignBuiltinRoleToCreatedUser);
    _userUpdatedHandle = domainEventReceiver.Subscribe<UserUpdatedEvent>(AssignBuiltinRoleToUpdatedUser);
    _permissionPreferenceUpdatedHandle =
      domainEventReceiver.Subscribe<PermissionPreferenceUpdatedEvent>(UpdateBuiltinRolesForAllUsers);
  }

  public async Task StartAsync(CancellationToken cancellationToken)
  {
    _cancellationToken = cancellationToken;

    await Task.Yield();
  }

  public Task StopAsync(CancellationToken cancellationToken)
  {
    try
    {
      _userCreatedHandle.Dispose();
      _userUpdatedHandle.Dispose();
      _permissionPreferenceUpdatedHandle.Dispose();
    }
    catch (Exception)
    {
      // ignore
    }

    return Task.CompletedTask;
  }

  private async Task AssignBuiltinRoleToCreatedUser(UserCreatedEvent ev)
  {
    await _builtinRoleAssignmentActions.AssignBuiltinRoleToUserAsync(
      ev.User,
      emitInvalidateCacheEvent: true,
      _cancellationToken ?? CancellationToken.None);
  }

  private async Task UpdateBuiltinRolesForAllUsers(PermissionPreferenceUpdatedEvent _)
  {
    await _builtinRoleAssignmentActions.UpdateBuiltinRolesForAllUsersAsync(_cancellationToken ??
                                                                           CancellationToken.None);
  }

  private async Task AssignBuiltinRoleToUpdatedUser(UserUpdatedEvent ev)
  {
    await _builtinRoleAssignmentActions.AssignBuiltinRoleToUserAsync(
      ev.User,
      emitInvalidateCacheEvent: true,
      _cancellationToken ?? CancellationToken.None);
  }
}
