namespace Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;

/// <summary>
/// Provides a way to retrieve the id of the user who created a resource.
/// </summary>
/// <typeparam name="TResource">The resource to check for created by user</typeparam>
public interface ICreatedByRelationshipProvider<in TResource>
  where TResource : class
{
  /// <summary>
  /// Returns the user id of the user who created the resource, or null if not applicable or the resource does not have a creator.
  /// </summary>
  /// <param name="resource"></param>
  /// <returns></returns>
  int? GetCreatedByUserId(TResource resource);

  /// <summary>
  /// Indicates whether the resource supports a created by relationship.
  /// </summary>
  bool SupportsCreatedByRelationship { get; }
}
