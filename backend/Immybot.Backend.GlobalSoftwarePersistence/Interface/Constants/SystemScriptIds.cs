namespace Immybot.Backend.GlobalSoftwarePersistence.Interface.Constants;

public static class SystemScriptIds
{
  public const int GetWindowsSystemInformationScriptId = -1;
  public const int GetWindowsNetworkAdaptersScriptId = -2;
  public const int GetWindowsExternalIpsScriptId = -3;
  public const int GetWindowsPhysicalDisksScriptId = -4;
  public const int GetWindowsLogicalDisksScriptId = -5;
  public const int GetWindowsPartitionsScriptId = -6;
  public const int GetWindowsDriversScriptId = -7;
  public const int GetWindowsAntivirusScriptId = -8;
  public const int GetWindowsPhysicalMemoryScriptId = -9;
  public const int GetWindowsProcessesScriptId = -10;
  public const int GetWindowsRebootPendingScriptId = -11;
  public const int GetWindowsSoftwareScriptId = -12;
  public const int GetWindowsExternalHostnameScriptId = -13;
  public const int GetWindowsLoggedOnUserScriptId = -14;
  public const int GetWindowsInternalIpScriptId = -15;
  public const int GetWindowsLoggedOnUserSidScriptId = -16;
  public const int ChocolateyIntallScriptId = -17;
  public const int RestartComputerAndWaitScriptId = -18;
  public const int GetOfflineDomainFileScriptId = -19;
  public const int JoinDomainScriptId = -20;
  public const int CheckDomainScriptId = -21;
  public const int CheckAzureADScriptId = -22;
  public const int RemoveComputerFromDomainScriptId = -23;
  public const int UninstallByProductCodeScriptId = 992;
}
