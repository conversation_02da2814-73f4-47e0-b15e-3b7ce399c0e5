using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations
{
    /// <inheritdoc />
    public partial class AddIntegrationTypeTag : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "tag",
                table: "dynamic_integration_types",
                type: "integer",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.Sql("UPDATE dynamic_integration_types SET tag = 4;");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "tag",
                table: "dynamic_integration_types");
        }
    }
}
