// <auto-generated />
using System;
using Immybot.Backend.GlobalSoftwarePersistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations
{
    [DbContext(typeof(SoftwareDbContext))]
    [Migration("20220214231527_AddDisabledToTargetAssignments")]
    partial class AddDisabledToTargetAssignments
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 63)
                .HasAnnotation("ProductVersion", "5.0.9")
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            modelBuilder.Entity("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer")
                        .HasColumnName("frequency");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int?>("SpecifiedNumMinutes")
                        .HasColumnType("integer")
                        .HasColumnName("specified_num_minutes");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_inventory_tasks");

                    b.ToTable("inventory_tasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int?>("AutoUpdateScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("auto_update_script_id");

                    b.Property<string>("ChocoProviderSoftwareId")
                        .HasColumnType("text")
                        .HasColumnName("choco_provider_software_id");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DetectionMethod")
                        .HasColumnType("integer")
                        .HasColumnName("detection_method");

                    b.Property<int?>("DetectionScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("detection_script_id");

                    b.Property<int?>("DownloadInstallerScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("download_installer_script_id");

                    b.Property<int?>("DynamicVersionsScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("dynamic_versions_script_id");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<int>("InstallOrder")
                        .HasColumnType("integer")
                        .HasColumnName("install_order");

                    b.Property<int?>("InstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_id");

                    b.Property<string>("LicenseDescription")
                        .HasColumnType("text")
                        .HasColumnName("license_description");

                    b.Property<int>("LicenseRequirement")
                        .HasColumnType("integer")
                        .HasColumnName("license_requirement");

                    b.Property<int>("LicenseType")
                        .HasColumnType("integer")
                        .HasColumnName("license_type");

                    b.Property<bool>("Licensed")
                        .HasColumnType("boolean")
                        .HasColumnName("licensed");

                    b.Property<int?>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<string>("NiniteProviderSoftwareId")
                        .HasColumnType("text")
                        .HasColumnName("ninite_provider_software_id");

                    b.Property<string>("Notes")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("notes");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_id");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_id");

                    b.Property<bool>("RebootNeeded")
                        .HasColumnType("boolean")
                        .HasColumnName("reboot_needed");

                    b.Property<bool>("Recommended")
                        .HasColumnType("boolean")
                        .HasColumnName("recommended");

                    b.Property<Guid>("RelativeCacheSourcePath")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("relative_cache_source_path")
                        .HasDefaultValueSql("uuid_generate_v4()");

                    b.Property<int?>("RepairScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("repair_script_id");

                    b.Property<int>("RepairType")
                        .HasColumnType("integer")
                        .HasColumnName("repair_type");

                    b.Property<int?>("SoftwareIconMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("software_icon_media_id");

                    b.Property<string>("SoftwareTableName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("software_table_name");

                    b.Property<int?>("SoftwareTableNameSearchMode")
                        .HasColumnType("integer")
                        .HasColumnName("software_table_name_search_mode");

                    b.Property<string>("TestFailedError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("test_failed_error");

                    b.Property<bool>("TestRequired")
                        .HasColumnType("boolean")
                        .HasColumnName("test_required");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("default_uninstall_script_id");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<string>("UpgradeCode")
                        .HasColumnType("text")
                        .HasColumnName("upgrade_code");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_id");

                    b.Property<int>("UpgradeStrategy")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_strategy");

                    b.Property<bool>("UseDynamicVersions")
                        .HasColumnType("boolean")
                        .HasColumnName("use_dynamic_versions");

                    b.Property<bool>("UseSoftwareTableDetection")
                        .HasColumnType("boolean")
                        .HasColumnName("use_software_table_detection");

                    b.HasKey("Id")
                        .HasName("pk_software");

                    b.HasIndex("AutoUpdateScriptId")
                        .HasDatabaseName("ix_software_auto_update_script_id");

                    b.HasIndex("DetectionScriptId")
                        .HasDatabaseName("ix_software_detection_script_id");

                    b.HasIndex("DownloadInstallerScriptId")
                        .HasDatabaseName("ix_software_download_installer_script_id");

                    b.HasIndex("DynamicVersionsScriptId")
                        .HasDatabaseName("ix_software_dynamic_versions_script_id");

                    b.HasIndex("InstallScriptId")
                        .HasDatabaseName("ix_software_install_script_id");

                    b.HasIndex("MaintenanceTaskId")
                        .HasDatabaseName("ix_software_maintenance_task_id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_software_name");

                    b.HasIndex("PostInstallScriptId")
                        .HasDatabaseName("ix_software_post_install_script_id");

                    b.HasIndex("PostUninstallScriptId")
                        .HasDatabaseName("ix_software_post_uninstall_script_id");

                    b.HasIndex("RepairScriptId")
                        .HasDatabaseName("ix_software_repair_script_id");

                    b.HasIndex("SoftwareIconMediaId")
                        .HasDatabaseName("ix_software_software_icon_media_id");

                    b.HasIndex("TestScriptId")
                        .HasDatabaseName("ix_software_test_script_id");

                    b.HasIndex("UninstallScriptId")
                        .HasDatabaseName("ix_software_uninstall_script_id");

                    b.HasIndex("UpgradeScriptId")
                        .HasDatabaseName("ix_software_upgrade_script_id");

                    b.ToTable("software");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.Property<int>("SoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("software_id");

                    b.Property<string>("SemanticVersion")
                        .HasColumnType("text")
                        .HasColumnName("semantic_version");

                    b.Property<int?>("Architecture")
                        .HasColumnType("integer")
                        .HasColumnName("architecture");

                    b.Property<string>("BlobName")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("blob_name");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("DependsOnSemanticVersion")
                        .HasColumnType("text")
                        .HasColumnName("depends_on_semantic_version");

                    b.Property<int?>("DeprecatedIdField")
                        .HasColumnType("integer")
                        .HasColumnName("deprecated_id_field");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("display_name");

                    b.Property<string>("DisplayVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("display_version");

                    b.Property<int?>("InstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_id");

                    b.Property<string>("InstallerFile")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("installer_file");

                    b.Property<int>("InstallerType")
                        .HasColumnType("integer")
                        .HasColumnName("installer_type");

                    b.Property<int?>("LastResult")
                        .HasColumnType("integer")
                        .HasColumnName("last_result");

                    b.Property<int>("LicenseType")
                        .HasColumnType("integer")
                        .HasColumnName("license_type");

                    b.Property<string>("Notes")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("notes");

                    b.Property<int>("NumActionFailures")
                        .HasColumnType("integer")
                        .HasColumnName("num_action_failures");

                    b.Property<int>("NumActionSuccesses")
                        .HasColumnType("integer")
                        .HasColumnName("num_action_successes");

                    b.Property<string>("PackageHash")
                        .HasColumnType("text")
                        .HasColumnName("package_hash");

                    b.Property<int>("PackageType")
                        .HasColumnType("integer")
                        .HasColumnName("package_type");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_id");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_id");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text")
                        .HasColumnName("product_code");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("relative_cache_source_path");

                    b.Property<string>("TestFailedError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("test_failed_error");

                    b.Property<bool>("TestRequired")
                        .HasColumnType("boolean")
                        .HasColumnName("test_required");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<string>("URL")
                        .HasMaxLength(2083)
                        .HasColumnType("character varying(2083)")
                        .HasColumnName("url");

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("uninstall_script_id");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_id");

                    b.Property<int>("UpgradeStrategy")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_strategy");

                    b.HasKey("SoftwareId", "SemanticVersion")
                        .HasName("pk_software_versions");

                    b.HasIndex("InstallScriptId")
                        .HasDatabaseName("ix_software_versions_install_script_id");

                    b.HasIndex("PostInstallScriptId")
                        .HasDatabaseName("ix_software_versions_post_install_script_id");

                    b.HasIndex("PostUninstallScriptId")
                        .HasDatabaseName("ix_software_versions_post_uninstall_script_id");

                    b.HasIndex("TestScriptId")
                        .HasDatabaseName("ix_software_versions_test_script_id");

                    b.HasIndex("UninstallScriptId")
                        .HasDatabaseName("ix_software_versions_uninstall_script_id");

                    b.HasIndex("UpgradeScriptId")
                        .HasDatabaseName("ix_software_versions_upgrade_script_id");

                    b.ToTable("software_versions");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("database_type");

                    b.Property<bool>("GetEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("get_enabled");

                    b.Property<int?>("GetScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("get_script_id");

                    b.Property<int?>("GetScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("get_script_type");

                    b.Property<int?>("IconMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("icon_media_id");

                    b.Property<bool>("IsConfigurationTask")
                        .HasColumnType("boolean")
                        .HasColumnName("is_configuration_task");

                    b.Property<int>("MaintenanceTaskCategory")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_category");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<bool>("Recommended")
                        .HasColumnType("boolean")
                        .HasColumnName("recommended");

                    b.Property<bool>("SetEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("set_enabled");

                    b.Property<int?>("SetScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("set_script_id");

                    b.Property<int?>("SetScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("set_script_type");

                    b.Property<bool>("TestEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("test_enabled");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<int?>("TestScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_type");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_tasks");

                    b.HasIndex("GetScriptId")
                        .HasDatabaseName("ix_maintenance_tasks_get_script_id");

                    b.HasIndex("IconMediaId")
                        .HasDatabaseName("ix_maintenance_tasks_icon_media_id");

                    b.HasIndex("SetScriptId")
                        .HasDatabaseName("ix_maintenance_tasks_set_script_id");

                    b.HasIndex("TestScriptId")
                        .HasDatabaseName("ix_maintenance_tasks_test_script_id");

                    b.ToTable("maintenance_tasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("DataType")
                        .HasColumnType("integer")
                        .HasColumnName("data_type");

                    b.Property<int?>("DefaultMediaDatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("default_media_database_type");

                    b.Property<int?>("DefaultMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("default_media_id");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("text")
                        .HasColumnName("default_value");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<int>("Order")
                        .HasColumnType("integer")
                        .HasColumnName("order");

                    b.Property<bool>("Required")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("required");

                    b.Property<string>("SelectableValues")
                        .HasColumnType("text")
                        .HasColumnName("selectable_values");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_task_parameters");

                    b.HasIndex("DefaultMediaId")
                        .HasDatabaseName("ix_maintenance_task_parameters_default_media_id");

                    b.HasIndex("MaintenanceTaskId")
                        .HasDatabaseName("ix_maintenance_task_parameters_maintenance_task_id");

                    b.ToTable("maintenance_task_parameters");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameterValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("DeploymentId")
                        .HasColumnType("integer")
                        .HasColumnName("deployment_id");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<int>("MaintenanceTaskParameterId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_parameter_id");

                    b.Property<int>("MaintenanceTaskType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("maintenance_task_type");

                    b.Property<int?>("MediaDatabaseType")
                        .HasColumnType("integer")
                        .HasColumnName("media_database_type");

                    b.Property<int?>("MediaId")
                        .HasColumnType("integer")
                        .HasColumnName("media_id");

                    b.Property<string>("Value")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_task_parameter_values");

                    b.HasIndex("DeploymentId")
                        .HasDatabaseName("ix_maintenance_task_parameter_values_deployment_id");

                    b.HasIndex("MaintenanceTaskId")
                        .HasDatabaseName("ix_maintenance_task_parameter_values_maintenance_task_id");

                    b.HasIndex("MaintenanceTaskParameterId")
                        .HasDatabaseName("ix_maintenance_task_parameter_values_maintenance_task_paramete");

                    b.HasIndex("MediaId")
                        .HasDatabaseName("ix_maintenance_task_parameter_values_media_id");

                    b.ToTable("maintenance_task_parameter_values");
                });

            modelBuilder.Entity("Immybot.Domain.Models.Media", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("BlobReference")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("blob_reference");

                    b.Property<int>("Category")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("category");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("database_type");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("file_name");

                    b.Property<string>("MimeType")
                        .HasColumnType("text")
                        .HasColumnName("mime_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("name");

                    b.Property<string>("PackageHash")
                        .HasColumnType("text")
                        .HasColumnName("package_hash");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasColumnType("text")
                        .HasColumnName("relative_cache_source_path");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_media");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_media_name");

                    b.ToTable("media");
                });

            modelBuilder.Entity("Immybot.Domain.Models.Script", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnType("text")
                        .HasColumnName("action");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int>("OutputType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("output_type");

                    b.Property<bool>("ReadOnly")
                        .HasColumnType("boolean")
                        .HasColumnName("read_only");

                    b.Property<string>("ScriptCacheName")
                        .HasColumnType("text")
                        .HasColumnName("script_cache_name");

                    b.Property<int>("ScriptCategory")
                        .HasColumnType("integer")
                        .HasColumnName("script_category");

                    b.Property<int>("ScriptExecutionContext")
                        .HasColumnType("integer")
                        .HasColumnName("script_execution_context");

                    b.Property<string>("ScriptHash")
                        .HasColumnType("text")
                        .HasColumnName("script_hash");

                    b.Property<int>("ScriptLanguage")
                        .HasColumnType("integer")
                        .HasColumnName("script_action_type");

                    b.Property<int>("ScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("script_type");

                    b.Property<int?>("Timeout")
                        .HasColumnType("integer")
                        .HasColumnName("timeout");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<int>("UserActionTrigger")
                        .HasColumnType("integer")
                        .HasColumnName("user_action_trigger");

                    b.HasKey("Id")
                        .HasName("pk_scripts");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_scripts_name");

                    b.HasIndex("ScriptCacheName")
                        .IsUnique()
                        .HasDatabaseName("ix_scripts_script_cache_name");

                    b.ToTable("scripts");
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("ActionToPerform")
                        .HasColumnType("integer")
                        .HasColumnName("action_to_perform");

                    b.Property<int>("Condition")
                        .HasColumnType("integer")
                        .HasColumnName("condition");

                    b.Property<int>("GlobalSoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("global_software_id");

                    b.Property<int>("SubjectQualifier")
                        .HasColumnType("integer")
                        .HasColumnName("subject_qualifier");

                    b.HasKey("Id")
                        .HasName("pk_software_prerequisite");

                    b.HasIndex("GlobalSoftwareId")
                        .HasDatabaseName("ix_software_prerequisite_global_software_id");

                    b.ToTable("software_prerequisite");
                });

            modelBuilder.Entity("Immybot.Domain.Models.TargetAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("database_type");

                    b.Property<int?>("DesiredSoftwareState")
                        .HasColumnType("integer")
                        .HasColumnName("desired_software_state");

                    b.Property<bool>("Excluded")
                        .HasColumnType("boolean")
                        .HasColumnName("excluded");

                    b.Property<string>("MaintenanceIdentifier")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("maintenance_identifier");

                    b.Property<int?>("MaintenanceTaskMode")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_mode");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<int?>("SoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("software_id");

                    b.Property<int?>("SoftwareProviderType")
                        .HasColumnType("integer")
                        .HasColumnName("software_provider_type");

                    b.Property<string>("SoftwareSemanticVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("software_semantic_version");

                    b.Property<int>("SoftwareVersionId")
                        .HasColumnType("integer")
                        .HasColumnName("software_version_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Target")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target");

                    b.Property<int>("TargetCategory")
                        .HasColumnType("integer")
                        .HasColumnName("target_category");

                    b.Property<short>("TargetGroupFilter")
                        .HasColumnType("smallint")
                        .HasColumnName("target_group_filter");

                    b.Property<string>("TargetName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target_name");

                    b.Property<int>("TargetType")
                        .HasColumnType("integer")
                        .HasColumnName("target_type");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_target_assignments");

                    b.ToTable("target_assignments");
                });

            modelBuilder.Entity("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTask", b =>
                {
                    b.OwnsMany("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTaskScript", "_Scripts", b1 =>
                        {
                            b1.Property<int>("InventoryTaskId")
                                .HasColumnType("integer")
                                .HasColumnName("inventory_task_id");

                            b1.Property<string>("InventoryKey")
                                .HasColumnType("text")
                                .HasColumnName("inventory_key");

                            b1.Property<bool>("SaveDebugStream")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("save_debug_stream");

                            b1.Property<bool>("SaveInformationStream")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("save_information_stream");

                            b1.Property<bool>("SaveVerboseStream")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("save_verbose_stream");

                            b1.Property<bool>("SaveWarningStream")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("save_warning_stream");

                            b1.Property<int>("ScriptId")
                                .HasColumnType("integer")
                                .HasColumnName("script_id");

                            b1.HasKey("InventoryTaskId", "InventoryKey")
                                .HasName("pk_inventory_task_scripts");

                            b1.HasIndex("ScriptId")
                                .HasDatabaseName("ix_inventory_task_scripts_script_id");

                            b1.ToTable("inventory_task_scripts");

                            b1.WithOwner()
                                .HasForeignKey("InventoryTaskId")
                                .HasConstraintName("fk_inventory_task_scripts_inventory_tasks_global_inventory_tas");

                            b1.HasOne("Immybot.Domain.Models.Script", null)
                                .WithMany()
                                .HasForeignKey("ScriptId")
                                .HasConstraintName("fk_inventory_task_scripts_scripts_script_id")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();
                        });

                    b.Navigation("_Scripts");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "AutoUpdateScript")
                        .WithMany()
                        .HasForeignKey("AutoUpdateScriptId")
                        .HasConstraintName("fk_software_scripts_auto_update_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "DetectionScript")
                        .WithMany()
                        .HasForeignKey("DetectionScriptId")
                        .HasConstraintName("fk_software_scripts_detection_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "DownloadInstallerScript")
                        .WithMany()
                        .HasForeignKey("DownloadInstallerScriptId")
                        .HasConstraintName("fk_software_scripts_download_installer_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "DynamicVersionsScript")
                        .WithMany()
                        .HasForeignKey("DynamicVersionsScriptId")
                        .HasConstraintName("fk_software_scripts_dynamic_versions_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "InstallScript")
                        .WithMany()
                        .HasForeignKey("InstallScriptId")
                        .HasConstraintName("fk_software_scripts_install_script_id");

                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany()
                        .HasForeignKey("MaintenanceTaskId")
                        .HasConstraintName("fk_software_maintenance_tasks_maintenance_task_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostInstallScript")
                        .WithMany()
                        .HasForeignKey("PostInstallScriptId")
                        .HasConstraintName("fk_software_scripts_post_install_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostUninstallScript")
                        .WithMany()
                        .HasForeignKey("PostUninstallScriptId")
                        .HasConstraintName("fk_software_scripts_post_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "RepairScript")
                        .WithMany()
                        .HasForeignKey("RepairScriptId")
                        .HasConstraintName("fk_software_scripts_repair_script_id");

                    b.HasOne("Immybot.Domain.Models.Media", "SoftwareIcon")
                        .WithMany("GlobalSoftware")
                        .HasForeignKey("SoftwareIconMediaId")
                        .HasConstraintName("fk_software_media_media_id")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId")
                        .HasConstraintName("fk_software_scripts_test_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UninstallScript")
                        .WithMany()
                        .HasForeignKey("UninstallScriptId")
                        .HasConstraintName("fk_software_scripts_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UpgradeScript")
                        .WithMany()
                        .HasForeignKey("UpgradeScriptId")
                        .HasConstraintName("fk_software_scripts_upgrade_script_id");

                    b.Navigation("AutoUpdateScript");

                    b.Navigation("DetectionScript");

                    b.Navigation("DownloadInstallerScript");

                    b.Navigation("DynamicVersionsScript");

                    b.Navigation("InstallScript");

                    b.Navigation("MaintenanceTask");

                    b.Navigation("PostInstallScript");

                    b.Navigation("PostUninstallScript");

                    b.Navigation("RepairScript");

                    b.Navigation("SoftwareIcon");

                    b.Navigation("TestScript");

                    b.Navigation("UninstallScript");

                    b.Navigation("UpgradeScript");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "InstallScript")
                        .WithMany()
                        .HasForeignKey("InstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_install_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostInstallScript")
                        .WithMany()
                        .HasForeignKey("PostInstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_post_install_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostUninstallScript")
                        .WithMany()
                        .HasForeignKey("PostUninstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_post_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", "Software")
                        .WithMany("SoftwareVersions")
                        .HasForeignKey("SoftwareId")
                        .HasConstraintName("fk_software_versions_software_software_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId")
                        .HasConstraintName("fk_software_versions_scripts_test_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UninstallScript")
                        .WithMany()
                        .HasForeignKey("UninstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UpgradeScript")
                        .WithMany()
                        .HasForeignKey("UpgradeScriptId")
                        .HasConstraintName("fk_software_versions_scripts_upgrade_script_id");

                    b.Navigation("InstallScript");

                    b.Navigation("PostInstallScript");

                    b.Navigation("PostUninstallScript");

                    b.Navigation("Software");

                    b.Navigation("TestScript");

                    b.Navigation("UninstallScript");

                    b.Navigation("UpgradeScript");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "GetScript")
                        .WithMany()
                        .HasForeignKey("GetScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_get_script_id");

                    b.HasOne("Immybot.Domain.Models.Media", "Icon")
                        .WithMany("IconForMaintenanceTasks")
                        .HasForeignKey("IconMediaId")
                        .HasConstraintName("fk_maintenance_tasks_media_icon_id")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Immybot.Domain.Models.Script", "SetScript")
                        .WithMany()
                        .HasForeignKey("SetScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_set_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_test_script_id");

                    b.Navigation("GetScript");

                    b.Navigation("Icon");

                    b.Navigation("SetScript");

                    b.Navigation("TestScript");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Media", "DefaultMedia")
                        .WithMany()
                        .HasForeignKey("DefaultMediaId")
                        .HasConstraintName("fk_maintenance_task_parameters_media_default_media_id");

                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("Parameters")
                        .HasForeignKey("MaintenanceTaskId")
                        .HasConstraintName("fk_maintenance_task_parameters_maintenance_tasks_maintenance_t")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DefaultMedia");

                    b.Navigation("MaintenanceTask");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameterValue", b =>
                {
                    b.HasOne("Immybot.Domain.Models.TargetAssignment", "Deployment")
                        .WithMany("MaintenanceTaskParameterValues")
                        .HasForeignKey("DeploymentId")
                        .HasConstraintName("fk_maintenance_task_parameter_values_target_assignments_deploy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("MaintenanceTaskParameterValues")
                        .HasForeignKey("MaintenanceTaskId")
                        .HasConstraintName("fk_maintenance_task_parameter_values_maintenance_tasks_mainten")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.MaintenanceTaskParameter", "MaintenanceTaskParameter")
                        .WithMany("MaintenanceTaskParameterValues")
                        .HasForeignKey("MaintenanceTaskParameterId")
                        .HasConstraintName("fk_maintenance_task_parameter_values_maintenance_task_paramete")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.Media", "Media")
                        .WithMany()
                        .HasForeignKey("MediaId")
                        .HasConstraintName("fk_maintenance_task_parameter_values_media_media_id");

                    b.Navigation("Deployment");

                    b.Navigation("MaintenanceTask");

                    b.Navigation("MaintenanceTaskParameter");

                    b.Navigation("Media");
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", null)
                        .WithMany("SoftwarePrerequisites")
                        .HasForeignKey("GlobalSoftwareId")
                        .HasConstraintName("fk_software_prerequisite_software_global_software_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresForCondition", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("software_identifier");

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer")
                                .HasColumnName("software_prerequisite_id");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer")
                                .HasColumnName("software_type");

                            b1.HasKey("Id")
                                .HasName("pk_software_prerequisite_softwares_for_condition");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasDatabaseName("ix_software_prerequisite_softwares_for_condition_software_prereq");

                            b1.ToTable("software_prerequisite_SoftwaresForCondition");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_for_condition_software_prereq");
                        });

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresToPerformActionOn", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("software_identifier");

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer")
                                .HasColumnName("software_prerequisite_id");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer")
                                .HasColumnName("software_type");

                            b1.HasKey("Id")
                                .HasName("pk_software_prerequisite_softwares_to_perform_action_on");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasDatabaseName("ix_software_prerequisite_softwares_to_perform_action_on_softwa");

                            b1.ToTable("software_prerequisite_softwares_to_perform_action_on");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_to_perform_action_on_softwa");
                        });

                    b.Navigation("SoftwaresForCondition");

                    b.Navigation("SoftwaresToPerformActionOn");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.Navigation("SoftwarePrerequisites");

                    b.Navigation("SoftwareVersions");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.Navigation("MaintenanceTaskParameterValues");

                    b.Navigation("Parameters");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.Navigation("MaintenanceTaskParameterValues");
                });

            modelBuilder.Entity("Immybot.Domain.Models.Media", b =>
                {
                    b.Navigation("GlobalSoftware");

                    b.Navigation("IconForMaintenanceTasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.TargetAssignment", b =>
                {
                    b.Navigation("MaintenanceTaskParameterValues");
                });
#pragma warning restore 612, 618
        }
    }
}
