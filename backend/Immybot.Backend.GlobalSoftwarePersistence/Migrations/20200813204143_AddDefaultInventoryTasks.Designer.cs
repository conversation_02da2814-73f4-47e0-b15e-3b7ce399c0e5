// <auto-generated />
using System;
using Immybot.Backend.GlobalSoftwarePersistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations
{
    [DbContext(typeof(SoftwareDbContext))]
    [Migration("20200813204143_AddDefaultInventoryTasks")]
    partial class AddDefaultInventoryTasks
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                .HasAnnotation("ProductVersion", "3.1.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            modelBuilder.Entity("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnName("created_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Frequency")
                        .HasColumnName("frequency")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<int?>("SpecifiedNumMinutes")
                        .HasColumnName("specified_num_minutes")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnName("updated_date")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id")
                        .HasName("pk_inventory_tasks");

                    b.ToTable("inventory_tasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int?>("AutoUpdateScriptId")
                        .HasColumnName("auto_update_script_id")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnName("created_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("DefaultUninstallScriptId")
                        .HasColumnName("default_uninstall_script_id")
                        .HasColumnType("integer");

                    b.Property<int?>("DetectionScriptId")
                        .HasColumnName("detection_script_id")
                        .HasColumnType("integer");

                    b.Property<bool>("Hidden")
                        .HasColumnName("hidden")
                        .HasColumnType("boolean");

                    b.Property<int>("InstallOrder")
                        .HasColumnName("install_order")
                        .HasColumnType("integer");

                    b.Property<bool>("Licensed")
                        .HasColumnName("licensed")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaintenanceTaskId")
                        .HasColumnName("maintenance_task_id")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("Notes")
                        .HasColumnName("notes")
                        .HasColumnType("character varying(5000)")
                        .HasMaxLength(5000);

                    b.Property<bool>("RebootNeeded")
                        .HasColumnName("reboot_needed")
                        .HasColumnType("boolean");

                    b.Property<string>("SoftwareTableName")
                        .HasColumnName("software_table_name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnName("updated_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("UpgradeCode")
                        .HasColumnName("upgrade_code")
                        .HasColumnType("text");

                    b.Property<bool>("UseSoftwareTableDetection")
                        .HasColumnName("use_software_table_detection")
                        .HasColumnType("boolean");

                    b.HasKey("Id")
                        .HasName("pk_software");

                    b.HasIndex("AutoUpdateScriptId")
                        .HasName("ix_software_auto_update_script_id");

                    b.HasIndex("DefaultUninstallScriptId")
                        .HasName("ix_software_default_uninstall_script_id");

                    b.HasIndex("DetectionScriptId")
                        .HasName("ix_software_detection_script_id");

                    b.HasIndex("MaintenanceTaskId")
                        .HasName("ix_software_maintenance_task_id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasName("ix_software_name");

                    b.ToTable("software");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.Property<int>("SoftwareId")
                        .HasColumnName("software_id")
                        .HasColumnType("integer");

                    b.Property<string>("SemanticVersion")
                        .HasColumnName("semantic_version")
                        .HasColumnType("text");

                    b.Property<string>("BlobName")
                        .HasColumnName("blob_name")
                        .HasColumnType("character varying(1024)")
                        .HasMaxLength(1024);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnName("created_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("DeprecatedIdField")
                        .HasColumnName("deprecated_id_field")
                        .HasColumnType("integer");

                    b.Property<string>("DisplayName")
                        .HasColumnName("display_name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("DisplayVersion")
                        .HasColumnName("display_version")
                        .HasColumnType("character varying(64)")
                        .HasMaxLength(64);

                    b.Property<int?>("InstallScriptId")
                        .HasColumnName("install_script_id")
                        .HasColumnType("integer");

                    b.Property<string>("InstallerFile")
                        .HasColumnName("installer_file")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<int>("InstallerType")
                        .HasColumnName("installer_type")
                        .HasColumnType("integer");

                    b.Property<int?>("LastResult")
                        .HasColumnName("last_result")
                        .HasColumnType("integer");

                    b.Property<int>("LicenseType")
                        .HasColumnName("license_type")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasColumnName("notes")
                        .HasColumnType("character varying(5000)")
                        .HasMaxLength(5000);

                    b.Property<int>("NumActionFailures")
                        .HasColumnName("num_action_failures")
                        .HasColumnType("integer");

                    b.Property<int>("NumActionSuccesses")
                        .HasColumnName("num_action_successes")
                        .HasColumnType("integer");

                    b.Property<string>("PackageHash")
                        .HasColumnName("package_hash")
                        .HasColumnType("text");

                    b.Property<int>("PackageType")
                        .HasColumnName("package_type")
                        .HasColumnType("integer");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnName("post_install_script_id")
                        .HasColumnType("integer");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnName("post_uninstall_script_id")
                        .HasColumnType("integer");

                    b.Property<string>("ProductCode")
                        .HasColumnName("product_code")
                        .HasColumnType("text");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasColumnName("relative_cache_source_path")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("TestFailedError")
                        .HasColumnName("test_failed_error")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<bool>("TestRequired")
                        .HasColumnName("test_required")
                        .HasColumnType("boolean");

                    b.Property<int?>("TestScriptId")
                        .HasColumnName("test_script_id")
                        .HasColumnType("integer");

                    b.Property<string>("URL")
                        .HasColumnName("url")
                        .HasColumnType("character varying(2083)")
                        .HasMaxLength(2083);

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnName("uninstall_script_id")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnName("updated_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnName("upgrade_script_id")
                        .HasColumnType("integer");

                    b.Property<int>("UpgradeStrategy")
                        .HasColumnName("upgrade_strategy")
                        .HasColumnType("integer");

                    b.HasKey("SoftwareId", "SemanticVersion")
                        .HasName("pk_software_versions");

                    b.HasIndex("InstallScriptId")
                        .HasName("ix_software_versions_install_script_id");

                    b.HasIndex("PostInstallScriptId")
                        .HasName("ix_software_versions_post_install_script_id");

                    b.HasIndex("PostUninstallScriptId")
                        .HasName("ix_software_versions_post_uninstall_script_id");

                    b.HasIndex("TestScriptId")
                        .HasName("ix_software_versions_test_script_id");

                    b.HasIndex("UninstallScriptId")
                        .HasName("ix_software_versions_uninstall_script_id");

                    b.HasIndex("UpgradeScriptId")
                        .HasName("ix_software_versions_upgrade_script_id");

                    b.ToTable("software_versions");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnName("created_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("database_type")
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("GetEnabled")
                        .HasColumnName("get_enabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("GetScriptId")
                        .HasColumnName("get_script_id")
                        .HasColumnType("integer");

                    b.Property<int?>("GetScriptType")
                        .HasColumnName("get_script_type")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name")
                        .HasColumnType("character varying(255)")
                        .HasMaxLength(255);

                    b.Property<bool>("SetEnabled")
                        .HasColumnName("set_enabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("SetScriptId")
                        .HasColumnName("set_script_id")
                        .HasColumnType("integer");

                    b.Property<int?>("SetScriptType")
                        .HasColumnName("set_script_type")
                        .HasColumnType("integer");

                    b.Property<bool>("TestEnabled")
                        .HasColumnName("test_enabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("TestScriptId")
                        .HasColumnName("test_script_id")
                        .HasColumnType("integer");

                    b.Property<int?>("TestScriptType")
                        .HasColumnName("test_script_type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnName("updated_date")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_tasks");

                    b.HasIndex("GetScriptId")
                        .HasName("ix_maintenance_tasks_get_script_id");

                    b.HasIndex("SetScriptId")
                        .HasName("ix_maintenance_tasks_set_script_id");

                    b.HasIndex("TestScriptId")
                        .HasName("ix_maintenance_tasks_test_script_id");

                    b.ToTable("maintenance_tasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("DataType")
                        .HasColumnName("data_type")
                        .HasColumnType("integer");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnName("maintenance_task_id")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name")
                        .HasColumnType("character varying(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Notes")
                        .HasColumnName("notes")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<bool>("Required")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("required")
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("SelectableValues")
                        .HasColumnName("selectable_values")
                        .HasColumnType("text");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_task_parameters");

                    b.HasIndex("MaintenanceTaskId")
                        .HasName("ix_maintenance_task_parameters_maintenance_task_id");

                    b.ToTable("maintenance_task_parameters");
                });

            modelBuilder.Entity("Immybot.Domain.Models.Script", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnName("action")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnName("created_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("Hidden")
                        .HasColumnName("hidden")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<bool>("ReadOnly")
                        .HasColumnName("read_only")
                        .HasColumnType("boolean");

                    b.Property<int>("ScriptActionType")
                        .HasColumnName("script_action_type")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptCategory")
                        .HasColumnName("script_category")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptExecutionContext")
                        .HasColumnName("script_execution_context")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptType")
                        .HasColumnName("script_type")
                        .HasColumnType("integer");

                    b.Property<int?>("Timeout")
                        .HasColumnName("timeout")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnName("updated_date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("UserActionTrigger")
                        .HasColumnName("user_action_trigger")
                        .HasColumnType("integer");

                    b.HasKey("Id")
                        .HasName("pk_scripts");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasName("ix_scripts_name");

                    b.ToTable("scripts");
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id")
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("ActionToPerform")
                        .HasColumnName("action_to_perform")
                        .HasColumnType("integer");

                    b.Property<int>("Condition")
                        .HasColumnName("condition")
                        .HasColumnType("integer");

                    b.Property<int>("GlobalSoftwareId")
                        .HasColumnName("global_software_id")
                        .HasColumnType("integer");

                    b.Property<int>("SubjectQualifier")
                        .HasColumnName("subject_qualifier")
                        .HasColumnType("integer");

                    b.HasKey("Id")
                        .HasName("pk_software_prerequisite");

                    b.HasIndex("GlobalSoftwareId")
                        .HasName("ix_software_prerequisite_global_software_id");

                    b.ToTable("software_prerequisite");
                });

            modelBuilder.Entity("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTask", b =>
                {
                    b.OwnsMany("Immybot.Backend.GlobalSoftwarePersistence.Interface.GlobalInventoryTaskScript", "Scripts", b1 =>
                        {
                            b1.Property<int>("InventoryTaskId")
                                .HasColumnName("inventory_task_id")
                                .HasColumnType("integer");

                            b1.Property<string>("InventoryKey")
                                .HasColumnName("inventory_key")
                                .HasColumnType("text");

                            b1.Property<int>("ScriptId")
                                .HasColumnName("script_id")
                                .HasColumnType("integer");

                            b1.HasKey("InventoryTaskId", "InventoryKey")
                                .HasName("pk_inventory_task_scripts");

                            b1.HasIndex("ScriptId")
                                .HasName("ix_inventory_task_scripts_script_id");

                            b1.ToTable("inventory_task_scripts");

                            b1.WithOwner()
                                .HasForeignKey("InventoryTaskId")
                                .HasConstraintName("fk_global_inventory_task_script_inventory_tasks_global_invento");

                            b1.HasOne("Immybot.Domain.Models.Script", null)
                                .WithMany()
                                .HasForeignKey("ScriptId")
                                .HasConstraintName("fk_inventory_task_scripts_scripts_script_id")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();
                        });
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "AutoUpdateScript")
                        .WithMany()
                        .HasForeignKey("AutoUpdateScriptId")
                        .HasConstraintName("fk_software_scripts_auto_update_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "DefaultUninstallScript")
                        .WithMany()
                        .HasForeignKey("DefaultUninstallScriptId")
                        .HasConstraintName("fk_software_scripts_default_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "DetectionScript")
                        .WithMany()
                        .HasForeignKey("DetectionScriptId")
                        .HasConstraintName("fk_software_scripts_detection_script_id");

                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany()
                        .HasForeignKey("MaintenanceTaskId")
                        .HasConstraintName("fk_software_maintenance_tasks_maintenance_task_id");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "InstallScript")
                        .WithMany()
                        .HasForeignKey("InstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_install_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostInstallScript")
                        .WithMany()
                        .HasForeignKey("PostInstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_post_install_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "PostUninstallScript")
                        .WithMany()
                        .HasForeignKey("PostUninstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_post_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", "Software")
                        .WithMany("SoftwareVersions")
                        .HasForeignKey("SoftwareId")
                        .HasConstraintName("fk_software_versions_software_software_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId")
                        .HasConstraintName("fk_software_versions_scripts_test_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UninstallScript")
                        .WithMany()
                        .HasForeignKey("UninstallScriptId")
                        .HasConstraintName("fk_software_versions_scripts_uninstall_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "UpgradeScript")
                        .WithMany()
                        .HasForeignKey("UpgradeScriptId")
                        .HasConstraintName("fk_software_versions_scripts_upgrade_script_id");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "GetScript")
                        .WithMany()
                        .HasForeignKey("GetScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_get_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "SetScript")
                        .WithMany()
                        .HasForeignKey("SetScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_set_script_id");

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId")
                        .HasConstraintName("fk_maintenance_tasks_scripts_test_script_id");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("Parameters")
                        .HasForeignKey("MaintenanceTaskId")
                        .HasConstraintName("fk_maintenance_task_parameters_maintenance_tasks_maintenance_t")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", null)
                        .WithMany("SoftwarePrerequisites")
                        .HasForeignKey("GlobalSoftwareId")
                        .HasConstraintName("fk_software_prerequisite_software_global_software_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresForCondition", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnName("id")
                                .HasColumnType("integer")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasColumnName("software_identifier")
                                .HasColumnType("character varying(150)")
                                .HasMaxLength(150);

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnName("software_prerequisite_id")
                                .HasColumnType("integer");

                            b1.Property<int>("SoftwareType")
                                .HasColumnName("software_type")
                                .HasColumnType("integer");

                            b1.HasKey("Id")
                                .HasName("pk_software_prerequisite_softwares_for_condition");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasName("ix_software_prerequisite_softwares_for_condition_software_prereq");

                            b1.ToTable("software_prerequisite_SoftwaresForCondition");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_for_condition_software_prereq");
                        });

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresToPerformActionOn", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnName("id")
                                .HasColumnType("integer")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasColumnName("software_identifier")
                                .HasColumnType("character varying(150)")
                                .HasMaxLength(150);

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnName("software_prerequisite_id")
                                .HasColumnType("integer");

                            b1.Property<int>("SoftwareType")
                                .HasColumnName("software_type")
                                .HasColumnType("integer");

                            b1.HasKey("Id")
                                .HasName("pk_software_prerequisite_softwares_to_perform_action_on");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasName("ix_software_prerequisite_softwares_to_perform_action_on_softwa");

                            b1.ToTable("software_prerequisite_softwares_to_perform_action_on");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_to_perform_action_on_softwa");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
