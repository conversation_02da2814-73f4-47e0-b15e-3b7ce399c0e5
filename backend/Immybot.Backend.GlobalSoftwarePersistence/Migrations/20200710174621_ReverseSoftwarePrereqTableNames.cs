using Microsoft.EntityFrameworkCore.Migrations;

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations;

public partial class ReverseSoftwarePrereqTableNames : Migration
{
  protected override void Up(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.DropForeignKey(
        name: "fk_software_prerequisite_softwares_for_condition_software_prer",
        table: "software_prerequisite_SoftwaresForCondition");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_softwares_to_perform_action_on",
        newName: "software_prerequisite_softwares_for_condition");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_SoftwaresForCondition",
        newName: "software_prerequisite_softwares_to_perform_action_on");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_softwares_for_condition",
        newName: "software_prerequisite_SoftwaresForCondition");

    migrationBuilder.AddForeignKey(
              name: "fk_software_prerequisite_softwares_for_condition_software_prereq",
              table: "software_prerequisite_SoftwaresForCondition",
              column: "software_prerequisite_id",
              principalTable: "software_prerequisite",
              principalColumn: "id",
              onDelete: ReferentialAction.Cascade);
  }

  protected override void Down(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.DropForeignKey(
        name: "fk_software_prerequisite_softwares_for_condition_software_prereq",
        table: "software_prerequisite_SoftwaresForCondition");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_softwares_to_perform_action_on",
        newName: "software_prerequisite_softwares_for_condition");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_SoftwaresForCondition",
        newName: "software_prerequisite_softwares_to_perform_action_on");

    migrationBuilder.RenameTable(
        name: "software_prerequisite_softwares_for_condition",
        newName: "software_prerequisite_SoftwaresForCondition");

    migrationBuilder.AddForeignKey(
              name: "fk_software_prerequisite_softwares_for_condition_software_prer",
              table: "software_prerequisite_SoftwaresForCondition",
              column: "software_prerequisite_id",
              principalTable: "software_prerequisite",
              principalColumn: "id",
              onDelete: ReferentialAction.Cascade);
  }
}
