using Microsoft.EntityFrameworkCore.Migrations;

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations;

public partial class AddMediaCategoryAndSoftwareIcon : Migration
{
  protected override void Up(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.AddColumn<int>(
        name: "software_icon_media_id",
        table: "software",
        nullable: true);

    migrationBuilder.AddColumn<int>(
        name: "category",
        table: "media",
        nullable: false,
        defaultValue: 0);

    migrationBuilder.CreateIndex(
        name: "ix_software_software_icon_media_id",
        table: "software",
        column: "software_icon_media_id");

    migrationBuilder.AddForeignKey(
        name: "fk_software_media_media_id",
        table: "software",
        column: "software_icon_media_id",
        principalTable: "media",
        principalColumn: "id",
        onDelete: ReferentialAction.SetNull);
  }

  protected override void Down(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.DropForeignKey(
        name: "fk_software_media_media_id",
        table: "software");

    migrationBuilder.DropIndex(
        name: "ix_software_software_icon_media_id",
        table: "software");

    migrationBuilder.DropColumn(
        name: "software_icon_media_id",
        table: "software");

    migrationBuilder.DropColumn(
        name: "category",
        table: "media");
  }
}
