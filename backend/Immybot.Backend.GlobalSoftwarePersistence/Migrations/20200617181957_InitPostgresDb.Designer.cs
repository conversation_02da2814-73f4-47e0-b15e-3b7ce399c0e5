// <auto-generated />
using System;
using Immybot.Backend.GlobalSoftwarePersistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Immybot.Backend.GlobalSoftwarePersistence.Migrations
{
    [DbContext(typeof(SoftwareDbContext))]
    [Migration("20200617181957_InitPostgresDb")]
    partial class InitPostgresDb
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                .HasAnnotation("ProductVersion", "3.1.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int?>("AutoUpdateScriptId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("DefaultUninstallScriptId")
                        .HasColumnType("integer");

                    b.Property<int?>("DetectionScriptId")
                        .HasColumnType("integer");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean");

                    b.Property<int>("InstallOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("Licensed")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaintenanceTaskId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("Notes")
                        .HasColumnType("character varying(5000)")
                        .HasMaxLength(5000);

                    b.Property<bool>("RebootNeeded")
                        .HasColumnType("boolean");

                    b.Property<string>("SoftwareTableName")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("UseSoftwareTableDetection")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("AutoUpdateScriptId");

                    b.HasIndex("DefaultUninstallScriptId");

                    b.HasIndex("DetectionScriptId");

                    b.HasIndex("MaintenanceTaskId");

                    b.ToTable("Software");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.Property<int>("SoftwareId")
                        .HasColumnType("integer");

                    b.Property<string>("SemanticVersion")
                        .HasColumnType("text");

                    b.Property<string>("BlobName")
                        .HasColumnType("character varying(1024)")
                        .HasMaxLength(1024);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("DeprecatedIdField")
                        .HasColumnType("integer");

                    b.Property<string>("DisplayName")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("DisplayVersion")
                        .HasColumnType("character varying(64)")
                        .HasMaxLength(64);

                    b.Property<int?>("InstallScriptId")
                        .HasColumnType("integer");

                    b.Property<string>("InstallerFile")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<int>("InstallerType")
                        .HasColumnType("integer");

                    b.Property<int?>("LastResult")
                        .HasColumnType("integer");

                    b.Property<int>("LicenseType")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasColumnType("character varying(5000)")
                        .HasMaxLength(5000);

                    b.Property<int>("NumActionFailures")
                        .HasColumnType("integer");

                    b.Property<int>("NumActionSuccesses")
                        .HasColumnType("integer");

                    b.Property<string>("PackageHash")
                        .HasColumnType("text");

                    b.Property<int>("PackageType")
                        .HasColumnType("integer");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnType("integer");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnType("integer");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<string>("TestFailedError")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<bool>("TestRequired")
                        .HasColumnType("boolean");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer");

                    b.Property<string>("URL")
                        .HasColumnType("character varying(2083)")
                        .HasMaxLength(2083);

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnType("integer");

                    b.Property<int>("UpgradeStrategy")
                        .HasColumnType("integer");

                    b.HasKey("SoftwareId", "SemanticVersion");

                    b.HasIndex("InstallScriptId");

                    b.HasIndex("PostInstallScriptId");

                    b.HasIndex("PostUninstallScriptId");

                    b.HasIndex("TestScriptId");

                    b.HasIndex("UninstallScriptId");

                    b.HasIndex("UpgradeScriptId");

                    b.ToTable("SoftwareVersions");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("GetEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("GetScriptId")
                        .HasColumnType("integer");

                    b.Property<int?>("GetScriptType")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("character varying(255)")
                        .HasMaxLength(255);

                    b.Property<bool>("SetEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("SetScriptId")
                        .HasColumnType("integer");

                    b.Property<int?>("SetScriptType")
                        .HasColumnType("integer");

                    b.Property<bool>("TestEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer");

                    b.Property<int?>("TestScriptType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("GetScriptId");

                    b.HasIndex("SetScriptId");

                    b.HasIndex("TestScriptId");

                    b.ToTable("MaintenanceTasks");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("DataType")
                        .HasColumnType("integer");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("character varying(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Notes")
                        .HasColumnType("character varying(1000)")
                        .HasMaxLength(1000);

                    b.Property<bool>("Required")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("SelectableValues")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MaintenanceTaskId");

                    b.ToTable("MaintenanceTaskParameters");
                });

            modelBuilder.Entity("Immybot.Domain.Models.Script", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean");

                    b.Property<bool>("ImmediateUser")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("character varying(150)")
                        .HasMaxLength(150);

                    b.Property<bool>("ReadOnly")
                        .HasColumnType("boolean");

                    b.Property<int>("ScriptActionType")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptCategory")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptExecutionContext")
                        .HasColumnType("integer");

                    b.Property<int>("ScriptType")
                        .HasColumnType("integer");

                    b.Property<int?>("Timeout")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("UserActionTrigger")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Scripts");
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("ActionToPerform")
                        .HasColumnType("integer");

                    b.Property<int>("Condition")
                        .HasColumnType("integer");

                    b.Property<int>("GlobalSoftwareId")
                        .HasColumnType("integer");

                    b.Property<int>("SubjectQualifier")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GlobalSoftwareId");

                    b.ToTable("SoftwarePrerequisite");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftware", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "AutoUpdateScript")
                        .WithMany()
                        .HasForeignKey("AutoUpdateScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "DefaultUninstallScript")
                        .WithMany()
                        .HasForeignKey("DefaultUninstallScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "DetectionScript")
                        .WithMany()
                        .HasForeignKey("DetectionScriptId");

                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany()
                        .HasForeignKey("MaintenanceTaskId");
                });

            modelBuilder.Entity("Immybot.Domain.Models.GlobalSoftwareVersion", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "InstallScript")
                        .WithMany()
                        .HasForeignKey("InstallScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "PostInstallScript")
                        .WithMany()
                        .HasForeignKey("PostInstallScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "PostUninstallScript")
                        .WithMany()
                        .HasForeignKey("PostUninstallScriptId");

                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", "Software")
                        .WithMany("SoftwareVersions")
                        .HasForeignKey("SoftwareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "UninstallScript")
                        .WithMany()
                        .HasForeignKey("UninstallScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "UpgradeScript")
                        .WithMany()
                        .HasForeignKey("UpgradeScriptId");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTask", b =>
                {
                    b.HasOne("Immybot.Domain.Models.Script", "GetScript")
                        .WithMany()
                        .HasForeignKey("GetScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "SetScript")
                        .WithMany()
                        .HasForeignKey("SetScriptId");

                    b.HasOne("Immybot.Domain.Models.Script", "TestScript")
                        .WithMany()
                        .HasForeignKey("TestScriptId");
                });

            modelBuilder.Entity("Immybot.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.HasOne("Immybot.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("Parameters")
                        .HasForeignKey("MaintenanceTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Immybot.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.HasOne("Immybot.Domain.Models.GlobalSoftware", null)
                        .WithMany("SoftwarePrerequisites")
                        .HasForeignKey("GlobalSoftwareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresForCondition", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasColumnType("character varying(150)")
                                .HasMaxLength(150);

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer");

                            b1.HasKey("Id");

                            b1.HasIndex("SoftwarePrerequisiteId");

                            b1.ToTable("SoftwarePrerequisite_SoftwaresForCondition");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId");
                        });

                    b.OwnsMany("Immybot.Domain.Models.SoftwareSpecifier", "SoftwaresToPerformActionOn", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                            b1.Property<string>("SoftwareIdentifier")
                                .HasColumnType("character varying(150)")
                                .HasMaxLength(150);

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer");

                            b1.HasKey("Id");

                            b1.HasIndex("SoftwarePrerequisiteId");

                            b1.ToTable("SoftwarePrerequisite_SoftwaresToPerformActionOn");

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
