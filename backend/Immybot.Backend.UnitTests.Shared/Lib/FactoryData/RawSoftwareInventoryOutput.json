{"Output": [{"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "82E6AC09-0FEF-4390-AD9F-0DD3F5561EFC", "DisplayName": "ShareX", "QuietUninstallString": "\"C:\\Program Files\\ShareX\\unins000.exe\" /SILENT", "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "15.0.0", "DisplayIcon": "C:\\Program Files\\ShareX\\ShareX.exe", "UninstallString": "\"C:\\Program Files\\ShareX\\unins000.exe\"", "GlobalSoftwareId": 702, "GlobalSoftwareName": "ShareX"}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "DBeaver 22.3.4", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "22.3.4", "DisplayIcon": "C:\\Program Files\\DBeaver\\dbeaver.exe,0", "UninstallString": "\"C:\\Program Files\\DBeaver\\Uninstall.exe\" /allusers", "GlobalSoftwareId": 663, "GlobalSoftwareName": "DBeaver"}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Dell Display Manager 2.0", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "*********", "DisplayIcon": "C:\\Program Files\\Dell\\Dell Display Manager 2.0\\Icon\\DellLogo.ico", "UninstallString": "C:\\Program Files\\Dell\\Dell Display Manager 2.0\\uninst.exe", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Dell Peripheral Manager", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "1.6.1", "DisplayIcon": "C:\\Program Files\\Dell\\Dell Peripheral Manager\\DPM.exe", "UninstallString": "C:\\Program Files\\Dell\\Dell Peripheral Manager\\Uninstall.exe", "GlobalSoftwareId": 1193, "GlobalSoftwareName": "Dell Peripheral Manager"}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "<PERSON><PERSON> Des<PERSON>", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "4.16.3", "DisplayIcon": "C:\\Program Files\\Docker\\Docker\\Docker Desktop Installer.exe", "UninstallString": "\"C:\\Program Files\\Docker\\Docker\\Docker Desktop Installer.exe\" \"uninstall\"", "GlobalSoftwareId": 692, "GlobalSoftwareName": "<PERSON><PERSON> Des<PERSON>"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Git", "QuietUninstallString": "\"C:\\Program Files\\Git\\unins000.exe\" /SILENT", "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "2.39.1", "DisplayIcon": "C:\\Program Files\\Git\\mingw64\\share\\git\\git-for-windows.ico", "UninstallString": "\"C:\\Program Files\\Git\\unins000.exe\"", "GlobalSoftwareId": 607, "GlobalSoftwareName": "Git (64-bit)"}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "LockHunter 3.4, 32/64 bit", "QuietUninstallString": "\"C:\\Program Files\\LockHunter\\unins000.exe\" /SILENT", "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "3.4.3.146", "DisplayIcon": "C:\\Program Files\\LockHunter\\LockHunter.exe", "UninstallString": "\"C:\\Program Files\\LockHunter\\unins000.exe\"", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft Azure Compute Emulator - v2.9.7", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "2.9.8999.43", "DisplayIcon": "C:\\PROGRA~1\\MIA713~1\\Azure\\Emulator\\CSMONI~1.EXE,0", "UninstallString": "msiexec /x{04ca054c-2f40-44b0-8610-8d51ec9444fe}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft 365 - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=O365HomePremRetail.16_en-us_x-none culture=en-us version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft 365 - es-es", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=O365HomePremRetail.16_es-es_x-none culture=es-es version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft 365 - fr-fr", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=O365HomePremRetail.16_fr-fr_x-none culture=fr-fr version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft 365 Apps for enterprise - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=O365ProPlusRetail.16_en-us_x-none culture=en-us version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft OneDrive", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "23.020.0125.0003", "DisplayIcon": "C:\\Program Files\\Microsoft OneDrive\\23.020.0125.0003\\OneDriveSetup.exe,-101", "UninstallString": "C:\\Program Files\\Microsoft OneDrive\\23.020.0125.0003\\OneDriveSetup.exe  /uninstall  /allusers ", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft OneNote - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=OneNoteFreeRetail.16_en-us_x-none culture=en-us version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft OneNote - es-es", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=OneNoteFreeRetail.16_es-es_x-none culture=es-es version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft OneNote - fr-fr", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": "C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=OneNoteFreeRetail.16_fr-fr_x-none culture=fr-fr version.16=16.0", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "03C6888D-BA2D-4336-B853-040ACF084DF4", "DisplayName": "Microsoft.NET.Runtime.MonoAOTCompiler.Task (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ffdcb2fa-2e15-3533-89e4-a5aff5868a8b", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{03C6888D-BA2D-4336-B853-040ACF084DF4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0400FAA6-461D-4877-81E8-5E931F7AEB30", "DisplayName": "Microsoft.Maui.Controls.Ref.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0400FAA6-461D-4877-81E8-5E931F7AEB30}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "04CA054C-2F40-44B0-8610-8D51EC9444FE", "DisplayName": "Microsoft Azure Compute Emulator - v2.9.7", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "73124509-cad6-4594-a6da-684376561173", "DisplayVersion": "2.9.8999.43", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{04CA054C-2F40-44B0-8610-8D51EC9444FE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "08274920-8908-45c2-9258-8ad67ff77b09", "DisplayName": "IIS Express Application Compatibility Database for x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": null, "DisplayIcon": null, "UninstallString": "C:\\Windows\\system32\\sdbinst.exe -u \"C:\\Windows\\AppPatch\\CustomSDB\\{08274920-8908-45c2-9258-8ad67ff77b09}.sdb\"", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "08CC2F1A-E69E-4F47-B93F-641E85C380B7", "DisplayName": "Microsoft.Maui.Controls.Ref.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f5974144-ef40-321f-be11-3af544c9ee3c", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{08CC2F1A-E69E-4F47-B93F-641E85C380B7}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0948366B-000A-4618-964C-43505AF8E889", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0948366B-000A-4618-964C-43505AF8E889}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0B12F7E3-EDAA-AF92-20BB-88540FEF54BA", "DisplayName": "Windows SDK DirectX x64 Remote", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a9b3076d-7d81-2e8e-7cb0-38582a5a6d03", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0B12F7E3-EDAA-AF92-20BB-88540FEF54BA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0B9C1225-F952-4503-B077-80C627DE2890", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "51d18764-ee41-39a7-9d05-fdc7c4db0474", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0B9C1225-F952-4503-B077-80C627DE2890}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0C082AC1-36AD-3E86-B72D-16EF3A4D907F", "DisplayName": "Microsoft ASP.NET Core 7.0.2 Targeting Pack (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3a7f57cd-30d6-3d37-a5e9-f461547dc91b", "DisplayVersion": "7.0.2.22606", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0C082AC1-36AD-3E86-B72D-16EF3A4D907F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "0C85743B-48E7-4948-96D6-C3BB90246418", "DisplayName": "Microsoft Web Deploy 4.0", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f63293ce-9eac-4f8d-a261-2a280dfeade8", "DisplayVersion": "10.0.5923", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0C85743B-48E7-4948-96D6-C3BB90246418}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "0D2DCFF8-7746-445B-B643-9E85ABE01173", "DisplayName": "Microsoft Build of OpenJDK with Hotspot 11.0.12+7 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "427174f5-1a8c-4755-ba9b-98a8ddf73933", "DisplayVersion": "*********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0D2DCFF8-7746-445B-B643-9E85ABE01173}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0D8459AB-4636-4CD5-A41B-569D7CE159B8", "DisplayName": "Python 3.11.0 pip <PERSON> (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a39b5a78-8e48-5aac-aa09-bfe6e50dd900", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0D8459AB-4636-4CD5-A41B-569D7CE159B8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0DE775A3-1C63-4210-9CDC-D23F5330D715", "DisplayName": "vs_devenx64vmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a6712af6-d30b-4b3b-b3af-d5813017e3b2", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0DE775A3-1C63-4210-9CDC-D23F5330D715}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0EF45019-7206-48FE-9493-EB220A563856", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3836ac3c-f2e9-345c-9c9d-248a36a5e116", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0EF45019-7206-48FE-9493-EB220A563856}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0FDF83A0-924C-4B8C-8C0A-AF48D737C6AB", "DisplayName": ".NET Android Templates (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "28b379c6-54ce-34b0-b135-8ef666b49363", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0FDF83A0-924C-4B8C-8C0A-AF48D737C6AB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-14T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "105FD2F1-E01D-4B26-BF9E-E3236E5614FF", "DisplayName": "paint.net", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "04a40f40-a207-4b48-aed7-6aa532e43275", "DisplayVersion": "4.3.12", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{105FD2F1-E01D-4B26-BF9E-E3236E5614FF}", "GlobalSoftwareId": 743, "GlobalSoftwareName": "paint.net"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1062F426-B047-424D-94C0-B30C1C57BE67", "DisplayName": "Microsoft.Maui.Controls.Ref.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2154a11f-b303-3ec9-b9e2-0d638a7d3501", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1062F426-B047-424D-94C0-B30C1C57BE67}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1196B792-54BB-48EA-B204-1A0805AEE237", "DisplayName": "Microsoft.Maui.Core.Runtime.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b12844fe-aa8d-3418-8b78-11afffee80e3", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1196B792-54BB-48EA-B204-1A0805AEE237}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "1309CCD0-A923-4203-8A92-377F37EE2C29", "DisplayName": "Dell Command | Update for Windows Universal", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "69488f91-be5b-40e1-9202-cd793ca948ed", "DisplayVersion": "4.7.1", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1309CCD0-A923-4203-8A92-377F37EE2C29}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1344E072-D68B-48FF-BD2A-C1CCCC511A50", "DisplayName": "Dell Optimizer", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "71773ce7-20ca-4e8c-acc1-42345d24da03", "DisplayVersion": "3.2.212.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1344E072-D68B-48FF-BD2A-C1CCCC511A50}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "15EBF233-0C17-4912-A402-AE457A4101E6", "DisplayName": "Microsoft.Maui.Resizetizer.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c7c50036-261f-3987-ad24-8c0bb8221475", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{15EBF233-0C17-4912-A402-AE457A4101E6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "167FE453-9020-43D2-B1A3-EF5717FA8CAE", "DisplayName": "Microsoft.Android.Runtime.33.android-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "151559ed-aabc-3e06-8dde-0d0afc002a5c", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{167FE453-9020-43D2-B1A3-EF5717FA8CAE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1AB2F81F-A360-4BE1-B68F-B50F0609A1AE", "DisplayName": "vs_minshellx64msi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "4494eb02-8270-4d2f-9d71-20d06509e2e2", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1AB2F81F-A360-4BE1-B68F-B50F0609A1AE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1B436841-4982-49DF-A850-5F75F0B1A3FD", "DisplayName": "Microsoft.Maui.Essentials.Ref.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "6ae1bb94-e64d-36b7-a913-3e9135bdb4b7", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1B436841-4982-49DF-A850-5F75F0B1A3FD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1BBAF4F4-B330-44E6-938A-E19D5F471EAB", "DisplayName": "Microsoft.Maui.Core.Runtime.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "185406c4-6077-348c-823a-df01f68bb982", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1BBAF4F4-B330-44E6-938A-E19D5F471EAB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1E1B7152-B9F7-47C6-802C-9CB06B5AF1FC", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bb8ecf91-0681-3cad-be82-d7de6e540075", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1E1B7152-B9F7-47C6-802C-9CB06B5AF1FC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1E665F2B-6760-42E4-8E97-02B10B97FBA2", "DisplayName": "Microsoft.NET.Sdk.macOS.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b4e485f1-091a-3ebd-8c75-2fbf9da2a758", "DisplayVersion": "13.0.17", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1E665F2B-6760-42E4-8E97-02B10B97FBA2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1ED03561-12AC-4A6A-AA85-583281BF0121", "DisplayName": "Python 3.11.0 Core Interpreter (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9568a462-2f48-5d1c-945f-bbc5d9010918", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1ED03561-12AC-4A6A-AA85-583281BF0121}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1F91524C-6712-4500-8580-DEE5D826BEC0", "DisplayName": "Microsoft.Maui.Controls.Runtime.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1F91524C-6712-4500-8580-DEE5D826BEC0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "20C1086D-C843-36B1-B678-990089D1BD44", "DisplayName": "Microsoft Visual C++ 2013 x64 Additional Runtime - 12.0.40649", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "12.0.40649", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{20C1086D-C843-36B1-B678-990089D1BD44}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "210A496F-E6CA-39C1-8FA4-1004059ED4E3", "DisplayName": "Microsoft ASP.NET Core 7.0.2 Shared Framework (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "877d1548-02ac-34e5-b145-57e674f77f71", "DisplayVersion": "7.0.2.22606", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{210A496F-E6CA-39C1-8FA4-1004059ED4E3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "22C5E7C3-E838-4C55-B7CF-43728A626AFF", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "282f3234-4998-33e5-8c16-c1a7268044aa", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{22C5E7C3-E838-4C55-B7CF-43728A626AFF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "23170F69-40C1-2702-2201-000001000000", "DisplayName": "7-Zip 22.01 (x64 edition)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{23170F69-40C1-2702-2201-000001000000}", "GlobalSoftwareId": 312, "GlobalSoftwareName": "7-<PERSON><PERSON>"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2505676D-0245-4775-B7BE-F4C1DDC902D8", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0d140f81-53e9-538a-efa4-fb0d0bee6fb6", "DisplayVersion": "56.11.53361", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2505676D-0245-4775-B7BE-F4C1DDC902D8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "270D2A13-4AA7-44FD-B9B3-EA09DD8F32E7", "DisplayName": "Microsoft .NET Core AppHost Pack - 3.1.32 (x64_arm)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "8cdd4225-5121-49c3-319a-0f361e027a0d", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{270D2A13-4AA7-44FD-B9B3-EA09DD8F32E7}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-13T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "285DD2D7-E94D-4CA5-B356-517E284BC064", "DisplayName": "FortiClient VPN", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "5b965e1a-7161-439f-ad32-235831cf147a", "DisplayVersion": "7.2.0.0690", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{285DD2D7-E94D-4CA5-B356-517E284BC064}", "GlobalSoftwareId": 248, "GlobalSoftwareName": "FortiClient VPN"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "28E7B326-6E09-4960-88C1-AF305A11665C", "DisplayName": "Microsoft Visual C++ 2022 X64 Debug Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "eb364181-62f9-3134-8208-7921a115d5ef", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{28E7B326-6E09-4960-88C1-AF305A11665C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "2910A105-C9DA-45B0-97A5-F149D4773C23", "DisplayName": "Everything 1.4.1.1022 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "429071b2-0162-45aa-84ad-28f047e15638", "DisplayVersion": "1.4.1.1022", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2910A105-C9DA-45B0-97A5-F149D4773C23}", "GlobalSoftwareId": 323, "GlobalSoftwareName": "Everything"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2A2F0BF7-29F2-4574-835B-D7C3FA48529B", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "39d4a977-d587-3bfd-ab0f-fad656c5e445", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2A2F0BF7-29F2-4574-835B-D7C3FA48529B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2CBA883F-51A6-3D7D-DBB9-0527D39433CB", "DisplayName": "Application Verifier x64 External Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d74ba1b1-e322-c15f-fc20-390c46696cd2", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{2CBA883F-51A6-3D7D-DBB9-0527D39433CB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2E4D063F-23CE-42A8-B301-409B710F0205", "DisplayName": "Microsoft.Maui.Essentials.Ref.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "35bfe792-9005-381a-a006-953531365d2d", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2E4D063F-23CE-42A8-B301-409B710F0205}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2E69E59E-17DF-3977-A405-49096F8B8432", "DisplayName": "Microsoft ASP.NET Core 3.1.32 Shared Framework (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "cd577096-baac-33c0-9b43-f246697a3a7c", "DisplayVersion": "3.1.32.22566", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2E69E59E-17DF-3977-A405-49096F8B8432}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "300348C3-038D-4434-B171-8E72354868EC", "DisplayName": "Microsoft.Maui.Core.Ref.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0dc5bd57-b7bd-32b0-8511-2becd281cb7d", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{300348C3-038D-4434-B171-8E72354868EC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "31EDE1E7-C855-4633-9D73-56F566136567", "DisplayName": "Microsoft .NET Core Targeting Pack - 3.1.0 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "831e2ad0-5053-50e8-091a-57bb898e0695", "DisplayVersion": "24.64.28315", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{31EDE1E7-C855-4633-9D73-56F566136567}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "322129E0-5812-4760-8A3E-056B2FFCD464", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.ios-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "85e11592-9634-3b18-a101-adbe0bd3927b", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{322129E0-5812-4760-8A3E-056B2FFCD464}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "323AD147-6FC4-40CB-A810-2AADF26D868A", "DisplayName": "PowerShell 7-x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "31ab5147-9a97-4452-8443-d9709f0516e1", "DisplayVersion": "7.3.2.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{323AD147-6FC4-40CB-A810-2AADF26D868A}", "GlobalSoftwareId": 196, "GlobalSoftwareName": "PowerShell 7-x64"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "34761DB2-89B9-4981-8122-5B1FAE3B7069", "DisplayName": "Microsoft .NET Targeting Pack - 5.0.0 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b8814677-57a7-58ea-fb07-d862bb70c925", "DisplayVersion": "40.0.29419", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{34761DB2-89B9-4981-8122-5B1FAE3B7069}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "34EB9ADD-F971-4DF6-990C-ADB62EDE6031", "DisplayName": "Microsoft.iOS.Ref (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9be72b89-2769-3682-b9fa-861e4f70b916", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{34EB9ADD-F971-4DF6-990C-ADB62EDE6031}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "36E492B8-CB83-4DA5-A5D2-D99A8E8228A1", "DisplayName": "Microsoft SQL Server 2019 LocalDB ", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f0176a51-908a-4240-8853-e229d0ae3f39", "DisplayVersion": "15.0.4153.1", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{36E492B8-CB83-4DA5-A5D2-D99A8E8228A1}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "38652E9D-E8D6-4E75-914C-472C1398D4E9", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.maccatalyst-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c72dc34e-955f-3c3a-b46c-237967a97e9b", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{38652E9D-E8D6-4E75-914C-472C1398D4E9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "38CA215A-103C-4C37-A86E-57E49C2220AD", "DisplayName": "Microsoft .NET AppHost Pack - 5.0.17 (x64_arm)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "8794c4cc-5158-649a-ee1b-4c48d2cbde8c", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{38CA215A-103C-4C37-A86E-57E49C2220AD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3910BEDF-2C11-4D8C-949D-72BB66F13F18", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "36b55217-d3a3-3cad-a37d-e11d9fa813f2", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3910BEDF-2C11-4D8C-949D-72BB66F13F18}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "392135F8-3109-4BDC-BFBF-23205DE86275", "DisplayName": "Microsoft.Maui.Core.Ref.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "db371e3c-2a91-379b-8825-6a10dcfa3f5a", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{392135F8-3109-4BDC-BFBF-23205DE86275}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "39A8BD94-0B6E-44E6-8058-95EAEF18F960", "DisplayName": "ExpressConnect Drivers & Services", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3b341bec-02d3-4d3a-9caa-8625d7aa2996", "DisplayVersion": "3.1222.1025", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{39A8BD94-0B6E-44E6-8058-95EAEF18F960}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "39D3E1EC-0C87-4456-B59B-AA2E9055D000", "DisplayName": "Microsoft.Maui.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{39D3E1EC-0C87-4456-B59B-AA2E9055D000}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "3BA7CAA9-97BA-4528-B7E1-B640910BB149", "DisplayName": "Microsoft Azure PowerShell - April 2018", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ebc1ad5e-1bbe-45b5-9e8d-acfc8951c252", "DisplayVersion": "5.7.0.18831", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3BA7CAA9-97BA-4528-B7E1-B640910BB149}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3C31CBA1-A0D9-4B95-A807-AD2313D12F47", "DisplayName": "Microsoft Windows Desktop Runtime - 5.0.17 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "eddd40d0-59a7-691c-a69e-f444ff1d1cbf", "DisplayVersion": "40.68.31219", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3C31CBA1-A0D9-4B95-A807-AD2313D12F47}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "3CBDE512-7510-4F90-B1C0-7C4EB9DD7C26", "DisplayName": "Windows Subsystem for Linux WSLg Preview", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "04201f79-6dc8-4b6b-b690-81e7e4a8fb47", "DisplayVersion": "1.0.27", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3CBDE512-7510-4F90-B1C0-7C4EB9DD7C26}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3DF1CBDF-172E-48BA-8093-A05B80C4A737", "DisplayName": "Microsoft.AspNetCore.Components.WebView.Maui (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e982489a-58e4-333f-9edf-23f6196b3bee", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3DF1CBDF-172E-48BA-8093-A05B80C4A737}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3F1442E9-266B-4418-9BE7-3754145CC34C", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7707a811-16b2-37cb-9d49-740d748350e6", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3F1442E9-266B-4418-9BE7-3754145CC34C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "3F961D72-A8FB-4029-B654-505A7A2A1387", "DisplayName": "3CX Plugins", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9db62438-79c2-4fc5-852b-100aacf5712f", "DisplayVersion": "14.0.897.704", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{3F961D72-A8FB-4029-B654-505A7A2A1387}", "GlobalSoftwareId": 13, "GlobalSoftwareName": "3CX TAPI Driver"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4036CD29-55D2-444D-9BD5-1C552076BAE3", "DisplayName": "Microsoft.Android.Sdk.Windows (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "20dad67f-18b7-32d2-bf50-8fcddece5e9d", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4036CD29-55D2-444D-9BD5-1C552076BAE3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "406BB598-E5A9-454A-8719-82644CB09B03", "DisplayName": "Microsoft .NET 7.0 Templates 7.0.102 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0b661e41-5d8e-70f8-43ff-8280aef64cd8", "DisplayVersion": "28.5.33023", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{406BB598-E5A9-454A-8719-82644CB09B03}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "41C0DB18-1790-465E-B0DD-D9CAA35CACBE", "DisplayName": "Microsoft Command Line Utilities 15 for SQL Server", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "11e5cc67-2eca-41a1-8775-5ea0b51ccbaa", "DisplayVersion": "15.0.1300.359", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{41C0DB18-1790-465E-B0DD-D9CAA35CACBE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4491220D-21E8-4C34-84FC-F2659E08BF7E", "DisplayName": "Microsoft.NET.Sdk.iOS.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "4ab98f70-4977-3082-936e-1bd6f41ce4ab", "DisplayVersion": "16.1.17", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4491220D-21E8-4C34-84FC-F2659E08BF7E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "46364955-9E50-4610-AC18-D90FD27D662A", "DisplayName": "Microsoft.MacCatalyst.Runtime.maccatalyst-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "5b861cec-429c-33ca-aedf-5269037cd818", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{46364955-9E50-4610-AC18-D90FD27D662A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "48C24F32-A313-4B3C-B2C0-692D05461628", "DisplayName": "Microsoft .NET 6.0 Templates 7.0.102 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "04dfe059-50ae-7ee0-adee-ee8937a5d41d", "DisplayVersion": "24.7.49407", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{48C24F32-A313-4B3C-B2C0-692D05461628}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4B6FEC3C-FAAF-4357-A213-A886EC8A42A8", "DisplayName": "Microsoft.Maui.Essentials.Ref.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "cb7dbb93-b76c-37d5-88c4-916b0f1fb99a", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4B6FEC3C-FAAF-4357-A213-A886EC8A42A8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4DCC6F4C-F44A-4863-94B8-A0875EEAEDBC", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bbc1f057-8582-3b63-bba1-c6c259e7cb84", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4DCC6F4C-F44A-4863-94B8-A0875EEAEDBC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4EC915BE-3750-4005-B470-596113F8063B", "DisplayName": "Microsoft.iOS.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a7481d3b-8866-367f-bd56-2f171e289d5c", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4EC915BE-3750-4005-B470-596113F8063B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-03T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "4EEF2644-700F-46F8-9655-915145248986", "DisplayName": "PuTTY release 0.78 (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c9eaa861-2b72-4faf-9fee-eeb1ad5fd15e", "DisplayVersion": "0.78.0.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4EEF2644-700F-46F8-9655-915145248986}", "GlobalSoftwareId": 315, "GlobalSoftwareName": "PuTTY"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4FCA00D9-2C06-435C-ACD2-27FE383B7800", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.ios-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4FCA00D9-2C06-435C-ACD2-27FE383B7800}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "51473273-20F8-4B0F-A6D1-2A5B39791739", "DisplayName": "Microsoft.NET.Runtime.MonoTargets.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bd9537bd-4f66-3d28-8646-74da248cfa57", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{51473273-20F8-4B0F-A6D1-2A5B39791739}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "51FB23C0-2978-423B-BC84-6F4923EFEF6D", "DisplayName": "Microsoft .NET Core AppHost Pack - 3.1.32 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bfa32401-5173-7946-caba-401afc0f4364", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{51FB23C0-2978-423B-BC84-6F4923EFEF6D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-03T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "5211034D-495B-4A5E-9B8D-8961BBB2B9E2", "DisplayName": "DB Browser for SQLite", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "124623d9-35d6-4d2e-9474-2adacc8babbb", "DisplayVersion": "3.12.2", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5211034D-495B-4A5E-9B8D-8961BBB2B9E2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5312C365-3703-4216-84C6-27724E69F393", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "38d4b2de-544e-31ac-b8d7-385ddb24c8a2", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5312C365-3703-4216-84C6-27724E69F393}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "56070933-B0D1-493F-8C12-4F7E83CA3071", "DisplayName": "IIS 10.0 Express", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "92594ebf-0b05-4bbf-84d0-6186fcfc7591", "DisplayVersion": "10.0.06614", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{56070933-B0D1-493F-8C12-4F7E83CA3071}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "561A6866-970D-4C56-A055-17D0AA58A3E2", "DisplayName": "Microsoft.Maui.Controls.Runtime.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bdd836c7-8504-3984-9987-1a1890ee426f", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{561A6866-970D-4C56-A055-17D0AA58A3E2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5637716A-31F4-4384-9132-9FA08FA28D7F", "DisplayName": "Microsoft.Maui.Essentials.Runtime.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ab53e98c-0e77-3e81-8594-81a809dedbef", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5637716A-31F4-4384-9132-9FA08FA28D7F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5691C5AB-0847-4862-8C49-43245E7DCE2C", "DisplayName": "vs_minshellinteropx64msi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3b81d2b7-233e-439a-99aa-a58efbecf7fe", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5691C5AB-0847-4862-8C49-43245E7DCE2C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "5828A5D2-9D4C-4ABF-BFC4-06944BD80CEE", "DisplayName": "ConnectWise Manage Client 64-bit", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e3e9ab17-f6de-4329-b860-80ce3eaea932", "DisplayVersion": "22.2.1", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5828A5D2-9D4C-4ABF-BFC4-06944BD80CEE}", "GlobalSoftwareId": 124, "GlobalSoftwareName": "Connectwise Manage"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "585A3B86-59F6-4129-8E07-8E389121E4F2", "DisplayName": "Microsoft.iOS.Runtime.ios-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a0a2bc83-6ae4-3aed-bd4b-675f40bd35a0", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{585A3B86-59F6-4129-8E07-8E389121E4F2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5A66E598-37BD-4C8A-A7CB-A71C32ABCD78", "DisplayName": "Microsoft .NET Runtime - 5.0.17 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7d82cfd9-56ab-726b-6699-16b0500001e2", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5A66E598-37BD-4C8A-A7CB-A71C32ABCD78}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5A784B54-A6B3-4975-AABF-FE0A7A79F7DE", "DisplayName": "Microsoft.MacCatalyst.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "5b2385d3-c2c4-31eb-9bcd-ad9d411d2a74", "DisplayVersion": "16.1.228.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5A784B54-A6B3-4975-AABF-FE0A7A79F7DE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "5A990909-DD22-48FA-BD8B-F564AFC81C4B", "DisplayName": "Sentinel Agent", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "95b9ab60-3bed-4c47-afc0-e5876a75512a", "DisplayVersion": "22.2.558", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5A990909-DD22-48FA-BD8B-F564AFC81C4B}", "GlobalSoftwareId": 181, "GlobalSoftwareName": "SentinelOne"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "5BC7E9EB-13E8-45DB-8A60-F2481FEB4595", "DisplayName": "Microsoft System CLR Types for SQL Server 2019", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b6da578a-35ee-4f9e-9531-796dea2f9f5c", "DisplayVersion": "15.0.2000.5", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5BC7E9EB-13E8-45DB-8A60-F2481FEB4595}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5BEE5F3E-4D78-4DE8-A8F3-36D3E9D8868C", "DisplayName": "Microsoft Windows Desktop Runtime - 3.1.32 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "6a9bf306-5968-6963-dd9e-07808b4ea1a9", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5BEE5F3E-4D78-4DE8-A8F3-36D3E9D8868C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5F0DB006-2AE3-4D36-8077-65247FD687D4", "DisplayName": "Microsoft .NET Runtime - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "fbae49fd-54fb-74b6-4f77-dc6e69f21efc", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5F0DB006-2AE3-4D36-8077-65247FD687D4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "60E48580-5DF7-4DAD-AE49-DE79848DB07B", "DisplayName": "Microsoft.MacCatalyst.Templates (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0f5f7833-4a1f-389f-a30d-535bcae4be4f", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{60E48580-5DF7-4DAD-AE49-DE79848DB07B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "62E8F7DA-76C4-46BC-AB40-581C42EC5AFA", "DisplayName": "Microsoft .NET Targeting Pack - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "92c600bf-5e83-41d3-13af-ea3fc4166a46", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{62E8F7DA-76C4-46BC-AB40-581C42EC5AFA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6664D26A-EFA2-4311-B79E-E03ECF1D8E07", "DisplayName": "Microsoft.Maui.Controls.Runtime.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b8570fea-385e-3580-b2cb-c4dba5610467", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{6664D26A-EFA2-4311-B79E-E03ECF1D8E07}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "69331A50-908A-0745-CFCF-8413360C5B96", "DisplayName": "Windows App Certification Kit Native Components", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "71f94612-0ce4-423d-3769-ac3bab695e7c", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{69331A50-908A-0745-CFCF-8413360C5B96}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6ED39B27-3CB5-4E86-BAC0-486C6A5A7E15", "DisplayName": "Microsoft .NET Core 3.1 Templates 7.0.102 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e800932f-5f1b-5b28-08ec-ff6846086bd1", "DisplayVersion": "12.29.33023", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6ED39B27-3CB5-4E86-BAC0-486C6A5A7E15}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "6F320B93-EE3C-4826-85E0-ADF79F8D4C61", "DisplayName": "Microsoft Visual Studio Installer", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "3.4.2246.31370", "DisplayIcon": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\"", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" /uninstall", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6FBFD1F4-0412-4DBB-AA00-F71278CAB664", "DisplayName": "Python 3.11.0 Tcl/Tk Support (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6FBFD1F4-0412-4DBB-AA00-F71278CAB664}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "7064293E-FAA0-47EC-92E0-5630A6A82E07", "DisplayName": "<PERSON><PERSON> Log Shipper", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "847f5872-8224-42ef-93ac-58f526c4ff99", "DisplayVersion": "2022.12.12", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{7064293E-FAA0-47EC-92E0-5630A6A82E07}", "GlobalSoftwareId": 421, "GlobalSoftwareName": "<PERSON><PERSON> Log Shipper"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "74A2D2BF-BD4F-4D82-812F-EDEB21EA443F", "DisplayName": "Python 3.11.0 Development Libraries (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f26e6705-6fca-5a75-a514-8e24c18afaad", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{74A2D2BF-BD4F-4D82-812F-EDEB21EA443F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "74B0754B-7323-4EA6-A198-BE851D9FD6AE", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "06141f21-418d-3c19-9606-179e174514e7", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{74B0754B-7323-4EA6-A198-BE851D9FD6AE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7519423C-A977-4160-83A2-48633600A216", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 3.1.0 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "24.64.28315", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{7519423C-A977-4160-83A2-48633600A216}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "754E7936-D264-406F-A76D-1C4E0102AE39", "DisplayName": "Microsoft.NET.Workload.Emscripten.net6.Manifest (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b32c42b4-68b4-3cd0-b222-29f70f5e115e", "DisplayVersion": "56.31.52114", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{754E7936-D264-406F-A76D-1C4E0102AE39}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "759A23A0-2B85-4FB0-B050-FA945CD3FB16", "DisplayName": "Microsoft.Maui.Core.Runtime.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2e0d3c1e-12b2-3ac4-91cb-19a344e026c4", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{759A23A0-2B85-4FB0-B050-FA945CD3FB16}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "76891AF7-E11B-492C-AAC9-74F6B4AFB6F6", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "36b8f649-4770-3f77-ad57-3c8e2daab2c5", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{76891AF7-E11B-492C-AAC9-74F6B4AFB6F6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7862864F-772B-4DDA-A4A6-FD7BC3271D31", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d57e297b-b8b0-3b59-8682-bd9df243292d", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{7862864F-772B-4DDA-A4A6-FD7BC3271D31}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "790E822D-8C41-4FCE-8CF2-4C90F7B3CE16", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.maccatalyst-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9a4c3390-4398-32b6-90cc-f9fbac3ea5d5", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{790E822D-8C41-4FCE-8CF2-4C90F7B3CE16}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7B8542BA-01E4-43EB-A172-1DA975AFD00B", "DisplayName": "VS JIT Debugger", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "dbf1f002-abce-480e-a895-7fcfb730d6de", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7B8542BA-01E4-43EB-A172-1DA975AFD00B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7D6A7B92-A26B-4DC6-A51F-0D741C9BC70F", "DisplayName": "icecap_collection_x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "aca69f0c-52d0-326d-8850-47fe9473dbb5", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7D6A7B92-A26B-4DC6-A51F-0D741C9BC70F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7DFEC04C-4CBC-4013-AAA2-A1E7B1CD135B", "DisplayName": "Dell SupportAssist OS Recovery Plugin for Dell Update", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2253caa0-0ba2-461c-a50e-fc18228ebadd", "DisplayVersion": "5.5.5.16208", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7DFEC04C-4CBC-4013-AAA2-A1E7B1CD135B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7E0C04EC-9D6F-36CD-A821-DC8493EE407F", "DisplayName": "Microsoft ASP.NET Core 5.0.0 Targeting Pack (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7eedbbc9-a63e-3a50-a4b0-99008ccd216d", "DisplayVersion": "5.0.0.20526", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{7E0C04EC-9D6F-36CD-A821-DC8493EE407F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7E20491D-8D7F-43AB-89A7-CD0067424B30", "DisplayName": "Microsoft.Maui.Core.Ref.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{7E20491D-8D7F-43AB-89A7-CD0067424B30}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "81E2E91E-C39F-4F97-B2FD-7FE1EB0B34A6", "DisplayName": "Microsoft.Maui.Essentials.Runtime.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "47156c8a-61d9-3403-863d-6c521605834a", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{81E2E91E-C39F-4F97-B2FD-7FE1EB0B34A6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "82C2B018-382B-4D06-9BDC-CF56F73BF62E", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ae0f70de-0937-305c-8bee-26c616769c18", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{82C2B018-382B-4D06-9BDC-CF56F73BF62E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "82F9F289-6088-8F39-1918-A45315FEF99A", "DisplayName": "Universal CRT Tools x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "00de42c6-5bfa-cfa9-df77-45303b2a54c9", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{82F9F289-6088-8F39-1918-A45315FEF99A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "83EE22D9-C7B3-4CBC-9956-9DF0D4D0FF46", "DisplayName": "Microsoft .NET AppHost Pack - 5.0.17 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ebe55afb-50fa-4d3f-d649-7dfe529d4146", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{83EE22D9-C7B3-4CBC-9956-9DF0D4D0FF46}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8484730A-68A4-4C63-93B4-52628D3B488D", "DisplayName": "Microsoft Windows Desktop Runtime - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a914caa7-536a-5241-f4b7-a5b2da66a45b", "DisplayVersion": "48.55.53270", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8484730A-68A4-4C63-93B4-52628D3B488D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "853997DA-6FCB-4FB9-918E-E0FF881FAF65", "DisplayName": "Microsoft ODBC Driver 17 for SQL Server", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0123a210-9b73-46e7-b5ce-7f33630300e7", "DisplayVersion": "17.7.2.1", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{853997DA-6FCB-4FB9-918E-E0FF881FAF65}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "85CB775C-CF14-4F3C-9086-2317ACA301A5", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7fe8275d-0bc0-343c-a8cf-0e8d229cd761", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{85CB775C-CF14-4F3C-9086-2317ACA301A5}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8813DB3F-A464-4501-A22C-689FBD6467F2", "DisplayName": "Microsoft.Maui.Controls.Runtime.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "433549c3-b548-32b1-abe9-0f4f9ef2c7a2", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8813DB3F-A464-4501-A22C-689FBD6467F2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8A4F7CD9-8D41-414C-8F68-6A3D291C16A4", "DisplayName": "Microsoft.NET.Workload.Mono.Toolchain.net6.Manifest (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "839d638b-5fc3-34b2-904a-37a0683c3fb6", "DisplayVersion": "56.3.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8A4F7CD9-8D41-414C-8F68-6A3D291C16A4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8ABF8D69-5124-43E0-BB56-1EC7B9C86CCA", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3637d347-45d3-3375-93c7-ebb71a05a74b", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8ABF8D69-5124-43E0-BB56-1EC7B9C86CCA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8BA25391-0BE6-443A-8EBF-86A29BAFC479", "DisplayName": "Microsoft .NET Host FX Resolver - 5.0.17 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "baa629a0-5a36-5753-5290-57d8663219b0", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8BA25391-0BE6-443A-8EBF-86A29BAFC479}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8C89D103-57BE-4509-9752-B1CD9F12D0B9", "DisplayName": "Microsoft .NET AppHost Pack - 7.0.2 (x64_x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "874d5251-57d2-75e0-596e-7afc296dd530", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8C89D103-57BE-4509-9752-B1CD9F12D0B9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8CDACE3C-0064-4A17-A02C-49F831D5F73A", "DisplayName": "Microsoft .NET Host FX Resolver - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8CDACE3C-0064-4A17-A02C-49F831D5F73A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8D07C8DD-BFF2-4360-A16E-FD6AC2B0B4F9", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "614625e2-7569-3b9a-9e96-6f2e2b9bd0a1", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8D07C8DD-BFF2-4360-A16E-FD6AC2B0B4F9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-01T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90160000-007E-0000-1000-0000000FF1CE", "DisplayName": "Office 16 Click-to-Run Licensing Component", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.16026.20146", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{90160000-007E-0000-1000-0000000FF1CE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-23T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90160000-008C-0000-1000-0000000FF1CE", "DisplayName": "Office 16 Click-to-Run Extensibility Component", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.15726.20202", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{90160000-008C-0000-1000-0000000FF1CE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-23T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90160000-008C-040C-1000-0000000FF1CE", "DisplayName": "Office 16 Click-to-Run Localization Component", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.15726.20202", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{90160000-008C-040C-1000-0000000FF1CE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-23T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90160000-008C-0C0A-1000-0000000FF1CE", "DisplayName": "Office 16 Click-to-Run Localization Component", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.0.15726.20202", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{90160000-008C-0C0A-1000-0000000FF1CE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "90462BD2-DF5B-449C-A401-FCC1DC264E4E", "DisplayName": "Microsoft Azure Authoring Tools - v2.9.7", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "44cc9885-fa1c-4345-92fa-cbbd26b50d1b", "DisplayVersion": "2.9.8999.45", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{90462BD2-DF5B-449C-A401-FCC1DC264E4E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9138874C-2D20-46BC-84BC-A13B31DF8955", "DisplayName": "vs_Graphics_Singletonx64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e071376c-1977-39d0-9b36-532fb97efc9b", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{9138874C-2D20-46BC-84BC-A13B31DF8955}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "93A17F63-9B12-48F6-86BD-84535F2955FA", "DisplayName": "Microsoft .NET AppHost Pack - 6.0.13 (x64_arm)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2d612ca9-51c2-7c75-af74-d39535cfcb4b", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{93A17F63-9B12-48F6-86BD-84535F2955FA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "94EFB81C-7A55-4639-B682-1DC1CCD5E0F6", "DisplayName": "Microsoft.iOS.Runtime.iossimulator-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "54072cf0-3ce5-395d-981d-d032182cda30", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{94EFB81C-7A55-4639-B682-1DC1CCD5E0F6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9511601E-12FF-4972-BF9C-2992F2CA5A32", "DisplayName": "Microsoft .NET Host - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "97c9f490-5379-6902-7b80-d2723ca59d95", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{9511601E-12FF-4972-BF9C-2992F2CA5A32}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9BF1FB82-CDDF-43EB-BD35-BC4B341D292A", "DisplayName": "Microsoft.Maui.Essentials.Runtime.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "826d4308-8d86-38d4-b47a-bf1adb8e67c4", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{9BF1FB82-CDDF-43EB-BD35-BC4B341D292A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9BF7BDD3-62E8-4E47-AF96-13EA1EB853AD", "DisplayName": "vs_communityx64msi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "4aff6be3-374c-4812-827d-65d53d5f93cd", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{9BF7BDD3-62E8-4E47-AF96-13EA1EB853AD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9FD3AB78-203C-4513-A5E9-754B9ED1575C", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.ios-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a595f68e-54e8-3a02-91d5-59cebb894c14", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{9FD3AB78-203C-4513-A5E9-754B9ED1575C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A0F31B66-D366-4C0E-86B1-511DE274FE2D", "DisplayName": "Microsoft.NET.Workload.Mono.Toolchain.net7.Manifest (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f74c9d67-79c8-3901-8925-dbb22266d611", "DisplayVersion": "56.3.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A0F31B66-D366-4C0E-86B1-511DE274FE2D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A12EDD08-C717-41D7-BC08-98D1775821EB", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b4dbefa4-af12-3d91-8b5f-21a95571d576", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A12EDD08-C717-41D7-BC08-98D1775821EB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A4CCC742-818F-4C8C-8B56-631F6F76C7C6", "DisplayName": "Microsoft .NET AppHost Pack - 7.0.2 (x64_arm)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3df79229-56f1-6a32-8179-6ac7a4a643d6", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A4CCC742-818F-4C8C-8B56-631F6F76C7C6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A4FF0652-BEA3-4E62-91A1-410194316D1F", "DisplayName": "Microsoft .NET Core AppHost Pack - 3.1.32 (x64_x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "781f00fd-54ad-70df-a313-9908241be692", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A4FF0652-BEA3-4E62-91A1-410194316D1F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "A5F504DF-2ED9-4A2D-A2F3-9D2750DD42D6", "DisplayName": "Python 2.7.18 (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "6a965a0c-6ee6-4e3a-9983-3263f56311ec", "DisplayVersion": "2.7.18150", "DisplayIcon": "C:\\Python27\\python.exe", "UninstallString": "MsiExec.exe /I{A5F504DF-2ED9-4A2D-A2F3-9D2750DD42D6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A6500837-F3BE-357E-9A21-6A78D098659F", "DisplayName": "Microsoft ASP.NET Core 6.0.13 Shared Framework (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "cb9e8276-f9c6-3ee3-8ab7-21e18f5d6226", "DisplayVersion": "6.0.13.22580", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A6500837-F3BE-357E-9A21-6A78D098659F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A7036CFB-B403-4598-85FF-D397ABB88173", "DisplayName": "Microsoft .NET Standard Targeting Pack - 2.1.0 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2186c278-55b5-608a-036d-6bf539d6434f", "DisplayVersion": "24.0.28113", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A7036CFB-B403-4598-85FF-D397ABB88173}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A741B803-3F0E-4684-81EF-FC128D15A92C", "DisplayName": "Microsoft .NET Core Runtime - 3.1.32 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c98b7b61-5b80-6e5a-47c5-51f05e1ee68c", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A741B803-3F0E-4684-81EF-FC128D15A92C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A95FEF96-3FF2-4387-9301-792B6721F648", "DisplayName": "Microsoft .NET AppHost Pack - 5.0.17 (x64_x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "caee0054-59c9-421c-e745-900f24d04bcf", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A95FEF96-3FF2-4387-9301-792B6721F648}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A9AD5393-F594-41A9-B94D-BD017285D510", "DisplayName": "Microsoft.Maui.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3e57a4c4-a81a-3b6d-8fb8-c198b46cc824", "DisplayVersion": "6.0.548.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A9AD5393-F594-41A9-B94D-BD017285D510}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ABB19BB4-838D-3082-BDA4-87C6604181A2", "DisplayName": "Microsoft Visual C++ 2013 x64 Minimum Runtime - 12.0.40649", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "20400cf0-de7c-327e-9ae4-f0f38d9085f8", "DisplayVersion": "12.0.40649", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{ABB19BB4-838D-3082-BDA4-87C6604181A2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ACF8763C-83E8-4BE1-B67C-DF86C2E1240A", "DisplayName": "Python 3.11.0 Add to Path (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "416e9073-0ee9-5a77-b565-83866c383ee0", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{ACF8763C-83E8-4BE1-B67C-DF86C2E1240A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ad846bae-d44b-4722-abad-f7420e08bcd9", "DisplayName": "IIS Express Application Compatibility Database for x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": null, "DisplayIcon": null, "UninstallString": "C:\\Windows\\system32\\sdbinst.exe -u \"C:\\Windows\\AppPatch\\CustomSDB\\{ad846bae-d44b-4722-abad-f7420e08bcd9}.sdb\"", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "AE2D80C7-F19D-463F-B48F-3F343806721C", "DisplayName": "Microsoft .NET Runtime - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f780c3de-5ef2-7b54-4a76-a2064663c35d", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{AE2D80C7-F19D-463F-B48F-3F343806721C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "AEB9693B-A4AD-47CD-9DEC-F0524FB12F8F", "DisplayName": "Microsoft.Maui.Core.Runtime.any (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c6fe3be8-0f21-37ae-9308-6caf0723368f", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{AEB9693B-A4AD-47CD-9DEC-F0524FB12F8F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B0FBA6B7-B985-4D20-AC10-7E16D4D8C9CB", "DisplayName": "Microsoft.NET.Sdk.Maui.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "78660d92-a568-3005-b38d-667f2d67f710", "DisplayVersion": "7.0.52", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B0FBA6B7-B985-4D20-AC10-7E16D4D8C9CB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B28E4BED-428C-40CB-9A29-41E46263246D", "DisplayName": "Python 3.11.0 Executables (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a4f1ff01-e37a-540c-927e-c305dcb03593", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B28E4BED-428C-40CB-9A29-41E46263246D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "B3A63F4D-11C4-46FF-94C1-695633384F54", "DisplayName": "Microsoft .NET SDK 7.0.102 (x64) from Visual Studio", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7395763d-5930-7a2e-2ede-6a880dc496dd", "DisplayVersion": "7.1.222.60703", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B3A63F4D-11C4-46FF-94C1-695633384F54}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B60BA18B-0F61-4536-8C75-A99190B18834", "DisplayName": "Microsoft.iOS.Runtime.iossimulator-x86 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "84840c31-19da-346d-b454-eb72c3c8aca9", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B60BA18B-0F61-4536-8C75-A99190B18834}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B6E424B1-194C-4F86-B4C8-1EF464650D66", "DisplayName": "Microsoft.iOS.Windows.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "1267014b-ca01-3d92-81c8-176a813592c3", "DisplayVersion": "16.1.228.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B6E424B1-194C-4F86-B4C8-1EF464650D66}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B7177578-AF27-44BF-A0B1-8B9D6AF7ECC3", "DisplayName": "Microsoft Windows Desktop Runtime - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2ff7bf64-5f27-5520-9834-c516a8cd0ebd", "DisplayVersion": "56.11.53361", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B7177578-AF27-44BF-A0B1-8B9D6AF7ECC3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B7846BB6-4EDE-409B-9147-631286EF7FDD", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 5.0.0 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7d6e88d9-51ae-6663-aa36-cf1b59aeca16", "DisplayVersion": "40.0.29420", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B7846BB6-4EDE-409B-9147-631286EF7FDD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "B78B0A80-98FD-407B-B67C-BDF0595E73D0", "DisplayName": "DeskDirector Portal", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "501906ba-42dd-4762-a50b-607de4d0c01a", "DisplayVersion": "5.0.17", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B78B0A80-98FD-407B-B67C-BDF0595E73D0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BA96B656-D270-4F11-8011-7DBCAB6C5E4C", "DisplayName": "Microsoft.Maui.Controls.Ref.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ec1cbb59-e51c-3cfa-9c37-b1b067aee6d7", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BA96B656-D270-4F11-8011-7DBCAB6C5E4C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BC4CF846-7024-491B-A93A-7547C1CF15D8", "DisplayName": "Microsoft.iOS.Windows.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "956d1166-13d9-35ce-bcda-fc2566a7d97d", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BC4CF846-7024-491B-A93A-7547C1CF15D8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "BCC0B8E0-16CB-43B7-B58E-EFD70E62DA24", "DisplayName": "Node.js", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "47c07a3a-42ef-4213-a85d-8f5a59077c28", "DisplayVersion": "18.13.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{BCC0B8E0-16CB-43B7-B58E-EFD70E62DA24}", "GlobalSoftwareId": 694, "GlobalSoftwareName": "Node.js (Current)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BD29D023-6B95-47FE-B480-598840EB9A28", "DisplayName": "Python 3.11.0 Utility Scripts (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e5277cda-8012-5ab6-b333-792e27561bdd", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{BD29D023-6B95-47FE-B480-598840EB9A28}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BE394B10-AD5D-4503-9AA9-E79F953B30E3", "DisplayName": "Microsoft .NET Host FX Resolver - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "1eb52adb-57c6-4725-1c15-ec4ebe4002e7", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BE394B10-AD5D-4503-9AA9-E79F953B30E3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BEB46B9B-6D72-46E5-968C-CDED3C01896B", "DisplayName": "Microsoft .NET AppHost Pack - 7.0.2 (x64_arm64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "82a176aa-5912-796f-2c3e-c9859c7a3a6c", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BEB46B9B-6D72-46E5-968C-CDED3C01896B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BED7E006-DA64-3E61-8836-A231C2F7DD81", "DisplayName": "Microsoft ASP.NET Core 6.0.13 Targeting Pack (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d1baef0a-fb31-3de0-a737-8f9c468956ac", "DisplayVersion": "6.0.13.22580", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BED7E006-DA64-3E61-8836-A231C2F7DD81}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BEDED70B-F09A-4121-90E4-B852951D7C38", "DisplayName": "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d88773fe-fd5b-3f3e-8f43-8380829605ee", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BEDED70B-F09A-4121-90E4-B852951D7C38}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C01C064D-0C6F-4D8C-AEBC-6E193721AF79", "DisplayName": "Microsoft.Maui.Essentials.Runtime.android (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "031f0669-f14f-3aee-a271-913cb0115848", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C01C064D-0C6F-4D8C-AEBC-6E193721AF79}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C0E951A8-6B2D-45CB-869D-EC4BC950D1E7", "DisplayName": "Microsoft .NET Targeting Pack - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "21806a38-5296-44d8-fd7c-2b1fc9f104ba", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C0E951A8-6B2D-45CB-869D-EC4BC950D1E7}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C16F5721-629E-416B-8E91-1F5A0FAC1B2D", "DisplayName": "Microsoft.Maui.Controls.Runtime.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "5adc6e43-12de-3903-bbc7-e53a16fdbde3", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C16F5721-629E-416B-8E91-1F5A0FAC1B2D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C1FF10EF-6BCB-3B08-AE1A-0D237C9F9F30", "DisplayName": "Microsoft ASP.NET Core 5.0.17 Shared Framework (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7487416f-9b34-3665-82b0-e31e377e109f", "DisplayVersion": "5.0.17.22215", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C1FF10EF-6BCB-3B08-AE1A-0D237C9F9F30}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C20CF802-93E3-4BF8-B2F0-8D0B9C826C00", "DisplayName": "Microsoft.NET.Sdk.MacCatalyst.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "16.1.17", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C20CF802-93E3-4BF8-B2F0-8D0B9C826C00}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "C5C91AA6-3E83-430E-8B7A-6B790083F28D", "DisplayName": "Microsoft Azure Libraries for .NET – v2.9", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "3.0.0127.060", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C5C91AA6-3E83-430E-8B7A-6B790083F28D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C7E53D98-B6B0-4AD5-9EE2-1FEF2C6DD246", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "4a625de2-671a-3031-a58f-8b526465e48f", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C7E53D98-B6B0-4AD5-9EE2-1FEF2C6DD246}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C8EA234A-FC2F-4EEC-BF7F-DB14C28C84D2", "DisplayName": "VS Script Debugging Common", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7fe0dd7b-18c6-4891-b2ee-694dc70b3c43", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C8EA234A-FC2F-4EEC-BF7F-DB14C28C84D2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CB0DEA40-4FE1-4630-8C18-4963AE60DE2C", "DisplayName": "Microsoft.Android.Sdk.Windows (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "bb159359-42af-39a1-8ad5-7e9aee3db2b5", "DisplayVersion": "32.0.476.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CB0DEA40-4FE1-4630-8C18-4963AE60DE2C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CB48447A-730B-46DE-BBD4-5A146D33BE79", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.ios-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "aa7c3f73-1f6c-3479-8a5e-c54f664b857b", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CB48447A-730B-46DE-BBD4-5A146D33BE79}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CB50F1A9-53E7-4833-8518-82E30DD8C498", "DisplayName": "Microsoft.iOS.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "3d266ffd-de78-3636-91a9-87abd82ae653", "DisplayVersion": "16.1.228.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CB50F1A9-53E7-4833-8518-82E30DD8C498}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CB7E1801-9FB8-4763-A369-1D7F290AB24D", "DisplayName": "Python 3.11.0 Standard Library (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ca5b7638-9e1d-52e8-93b3-1fc4b781b85d", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{CB7E1801-9FB8-4763-A369-1D7F290AB24D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CC07497D-B5AF-463E-B35E-2B35F73E4186", "DisplayName": "Microsoft.Android.Ref.33 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d8d0e6cf-d11d-35ae-957d-8995e9fd7b91", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CC07497D-B5AF-463E-B35E-2B35F73E4186}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CC2A595B-74D2-45E6-8B53-45F20215850D", "DisplayName": "Microsoft .NET AppHost Pack - 6.0.13 (x64_arm64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a48f5782-5f3e-7a5b-44a6-deee9d7f8ed6", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CC2A595B-74D2-45E6-8B53-45F20215850D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CD3AD81A-A806-473F-BB60-A402AFAD8D84", "DisplayName": "Microsoft .NET Toolset 7.0.102 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "eef3412a-5edb-541a-558d-af7233ffb3fb", "DisplayVersion": "28.5.33023", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CD3AD81A-A806-473F-BB60-A402AFAD8D84}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CE02EE5D-638F-4A4A-BC95-B189CC8CA6AB", "DisplayName": ".NET MAUI Templates (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "4288192d-7fd4-3b77-82a8-8e6a3a7b2cff", "DisplayVersion": "6.0.548.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CE02EE5D-638F-4A4A-BC95-B189CC8CA6AB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CF4C347D-954E-4543-88D2-EC17F07F466F", "DisplayName": "Microsoft Visual C++ 2022 X64 Minimum Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "36f68a90-239c-34df-b58c-64b30153ce35", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{CF4C347D-954E-4543-88D2-EC17F07F466F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D03368DF-FD08-4AAA-AAA3-EB329C2B9448", "DisplayName": "Microsoft.Maui.Controls.Ref.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "687b7177-9d50-354b-bd69-a43c683ed2e1", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D03368DF-FD08-4AAA-AAA3-EB329C2B9448}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D0DCE6F1-0123-4450-86DC-4933B0C1D47B", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.iossimulator-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "5bfbef71-00f3-3bc5-aa31-225059d181ab", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D0DCE6F1-0123-4450-86DC-4933B0C1D47B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D2AD09BE-81EA-48DB-9A3B-424DD88F59EB", "DisplayName": "Microsoft .NET 5.0 Templates 7.0.102 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "8a0ffcdf-5d6d-60ae-9218-7b2dd54c336b", "DisplayVersion": "20.5.49407", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D2AD09BE-81EA-48DB-9A3B-424DD88F59EB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D3773C88-43C6-46CD-AE5F-627FF6C6E5D4", "DisplayName": "Python 3.11.0 Documentation (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ffe45e56-c510-5fa9-975e-cc95a3147880", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D3773C88-43C6-46CD-AE5F-627FF6C6E5D4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D4A2E83A-3BD5-4E0D-9577-7F8CA74B876B", "DisplayName": "Python 3.11.0 Test Suite (64-bit)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "78109317-3359-533c-9f2b-d10a7c4a3cfb", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D4A2E83A-3BD5-4E0D-9577-7F8CA74B876B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D69D0FB6-5E17-459F-80A2-B3201AFC3710", "DisplayName": "Microsoft.MacCatalyst.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "52d9e4cb-9f5c-30ba-a541-5fc78fa65865", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D69D0FB6-5E17-459F-80A2-B3201AFC3710}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D6B0DB83-8568-4534-B2F4-DDCDA1169663", "DisplayName": "Microsoft.NET.Runtime.MonoAOTCompiler.Task (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "dc92c27e-7c3e-392e-8e58-5f780eeb66c1", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D6B0DB83-8568-4534-B2F4-DDCDA1169663}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D7A77FBE-E0E7-47D7-86B4-51CB65B502C0", "DisplayName": "Microsoft .NET AppHost Pack - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "8257de6c-593d-70fa-a0e3-f95ed13f1558", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D7A77FBE-E0E7-47D7-86B4-51CB65B502C0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D98B986D-3D40-4E37-A584-F82381A3E74B", "DisplayName": "Microsoft.NET.Sdk.tvOS.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "eb660764-b611-3b9b-a6d4-6f01f6e6ab9a", "DisplayVersion": "16.1.17", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D98B986D-3D40-4E37-A584-F82381A3E74B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DA4C11CA-13A9-48AE-96A3-225BED582566", "DisplayName": "Microsoft.Maui.Core.Runtime.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "2b0e43c8-63b0-324c-b8c1-6b539c48d0a6", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{DA4C11CA-13A9-48AE-96A3-225BED582566}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DB99B31D-2AA6-4929-AC11-FE093989FEB2", "DisplayName": "Microsoft.Maui.Core.Ref.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e13a06c4-132d-3deb-a0e9-939154f70a21", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{DB99B31D-2AA6-4929-AC11-FE093989FEB2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DE06DCD3-71A1-42AC-9F6C-B2F391B63B18", "DisplayName": "Microsoft.Android.Runtime.33.android-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b7819f59-6b99-33c0-ba7b-0d6a20c8f088", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{DE06DCD3-71A1-42AC-9F6C-B2F391B63B18}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DEF2160E-12B6-477C-9D55-DF4B100E3E2B", "DisplayName": "Dell SupportAssist Remediation", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "39cc6fd4-90e8-4916-870e-c5d7725e8b23", "DisplayVersion": "5.5.5.16208", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{DEF2160E-12B6-477C-9D55-DF4B100E3E2B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E0BCED2D-A3CE-4DF7-B0F1-5E51140E65FE", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "35f8b0b1-96e6-3c73-b850-4f23ae7ffe2a", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E0BCED2D-A3CE-4DF7-B0F1-5E51140E65FE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E22E63FE-031E-4173-AFC5-A9A994DCAC93", "DisplayName": "Microsoft.Maui.Essentials.Ref.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d32ea3bc-45eb-3c91-a5d8-6d56f9c17e91", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E22E63FE-031E-4173-AFC5-A9A994DCAC93}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E27862BD-4371-4245-896A-7EBE989B6F7F", "DisplayName": "DellOptimizerUI", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "543345d0-978e-4b9e-9a9c-c52a064e3513", "DisplayVersion": "3.2.212.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{E27862BD-4371-4245-896A-7EBE989B6F7F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-30T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E2879C76-FEE0-4E13-8394-6DA97A4A986D", "DisplayName": "<PERSON><PERSON><PERSON> (Machine - MSI)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "af13b8f2-70f3-4469-95f5-6f09537227c0", "DisplayVersion": "4.29.149.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{E2879C76-FEE0-4E13-8394-6DA97A4A986D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "E2879C76-FEE0-4E13-8394-6DA97A4A986D", "DisplayName": "<PERSON><PERSON><PERSON> (Machine)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "af13b8f2-70f3-4469-95f5-6f09537227c0", "DisplayVersion": "4.29.149", "DisplayIcon": "C:\\Windows\\system32\\msiexec.exe", "UninstallString": "MsiExec.exe /X {E2879C76-FEE0-4E13-8394-6DA97A4A986D}", "GlobalSoftwareId": 396, "GlobalSoftwareName": "Slack Machine Wide"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E3D24504-A3F2-495D-A778-7C3511DDA07E", "DisplayName": ".NET MAUI Templates (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b4624142-296c-33c2-a8d3-9ad3b7d73cfe", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E3D24504-A3F2-495D-A778-7C3511DDA07E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E61152B0-06C3-4EA3-AE31-13BCB874406A", "DisplayName": "Microsoft .NET AppHost Pack - 5.0.17 (x64_arm64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "b68d03e2-5849-7354-e56c-f553d9ce2f99", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E61152B0-06C3-4EA3-AE31-13BCB874406A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-19T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E663ED1E-899C-40E8-91D0-8D37B95E3C69", "DisplayName": "Microsoft .NET Host - 5.0.17 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "8da8ddfa-591b-6f59-58df-79f2fa881f7b", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E663ED1E-899C-40E8-91D0-8D37B95E3C69}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E76676E1-46FD-462E-B38D-D743E91C49F4", "DisplayName": "Microsoft.iOS.Runtime.ios-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d2180c70-5e92-3131-af07-371cbbbff018", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E76676E1-46FD-462E-B38D-D743E91C49F4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E90DD6FE-031D-4AE4-B3E2-2FEE37292DA5", "DisplayName": "Microsoft.Maui.Graphics (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "ee09f4bc-c5f1-30c9-a1ba-f6bc80e36700", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E90DD6FE-031D-4AE4-B3E2-2FEE37292DA5}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E9863518-AF32-4AC9-9E12-1121C3D39FDD", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-arm (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "48bba8ab-d4dc-3c3c-9589-bb32c7893bc3", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{E9863518-AF32-4AC9-9E12-1121C3D39FDD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-13T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "EA457B21-F73E-494C-ACAB-524FDE069978", "DisplayName": "Microsoft Visual Studio Code", "QuietUninstallString": "\"C:\\Program Files\\Microsoft VS Code\\unins000.exe\" /SILENT", "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "1.75.1", "DisplayIcon": "C:\\Program Files\\Microsoft VS Code\\Code.exe", "UninstallString": "\"C:\\Program Files\\Microsoft VS Code\\unins000.exe\"", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "EAE242B1-0A26-485A-BFEB-0292EE9F03CB", "DisplayName": "Microsoft Visual C++ 2022 X64 Additional Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9b0baa88-e15f-3a1f-acc0-b206e9ddf71c", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{EAE242B1-0A26-485A-BFEB-0292EE9F03CB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "EBA92FB4-E361-4534-9CCA-B8FE2280B825", "DisplayName": "Microsoft.iOS.Templates (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "048e06ac-c9eb-3fdc-b4d2-94e64dcf5dc5", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{EBA92FB4-E361-4534-9CCA-B8FE2280B825}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "EC790CED-7E76-4B13-B37E-879B52F19367", "DisplayName": "Microsoft.MacCatalyst.Runtime.maccatalyst-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "afac6df3-0536-3842-b981-8c0ce0bc0b22", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{EC790CED-7E76-4B13-B37E-879B52F19367}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ED9E82B0-463B-4CB6-A501-93DFD1FE05FD", "DisplayName": "Microsoft.iOS.Runtime.iossimulator-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "25d8f5f8-f890-390c-8dbd-265facf87392", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{ED9E82B0-463B-4CB6-A501-93DFD1FE05FD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-23T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "EF9EBC42-6969-45CE-A8D2-B9249B00C838", "DisplayName": "Microsoft Update Health Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": null, "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{EF9EBC42-6969-45CE-A8D2-B9249B00C838}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F0719345-342D-4A57-8EE4-30BF4FA3FBE9", "DisplayName": "Microsoft .NET Host - 7.0.2 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d624f3e4-50db-5a2f-906c-68b719448362", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F0719345-342D-4A57-8EE4-30BF4FA3FBE9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F0E9CE77-BF19-4BBE-B228-A42F782F82E4", "DisplayName": "Microsoft ASP.NET Core Module V2 for IIS Express", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "da062c0f-12c2-4703-8ed7-0cad342a53a1", "DisplayVersion": "17.0.22116.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F0E9CE77-BF19-4BBE-B228-A42F782F82E4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F11CFE11-BE79-4A34-8649-404C8184A4D4", "DisplayName": "Microsoft .NET Core AppHost Pack - 3.1.32 (x64_arm64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "7b812bf4-5e16-7f23-53f0-e05125fe662a", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F11CFE11-BE79-4A34-8649-404C8184A4D4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F260A343-BE37-4B68-A9A1-363837F6654F", "DisplayName": "Microsoft.NET.Runtime.MonoTargets.Sdk (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "020473dd-9a2d-3d6e-a486-ff45c8ca7991", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F260A343-BE37-4B68-A9A1-363837F6654F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F285BA87-F560-4587-8C08-7BF5A4A68762", "DisplayName": "Microsoft .NET AppHost Pack - 6.0.13 (x64_x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "446dc61d-5e46-653f-5e82-cd21d629ad4c", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F285BA87-F560-4587-8C08-7BF5A4A68762}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F3E2081B-744C-4023-A61E-EEAB556A2A33", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "cd5889ef-52da-6de5-a08b-4f0d85f09a9b", "DisplayVersion": "48.55.53270", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F3E2081B-744C-4023-A61E-EEAB556A2A33}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F4EF9619-F8A6-4844-B56D-9D411EC0AAB2", "DisplayName": "Microsoft.NETCore.App.Runtime.AOT.win-x64.Cross.android-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a2006a23-fafe-3f24-bd10-2045c5c9d621", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F4EF9619-F8A6-4844-B56D-9D411EC0AAB2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F5691AC1-8825-4A5B-90EC-999FFC531E78", "DisplayName": "Microsoft.Maui.Core.Ref.win (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "d067255f-236d-3e17-a062-79a409c29748", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F5691AC1-8825-4A5B-90EC-999FFC531E78}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F612BCA2-6E26-4F93-834B-8877939C0293", "DisplayName": "Microsoft .NET AppHost Pack - 6.0.13 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "9a78e27d-589e-446a-d53c-14b9f2542370", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F612BCA2-6E26-4F93-834B-8877939C0293}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F6EE26FC-8025-4671-B495-D00389BDED3F", "DisplayName": "Microsoft.MacCatalyst.Ref (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "1a19f2d7-04d7-3f11-a0b4-93fde44e6768", "DisplayVersion": "16.1.1477.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F6EE26FC-8025-4671-B495-D00389BDED3F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F790CD5C-7A08-4E87-8C45-1F068EEC9397", "DisplayName": "Microsoft.Maui.Essentials.Runtime.maccatalyst (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "c0fe937c-e538-36a7-8bcc-690a25bed5da", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F790CD5C-7A08-4E87-8C45-1F068EEC9397}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "F8474A47-8B5D-4466-ACE3-78EAB3BF21A8", "DisplayName": "Windows Subsystem for Linux Update", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "1c3db5b6-65a5-4ebc-a5b9-2f2d6f665f48", "DisplayVersion": "5.10.102.1", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F8474A47-8B5D-4466-ACE3-78EAB3BF21A8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F893E9F8-0105-4EA1-8682-3B6F0E74149E", "DisplayName": "Microsoft.Android.Runtime.33.android-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a8e66b75-a0af-395e-856a-0d8bf8ca0dc6", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F893E9F8-0105-4EA1-8682-3B6F0E74149E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F8B9E8C8-61E8-4E9E-879D-F3F498AD0230", "DisplayName": "IntelliTraceProfilerProxy", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "de8ee44c-e4f6-4f48-b4b2-6ed0a84c5417", "DisplayVersion": "15.0.21225.01", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{F8B9E8C8-61E8-4E9E-879D-F3F498AD0230}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F8FD63B1-6F4D-4E64-8234-2A46AA5112DA", "DisplayName": "Microsoft.Maui.Essentials.Ref.ios (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "da6d55f2-7b37-3067-a20b-d07fa5f93627", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F8FD63B1-6F4D-4E64-8234-2A46AA5112DA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F99D901B-F288-4D79-ACAA-E31BE48D789D", "DisplayName": "Microsoft.NET.Workload.Emscripten.net7.Manifest (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "fe529ab9-7626-3676-8f52-ebb0a2e82c72", "DisplayVersion": "56.31.52114", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{F99D901B-F288-4D79-ACAA-E31BE48D789D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FAFEE5E3-E00A-4CE8-B495-8F66A5FAB236", "DisplayName": "Microsoft ASP.NET Core Module for IIS Express", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "baad43d4-cfd2-4082-ad7c-ba9ef673cceb", "DisplayVersion": "12.2.18292.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{FAFEE5E3-E00A-4CE8-B495-8F66A5FAB236}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FAFFE35F-C942-43D5-AD40-F8F24E9F1C8A", "DisplayName": "Microsoft.Android.Runtime.33.android-x64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "a30a8700-f46a-3ecd-b6e8-07826bdf711d", "DisplayVersion": "********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{FAFFE35F-C942-43D5-AD40-F8F24E9F1C8A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FB37C3DF-1DE6-4CF2-A00D-559D6195EF11", "DisplayName": "Microsoft.NETCore.App.Runtime.Mono.android-arm64 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "f36391d1-170e-335e-9f95-596785ad30cc", "DisplayVersion": "*******", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{FB37C3DF-1DE6-4CF2-A00D-559D6195EF11}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FE768F65-89B5-40E5-9CE5-25D002197AE7", "DisplayName": "Microsoft.NET.Sdk.Android.Manifest-7.0.100 (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "0bfc9fa6-3428-3242-84f5-a8516f5dc798", "DisplayVersion": "33.0.4", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{FE768F65-89B5-40E5-9CE5-25D002197AE7}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FEA48357-CE2F-3ED0-B2A0-8548BEC6F111", "DisplayName": "Microsoft ASP.NET Core 3.1.10 Targeting Pack (x64)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "e9a94772-935d-3e6a-bdf1-3310f0bb20db", "DisplayVersion": "3.1.10.20520", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{FEA48357-CE2F-3ED0-B2A0-8548BEC6F111}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FECAFEB5-8D0E-4AE4-8FA0-745BAA835C35", "DisplayName": "DiagnosticsHub_CollectionService", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "64", "UpgradeCode": "86b398db-b702-30aa-a40b-b7818162613d", "DisplayVersion": "17.3.32601", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{FECAFEB5-8D0E-4AE4-8FA0-745BAA835C35}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "3CXPhone for Windows", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "16.3.0.264", "DisplayIcon": "C:\\Windows\\Installer\\{110A3CCE-3D00-4E4A-8E61-EE5A4DFDA908}\\CXPhoneWin8SetupIcon.exe", "UninstallString": "msiexec.exe /x {110A3CCE-3D00-4E4A-8E61-EE5A4DFDA908} AI_UNINSTALLER_CTP=1", "GlobalSoftwareId": 12, "GlobalSoftwareName": "3CXPhone for Windows"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Visual Studio Enterprise 2022", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "17.4.4", "DisplayIcon": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\Common7\\IDE\\devenv.exe", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" uninstall --installPath \"C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\"", "GlobalSoftwareId": 532, "GlobalSoftwareName": "Visual Studio 2022 Enterprise"}, {"InstallDate": "2023-02-15T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft Edge", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "110.0.1587.46", "DisplayIcon": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe,0", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\110.0.1587.46\\Installer\\setup.exe\" --uninstall --msedge --channel=stable --system-level --verbose-logging", "GlobalSoftwareId": 234, "GlobalSoftwareName": "Microsoft Edge Chromium"}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft Edge Update", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "1.3.173.45", "DisplayIcon": null, "UninstallString": null, "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-16T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": null, "DisplayName": "Microsoft Edge WebView2 Runtime", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "110.0.1587.46", "DisplayIcon": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\110.0.1587.46\\msedgewebview2.exe,0", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\110.0.1587.46\\Installer\\setup.exe\" --uninstall --msedgewebview --system-level --verbose-logging", "GlobalSoftwareId": 417, "GlobalSoftwareName": "Microsoft Edge WebView2 Runtime"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "02594FB6-9905-CBB9-10E8-EFCFB7122D7C", "DisplayName": "Windows SDK Desktop Tools x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "17974c46-dacd-02ab-ec10-c61087cb87bc", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{02594FB6-9905-CBB9-10E8-EFCFB7122D7C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "04D84C30-B442-49D2-A139-B8AAA7ACD445", "DisplayName": "vs_devenvsharedmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0ee5a545-2b7d-4507-8326-50719f7bebeb", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{04D84C30-B442-49D2-A139-B8AAA7ACD445}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "08C9E30B-298C-47FB-8C21-14B65593770F", "DisplayName": "Visual C++ Library CRT Desktop Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e3316a41-4b17-305a-990d-9cf9ec1ae1ac", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{08C9E30B-298C-47FB-8C21-14B65593770F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0B826206-3626-4E96-A675-0BDE6B8711B6", "DisplayName": "VS Immersive Activate Helper", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1e291769-c0e1-44f0-a6e2-43151ed3f553", "DisplayVersion": "**********", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0B826206-3626-4E96-A675-0BDE6B8711B6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0D02D706-44F2-4957-A448-E7259A0B56B9", "DisplayName": "Microsoft Windows Desktop Runtime - 5.0.17 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "15504876-5c5b-672b-9b12-4cf0cbf56ff8", "DisplayVersion": "40.68.31219", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0D02D706-44F2-4957-A448-E7259A0B56B9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "0E6EEAC9-4913-4C2F-B7D2-761B27C35D7C", "DisplayName": "Python Launcher", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1b68a0ec-4dd3-5134-840e-73854b0863f1", "DisplayVersion": "3.11.7966.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{0E6EEAC9-4913-4C2F-B7D2-761B27C35D7C}", "GlobalSoftwareId": 709, "GlobalSoftwareName": "Python"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0F2D2736-F436-3F10-FB30-9D279F58305B", "DisplayName": "WinRT Intellisense Mobile - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c31e491d-d563-9e25-3bec-30f742f6b134", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0F2D2736-F436-3F10-FB30-9D279F58305B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "0F5E7D7D-8969-DC1B-205D-024FB54A0417", "DisplayName": "Windows SDK Signing Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0f833c0d-80b0-9386-6d9a-fdcb3323a14c", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{0F5E7D7D-8969-DC1B-205D-024FB54A0417}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "101E4BA2-FD92-4B06-8FDC-BB8E23A57D64", "DisplayName": "icecap_collectionresourcesx64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c6f1738a-1b78-3716-9b0f-0f526fe7bbb5", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{101E4BA2-FD92-4B06-8FDC-BB8E23A57D64}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "110A3CCE-3D00-4E4A-8E61-EE5A4DFDA908", "DisplayName": "3CXPhone for Windows", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "16.3.0.264", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{110A3CCE-3D00-4E4A-8E61-EE5A4DFDA908}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "12702494-9E6A-3F5E-9441-2B7D258A639B", "DisplayName": "Microsoft .NET CoreRuntime SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "77b948c3-14c5-4c45-98af-2314885d19f0", "DisplayVersion": "1.1.27004.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{12702494-9E6A-3F5E-9441-2B7D258A639B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "12B0A225-610B-43DA-8585-E2EAD563D611", "DisplayName": "vs_filehandler_amd64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "3ff75236-49c4-35c8-8fac-79471939f5a1", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{12B0A225-610B-43DA-8585-E2EAD563D611}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "14705B25-5AC2-82AC-DB79-A35219016ABB", "DisplayName": "Windows Team Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "99b458ff-1d8c-a2a3-825c-74f7c9fc1686", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{14705B25-5AC2-82AC-DB79-A35219016ABB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "15941C7F-810D-41DF-8C5A-8D0490277AFB", "DisplayName": "Windows SDK AddOn", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "8061f133-4fe2-42ac-8145-03f3f866cf69", "DisplayVersion": "10.1.0.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{15941C7F-810D-41DF-8C5A-8D0490277AFB}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1784A8CD-F7FE-47E2-A87D-1F31E7242D0D", "DisplayName": "Microsoft .NET Framework 4.7.2 Targeting Pack", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "6f62c73d-9b2a-40d8-ba85-b5b4fc16718b", "DisplayVersion": "4.7.03062", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1784A8CD-F7FE-47E2-A87D-1F31E7242D0D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1A457607-6262-3949-63D4-E1F85A3D95FD", "DisplayName": "Windows SDK Desktop Libs x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "72552030-b67e-73bf-0a1b-4096349acea1", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1A457607-6262-3949-63D4-E1F85A3D95FD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1A9C3A1A-566B-4CFA-8B27-71FC623963BE", "DisplayName": "Microsoft .NET Framework Cumulative Intellisense Pack for Visual Studio (ENU)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "3934739b-7cc2-4c66-8103-06b1647f9f41", "DisplayVersion": "4.8.09037", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{1A9C3A1A-566B-4CFA-8B27-71FC623963BE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1EF48E65-5B07-4502-B6C2-43BC8311DAC3", "DisplayName": "vs_minshellinteropsharedmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "409e2b4d-07c2-496f-8d18-15dcc920091b", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1EF48E65-5B07-4502-B6C2-43BC8311DAC3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "1FF69612-5A91-3565-7EE3-0539A04E3B8D", "DisplayName": "Windows IoT Extension SDK Contracts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "a4673eeb-f651-0b06-6526-257e2062c6c4", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{1FF69612-5A91-3565-7EE3-0539A04E3B8D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "20d5df4e-006c-4d6d-a0dc-490d009b9786", "DisplayName": "Microsoft Windows Desktop Runtime - 5.0.17 (x64)", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{20d5df4e-006c-4d6d-a0dc-490d009b9786}\\windowsdesktop-runtime-5.0.17-win-x64.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "5.0.17.31219", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{20d5df4e-006c-4d6d-a0dc-490d009b9786}\\windowsdesktop-runtime-5.0.17-win-x64.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{20d5df4e-006c-4d6d-a0dc-490d009b9786}\\windowsdesktop-runtime-5.0.17-win-x64.exe\"  /uninstall", "GlobalSoftwareId": 970, "GlobalSoftwareName": "Microsoft Windows Desktop Runtime (Dotnet) (x64)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2293D9E3-FC5F-465F-A5C4-C57513EA8837", "DisplayName": "vs_cuitextensionmsi17", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "aaff996a-38a9-442d-9c4a-9d14b4ad6796", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{2293D9E3-FC5F-465F-A5C4-C57513EA8837}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "22AC2DC4-99C8-469F-837B-03AE342E3827", "DisplayName": "Microsoft .NET Targeting Pack - 6.0.13 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c79f66ef-56d3-7e1b-1bf3-8558a86f386a", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{22AC2DC4-99C8-469F-837B-03AE342E3827}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "22E13608-4DB0-4977-A267-3AAFA09CD54A", "DisplayName": "ClickOnce Bootstrapper Package for Microsoft .NET Framework", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "d4bb1cbc-c0f2-4f99-a2af-fee36274150c", "DisplayVersion": "4.8.09037", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{22E13608-4DB0-4977-A267-3AAFA09CD54A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "25D5B94A-E3CD-44E8-9C3A-FE320B7B38FC", "DisplayName": "Microsoft Windows Desktop Runtime - 3.1.32 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "d11c218c-53c4-7040-1770-2bccf38b3130", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{25D5B94A-E3CD-44E8-9C3A-FE320B7B38FC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "26A24AE4-039D-4CA4-87B4-2F32180361F0", "DisplayName": "Java 8 Update 361", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "57bda5c6-443c-4d65-b233-282393218000", "DisplayVersion": "8.0.3610.9", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{26A24AE4-039D-4CA4-87B4-2F32180361F0}", "GlobalSoftwareId": 310, "GlobalSoftwareName": "Java 8 SE Runtime Environment"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "26D20F81-7672-441B-9092-88E0D2409992", "DisplayName": "Microsoft Visual C++ 2022 X86 Debug Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7c1c6adf-3549-31f7-9e93-bc30719eb9da", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{26D20F81-7672-441B-9092-88E0D2409992}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "286A9ADE-A581-43E8-AA85-6F5D58C7DC88", "DisplayName": "Dell Optimizer Service", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "3.2.212.0", "DisplayIcon": "C:\\Program Files (x86)\\InstallShield Installation Information\\{286A9ADE-A581-43E8-AA85-6F5D58C7DC88}\\appic-dell-optimize-launch.ico,0", "UninstallString": "\"C:\\Program Files (x86)\\InstallShield Installation Information\\{286A9ADE-A581-43E8-AA85-6F5D58C7DC88}\\DellOptimizer.exe\" -remove -runfromtemp", "GlobalSoftwareId": 1093, "GlobalSoftwareName": "Dell Optimizer Service (Uninstall Only)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2CC6A4A7-AAC2-46C9-9DBB-3727B5954F65", "DisplayName": "Microsoft .NET Framework 4.6 Targeting Pack", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "90d2f3f7-69e3-3338-a2ef-ff616df76a87", "DisplayVersion": "4.6.00081", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2CC6A4A7-AAC2-46C9-9DBB-3727B5954F65}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2D2EBB11-484D-8F73-FA25-3FFABCE371F0", "DisplayName": "Windows SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "cdac2749-dc3f-9182-c687-e99f09677cf7", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{2D2EBB11-484D-8F73-FA25-3FFABCE371F0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "2F0ECC80-B9E4-4485-8083-CD32F22ABD92", "DisplayName": "Microsoft .NET Framework 4.6.1 SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "57a9cb2f-ffad-35dd-b80d-a86f98f488c3", "DisplayVersion": "4.6.01055", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{2F0ECC80-B9E4-4485-8083-CD32F22ABD92}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "2F21FBB0-B45A-47E9-AE54-63AC3BDF8570", "DisplayName": "SnapAgent", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0282cc1b-1171-4dbf-86e9-9e11ba0ad76c", "DisplayVersion": "1.0.0.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{2F21FBB0-B45A-47E9-AE54-63AC3BDF8570}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "30092A0E-1D50-8D66-E5EB-01F6AA8C1FFE", "DisplayName": "Windows SDK Desktop Headers arm64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "2654913e-0c6a-556a-eb13-80b15edc9b39", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{30092A0E-1D50-8D66-E5EB-01F6AA8C1FFE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "31A3EB09-E226-B0E5-FA70-FF4B3CAD2ECF", "DisplayName": "Windows SDK for Windows Store Apps DirectX x86 Remote", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "f341c4ba-213e-052a-b943-738f6a79eb61", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{31A3EB09-E226-B0E5-FA70-FF4B3CAD2ECF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "31B6D021-BC97-82C5-9C42-16AB86C37215", "DisplayName": "Windows SDK Facade Windows WinMD Versioned", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "9ea55672-3421-4c99-fc0d-532fb366aba6", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{31B6D021-BC97-82C5-9C42-16AB86C37215}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "35b83883-40fa-423c-ae73-2aff7e1ea820", "DisplayName": "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40649", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{35b83883-40fa-423c-ae73-2aff7e1ea820}\\vcredist_x86.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "12.0.40649.5", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{35b83883-40fa-423c-ae73-2aff7e1ea820}\\vcredist_x86.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{35b83883-40fa-423c-ae73-2aff7e1ea820}\\vcredist_x86.exe\"  /uninstall", "GlobalSoftwareId": 361, "GlobalSoftwareName": "Microsoft Visual C++ 2013 Redistributable (x86)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3826E149-22D5-470D-B8A7-D11244EA2720", "DisplayName": "vs_clickoncebootstrappermsires", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c85be10c-2863-4daa-bd4f-052ac4f7f440", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{3826E149-22D5-470D-B8A7-D11244EA2720}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3A8F388C-D0F7-0A66-0EDE-D477B1B1B2F4", "DisplayName": "WinRT Intellisense IoT - Other Languages", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7c47c8eb-5d53-821e-fd90-2b804aaff338", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{3A8F388C-D0F7-0A66-0EDE-D477B1B1B2F4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "3F737DA8-C5B7-8740-6B07-BA73B5E62CDF", "DisplayName": "Windows Desktop Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "90e38413-50cd-7d42-6783-b3edae6dd2b9", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{3F737DA8-C5B7-8740-6B07-BA73B5E62CDF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "408DE8D9-0019-492A-8CD2-B84BEAE25E12", "DisplayName": "Visual C++ Library CRT Desktop Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{408DE8D9-0019-492A-8CD2-B84BEAE25E12}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "41167EB6-66D6-4E1B-8811-730EE2A097B1", "DisplayName": "Xamarin Remoted iOS Simulator", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "ce4792a8-6eaa-4cd7-aac1-d73563d087ef", "DisplayVersion": "17.4.0.477", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{41167EB6-66D6-4E1B-8811-730EE2A097B1}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "41D5B674-7513-11ED-95BA-54BF64A63C26", "DisplayName": "Foxit PDF Editor", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "02a12093-aad6-4f1b-911c-b535a427def0", "DisplayVersion": "12.1.0.15250", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{41D5B674-7513-11ED-95BA-54BF64A63C26}", "GlobalSoftwareId": 1, "GlobalSoftwareName": "Foxit PhantomPDF Business"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4308116D-A763-144D-DC81-6243BE6B2ADF", "DisplayName": "WinRT Intellisense UAP - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "edebebdd-1751-bd65-37fc-23718ec4a404", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{4308116D-A763-144D-DC81-6243BE6B2ADF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4349F57C-0C9C-C16B-9A40-E7B406C451E2", "DisplayName": "SDK ARM Redistributables", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7a7df351-3a4b-2719-85bc-2094d0801faf", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{4349F57C-0C9C-C16B-9A40-E7B406C451E2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "43E10FF2-0E80-3E13-8AD8-302355855BB3", "DisplayName": "Microsoft ASP.NET Core 6.0.13 Shared Framework (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "fff2ce7f-f48c-3c84-b23c-11bc4593f3f2", "DisplayVersion": "6.0.13.22580", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{43E10FF2-0E80-3E13-8AD8-302355855BB3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-13T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "46D9F20D-CE88-41AA-857F-37AB4786B1FF", "DisplayName": "Microsoft Intune Management Extension", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "9fe9701c-0f89-40b4-b77a-aa65607e87d8", "DisplayVersion": "1.63.103.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{46D9F20D-CE88-41AA-857F-37AB4786B1FF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "48A8F171-52F2-372B-8414-EA50617708BE", "DisplayName": "Microsoft .NET CoreRuntime For CoreCon", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "f125e176-597c-4e5d-837e-c37c2aacb2f7", "DisplayVersion": "1.0.0.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{48A8F171-52F2-372B-8414-EA50617708BE}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "49477074-4D52-40F3-852B-CA8BE5253817", "DisplayName": "vs_codeduitestframeworkmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "296d5f66-dbfd-40c9-9a21-c8773bbab570", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{49477074-4D52-40F3-852B-CA8BE5253817}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "49559293-3192-40D3-864C-5AB88E744A79", "DisplayName": "vs_FileTracker_Singleton", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "92eb809e-f05d-4c87-90da-4baf291f6d8c", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{49559293-3192-40D3-864C-5AB88E744A79}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4A03706F-666A-4037-7777-5F2748764D10", "DisplayName": "Java Auto Updater", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "4caacc5a-b5f0-4b96-7777-ca8f526664c7", "DisplayVersion": "2.8.361.9", "DisplayIcon": null, "UninstallString": null, "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4B87C1C9-F1F5-4308-BE65-E23B51BACC60", "DisplayName": "Microsoft .NET Runtime - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "790a8ef6-58c3-4822-5327-42d5cbdb80d1", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4B87C1C9-F1F5-4308-BE65-E23B51BACC60}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4EDC2B14-E02A-37B9-810B-336C3DDA6424", "DisplayName": "Microsoft ASP.NET Core 7.0.2 Shared Framework (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "92c37e7f-cd50-3224-bf6a-53dfc5e3aaef", "DisplayVersion": "7.0.2.22606", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4EDC2B14-E02A-37B9-810B-336C3DDA6424}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4F0D14D8-BE9F-4879-B00D-6BDA4018C528", "DisplayName": "windows_toolscorepkg", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "2a9480c0-df19-458c-b27a-119aad2d797d", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{4F0D14D8-BE9F-4879-B00D-6BDA4018C528}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "4FFCA92E-5974-473C-9239-516D02E11AEA", "DisplayName": "Microsoft .NET Host FX Resolver - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1dd0f20f-5b56-4649-cb1e-fa20fa758ac6", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{4FFCA92E-5974-473C-9239-516D02E11AEA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "54E5E684-132E-23D3-CBF6-962122E2C568", "DisplayName": "Windows SDK Desktop Tools arm64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "fafcf826-867f-31fb-627c-178f34ecbae5", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{54E5E684-132E-23D3-CBF6-962122E2C568}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5527C189-8BB6-4E23-A49A-17DF29EF8019", "DisplayName": "vs_communitymsires", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "f188287e-53a8-3b94-8150-608c1d805b3b", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5527C189-8BB6-4E23-A49A-17DF29EF8019}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "56373DD6-9A8E-4C0C-95F6-F64DF2054A6F", "DisplayName": "vs_filehandler_x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "cb16307a-e8c6-43bf-a66f-55a54e56e4d5", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{56373DD6-9A8E-4C0C-95F6-F64DF2054A6F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "58791073-AD3F-3AF3-BA24-89E84F780D6B", "DisplayName": "Microsoft ASP.NET Web Tools Packages 17.0 - ENU", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "235e0aa0-fa75-49e3-89e5-5c7e067eb2e9", "DisplayVersion": "17.0.20604.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{58791073-AD3F-3AF3-BA24-89E84F780D6B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "58A3001D-B675-4D67-A5A1-0FA9F08CF7CA", "DisplayName": "ConnectWise Automate Remote Agent", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "22.0.440", "DisplayIcon": null, "UninstallString": null, "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "59650A2A-3839-46EC-9D9C-6B3B1C743C55", "DisplayName": "Microsoft .NET Runtime - 5.0.17 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "12e9ec96-5dd8-5935-1ea2-b2409b403ee6", "DisplayVersion": "40.68.31213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{59650A2A-3839-46EC-9D9C-6B3B1C743C55}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5B64C01B-2798-A2F6-89C3-AC03906F8788", "DisplayName": "Windows IoT Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "18dbaf13-27a3-dc17-4d59-e2d38d1c8908", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5B64C01B-2798-A2F6-89C3-AC03906F8788}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5C571D16-6AAC-44DD-B98F-E07BD28EB774", "DisplayName": "Visual C++ Library CRT Appx Resource Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "475ab7a7-79a5-3404-876a-842a2768c2d5", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5C571D16-6AAC-44DD-B98F-E07BD28EB774}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "5d0723d3-cff7-4e07-8d0b-ada737deb5e6", "DisplayName": "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40649", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{5d0723d3-cff7-4e07-8d0b-ada737deb5e6}\\vcredist_x64.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "12.0.40649.5", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{5d0723d3-cff7-4e07-8d0b-ada737deb5e6}\\vcredist_x64.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{5d0723d3-cff7-4e07-8d0b-ada737deb5e6}\\vcredist_x64.exe\"  /uninstall", "GlobalSoftwareId": 360, "GlobalSoftwareName": "Microsoft Visual C++ 2013 Redistributable (x64)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5E6DAEFE-CE30-4268-A2EC-80CEB393AC70", "DisplayName": "icecap_collectionresources", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "dcd18d82-604c-3235-b16d-502c445b2417", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5E6DAEFE-CE30-4268-A2EC-80CEB393AC70}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5F01B3C4-9BEC-465D-9C68-BB97D381FFAD", "DisplayName": "Microsoft .NET Framework 4.6.2 SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "09fae6ab-5923-38cf-942d-06efc97fc80e", "DisplayVersion": "4.6.01590", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{5F01B3C4-9BEC-465D-9C68-BB97D381FFAD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "5F1D4DE5-BFF3-503B-D05F-40ED6927DE17", "DisplayName": "Windows Team Extension SDK Contracts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "9a32795a-6c22-dc4c-4ebf-83770e87946d", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{5F1D4DE5-BFF3-503B-D05F-40ED6927DE17}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6221E085-FE37-1068-E6D7-9D08ABD32AE2", "DisplayName": "Windows SDK Desktop Libs arm", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "501a320b-9e28-37b9-111d-8a52e7053c51", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6221E085-FE37-1068-E6D7-9D08ABD32AE2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "667150AA-8DA3-433E-91EC-88BD8730D32A", "DisplayName": "vs_CoreEditorFonts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "98786dd8-a299-4c50-a5bc-10aa8b9949e3", "DisplayVersion": "17.4.33213", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{667150AA-8DA3-433E-91EC-88BD8730D32A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "68F11757-8C35-BEB6-2AED-6F7C6CC5B8C0", "DisplayName": "Windows SDK for Windows Store Apps Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "2eedbc6c-430a-c096-1ce5-b5ef346c875d", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{68F11757-8C35-BEB6-2AED-6F7C6CC5B8C0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6A095B1E-4950-3F81-9E38-C0781147C932", "DisplayName": "Microsoft ASP.NET Core 5.0.17 Shared Framework (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "bd68daea-a7df-346e-860b-9e3b2a026494", "DisplayVersion": "5.0.17.22215", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{6A095B1E-4950-3F81-9E38-C0781147C932}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6A7EA630-41A1-455D-849D-2AA2D1420236", "DisplayName": "vs_tipsmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "74754c8f-3189-4213-bbc9-0314f02cf222", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6A7EA630-41A1-455D-849D-2AA2D1420236}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6AF210BE-348B-4156-8B2E-CD57FC573ABD", "DisplayName": "vs_clickoncebootstrappermsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b7dbc26e-49fa-465b-ac33-6f53e38288a3", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6AF210BE-348B-4156-8B2E-CD57FC573ABD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "6ba9fb5e-8366-4cc4-bf65-25fe9819b2fc", "DisplayName": "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.34.31931", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{6ba9fb5e-8366-4cc4-bf65-25fe9819b2fc}\\VC_redist.x86.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "14.34.31931.0", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{6ba9fb5e-8366-4cc4-bf65-25fe9819b2fc}\\VC_redist.x86.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{6ba9fb5e-8366-4cc4-bf65-25fe9819b2fc}\\VC_redist.x86.exe\"  /uninstall", "GlobalSoftwareId": 363, "GlobalSoftwareName": "Microsoft Visual C++ 2015-2022 Redistributable (x86)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6D5C07E3-6A4D-4126-A7A7-8B6EFFE16E51", "DisplayName": "Entity Framework 6.2.0 Tools  for Visual Studio 2022", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "25c15c9a-bf44-418a-b02b-26744b72c389", "DisplayVersion": "6.2.0.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{6D5C07E3-6A4D-4126-A7A7-8B6EFFE16E51}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6F1B1243-7C12-5398-F575-5102906569A6", "DisplayName": "Windows SDK Desktop Headers x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "08f39866-b917-aa58-99fd-348ef3e473dc", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{6F1B1243-7C12-5398-F575-5102906569A6}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "6FE1E993-8813-44E5-945A-612BB76A99FA", "DisplayName": "Microsoft .NET Targeting Pack - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "49d6799e-5a0d-52f6-348b-98400484ff80", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{6FE1E993-8813-44E5-945A-612BB76A99FA}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7146BE29-EB2D-45A1-A9BD-E7E397397B34", "DisplayName": "Visual C++ Library CRT Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "19b6349a-72c6-3cbb-bc5c-b84e962d15f5", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7146BE29-EB2D-45A1-A9BD-E7E397397B34}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "721FAF11-41E2-45DB-949A-7E3E510A0EF0", "DisplayName": "vs_minshellmsires", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "816b218f-4ecd-351e-a416-6ecfb8899ffb", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{721FAF11-41E2-45DB-949A-7E3E510A0EF0}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "737FDDA7-B944-4CB5-92D9-3D56373BD301", "DisplayName": "Microsoft NetStandard SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0b6ae04b-db8c-42c6-b25c-b577bf389a9d", "DisplayVersion": "15.0.51105", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{737FDDA7-B944-4CB5-92D9-3D56373BD301}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7907F380-3CFD-247C-9DFC-A23CED2012F8", "DisplayName": "Windows SDK for Windows Store Apps Metadata", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7eac525a-5685-158d-000f-d7a16081f697", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7907F380-3CFD-247C-9DFC-A23CED2012F8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7B907A0B-2606-42EB-A880-80E1F6AA4C48", "DisplayName": "vs_clickoncesigntoolmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "5c6f5296-ac5d-40fd-ae20-3c5e2e704077", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7B907A0B-2606-42EB-A880-80E1F6AA4C48}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7CAF3DA2-79F6-C0DB-6C56-2462C8C4914C", "DisplayName": "Windows SDK Desktop Libs x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "125a50c2-ea6a-035b-ad95-f35a1e83d02b", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7CAF3DA2-79F6-C0DB-6C56-2462C8C4914C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "7DBE357D-2AA2-7B68-267E-F8DAEB182D6F", "DisplayName": "Windows SDK Redistributables", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e3dabcbe-5470-08ef-a072-8e07cf7aafc5", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{7DBE357D-2AA2-7B68-267E-F8DAEB182D6F}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "81D4E442-F6C5-DF4B-DEF8-76E51ACC56F8", "DisplayName": "Windows App Certification Kit SupportedApiList x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "61d74743-f687-83eb-3108-488434ff783f", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{81D4E442-F6C5-DF4B-DEF8-76E51ACC56F8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "823E92A6-7572-174D-0671-95A55785F991", "DisplayName": "WinRT Intellisense IoT - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e2c21107-7b53-68a7-4b68-dc26c2b49947", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{823E92A6-7572-174D-0671-95A55785F991}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "82931C8D-83CC-CF2E-F4FA-9AF0829BCA22", "DisplayName": "Windows SDK Desktop Libs arm64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "cb02596f-36c8-0ca5-db94-36dd63259023", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{82931C8D-83CC-CF2E-F4FA-9AF0829BCA22}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "837DD890-14CE-9EB3-E8E1-F96A7EE5CFE5", "DisplayName": "WinRT Intellisense Desktop - Other Languages", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7b83767c-37f3-3c56-ce76-69c3d799e605", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{837DD890-14CE-9EB3-E8E1-F96A7EE5CFE5}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "839C2D45-DDF6-432C-A6A2-C6AF2EF281BF", "DisplayName": "Microsoft TestPlatform SDK Local Feed", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "2bca72b7-ea6d-47cf-9b02-f7d6b96e5110", "DisplayVersion": "17.0.0.5175695", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{839C2D45-DDF6-432C-A6A2-C6AF2EF281BF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-02-13T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "83EEB97D-226F-4733-B3A7-7CE1EFF8CD62", "DisplayName": "ImmyBot Agent", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "5b7db838-1bec-43df-9f28-1288cacffd7a", "DisplayVersion": "0.55.1.19466", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{83EEB97D-226F-4733-B3A7-7CE1EFF8CD62}", "GlobalSoftwareId": 657, "GlobalSoftwareName": "ImmyBot Agent"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "841FE4B1-2C3F-4304-A686-6DF41B4CC1A1", "DisplayName": "Microsoft .NET Core Runtime - 3.1.32 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b0e21278-5877-58fb-675e-7c6ebac794f8", "DisplayVersion": "24.192.31915", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{841FE4B1-2C3F-4304-A686-6DF41B4CC1A1}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "85516DED-4402-44CD-9D01-1D1F3D0C0178", "DisplayName": "Microsoft Visual Studio Setup WMI Provider", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0a671536-acc6-4075-bcdf-2f1fa4c794ea", "DisplayVersion": "3.4.1128.26111", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{85516DED-4402-44CD-9D01-1D1F3D0C0178}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "85CECCAF-91AA-3D0E-8AA5-B68904EEBB85", "DisplayName": "Microsoft ASP.NET Core 7.0.2 Targeting Pack (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "743aaaa0-3c40-3392-b8f2-836d6078e6b7", "DisplayVersion": "7.0.2.22606", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{85CECCAF-91AA-3D0E-8AA5-B68904EEBB85}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "87EA745D-32DA-4DCA-9ED4-BF4BA6232E1E", "DisplayName": "Microsoft .NET Runtime - 6.0.13 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "27294a3c-5e37-6e29-ddd7-c9059f78fa25", "DisplayVersion": "48.55.52137", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{87EA745D-32DA-4DCA-9ED4-BF4BA6232E1E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8BC3EEC9-090F-4C53-A8DA-1BEC913040F9", "DisplayName": "Microsoft .NET Framework 4.6.1 Targeting Pack", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "dc6dcfe7-bb75-448d-8999-401cd18bc1d5", "DisplayVersion": "4.6.01055", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{8BC3EEC9-090F-4C53-A8DA-1BEC913040F9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8C14D3DF-69AD-443A-A607-B4B1BE9575CC", "DisplayName": "vs_BlendMsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7ce60269-ce5c-48da-aac4-db821e49548c", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{8C14D3DF-69AD-443A-A607-B4B1BE9575CC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8C352959-35A5-40CA-A49C-91B349AB2778", "DisplayName": "vs_SQLClickOnceBootstrappermsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "391994fb-417f-4f64-9ee5-b9cbd1974599", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{8C352959-35A5-40CA-A49C-91B349AB2778}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8E3AB865-9E38-1E5F-7B49-C8E3A70C6303", "DisplayName": "WinAppDeploy", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "0b63466e-7070-775a-62c3-5b667a7da6a7", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{8E3AB865-9E38-1E5F-7B49-C8E3A70C6303}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8EC1DC78-0A65-B6AC-664D-DFB3AC8E9736", "DisplayName": "Universal CRT Redistributable", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "bfbbca0e-37cb-a252-dea3-030b80884128", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{8EC1DC78-0A65-B6AC-664D-DFB3AC8E9736}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "8EDE105B-885A-D173-50F8-F50F39C51CF9", "DisplayName": "Windows Mobile Extension SDK Contracts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c6e331c1-37c7-641f-de86-c763f7c67a08", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{8EDE105B-885A-D173-50F8-F50F39C51CF9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90819A24-C7DD-4956-8CBE-AE50B43D020D", "DisplayName": "icecap_collection_neutral", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "936df2d2-0e30-316f-a255-1854d6003b9b", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{90819A24-C7DD-4956-8CBE-AE50B43D020D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "90AAE845-42CE-EC12-4041-8ADF7BE765E7", "DisplayName": "Universal CRT Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "fdfc8b3e-fecd-652e-abbb-06e3ec688308", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{90AAE845-42CE-EC12-4041-8ADF7BE765E7}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "93F9D7FA-F2FD-837D-E53F-D79767071E44", "DisplayName": "Windows SDK EULA", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "fcd7606c-7339-37a9-a399-3d5d2eea704e", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{93F9D7FA-F2FD-837D-E53F-D79767071E44}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "949C0535-171C-480F-9CF4-D25C9E60FE88", "DisplayName": "Microsoft .NET Framework 4.8 SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e5045af3-53d3-335b-b1e0-499c2a86c96c", "DisplayVersion": "4.8.03928", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{949C0535-171C-480F-9CF4-D25C9E60FE88}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "95A498A9-5E6E-5779-1523-876224F41F94", "DisplayName": "MSI Development Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "8dd9bb34-cae8-8212-3090-7db1052cf408", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{95A498A9-5E6E-5779-1523-876224F41F94}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "96cf40b0-81d6-43ed-ad0e-611e67899196", "DisplayName": "Microsoft Windows Desktop Runtime - 6.0.13 (x64)", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{96cf40b0-81d6-43ed-ad0e-611e67899196}\\windowsdesktop-runtime-6.0.13-win-x64.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "6.0.13.32001", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{96cf40b0-81d6-43ed-ad0e-611e67899196}\\windowsdesktop-runtime-6.0.13-win-x64.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{96cf40b0-81d6-43ed-ad0e-611e67899196}\\windowsdesktop-runtime-6.0.13-win-x64.exe\"  /uninstall", "GlobalSoftwareId": 970, "GlobalSoftwareName": "Microsoft Windows Desktop Runtime (Dotnet) (x64)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "98D28609-D108-43DD-91C8-636C1B8A9087", "DisplayName": "vcpp_crt.redist.clickonce", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "67db1df0-8c31-37ab-95a3-6f05db7b0f04", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{98D28609-D108-43DD-91C8-636C1B8A9087}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "98D5C5AA-993C-1371-C7AF-8F8A0E8CAF86", "DisplayName": "Universal CRT Headers Libraries and Sources", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b591aa94-12f0-bc86-dfb3-464389aeed32", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{98D5C5AA-993C-1371-C7AF-8F8A0E8CAF86}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9B95262A-EC82-31AA-BFC9-FF4C1FB21C48", "DisplayName": "Microsoft ASP.NET Diagnostic Pack for Visual Studio", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "eba4c6f5-d167-495e-9add-6b0f635d7e2c", "DisplayVersion": "17.4.326.54890", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{9B95262A-EC82-31AA-BFC9-FF4C1FB21C48}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9D4033AD-2990-469B-A2CC-CEE9A707106A", "DisplayName": "vs_Graphics_Singletonx86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b55a551c-166e-366c-830c-63b57d142e54", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{9D4033AD-2990-469B-A2CC-CEE9A707106A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "9dd30d6d-7999-4e32-9295-a2d7ece703ba", "DisplayName": "Dell SupportAssist Remediation", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{9dd30d6d-7999-4e32-9295-a2d7ece703ba}\\DellSupportAssistRemediationServiceInstaller.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "5.5.5.16208", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{9dd30d6d-7999-4e32-9295-a2d7ece703ba}\\DellSupportAssistRemediationServiceInstaller.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{9dd30d6d-7999-4e32-9295-a2d7ece703ba}\\DellSupportAssistRemediationServiceInstaller.exe\"  /uninstall", "GlobalSoftwareId": 838, "GlobalSoftwareName": "Dell SupportAssist Remediation (Uninstall Only)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "9F2641F6-5F32-48BC-8B45-53A0D2F5EC85", "DisplayName": "vs_cuitextensionmsi17_x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b119f6d5-5b8f-4b60-a7e6-d0c460ae895c", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{9F2641F6-5F32-48BC-8B45-53A0D2F5EC85}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A06212FE-3C20-31C7-F88A-46673EF72E83", "DisplayName": "WinRT Intellisense PPI - Other Languages", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "ad043d16-a9a6-98f7-ab90-edb50e8dcc87", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{A06212FE-3C20-31C7-F88A-46673EF72E83}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A18D4C2A-07A8-40E4-9797-DD324E6EA4FC", "DisplayName": "Microsoft .NET Framework 4.6.2 Targeting Pack", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "5d991f6d-92a6-4ec2-9cfd-6075dccd6f62", "DisplayVersion": "4.6.01590", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A18D4C2A-07A8-40E4-9797-DD324E6EA4FC}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A1DA436A-2FA4-FDC2-9B0A-FBB40CF0499D", "DisplayName": "Windows SDK for Windows Store Apps Headers", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "41327195-e5d9-bda2-6fbc-45cb2b9daa61", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{A1DA436A-2FA4-FDC2-9B0A-FBB40CF0499D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A4EA9EE5-7CFF-4C5F-B159-B9B4E5D2BDE2", "DisplayName": "Microsoft .NET Framework 4.8 Targeting Pack (ENU)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b2691d82-1b17-4827-b482-d664c7d3f862", "DisplayVersion": "4.8.03761", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A4EA9EE5-7CFF-4C5F-B159-B9B4E5D2BDE2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A58EBFF5-0BF1-3F07-889B-31B20843BA1C", "DisplayName": "Microsoft ASP.NET Core 6.0.13 Targeting Pack (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "aa65e1b6-9a41-367e-8b0f-7dc5c7d8d77f", "DisplayVersion": "6.0.13.22580", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A58EBFF5-0BF1-3F07-889B-31B20843BA1C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A829BD53-4BE5-23F2-C4A8-026CB5D47ECF", "DisplayName": "Universal CRT Tools x86", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "15acd7a6-6f43-f46c-6dfc-10cc09a704fc", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{A829BD53-4BE5-23F2-C4A8-026CB5D47ECF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A8589745-51BC-3963-B4E9-201CF8693538", "DisplayName": "Microsoft Visual C++ 2013 x86 Additional Runtime - 12.0.40649", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "936e696b-0c8d-3a48-98df-344fea4e1139", "DisplayVersion": "12.0.40649", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{A8589745-51BC-3963-B4E9-201CF8693538}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "A99B19D4-7F87-03E5-B4A8-80420ECD7F53", "DisplayName": "Windows SDK ARM Desktop Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "d239df29-8cf7-ce98-416e-600d4ed982b9", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{A99B19D4-7F87-03E5-B4A8-80420ECD7F53}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "AA30629A-F6B4-48C7-A7AE-912A461E9249", "DisplayName": "Visual C++ Library CRT Desktop Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "730e79c6-b237-3856-8146-4060095b1cd7", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{AA30629A-F6B4-48C7-A7AE-912A461E9249}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "AB1BDF73-7393-42CE-812D-9A90918814D5", "DisplayName": "Microsoft Visual C++ 2022 X86 Minimum Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "65e5bd06-6392-3027-8c26-853107d3cf1a", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{AB1BDF73-7393-42CE-812D-9A90918814D5}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "ab3f7261-beee-49b8-b31a-27dd1dfd122d", "DisplayName": "Dell SupportAssist OS Recovery Plugin for Dell Update", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{ab3f7261-beee-49b8-b31a-27dd1dfd122d}\\DellUpdateSupportAssistPlugin.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "5.5.5.16208", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{ab3f7261-beee-49b8-b31a-27dd1dfd122d}\\DellUpdateSupportAssistPlugin.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{ab3f7261-beee-49b8-b31a-27dd1dfd122d}\\DellUpdateSupportAssistPlugin.exe\"  /uninstall", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "AC86466D-28FE-4EA0-A44D-617D3715AC51", "DisplayName": "ScreenConnect Client (cb44286ee78cfa8e)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "501f249a-148e-c2ae-cb44-286ee78cfa8e", "DisplayVersion": "22.9.10589.8370", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{AC86466D-28FE-4EA0-A44D-617D3715AC51}", "GlobalSoftwareId": 780, "GlobalSoftwareName": "ConnectWise Control (ScreenConnect)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ACBBFCA9-A48A-425A-BF50-B6FB8EFE7934", "DisplayName": "vs_vswebprotocolselectormsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "aa01b09c-39d0-4651-b2bd-baa02637cce4", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{ACBBFCA9-A48A-425A-BF50-B6FB8EFE7934}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "ACFA81A9-FD2F-4731-BE64-9163E3E9FF58", "DisplayName": "Microsoft Windows Desktop Runtime - 6.0.13 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "89ec1260-51f5-460e-4e66-0b0a51ba4f18", "DisplayVersion": "48.55.53270", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{ACFA81A9-FD2F-4731-BE64-9163E3E9FF58}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "AD3B32A0-5F71-DE89-B55A-9E85964186D8", "DisplayName": "Windows SDK for Windows Store Apps Contracts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "ce462ec7-da2e-5ab1-59a6-60bab964e870", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{AD3B32A0-5F71-DE89-B55A-9E85964186D8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B0CB401F-F1EF-E63C-8191-9A447FC24C58", "DisplayName": "Windows SDK for Windows Store Managed Apps Libs", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "655b95a6-0bd7-e72a-5b62-3213d49e48bc", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B0CB401F-F1EF-E63C-8191-9A447FC24C58}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B1CFE19E-298A-4D14-BACD-CAA36AC4895B", "DisplayName": "Microsoft Visual Studio Setup Configuration", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1571205c-bad1-4237-bfe6-b77e622c51db", "DisplayVersion": "3.4.1128.26111", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B1CFE19E-298A-4D14-BACD-CAA36AC4895B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B517DBD3-B542-4FC8-9957-FFB2C3E65D1D", "DisplayName": "Microsoft .NET Framework 4.7.2 Targeting Pack (ENU)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b652a957-1318-471d-9d3e-62b44d9576b4", "DisplayVersion": "4.7.03062", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B517DBD3-B542-4FC8-9957-FFB2C3E65D1D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B56EF9C1-B982-45B1-BEB5-3C8F50B21CF2", "DisplayName": "vs_communitysharedmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "8c27f2b4-9a95-48f3-af0e-94d3ccf62b36", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B56EF9C1-B982-45B1-BEB5-3C8F50B21CF2}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B5CB1A18-CEB8-464B-B83C-556D1DDBF3B5", "DisplayName": "Microsoft Windows Desktop Runtime - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "302fd0a6-5e4f-7d99-b557-a8ad9545a5e4", "DisplayVersion": "56.11.53361", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{B5CB1A18-CEB8-464B-B83C-556D1DDBF3B5}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B98ADDC9-A38E-418A-8625-22E47564EC53", "DisplayName": "vs_networkemulationmsi_x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "3c3a0fd6-3b1c-4534-b809-8cc510e97213", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B98ADDC9-A38E-418A-8625-22E47564EC53}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "B9A2EE66-E1B9-ED85-E75B-041A348EB46D", "DisplayName": "Windows SDK DirectX x86 Remote", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "620ac2fc-fc2b-3bfd-42a8-357bb901fee6", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{B9A2EE66-E1B9-ED85-E75B-041A348EB46D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BAAF5851-0759-422D-A1E9-90061B597188", "DisplayName": "Microsoft .NET Framework 4.8 Targeting Pack", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "4d94e3b0-1cc6-470f-814a-e842c12b151c", "DisplayVersion": "4.8.03761", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BAAF5851-0759-422D-A1E9-90061B597188}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BD786819-AA80-7B18-125D-AA03AAF2759B", "DisplayName": "SDK ARM Additions", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "948b4170-088b-f43c-4711-1d5949d505e6", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{BD786819-AA80-7B18-125D-AA03AAF2759B}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "BDC8107C-65E1-41C2-BE5E-8E27D70AB3B4", "DisplayName": "Microsoft .NET Host - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1fc060bc-5abd-7c1e-187d-2054fb4caf23", "DisplayVersion": "56.11.53349", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{BDC8107C-65E1-41C2-BE5E-8E27D70AB3B4}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C14BA668-6C98-45BC-9F48-EDB4128B21D1", "DisplayName": "vs_cuitcommoncoremsi17", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "709a5a8b-5d35-4872-a9d4-05b8410cbf48", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C14BA668-6C98-45BC-9F48-EDB4128B21D1}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C1C4EDF1-D834-4FE9-A945-24EBC2574A0C", "DisplayName": "Visual C++ Library CRT ARM64 Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c43e6482-5caf-30a3-b02e-c35c9acf5cea", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C1C4EDF1-D834-4FE9-A945-24EBC2574A0C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C2662EFF-06E6-4FD1-9D6D-FDCA91025757", "DisplayName": "Microsoft Visual C++ 2022 X86 Additional Runtime - 14.34.31931", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c78b8e51-0c65-377e-85d1-282f689fe505", "DisplayVersion": "14.34.31931", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C2662EFF-06E6-4FD1-9D6D-FDCA91025757}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "C28B5613-E59B-47BD-9D33-441496172A43", "DisplayName": "ITSPlatform", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "18f39771-f9d8-4cfd-9654-f6c67c8ad9f4", "DisplayVersion": "3.0.3", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C28B5613-E59B-47BD-9D33-441496172A43}", "GlobalSoftwareId": 390, "GlobalSoftwareName": "ConnectWise Command (Continuum) Agent"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C2C8E71E-42CF-3CFE-8CFB-F8F96670C190", "DisplayName": "Microsoft ASP.NET Core 3.1.32 Shared Framework (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b57011fb-f57f-32a8-90d5-f805a7c186c8", "DisplayVersion": "3.1.32.22566", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C2C8E71E-42CF-3CFE-8CFB-F8F96670C190}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C32CB038-8A83-4860-853F-9168214E3536", "DisplayName": "vs_minshellsharedmsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "f50bacc4-5d9b-4946-891d-0ec8b051445e", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C32CB038-8A83-4860-853F-9168214E3536}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C47F0820-6424-AE53-6BAF-2D41E829C855", "DisplayName": "Windows SDK Desktop Headers x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "65a603e2-5239-7866-f5b5-bf6dfd5835bd", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C47F0820-6424-AE53-6BAF-2D41E829C855}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C6DF1343-B58A-44E4-B3FB-4C05649A7B59", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 7.0.2 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "a1e87b30-5b59-4fa8-e189-3a39f21a8bb4", "DisplayVersion": "56.11.53361", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C6DF1343-B58A-44E4-B3FB-4C05649A7B59}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "C719DC4C-8FB0-4BB1-B622-A165776D8AF8", "DisplayName": "Yarn", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "241362e6-53bb-4a50-9c58-15f95734e43d", "DisplayVersion": "1.22.19", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C719DC4C-8FB0-4BB1-B622-A165776D8AF8}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C756420B-C91C-4410-8092-F49C24CEF594", "DisplayName": "Microsoft UniversalWindowsPlatform SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c94381db-3c75-4efa-8544-7c23a9b9af14", "DisplayVersion": "15.9.16", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C756420B-C91C-4410-8092-F49C24CEF594}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C7C68DA0-8501-16DD-1E6A-6C34AAA28F21", "DisplayName": "Windows SDK Desktop Headers arm", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "acb6fd0e-fafd-5f8b-293d-8c37332ea826", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C7C68DA0-8501-16DD-1E6A-6C34AAA28F21}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C80951BD-6904-474F-BBC5-03A6C777F37C", "DisplayName": "Microsoft .NET Framework 4.6.2 Targeting Pack (ENU)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "dad199ad-a912-4619-bf30-446d37311deb", "DisplayVersion": "4.6.01590", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{C80951BD-6904-474F-BBC5-03A6C777F37C}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C8891AD2-C223-45CD-A9BE-617A68923B61", "DisplayName": "IntelliTraceProfilerProxy", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e6d8e65c-7be6-42bb-86fb-72418fb4401e", "DisplayVersion": "15.0.21225.01", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C8891AD2-C223-45CD-A9BE-617A68923B61}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "C8977C7D-F685-8282-3C78-6276E03E3B32", "DisplayName": "Windows SDK for Windows Store Apps Libs", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "cbbade89-fac6-efad-31f8-5c9f5a85534e", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{C8977C7D-F685-8282-3C78-6276E03E3B32}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CAC88080-438A-48B3-9338-D2A49DF03045", "DisplayName": "vs_codecoveragemsi", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "2033c41b-7edf-345c-b140-22e2a34e50d6", "DisplayVersion": "17.4.33006", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{CAC88080-438A-48B3-9338-D2A49DF03045}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CF83D17D-FB70-21A1-36E4-37EE7EC1B587", "DisplayName": "Kits Configuration Installer", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e3131ebe-c8d6-e25e-c451-dc7929ece129", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{CF83D17D-FB70-21A1-36E4-37EE7EC1B587}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-23T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "CF95CED4-3A1E-4486-B7FA-428C25D617ED", "DisplayName": "Dell Digital Delivery Services", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "1ede92d0-6392-4814-bd91-970291e1ba60", "DisplayVersion": "5.0.64.0", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{CF95CED4-3A1E-4486-B7FA-428C25D617ED}", "GlobalSoftwareId": 1224, "GlobalSoftwareName": "Dell Digital Delivery Services (Uninstall Only)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "CFC51C75-8F08-1968-BC86-70A396D353D9", "DisplayName": "Windows SDK for Windows Store Apps", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "c3398a08-4d06-1fa4-cdaf-858f22397dc8", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{CFC51C75-8F08-1968-BC86-70A396D353D9}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D307C8A5-7335-056E-6E64-E28544674EFD", "DisplayName": "WinRT Intellisense UAP - Other Languages", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "9efa6ef1-4dcc-5ffe-eb72-7312a0ca06da", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D307C8A5-7335-056E-6E64-E28544674EFD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "d4cecf3b-b68f-4995-8840-52ea0fab646e", "DisplayName": "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.34.31931", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{d4cecf3b-b68f-4995-8840-52ea0fab646e}\\VC_redist.x64.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "14.34.31931.0", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{d4cecf3b-b68f-4995-8840-52ea0fab646e}\\VC_redist.x64.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{d4cecf3b-b68f-4995-8840-52ea0fab646e}\\VC_redist.x64.exe\"  /uninstall", "GlobalSoftwareId": 362, "GlobalSoftwareName": "Microsoft Visual C++ 2015-2022 Redistributable (x64)"}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D54B24F7-43DE-C7BF-3A5C-83F9E8E0700D", "DisplayName": "Windows Desktop Extension SDK Contracts", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "79fc50d1-5673-6e16-e4e0-9fd67b3f9053", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D54B24F7-43DE-C7BF-3A5C-83F9E8E0700D}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D62BC488-4C4A-BF17-A0CE-21B4BF8F9B30", "DisplayName": "Universal General MIDI DLS Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "8e9e4b85-fec2-771f-dd49-11b00fc23487", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{D62BC488-4C4A-BF17-A0CE-21B4BF8F9B30}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "0001-01-01T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "d6a76ead-c762-4d93-9c24-1fa3efa1e12d", "DisplayName": "Windows Software Development Kit - Windows 10.0.22000.832", "QuietUninstallString": "\"C:\\ProgramData\\Package Cache\\{d6a76ead-c762-4d93-9c24-1fa3efa1e12d}\\winsdksetup.exe\" /uninstall /quiet", "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": null, "DisplayVersion": "10.1.22000.832", "DisplayIcon": "C:\\ProgramData\\Package Cache\\{d6a76ead-c762-4d93-9c24-1fa3efa1e12d}\\winsdksetup.exe,0", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{d6a76ead-c762-4d93-9c24-1fa3efa1e12d}\\winsdksetup.exe\"  /uninstall", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "D73590E4-7314-4E3D-B2CB-B308B256D8AF", "DisplayName": "Microsoft Windows Desktop Targeting Pack - 6.0.13 (x86)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b1e14c04-57da-62db-703a-9e4f4bb68ba3", "DisplayVersion": "48.55.53270", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{D73590E4-7314-4E3D-B2CB-B308B256D8AF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": null, "ProductCode": "DAB9740A-FE73-4CB6-A480-5F86048C0C98", "DisplayName": "ScreenConnect Client (9b46c9f1da2990b0)", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "7bffd4d2-03e0-53e5-9b46-c9f1da2990b0", "DisplayVersion": "22.9.10589.8370", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{DAB9740A-FE73-4CB6-A480-5F86048C0C98}", "GlobalSoftwareId": 780, "GlobalSoftwareName": "ConnectWise Control (ScreenConnect)"}, {"InstallDate": "2023-01-25T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DEA7F8E3-B7B9-3C3C-945B-7F8CE9041748", "DisplayName": "Microsoft Visual C++ 2013 x86 Minimum Runtime - 12.0.40649", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "b59f5bf1-67c8-3802-8e59-2ce551a39fc5", "DisplayVersion": "12.0.40649", "DisplayIcon": null, "UninstallString": "MsiExec.exe /X{DEA7F8E3-B7B9-3C3C-945B-7F8CE9041748}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "DF2A6855-DB81-9047-3033-8D6AC6055AEF", "DisplayName": "Windows Mobile Extension SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "e69e79c1-5e03-5795-6bc2-e8dcbff50ea5", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{DF2A6855-DB81-9047-3033-8D6AC6055AEF}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "E5715C32-34B0-6F8E-81B8-13FB19B1B682", "DisplayName": "Windows SDK Desktop Tools x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "38a7fef7-97eb-5c96-330d-8e38ad71fa84", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{E5715C32-34B0-6F8E-81B8-13FB19B1B682}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "EA338ECA-C63F-4BD6-B66F-274433C75A49", "DisplayName": "Windows SDK Modern Versioned Developer Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "f3ee49aa-e3c3-5772-3049-2e9957ad55cc", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{EA338ECA-C63F-4BD6-B66F-274433C75A49}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "EF0C772D-F5E3-36D0-BDAB-FD378533CD40", "DisplayName": "Microsoft .NET Native SDK", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "9dfe661c-1015-3269-a8d8-5a1efacb6a3f", "DisplayVersion": "15.0.24211.07", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{EF0C772D-F5E3-36D0-BDAB-FD378533CD40}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "F273FD0A-40B2-4D49-B1FA-DFA0E6D380FD", "DisplayName": "Visual C++ Library CRT Appx Package", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "d8c910b0-cbb1-3282-a5e0-49f144141813", "DisplayVersion": "14.34.31933", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{F273FD0A-40B2-4D49-B1FA-DFA0E6D380FD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FA582B75-7D9C-2717-5E64-1A8CC5E46ABD", "DisplayName": "Windows SDK Modern Non-Versioned Developer Tools", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "341879fe-d98d-a95a-d805-905742172b30", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{FA582B75-7D9C-2717-5E64-1A8CC5E46ABD}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FBF034E1-563E-1F9D-DC45-491BB88E9B9E", "DisplayName": "WinRT Intellisense Desktop - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "a56c4924-417b-94f8-5f4d-d9c1a75baa0a", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{FBF034E1-563E-1F9D-DC45-491BB88E9B9E}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FD68622A-634F-FB49-6E94-E21A451A3B5A", "DisplayName": "Windows App Certification Kit x64", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "a43d9f71-8069-869d-5689-6fdc76071b8e", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{FD68622A-634F-FB49-6E94-E21A451A3B5A}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"InstallDate": "2023-01-24T00:00:00", "UserName": null, "SystemComponent": 1, "ProductCode": "FEAF2203-A2AC-080C-9D55-9F09623F38C3", "DisplayName": "WinRT Intellisense PPI - en-us", "QuietUninstallString": null, "Context": "Machine", "UserSID": "", "Platform": "32", "UpgradeCode": "94e92b20-b0f9-fd1c-b536-9a30ff20c284", "DisplayVersion": "10.1.22000.832", "DisplayIcon": null, "UninstallString": "MsiExec.exe /I{FEAF2203-A2AC-080C-9D55-9F09623F38C3}", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}, {"DisplayName": "Windows 10 x64", "Context": "System", "InstallDate": null, "SystemComponent": 1, "DisplayVersion": "10.0.22621.0", "Platform": "64", "GlobalSoftwareId": null, "GlobalSoftwareName": ""}]}