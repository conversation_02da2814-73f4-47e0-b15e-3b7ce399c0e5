using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.CwManageProvider;
using Immybot.Backend.Providers.Interfaces;
using Newtonsoft.Json.Linq;

namespace CwManageProvider;

public partial class CwManageProvider : ISupportsProviderClientType
{
  private const string TypesRoute = "company/companies/types";

  public async Task<IEnumerable<IClientType>> GetClientTypes(CancellationToken token)
  {
    JArray? res = null;
    try
    {
      res = await GetPagedItems(TypesRoute, "name", token, fields: "id,name");
    }
    catch (CwManageException ex)
    {
      if (ex.Response.StatusCode == System.Net.HttpStatusCode.Forbidden)
      {
        // update the http problem with more specific data
        ex.HttpProblem.Detail = "The permissions to retrieve company types are not set correctly. Provide access to \"System -> Table Setup (customize) -> Company / Company Type\".  See https://docs.immy.bot/connectwise-manage-integration-setup.html#connectwise-manage for more help.";
      }
      throw;
    }
    var types = res.Select(a => new ClientType(a["name"]?.ToString(), a["id"]?.ToString()));
    List<ClientType> distinctTypes = [];
    foreach (var type in types)
    {
      if (!string.IsNullOrEmpty(type.ClientTypeDisplayName) && !string.IsNullOrEmpty(type.ClientTypeId))
      {
        distinctTypes.Add(type);
      }
    }
    return distinctTypes;
  }

  public class ClientType : IClientType
  {
    public string ClientTypeDisplayName { get; set; }

    public string ClientTypeId { get; set; }

    public ClientType(string? name, string? id)
    {
      ClientTypeDisplayName = name ?? string.Empty;
      ClientTypeId = id ?? string.Empty;
    }
  }
}
