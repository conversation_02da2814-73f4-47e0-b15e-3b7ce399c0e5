using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.HaloPsaProvider.Exceptions;
using Immybot.Backend.Providers.HaloPsaProvider.Response_Objects;
using Immybot.Backend.Providers.Interfaces;
using static Immybot.Backend.Providers.HaloPsaProvider.HttpHelpers;

namespace HaloPsaProvider;

public partial class HaloPsaProvider : ISupportsProviderClientType
{
  private const string _typesRoute = "/api/Lookup?showallcodes=true&lookupid=33&exclude_zero=true";

  public async Task<IEnumerable<IClientType>> GetClientTypes(CancellationToken token)
  {
    List<HaloLookup>? clientTypes;
    try
    {
      var psaClient = await BuildHaloPSAClient(token);
      clientTypes = await GetAsync<List<HaloLookup>>(psaClient, _typesRoute, token: token);
    }
    catch (HaloPsaException ex)
    {
      if (ex.Response?.StatusCode == System.Net.HttpStatusCode.Forbidden)
      {
        // update the http problem with more specific data
        ex.HttpProblem.Detail = "The permissions to retrieve company types are not set correctly. See https://docs.immy.bot/halopsa-integration-setup.html for more help.";
      }
      throw;
    }
    return clientTypes ?? Array.Empty<IClientType>().AsEnumerable();
  }
}
