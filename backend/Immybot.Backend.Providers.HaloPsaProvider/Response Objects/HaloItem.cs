using Immybot.Backend.Domain.Providers;
using Newtonsoft.Json;

namespace Immybot.Backend.Providers.HaloPsaProvider.Response_Objects;

public class HaloItem : IClientGroup
{
  public string ClientGroupDisplayName => Name ?? string.Empty;
  public string ClientGroupId => Id.ToString();

  [JsonProperty("id")]
  public int Id { get; set; }
  [JsonProperty("name")]
  public string? Name { get; set; }
  [JsonProperty("isrecurringitem")]
  public bool? IsRecurringItem { get; set; }
}
