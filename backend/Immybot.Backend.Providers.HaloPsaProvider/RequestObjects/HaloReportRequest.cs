using Newtonsoft.Json;

namespace Immybot.Backend.Providers.HaloPsaProvider.RequestObjects;

public class HaloReportRequest
{
  [JsonProperty("name", Required = Required.Always)]
  public required string Name { get; set; }

  [JsonProperty("sql", Required = Required.Always)]
  public required string Sql { get; set; }

  [JsonProperty("_loadreportonly", Required = Required.Always)]
  public required bool LoadReportOnly { get; set; }

  [JsonProperty("_testonly", Required = Required.Always)]
  public required bool TestOnly { get; set; }
}
