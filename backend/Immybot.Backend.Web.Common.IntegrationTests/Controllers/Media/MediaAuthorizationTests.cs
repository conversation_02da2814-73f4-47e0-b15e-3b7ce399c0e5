using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Controllers.V1;
using Immybot.Backend.Web.Common.IntegrationTests.Abstractions;

namespace Immybot.Backend.Web.Common.IntegrationTests.Controllers.Media;

public class MediaAuthorizationTests(WebAppFixture fixture) : ControllerAuthorizationTest<MediaController>(fixture)
{
  [Theory]
  [InlineData(nameof(MediaController.GetLocal), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.GetLocalById), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.Search), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.GetLocalDownloadUrl), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.UploadLocalMedia), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.UpdateLocal), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.DeleteLocal), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.GetDownloadUrl), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.UploadSupportMedia), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.GetGlobal), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.GetGlobalById), IsAllowedFor.AllRoles)]
  [InlineData(nameof(MediaController.UpdateGlobal), IsAllowedFor.ImmenseOnly)]
  [InlineData(nameof(MediaController.DeleteGlobal), IsAllowedFor.ImmenseOnly)]
  [InlineData(nameof(MediaController.UploadGlobalMedia), IsAllowedFor.ImmenseOnly)]
  [InlineData(nameof(MediaController.GetGlobalDownloadUrl), IsAllowedFor.AllRoles)]
  public override async Task ControllerMethod_ShouldThrow403_WhenBuiltinRoleDoesNotHaveAccess(
    string method,
    Role methodAllowedInRoles) =>
    await base.ControllerMethod_ShouldThrow403_WhenBuiltinRoleDoesNotHaveAccess(method, methodAllowedInRoles);
}
