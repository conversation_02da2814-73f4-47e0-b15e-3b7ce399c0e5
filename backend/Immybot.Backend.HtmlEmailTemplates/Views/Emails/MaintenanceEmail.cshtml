@using Immybot.Backend.HtmlEmailTemplates.ViewModels

@model MaintenanceEmailViewModel


<table class="mw-680 body" role="presentation" cellspacing="0" cellpadding="0" width="100%" align="center">
  @if (!string.IsNullOrEmpty(Model.Title))
  {
<tr>
  <td class="foreground-branding">
    <table role="presentation" cellspacing="0" cellpadding="0" width="100%" align="center">
      <tr>
        <td class="title">
          @Model.Title
        </td>
      </tr>
    </table>
  </td>
</tr>}

  @if (!string.IsNullOrEmpty(Model.Message))
  {
<tr>
  <td class="foreground-branding">
    <table role="presentation" cellspacing="0" cellpadding="0" width="100%" align="center">
      <tr>
        <td class="message">
          @Model.Message
        </td>
      </tr>
    </table>
  </td>
</tr>}

  @if (Model is { ShowPostponeButton: true, PostponeUrl: not null })
  {
@await Html.PartialAsync("_PostponeButton", new ButtonLinkViewModel(Model.PostponeUrl))}

  @if (Model is { ShowUpdateNowButton: true, UpdateNowUrl: not null })
  {
@await Html.PartialAsync("_UpdateNowButton", new ButtonLinkViewModel(Model.UpdateNowUrl))}

  @if (Model is { ShowRebootButton: true, RebootUrl: not null })
  {
@await Html.PartialAsync("_RebootNowButton", new ButtonLinkViewModel(Model.RebootUrl))}



  @if (Model.ManagedSoftware.Any())
  {
<tr>
  <td class="foreground-branding" style="padding: 40px;">
    <h2>Managed Software</h2>
    @await Html.PartialAsync("_ActionTable", new ActionTableViewModel(Model.ManagedSoftware, showResultColumn: true))
  </td>
</tr>}


  @if (Model.NiniteSoftware.Any())
  {
<tr>
  <td class="foreground-branding" style="padding: 40px;">
    <h2>Ninite Software</h2>
    @await Html.PartialAsync("_ActionTable", new ActionTableViewModel(Model.NiniteSoftware, showResultColumn: true))
  </td>
</tr>}

  @if (Model.WindowsUpdates.Any())
  {
<tr>
  <td class="foreground-branding" style="padding: 40px;">
    <h2>Windows Updates</h2>
    @await Html.PartialAsync("_ActionTable", new ActionTableViewModel(Model.WindowsUpdates, showResultColumn: true))
  </td>
</tr>}

  @if (Model.Tasks.Any())
  {
<tr>
  <td class="foreground-branding" style="padding: 40px;">
    <h2>Scripts</h2>
    @await Html.PartialAsync("_ActionTable", new ActionTableViewModel(Model.Tasks, showDescriptionColumn: false, showResultColumn: true))
  </td>
</tr>}

</table>
