using System.Collections.Generic;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Providers.CwAutomateProvider;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.ProxyHelpers;
using Microsoft.PowerShell.Commands;
using Microsoft.VisualStudio.Threading;

namespace CwAutomateProvider;

[Cmdlet("Get", "CWARestPages")]
public class GetCwaRestPagesCommand : PSCmdlet
{
  [Parameter(Mandatory = false, Position = 0)]
  public string? RelativeUri { get; set; }

  [Parameter(Mandatory = false, Position = 1)]
  public string? IncludeFields { get; set; }

  [Parameter(Mandatory = false, Position = 2)]
  public string? ExcludeFields { get; set; }

  [Parameter(Mandatory = false, Position = 3)]
  public string? Conditions { get; set; }

  [Parameter(Mandatory = false, Position = 4)]
  public string? Expand { get; set; }

  [Parameter(Mandatory = false, Position = 5)]
  public string? OrderBy { get; set; }

  [Parameter(Mandatory = false, Position = 6)]
  public IEnumerable<string>? Ids { get; set; }

  [Parameter(Mandatory = true, Position = 7)]
  public IProvider? Provider { get; set; }

  protected override void ProcessRecord()
  {
    if (Provider == null)
    {
      throw new CwAutomateException("Provider is missing");
    }

    var providerUnwrapped = ProxyHelpers.UnwrapProxy(Provider) ?? throw new CwAutomateException("Provider proxy cannot be unwrapped.");
    if (providerUnwrapped is not CwAutomateProvider cwAutomateProvider) throw new CwAutomateException("Provider is not an CWAutomateProvider.");

    if (RelativeUri == null)
    {
      throw new CwAutomateException("RelativeUri is missing");
    }
    var token = this.DemandVariableValue<CancellationToken>("CancellationToken");
    var result = new JoinableTaskContext().Factory.Run(async () => await cwAutomateProvider.GetAllPages(RelativeUri, token, IncludeFields,
      ExcludeFields,
      Conditions,
      Expand,
      OrderBy,
      ids: Ids));
    var objResult = JsonObject.ConvertFromJson(result.ToString(), out ErrorRecord error);
    if (error != null)
    {
      ThrowTerminatingError(error);
    }
    WriteObject(objResult, true);
  }
}
