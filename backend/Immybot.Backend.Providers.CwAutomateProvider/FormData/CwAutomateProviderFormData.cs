using System;
using System.Management.Automation;
using Immybot.Backend.Providers.Shared;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Providers.CwAutomateProvider.FormData;


public class CwAutomateProviderFormData
{
#pragma warning disable CS8618
  // required for powershell to instantiate
  public CwAutomateProviderFormData() {}
#pragma warning restore CS8618

  [Parameter(Mandatory = true, HelpMessageBaseName = "Automate URL")]
  [DisplayName("Automate URL")]
  [ValidateNotNullOrEmpty]
  [ValidatePattern("^https://", ErrorMessage = "URL must use HTTPS")]
  public Uri AutomateUrl { get; set; }

  [Parameter(Mandatory = true, HelpMessageBaseName = "Automate Username")]
  [DisplayName("Automate Username")]
  [ValidateNotNullOrEmpty]
  public string AutomatePublicKey { get; set; }

  [Parameter(Mandatory = true)]
  [DisplayName("Automate Password")]
  [Password(true)]
  [ValidateNotNullOrEmpty]
  public string AutomatePrivateKey { get; set; }

  public string CwClientId { get; } = Constants.CwClientId;

  [Parameter(Mandatory = true, HelpMessageBaseName = "Manual Entry Code", HelpMessage = "The manual entry code provided in the initial welcome email when enabling Google Two-Factor Authentication for the ImmyBot user.")]
  [DisplayName("Manual Entry Code")]
  [Password(true)]
  [ValidatePattern("^[A-Z2-7=]+$", ErrorMessage = "Please enter the code you received via email from the Google MFA plugin")]
  public string? ManualEntryCode { get; set; }
}
