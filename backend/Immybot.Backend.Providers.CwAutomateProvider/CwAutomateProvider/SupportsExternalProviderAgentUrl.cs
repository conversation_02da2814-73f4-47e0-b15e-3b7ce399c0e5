using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Providers.CwAutomateProvider;
using Immybot.Backend.Providers.Interfaces;

namespace CwAutomateProvider;
public partial class CwAutomateProvider : ISupportsExternalProviderAgentUrl
{
  public Task<string> GetExternalProviderAgentUrl(ProviderAgent agent, CancellationToken token)
  {
    if (_formData is null) throw new CwAutomateException("Form data is null");
    return Task.FromResult(new Uri(_formData.AutomateUrl, $"automate/computer/{agent.ExternalAgentId}").ToString());
  }
}
