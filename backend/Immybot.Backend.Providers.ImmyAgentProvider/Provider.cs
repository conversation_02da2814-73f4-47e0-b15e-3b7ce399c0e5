using System.Security.Cryptography;
using System.Text.Json;
using Immense.RemoteControl.Server.Services;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Providers.Attributes;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.ImmyAgentProvider;
using Immybot.Backend.Providers.ImmyAgentProvider.Signalr.AgentHub;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.Threading;
using NuGet.Versioning;

namespace ImmyAgentProvider;

[Provider(
  Constants.ProviderId,
  "ImmybotAgent",
  "ImmyBot Agent",
  typeof(FormData),
  Constants.ImmyBotLogoSrc,
  SupportsScreenShare = true,
  DocsUrl = null,
  CanManage = false)]
public partial class ImmyAgentProvider : IProvider
{
  internal readonly ILoggerFactory LoggerFactory;
  private readonly IProviderQueryHandlerFactory<IProviderQueryHandler> _queryHandlerFactory;
  internal readonly ILogger Logger;
  internal int ProviderLinkId;
  internal IProviderAgentEventHandler? EventHandler;
  internal Func<CancellationToken, IProviderQueryHandler>? QueryHandlerMaker;
  internal Func<ImmybotDbContext>? ImmybotDbContextFactory;

  private readonly IAgentHubSessionCache _agentHubSessions;
  private readonly IHubContext<AgentHub, IAgentHubClient> _agentHub;
  private readonly IOptionsMonitor<AppSettingsOptions> _appSettingsOptions;
  private readonly ILogger<ImmyAgentProvider> _logger;
  private readonly IRemoteControlSessionCache _remoteControlSessions;
  private readonly IFeatureTracker _featureTracker;
  private readonly IProviderMetrics _metrics;
  private readonly IOptions<ImmyAgentOptions> _agentOptions;
  private readonly ICurrentImmyBotRelease _currentImmyBotRelease;
  #pragma warning disable S4487 // Unread "private" fields should be removed
  #pragma warning restore S4487 // Unread "private" fields should be removed
  private readonly CancellationTokenSource _tokenSource = new();
  private HttpClient? _httpClient;
  private SemanticVersion? _currentAgentInstallerVersion;
  private bool _disposedValue;
  private Uri? _currentAgentInstallerVersionDownloadUrl;

  private string? _challengeSigningKey;
  private string ChallengeSigningKey
  {
    get
    {
      // return cached key
      if (!string.IsNullOrEmpty(_challengeSigningKey)) return _challengeSigningKey;

      // use from options if present
      if (!string.IsNullOrEmpty(_agentOptions.Value.ChallengeSigningKey))
      {
        _challengeSigningKey = _agentOptions.Value.ChallengeSigningKey;
        return _challengeSigningKey;
      }

      // generate a new key
      var key = RandomNumberGenerator.GetBytes(64);
      _challengeSigningKey = Convert.ToBase64String(key);
      return _challengeSigningKey;
    }
  }

  private int ChallengeValiditySpan => _agentOptions.Value.ChallengeValiditySpan;

  private string InstallerBundlerFunctionEndpoint => _agentOptions.Value.InstallerBundlerFunctionEndpoint;

  private string PPKGBuilderFunctionEndpoint => _agentOptions.Value.PPKGBuilderFunctionEndpoint;

  public ImmyAgentProvider(
    IHubContext<AgentHub, IAgentHubClient> agentHub,
    ILogger<ImmyAgentProvider> logger,
    ILoggerFactory loggerFactory,
    IProviderQueryHandlerFactory<IProviderQueryHandler> queryHandlerFactory,
    IAgentHubSessionCache agentHubSessions,
    IOptionsMonitor<AppSettingsOptions> appSettingsOptions,
    IRemoteControlSessionCache remoteControlSessions,
    IFeatureTracker featureTracker,
    Func<ImmybotDbContext> immybotDbContextFactory,
    IProviderAgentEventHandler events,
    IProviderMetrics metrics,
    IOptions<ImmyAgentOptions> agentOptions,
    ICurrentImmyBotRelease currentImmyBotRelease)
  {
    _logger = logger;
    LoggerFactory = loggerFactory;
    _queryHandlerFactory = queryHandlerFactory;
    Logger = loggerFactory.CreateLogger<ImmyAgentProvider>();
    _agentHubSessions = agentHubSessions;
    _agentHub = agentHub;
    _metrics = metrics;
    _agentOptions = agentOptions;
    _currentImmyBotRelease = currentImmyBotRelease;
    _appSettingsOptions = appSettingsOptions;
    _remoteControlSessions = remoteControlSessions;
    _featureTracker = featureTracker;
    EventHandler = events;
    ImmybotDbContextFactory = immybotDbContextFactory;
  }

  /// <summary>
  /// implement me
  /// </summary>
  /// <param name="providerTypeFormData"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  #pragma warning disable IDE0060, S1172 // Disable unused parameter warnings
  private static async Task<OpResult> Verify(JsonElement providerTypeFormData, CancellationToken token)
  {
    await Task.Yield(); // remove this line when implementing the method
    return OpResult.Ok();
  }
  #pragma warning restore IDE0060, S1172 // Disable unused parameter warnings

  public async Task<HealthCheckResult> HealthCheck(CancellationToken token)
  {
    await Task.Yield();
    return HealthCheckResult.Healthy();
  }

  public async Task<OpResult?> Init(
    int providerLinkId,
    JsonElement providerTypeFormData,
    CancellationToken token)
  {
    var verifyResult = await Verify(providerTypeFormData, token);
    if (!verifyResult.IsSuccess) return verifyResult;

    ProviderLinkId = providerLinkId;
    QueryHandlerMaker = _queryHandlerFactory.MakeFactory(providerLinkId);

    SetCurrentAgentInstallerVersion(token);

    return OpResult.Ok();
  }

  internal HttpClient GetHttpClient()
  {
    return _httpClient ??= new HttpClient();
  }

  public void Dispose()
  {
    Dispose(true);
    GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing)
  {
    if (!_disposedValue && disposing)
    {
      // We should replace IDisposable interface on IProvider
      // with IAsyncDisposable so we can properly await these.
      using var context = new JoinableTaskContext();
      context.Factory.Run(DisposeAsync);

      _disposedValue = true;
    }
  }

  // https://github.com/Microsoft/vs-threading/blob/main/doc/analyzers/VSTHRD104.md
  protected virtual async Task DisposeAsync()
  {
    try
    {
      await _tokenSource.CancelAsync();
      _tokenSource.Dispose();
    }
    catch (Exception ex)
    {
      const string msg = "Failed to dispose provider";
      _logger.LogError(ex, msg);
    }
  }
}
