using Immybot.Manager.Shared;
using NuGet.Versioning;

namespace Immybot.Backend.Manager.Domain;

public interface IManagerRequestsHandler
{
  void HandleUpdateReleaseDetailsRequest(ReleaseDetails release);
  void HandleDeleteReleaseRequest(SemanticVersion tag);
  void HandleSetInstanceUpdatingRequest(string source);
  void HandleSetInstanceUpdateFailedRequest();
  Task HandleUpdateSubscriptionRequest(SubscriptionDetails body);
  Task HandleUpdateImmySupportAccessGrantDetails(MspInstanceImmySupportAccessGrantDetails body);
  Task HandleReleaseChannelUpdated(ReleaseChannel releaseChannel);
}
