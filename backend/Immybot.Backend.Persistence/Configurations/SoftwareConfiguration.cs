using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class SoftwareConfiguration : IEntityTypeConfiguration<LocalSoftware>
{
  public void Configure(EntityTypeBuilder<LocalSoftware> builder)
  {
    builder.HasKey(a => a.Id);
    builder
      .HasMany(a => a.SoftwareVersions)
      .WithOne(b => b.Software);
    builder.HasMany(a => a.SoftwarePrerequisites)
      .WithOne()
      .HasForeignKey("LocalSoftwareId")
      .IsRequired();

    builder.HasMany(s => s.TenantSoftware)
      .WithOne(t => t.Software)
      .HasPrincipalKey(s => s.Id)
      .HasForeignKey(t => t.SoftwareId)
      .OnDelete(DeleteBehavior.Cascade);

    #pragma warning disable CS0612 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
    builder.Ignore(a => a.AutoUpdateScript);
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0612 // Type or member is obsolete
    builder.Ignore(a => a.DetectionScript);
    builder.Ignore(a => a.MaintenanceTask);
    builder.Ignore(a => a.RepairScript);
    builder.Ignore(a => a.InstallScript);
    builder.Ignore(a => a.UninstallScript);
    builder.Ignore(a => a.PostInstallScript);
    builder.Ignore(a => a.PostUninstallScript);
    builder.Ignore(a => a.TestScript);
    builder.Ignore(a => a.UpgradeScript);
    builder.Ignore(a => a.DynamicVersionsScript);
    builder.Ignore(a => a.DownloadInstallerScript);
    builder.Ignore(a => a.CustomAuditProperties);

    builder.Property(a => a.Name).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.SoftwareTableName).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.Notes).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.TestFailedError).HasMaxLength(MaxLengthConstants.MediumText);

    builder.Property(a => a.RelativeCacheSourcePath)
      .IsRequired();

    builder
      .HasOne(a => a.SoftwareIcon)
      .WithMany(a => a.LocalSoftware)
      .HasForeignKey(a => a.SoftwareIconMediaId)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.UpdatedByUser)
      .WithMany(a => a.UpdatedSoftware)
      .HasForeignKey(a => a.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedSoftware)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.Name).IsRequired();

    builder.Property(a => a.RepairType).HasDefaultValue(RepairActionType.UninstallInstall);

    builder.Property(a => a.DetectionMethod).HasDefaultValue(DetectionMethod.SoftwareTable);
    builder.Property(a => a.Recommended).HasDefaultValue(false);
    builder.Property(a => a.LicenseRequirement).HasDefaultValue(SoftwareLicenseRequirement.None);
    builder.Property(a => a.LicenseType).HasDefaultValue(LicenseType.None);
    builder.Property(a => a.TestRequired).HasDefaultValue(false);
    builder.Property(a => a.UpgradeStrategy).HasDefaultValue(UpdateActionType.None);
    builder.Property(a => a.UseDynamicVersions).HasDefaultValue(false);
  }
}
public class SoftwarePrerequisitesConfiguration : IEntityTypeConfiguration<SoftwarePrerequisite>
{
  public void Configure(EntityTypeBuilder<SoftwarePrerequisite> builder)
  {
    builder.Property<int>("Id");
    builder.HasKey("Id");
    builder
      .OwnsMany(p => p.SoftwaresForCondition, b =>
      {
          // For some reaso, ef core really doesn't want us to name this table
          // software_prerequisite_softwares_for_condition. So on chance that they
          // fix whatever bug that's preventing us from naming it that, we'll manually
          // specify the badly-named version here so it won't get changed erroneously
          // in the future
          b.ToTable("software_prerequisite_SoftwaresForCondition");
        b.Property<int>("Id");
        b.HasKey("Id");
        b.Property(a => a.SoftwareIdentifier).HasMaxLength(MaxLengthConstants.SmallText);
      });
    builder.OwnsMany(p => p.SoftwaresToPerformActionOn, b =>
    {
      b.ToTable("software_prerequisite_softwares_to_perform_action_on");
      b.Property<int>("Id");
      b.HasKey("Id");
      b.Property(a => a.SoftwareIdentifier).HasMaxLength(MaxLengthConstants.SmallText);
    });
    builder.Ignore(a => a.SoftwareSpecifiers);
  }
}
