using Immybot.Backend.Domain.Models.Preferences;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Immybot.Backend.Persistence.Configurations;

public class TenantPreferencesConfiguration : IEntityTypeConfiguration<TenantPreferences>
{
  public void Configure(EntityTypeBuilder<TenantPreferences> builder)
  {
    builder.Property(a => a.TenantId).IsRequired();
    builder.HasIndex(a => a.TenantId).IsUnique();

    builder.Property(a => a.BusinessHoursStart).HasDefaultValue("09:00");
    builder.Property(a => a.BusinessHoursEnd).HasDefaultValue("17:00");

    builder.Property(a => a.EnableOnboarding).HasDefaultValue(false);
    builder.Property(a => a.RequireConsentForExternalSessionProviders).HasDefaultValue(null);
    builder.Property(a => a.EnableImmyBotRemoteControl).HasDefaultValue(null);
    builder.Property(a => a.EnableImmyBotRemoteControlRecording).HasDefaultValue(null);
    builder
      .Property(a => a.DefaultEmailBccList)
      .HasConversion(a => JsonConvert.SerializeObject(a), a => JsonConvert.DeserializeObject<DefaultEmailBccList>(a));
  }
}
