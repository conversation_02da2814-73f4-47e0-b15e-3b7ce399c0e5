using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;

namespace Immybot.Backend.Persistence.Configurations;
public class TagConfiguration : IEntityTypeConfiguration<Tag>
{
  public void Configure(EntityTypeBuilder<Tag> builder)
  {
    builder.HasKey(t => t.Id);
    builder.HasIndex(t => t.Name).IsUnique();
    builder.Property(t => t.Name).IsRequired();
    builder.Property(t => t.Name).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(t => t.Description).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder
      .HasOne(a => a.UpdatedByUser)
      .WithMany(a => a.UpdatedTags)
      .HasForeignKey(a => a.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedTags)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
  }
}
