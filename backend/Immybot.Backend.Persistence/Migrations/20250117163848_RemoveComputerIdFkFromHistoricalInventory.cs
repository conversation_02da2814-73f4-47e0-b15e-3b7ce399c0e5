using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemoveComputerIdFkFromHistoricalInventory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_historical_computer_inventory_task_script_results_computers",
                table: "historical_computer_inventory_task_script_results");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddForeignKey(
                name: "fk_historical_computer_inventory_task_script_results_computers",
                table: "historical_computer_inventory_task_script_results",
                column: "computer_id",
                principalTable: "computers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
