using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Shared.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Commands;

internal class DuplicateAssignmentCmd(
  Func<ImmybotDbContext> _ctxFactory,
  Func<SoftwareDbContext> _globalCtxFactory)
  : IDuplicateAssignmentCmd
{
  public int Run(int assignmentId, DatabaseType databaseType, User duplicatedByUser)
  {
    TargetAssignment? existing;
    if (databaseType == DatabaseType.Local)
    {
      existing = _ctxFactory.With(localCtx => localCtx
        .TargetAssignments
        .AsNoTracking()
        .FirstOrDefault(a => a.Id == assignmentId));
    }
    else
    {
      existing = _globalCtxFactory.With(globalCtx => globalCtx
        .TargetAssignments
        .AsNoTracking()
        .FirstOrDefault(a => a.Id == assignmentId));
    }

    if (existing == null) throw new KeyNotFoundException($"Assignment {assignmentId} does not exist.");

    var newAssignment = existing;

    newAssignment.Id = 0;
    newAssignment.DatabaseType = DatabaseType.Local;
    using var localCtx = _ctxFactory();
    localCtx.SetUser(duplicatedByUser);
    localCtx.TargetAssignments.Add(newAssignment);
    localCtx.SaveChanges();

    return newAssignment.Id;
  }
}
