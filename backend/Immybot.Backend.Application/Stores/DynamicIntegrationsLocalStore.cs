using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Stores;

public interface IDynamicIntegrationsLocalStore : IDynamicIntegrationsStore;

public class DynamicIntegrationsLocalStore(Func<ImmybotDbContext> ctxFactory) : IDynamicIntegrationsLocalStore
{
  public DisposableValue<IQueryable<DynamicIntegrationType>> GetAllDynamicIntegrationTypes() =>
    ctxFactory.CreateDisposableValue(ctx => ctx.GetAllDynamicIntegrationTypes());

  public DynamicIntegrationType? GetDynamicIntegrationType(int id) =>
    ctxFactory.With(ctx => ctx.GetDynamicIntegrationType(id));

  public DynamicIntegrationType? GetDynamicIntegrationTypeByTypeId(Guid typeId) =>
    ctxFactory.With(ctx => ctx.GetDynamicIntegrationTypeByTypeId(typeId));

  public Task DeleteDynamicIntegrationType(
    DynamicIntegrationType dynamicIntegrationType,
    User currentUser) => ctxFactory.With(ctx =>
  {
    ctx.SetUser(currentUser);
    return ctx.DeleteDynamicIntegrationType(dynamicIntegrationType);
  });


  public Task<DynamicIntegrationType?> CreateDynamicIntegrationType(
    CreateDynamicIntegrationTypePayload payload,
    User currentUser) => ctxFactory.With(ctx =>
  {
    ctx.SetUser(currentUser);
    return ctx.CreateDynamicIntegrationType(payload);
  });

  public Task<DynamicIntegrationType?> UpdateDynamicIntegrationType(
    UpdateDynamicIntegrationTypePayload payload,
    User currentUser) => ctxFactory.With(ctx =>
  {
    ctx.SetUser(currentUser);
    return ctx.UpdateDynamicIntegrationType(payload);
  });

  public Task UpdateCreationErrorMessage(
    DynamicIntegrationType type,
    string? errorMessage,
    CancellationToken token) => ctxFactory.With(ctx =>
    ctx
      .DynamicIntegrationTypes
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => a.Id == type.Id)
      .ExecuteUpdateAsync(
        a => a.SetProperty(b => b.CreationErrorMessage, errorMessage),
        token));
}
