using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;
using Z.EntityFramework.Plus;

namespace Immybot.Backend.Application.DbContextExtensions;

public static class ProviderAgentExtensions
{
  public static void ResolveIdentificationFailuresForAgents(
    this ImmybotDbContext ctx, ICollection<int> agentIds)
  {
    ctx.AgentIdentificationFailures
      .Where(f => agentIds.Contains(f.PendingAgentId))
      .Update(f => new AgentIdentificationFailure { Resolved = true });
  }
  public static IQueryable<ProviderAgent> GetProviderAgents(
    this ImmybotDbContext ctx,
    bool includeClients = false,
    bool includeLinks = false,
    bool includeFailures = false)
  {
    var q = ctx.ProviderAgents.AsNoTracking().TagForTelemetry();
    if (includeClients) q = q.Include(a => a.ProviderClient);
    if (includeLinks) q = q.Include(a => a.ProviderLink);
    if (includeFailures) q = q.Include(a => a.IdentificationFailures);
    return q;
  }
  public static IQueryable<ProviderAgent> GetAgentsForProviderLink(
    this ImmybotDbContext ctx,
    int providerLinkId)
    => ctx.ProviderAgents.AsNoTracking().Where(a => a.ProviderLinkId == providerLinkId);
  public static ProviderAgent? GetProviderAgent(this ImmybotDbContext ctx, int agentId)
    => ctx.ProviderAgents.AsNoTracking().FirstOrDefault(a => a.Id == agentId);
  public static IQueryable<ProviderAgent> GetPendingAgentsForProviderLink(
    this ImmybotDbContext ctx,
    int providerLinkId)
    => ctx.GetAgentsForProviderLink(providerLinkId).Where(a => a.ComputerId == null);
  public static IQueryable<ProviderAgent> GetPendingProviderAgents(
    this ImmybotDbContext ctx,
    bool includeClients = false,
    bool includeLinks = false,
    bool includeFailures = false)
  {
    return ctx.GetProviderAgents(
      includeClients: includeClients,
      includeLinks: includeLinks,
      includeFailures: includeFailures)
      .Where(a => a.ComputerId == null)
      .TagForTelemetry();
  }

  public static void CreateProviderAgents(
    this ImmybotDbContext ctx,
    ICollection<ProviderAgent> agents)
  {
    // NOTE: BulkInsert will throw an exception when attempting to insert anything that has null
    // owned references when the owned is on the same table as the entity.
    // Thus, these properties cannot be null when using this method:
    // - DeviceDetails
    // - OnboardingOptions
    using var transaction = ctx.Database.BeginTransaction();
    try
    {
      ctx.BulkInsert(agents);
      transaction.Commit();
    }
    catch
    {
      transaction.Rollback();
      throw;
    }
  }

  /// <summary>
  /// Hard-delete agents for provider link
  /// </summary>
  /// <param name="ctx"></param>
  /// <param name="providerLinkId"></param>
  /// <returns></returns>
  public static Task DeleteAgentsForProviderLink(this ImmybotDbContext ctx, int providerLinkId)
    => ctx.ProviderAgents.AsNoTracking().Where(a => a.ProviderLinkId == providerLinkId).DeleteFromQueryAsync();

  public static void DeleteAgentsForProviderClient(
    this ImmybotDbContext ctx,
    int providerLinkId,
    string externalClientId,
    string reason)
    => ctx.ProviderAgents
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(a => a.ProviderLinkId == providerLinkId && a.ExternalClientId == externalClientId)
      .SoftDelete(reason);

  public static Task DeleteProviderAgentsByIds(this ImmybotDbContext ctx, List<int> agentIds, string reason)
    => ctx.ProviderAgents.Where(a => agentIds.Contains(a.Id)).SoftDeleteAsync(reason);

  public static void UpdateProviderAgent(
    this ImmybotDbContext ctx, ProviderAgent agent)
  {
    ctx.ProviderAgents.Update(agent);
    ctx.SaveChanges();
  }
}
