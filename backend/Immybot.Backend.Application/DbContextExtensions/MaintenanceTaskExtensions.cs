using System.Linq;
using Immybot.Backend.Application.Interface.Commands.Payloads.MaintenanceTasks;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;


namespace Immybot.Backend.Application.DbContextExtensions.MaintenanceTaskExtensions;

public static class MaintenanceTaskExtensions
{
  public static IQueryable<MaintenanceTask> GetAllMaintenanceTasks(this ImmybotDbContext ctx, bool asNoTracking = true)
  => ctx.MaintenanceTasks
      .Include(a => a.Tenants)
      .Include(a => a.Parameters)
      .Include(a => a.Icon)
      .AsNoTracking();

  public static IQueryable<MaintenanceTask> GetAllMaintenanceTasksForTenant(this ImmybotDbContext ctx, int tenantId)
  => ctx.GetAllMaintenanceTasks()
      .Where(a => !a.Tenants.Any() || a.Tenants.Any(b => b.TenantId == tenantId));

  public static MaintenanceTask? GetMaintenanceTask(this ImmybotDbContext ctx, int id)
  => ctx.GetAllMaintenanceTasks()
      .Include(a => a.Parameters)
      .Include(a => a.UpdatedByUser).ThenInclude(a => a!.Person)
      .FirstOrDefault(a => a.Id == id);

  public static MaintenanceTask? UpdateMaintenanceTask(this ImmybotDbContext ctx, UpdateLocalMaintenanceTaskPayload payload)
  {
    var task = ctx.MaintenanceTasks
      .Include(a => a.Tenants)
      .Include(a => a.Parameters)
      .FirstOrDefault(a => a.Id == payload.Id);

    if (task == null) return null;

    ctx.Entry(task).CurrentValues.SetValues(payload);

    task.Tenants.Clear();
    foreach (var tenant in payload.Tenants)
      task.Tenants.Add(new TenantMaintenanceTask { TenantId = tenant.TenantId, Relationship = tenant.Relationship });

    // Update parent
    ctx.Entry(task).CurrentValues.SetValues(payload);

    // Delete children
    foreach (var existingChild in task.Parameters.ToList())
    {
      if (!payload.Parameters.Any(c => c.Id == existingChild.Id))
        ctx.MaintenanceTaskParameters.Remove(existingChild);
    }

    // Update and Insert Parameters
    foreach (var param in payload.Parameters.Where(a => !string.IsNullOrWhiteSpace(a.Name)))
    {
      var existingParam = task.Parameters
        .SingleOrDefault(c => c.Id == param.Id && c.Id != 0);

      if (existingParam != null)
      {
        // Update child
        var updatedParamName = TextHelpers.RemoveAllWhiteSpace(param.Name);
        if (!string.IsNullOrEmpty(updatedParamName)) param.Name = updatedParamName;
        ctx.Entry(existingParam).CurrentValues.SetValues(param);
      }
      else
      {
        var updatedParamName = TextHelpers.RemoveAllWhiteSpace(param.Name);
        // Insert child
        var newParam = new MaintenanceTaskParameter
        {
          MaintenanceTaskId = task.Id,
          DataType = param.DataType,
          Name = updatedParamName ?? param.Name,
          Notes = param.Notes,
          Required = param.Required,
          SelectableValues = param.SelectableValues.ToList(),
          Hidden = param.Hidden,
          DefaultValue = param.DefaultValue,
          DefaultMediaId = param.DefaultMediaId,
          DefaultMediaDatabaseType = param.DefaultMediaDatabaseType,
          Order = param.Order
        };
        // check for media
        task.Parameters.Add(newParam);
      }
    }

    ctx.SaveChanges();
    ctx.Entry(task).State = EntityState.Detached;
    return task;
  }

  public static MaintenanceTask CreateMaintenanceTask(this ImmybotDbContext ctx, CreateLocalMaintenanceTaskPayload payload)
  {
    var task = new MaintenanceTask { Name = payload.Name };
    ctx.Entry(task).CurrentValues.SetValues(payload);
    ctx.MaintenanceTasks.Add(task);

    foreach (var tenant in payload.Tenants)
      task.Tenants.Add(new TenantMaintenanceTask { TenantId = tenant.TenantId, Relationship = tenant.Relationship });

    foreach (var param in payload.Parameters.Where(a => !string.IsNullOrWhiteSpace(a.Name)))
    {
      var updatedParamName = TextHelpers.RemoveAllWhiteSpace(param.Name);
      task.Parameters.Add(new MaintenanceTaskParameter
      {
        DataType = param.DataType,
        Name = updatedParamName ?? param.Name,
        Notes = param.Notes,
        Required = param.Required,
        SelectableValues = param.SelectableValues.ToList(),
        Hidden = param.Hidden,
        DefaultValue = param.DefaultValue,
        DefaultMediaId = param.DefaultMediaId,
        DefaultMediaDatabaseType = param.DefaultMediaDatabaseType,
        Order = param.Order,
      });
    }

    ctx.SaveChanges();
    ctx.Entry(task).State = EntityState.Detached;
    return task;
  }

  public static void DeleteMaintenanceTask(this ImmybotDbContext ctx, MaintenanceTask task)
  {
    ctx.MaintenanceTasks.Remove(task);
    ctx.SaveChanges();
  }

  public static bool IsTaskDeprecated(this ImmybotDbContext ctx, int taskId) =>
    ctx.MaintenanceTasks.AsNoTracking().Any(a => a.Id == taskId && a.SupersededByTaskId != null);
}
