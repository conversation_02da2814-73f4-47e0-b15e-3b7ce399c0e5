using Immybot.Backend.Domain.Models;
using Immybot.Shared.Telemetry;

namespace Immybot.Backend.Application.Maintenance;

// Extension of the SessionRunContext to contain extra contextual data.
// AddLog will automatically provide the configured Session and Stage to the
// AddLogHandler
internal class StageRunContext : SessionRunContext, IStageRunContext
{
  public StageRunContext(
    MaintenanceSession session,
    MaintenanceSessionStage stage,
    IRunContextArgs args) : base(session, args)
  {
    Stage = stage;
  }

  internal LogPhaseHandle? CurrentStagePhase;

  public MaintenanceSessionStage Stage { get; }
  public int StageId => Stage.Id;

  public async Task UpdateStage(
    SessionStatus? stageStatus = null,
    string? jobId = null)
  {
    if (stageStatus.HasValue) Stage.StageStatus = stageStatus.Value;
    if (jobId != null) Stage.JobId = jobId;
    await UpdateStage(Stage);
  }

  public override async Task<SessionPhase> AddPhase(SessionPhase phase)
  {
    Args.StopProcessing.ThrowIfCancellationRequested();
    if (Stage.Phases.Contains(phase)) return phase;
    await base.AddPhase(phase);
    Stage.Phases.Add(phase);
    return phase;
  }

  public override SessionLog AddLog(
    string? message,
    MaintenanceSession? session = null,
    MaintenanceSessionStage? stage = null,
    MaintenanceAction? action = null,
    Script? script = null,
    int? scriptTimeout = null,
    SessionLogType sessionLogType = SessionLogType.Generic,
    Guid? progressCorrelationId = null,
    string? progressActivity = null,
    decimal? progressPercentComplete = null,
    string? progressStatus = null,
    double? progressSecondsRemaining = null,
    bool progressCompleted = false,
    string? progressCurrentOperation = null,
    bool isPrimary = false,
    int? phaseId = null,
    string? scriptOutput = null
  ) => base.AddLog(
    message,
    session: session ?? Session,
    stage: stage ?? Stage,
    action: action,
    script: script,
    scriptTimeout: scriptTimeout,
    sessionLogType: sessionLogType,
    progressCorrelationId: progressCorrelationId,
    progressActivity: progressActivity,
    progressPercentComplete: progressPercentComplete,
    progressStatus: progressStatus,
    progressSecondsRemaining: progressSecondsRemaining,
    progressCompleted: progressCompleted,
    progressCurrentOperation: progressCurrentOperation,
    isPrimary: isPrimary,
    phaseId: phaseId ?? CurrentStagePhase?.Phase.Id,
    scriptOutput: scriptOutput
  );

  // Returns a new context (specifically an ActionRunContext) that has all of
  // the contextual data/utils of this instance and has the provided action
  public async Task<IActionRunContext> Extend(MaintenanceAction action)
  {
    var actionRunContext = new ActionRunContext(Session, Stage, action, Args);
    await actionRunContext.Initialize();
    return actionRunContext;
  }

  public async Task<ILogPhaseHandle> BeginNewStagePhase(string phaseName)
  {
    if (CurrentStagePhase is not null)
      throw new NotSupportedException("Cannot start stage phase until previous phase has been finished");
    var phase = new SessionPhase
    {
      PhaseName = phaseName,
      MaintenanceSessionId = Session.Id,
      MaintenanceSessionStageId = Stage.Id,
    };
    await AddPhase(phase);

    return CurrentStagePhase = new LogPhaseHandle(
      this,
      phase,
      Telemetry.StartActivity(ActivityType.Maintenance, $"Stage phase `{phaseName}`")
    );
  }
}
