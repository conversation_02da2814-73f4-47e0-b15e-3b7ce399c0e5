using System;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Maintenance;

// Extension of the basic run context to contain extra contextual data. AddLog
// will automatically provide the configured Session to the AddLogHandler
internal class SessionRunContext : RunContext, ISessionRunContext
{
  public SessionRunContext(
    MaintenanceSession session,
    IRunContextArgs args) : base(args)
  {
    Session = session;
  }

  public MaintenanceSession Session { get; }
  public MaintenanceSessionStage? OnboardingStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.Onboarding);
  public MaintenanceSessionStage? ResolutionStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.Resolution);
  public MaintenanceSessionStage? DetectionStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.Detection);
  public MaintenanceSessionStage? ExecutionStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.Execution);
  public MaintenanceSessionStage? AgentUpdatesStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.AgentUpdates);
  public MaintenanceSessionStage? InventoryStage => Session.Stages.FirstOrDefault(s => s.Type == SessionStageType.Inventory);

  public async Task SetSessionFeatureHasBeenTracked()
  {
    Session.JobArgs.HasSessionFeatureBeenTracked = true;
    Args.HasSessionFeatureBeenTracked = true;
    await UpdateSession();
  }

  public int SessionId => Session.Id;

  public bool IsResolutionOnlySession()
    => Session.Stages.Count == 1 && Session.Stages.First().Type == SessionStageType.Resolution;

  private bool IsOnboardingSession() => Session.Stages.Any(s => s.Type == SessionStageType.Onboarding);

  public bool IsDetectionOnlySession()
    => (Session.Stages.Count == 1 && Session.Stages.First().Type == SessionStageType.Detection) ||
    (Session.Stages.Count == 2 && Session.Stages.First().Type == SessionStageType.Resolution && Session.Stages.Skip(1).First().Type == SessionStageType.Detection);

  public bool AllStagesArePassedOrPartialPassed()
  {
    return Session.Stages.All(a => a.StageStatus == SessionStatus.Passed || a.StageStatus == SessionStatus.PartialPassed);
  }

  public bool AllActionsWereSuccessful()
  {
    if (Args.RerunningAction)
    {
      return Session.MaintenanceActions
        .GroupBy(a => new { a.MaintenanceIdentifier, a.MaintenanceType })
        .All(a => a.OrderByDescending(b => b.Id).First().ActionResult == MaintenanceActionResult.Success);
    }
    else
    {
      return !Session.MaintenanceActions.Any() || Session.MaintenanceActions.All(a => a.ActionResult == MaintenanceActionResult.Success);
    }
  }

  public bool AllActionsWereFailed()
  {
    if (Args.RerunningAction)
    {
      return Session.MaintenanceActions
        .GroupBy(a => new { a.MaintenanceIdentifier, a.MaintenanceType })
        .All(a => a.OrderByDescending(b => b.Id).First().ActionResult == MaintenanceActionResult.Failed);
    }
    else
    {
      return Session.MaintenanceActions.Any() && Session.MaintenanceActions.All(a => a.ActionResult == MaintenanceActionResult.Failed);
    }
  }

  public bool SomeActionsWereSuccessful()
  {
    if (Args.RerunningAction)
    {
      return Session.MaintenanceActions
      .GroupBy(a => new { a.MaintenanceIdentifier, a.MaintenanceType })
      .Any(a => a.OrderByDescending(b => b.Id).First().ActionResult == MaintenanceActionResult.Success);
    }
    else
    {
      return Session.MaintenanceActions.Any(a => a.ActionResult == MaintenanceActionResult.Success);
    }
  }

  public bool AllActionsWereCompleted()
  {
    if (Args.RerunningAction)
    {
      return Session.MaintenanceActions
        .GroupBy(a => new { a.MaintenanceIdentifier, a.MaintenanceType })
        .All(a => a.OrderByDescending(b => b.Id).First().ActionResult != MaintenanceActionResult.Pending);
    }
    else
    {
      return Session.MaintenanceActions.Any() && Session.MaintenanceActions.All(a => a.ActionResult != MaintenanceActionResult.Pending);
    }
  }

  public MaintenanceAction GetSoftwareActionForConfigurationTaskAction(MaintenanceAction action)
  {
    Args.StopProcessing.ThrowIfCancellationRequested();
    if (action.SoftwareActionIdForConfigurationTask is { } id)
    {
      return Session.MaintenanceActions.First(a => a.Id == id);
    }

    var assignmentTypeText = action.AssignmentType?.ToString() ?? "Adhoc";
    var assignmentIdText = action.AssignmentId is not null ? $"#{action.AssignmentId}" : "";
    var assignmentText = $"Deployment: {assignmentTypeText} {assignmentIdText}";
    throw new RunContextException($"Action #{action.Id}: {action.MaintenanceDisplayName} is a configuration task but is not associated with a software. Double-check the deployment is saved correctly and verify that {action.MaintenanceDisplayName} is indeed a configuration task. {assignmentText}");
  }

  public async Task AddOrUpdateMaintenanceActionWithDependencies(MaintenanceAction action)
  {
    Args.StopProcessing.ThrowIfCancellationRequested();
    if (Session.MaintenanceActions.Any(a => a.MaintenanceIdentifier == action.MaintenanceIdentifier && a.MaintenanceType == action.MaintenanceType))
      await Args.RunContextActions.UpdateAction(this, action);
    else
      await CreateAction(action);

    foreach (var dependsOn in action.DependsOn)
    {
      HydrationException.ThrowIfNull(dependsOn.DependsOn, nameof(dependsOn.DependsOn));
      dependsOn.DependentId = action.Id;
      dependsOn.DependsOnId = dependsOn.DependsOn.Id;
      await Args.MaintenanceActionActions.CreateMaintenanceActionDependency(dependsOn, Args.StopProcessing);
    }
  }

  public override async Task<SessionPhase> AddPhase(SessionPhase phase)
  {
    Args.StopProcessing.ThrowIfCancellationRequested();
    if (Session.Phases.Contains(phase)) return phase;
    await base.AddPhase(phase);
    Session.Phases.Add(phase);
    return phase;
  }

  public override async Task AddMaintenanceAction(MaintenanceAction action)
  {
    Args.StopProcessing.ThrowIfCancellationRequested();
    if (Session.MaintenanceActions.Contains(action)) return;
    await CreateAction(action);
  }

  private async Task CreateAction(MaintenanceAction action)
  {
    await Args.RunContextActions.CreateAction(this, action);
    Session.MaintenanceActions.Add(action);
  }
  public async Task<MaintenanceAction> FailAction(
    MaintenanceAction action,
    MaintenanceActionResultReason? resultReason = null,
    string? resultReasonMsg = null)
  {
    action.MaintenanceSessionId = SessionId;
    return await CompleteAction(action, MaintenanceActionResult.Failed,
      resultReason: resultReason,
      resultReasonMsg: resultReasonMsg);
  }

  public async Task<MaintenanceAction> CompleteAction(
    MaintenanceAction action,
    MaintenanceActionResult result,
    MaintenanceActionType? type = null,
    MaintenanceActionReason? reason = null,
    MaintenanceActionResultReason? resultReason = null,
    string? resultReasonMsg = null,
    string? taskGetResult = null)
  {
    action.MaintenanceSessionId = SessionId;
    action.EndTime = DateTime.UtcNow;
    action.ActionStatus = MaintenanceActionStatus.Complete;
    action.ActionResult = result;
    if (type.HasValue) action.ActionType = type.Value;
    if (resultReason.HasValue) action.ActionResultReason = resultReason.Value;
    if (resultReasonMsg != null) action.ActionResultReasonMessage = resultReasonMsg;
    if (taskGetResult != null) action.MaintenanceTaskGetResult = taskGetResult;
    if (reason.HasValue) action.ActionReason = reason.Value;
    await UpdateAction(action);

    Args.DomainEventEmitter.EmitEvent(new MaintenanceActionCompletedEvent(action));

    var isResolutionOnly = IsResolutionOnlySession();
    var isDetectionOnly = IsDetectionOnlySession();
    if (action.MaintenanceType is MaintenanceType.GlobalMaintenanceTask or MaintenanceType.GlobalSoftware &&
        !isResolutionOnly && !isDetectionOnly)
    {
      Args.MaintenanceActionDiagnosticEventHubService.AddEvent(
        action,
        IsOnboardingSession(),
        Args.ScheduleId is not null,
        triggeredBy: Args?.ManuallyTriggeredBy?.EmailAddress);
    }

    return action;
  }

  public override async Task RemoveMaintenanceAction(MaintenanceAction action)
  {
    await base.RemoveMaintenanceAction(action);
    Session.MaintenanceActions.Remove(action);
  }

  public async Task UpdateSession(SessionStatus? sessionStatus = null)
  {
    if (sessionStatus.HasValue) Session.SessionStatus = sessionStatus.Value;
    await UpdateSession(Session, ignoreCancellation: true);
  }

  public async Task UpdateSessionScheduledExecutionDate(DateTime dateUtc, bool usingActiveHours = false)
  {
    Session.ScheduledExecutionDate = dateUtc;
    Session.UsingActiveHours = usingActiveHours;
    await UpdateSession(Session, ignoreCancellation: true);
  }

  public async Task SetDuration(DateTime? sessionStarted)
  {
    if (sessionStarted.HasValue)
    {
      Session.Duration = Session.Duration?.Add(DateTime.UtcNow - sessionStarted.Value) ?? DateTime.UtcNow - sessionStarted.Value;
      await UpdateSession(Session, ignoreCancellation: true);
    }
  }

  public async Task CompleteSession(SessionStatus sessionStatus)
  {
    Session.SessionStatus = sessionStatus;
    await UpdateSession(Session, ignoreCancellation: true);

    Args.DomainEventEmitter.EmitEvent(new MaintenanceSessionCompletedEvent(SessionId));
  }

  public override SessionLog AddLog(
    string? message,
    MaintenanceSession? session = null,
    MaintenanceSessionStage? stage = null,
    MaintenanceAction? action = null,
    Script? script = null,
    int? scriptTimeout = null,
    SessionLogType sessionLogType = SessionLogType.Generic,
    Guid? progressCorrelationId = null,
    string? progressActivity = null,
    decimal? progressPercentComplete = null,
    string? progressStatus = null,
    double? progressSecondsRemaining = null,
    bool progressCompleted = false,
    string? progressCurrentOperation = null,
    bool isPrimary = false,
    int? phaseId = null,
    string? scriptOutput = null
  ) => base.AddLog(
    message,
    session: session ?? Session,
    stage: stage,
    action: action,
    script: script,
    scriptTimeout: scriptTimeout,
    sessionLogType: sessionLogType,
    progressCorrelationId: progressCorrelationId,
    progressActivity: progressActivity,
    progressPercentComplete: progressPercentComplete,
    progressStatus: progressStatus,
    progressSecondsRemaining: progressSecondsRemaining,
    progressCompleted: progressCompleted,
    progressCurrentOperation: progressCurrentOperation,
    isPrimary: isPrimary,
    phaseId: phaseId,
    scriptOutput: scriptOutput
  );

  // Returns a new context (specifically a StageRunContext) that has all of
  // the contextual data/utils of this instance and has the provided stage
  public async Task<IStageRunContext> Extend(MaintenanceSessionStage stage)
  {
    var context = new StageRunContext(Session, stage, Args);
    await context.Initialize();
    return context;
  }

  public override bool CanAccessParentTenant()
  {
    // allow access to parent tenant if the session is an onboarding session
    return Session.Onboarding || base.CanAccessParentTenant();
  }
}
