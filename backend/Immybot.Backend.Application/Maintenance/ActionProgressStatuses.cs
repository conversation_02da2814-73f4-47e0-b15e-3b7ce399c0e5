namespace Immybot.Backend.Application.Maintenance;

public static class ActionProgressStatuses
{
  public const string HandlingDetectionResult = "Handling Detection Result";
  public const string GettingNiniteSoftware = "Getting Ninite Software";
  public const string DetectingVersion = "Detecting Version";
  public const string DetectingInstalledVersion = "Detecting Installed Version";
  public const string DownloadingFileParameters = "Downloading File Parameters";
  public const string PreparingScriptParameters = "Preparing script parameters";
  public const string RunningTestScript = "Running Test Script";
  public const string RunningGetScript = "Running Get Script";
  public const string RunningSetScript = "Running Set Script";
  public const string RunningInstallScript = "Running Install Script";
  public const string RunningUninstallScript = "Running Uninstall Script";
  public const string RunningUpgradeScript = "Running Upgrade Script";
  public const string RunningPostInstallScript = "Running Post Install Script";
  public const string RunningPostUninstallScript = "Running Post Uninstall Script";
  public const string RunningDynamicVersionScript = "Running Dynamic Version Script";
  public const string RebootingIfNecessary = "Rebooting If Necessary";
  public const string RunningRepairScript = "Running Repair Script";
  public const string Verifying = "Verifying";
  public const string TryingDetectedVersionUninstallScript = "Trying Detected Version Uninstall Script";
  public const string TryingDefaultUninstallScript = "Trying Default Uninstall Script";
  public const string TryingLatestVersionsUninstallScript = "Trying Latest Version's Uninstall Script";
  public const string DownloadingLicense = "Downloading License";
  public const string UnrecognizedScriptActionType = "Unrecognized Script Action Type";
  public const string GettingDynamicSoftwareVersion = "Getting Dynamic Software Version";
  public const string AttemptingToRetrieveMissingFilename = "Attempting To Retrieve Missing Filename";
  public const string DeterminingSoftwareVersionInstallUrl = "Determing Software Version Install Url";
  public const string Downloading = "Downloading";
  public const string Unzipping = "Unzipping";
  public const string DetectionFailed = "Detection Failed";
  public const string Applying = "Applying";
  public const string DeterminingComputerArchitecture = "Determining Computer Architecture";
}
