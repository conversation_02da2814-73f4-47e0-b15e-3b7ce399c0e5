using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Domain.Models;
using static Immybot.Backend.Domain.Helpers.ExceptionHelpers;

namespace Immybot.Backend.Application.Maintenance.AssignmentResolution;

internal class DependencyResolver : IDependencyResolver
{
  private readonly IMachineSoftwareOperations _immySoftwareOperations;
  private readonly IMaintenanceActionInitializer _maintenanceActionResolver;
  private readonly IImmyDetectionResolver _immyDetectionResolver;

  public DependencyResolver(
    IMachineSoftwareOperations immySoftwareOperations,
    IMaintenanceActionInitializer maintenanceActionResolver,
    IImmyDetectionResolver immyDetectionResolver)
  {
    _immySoftwareOperations = immySoftwareOperations;
    _maintenanceActionResolver = maintenanceActionResolver;
    _immyDetectionResolver = immyDetectionResolver;
  }

  public async Task<ICollection<MaintenanceAction>> ResolveDependencies(
    IStageRunContext context,
    ICollection<MaintenanceAction> actions)
  {
    var maintActions = new List<MaintenanceAction>();

    foreach (var a in await GetFullActionsList(context, actions))
    {
      // TODO:
      // How do we handle dependencies and dependents from re-ran actions?
      // example:
      //    A dependency failed the first time, so the dependent failed as well.
      //    I fixed the dependency and re-ran it, and it ran succesfully.
      //    I attempted to re-run the dependent, but it failed to resolve the re-ran dependency.
      if (a is StaticAction s)
      {
        if (s.Status != ActionStatus.Killed)
        {
          // static action is good, pass it back
          maintActions.Add(s.MaintenanceAction);

          if (s.EvolvingActions != null)
          {
            foreach (var e in s.EvolvingActions)
            {
              if (e.Status == ActionStatus.Settled)
              {
                // some other maintenance action settled this dependency
                s.MaintenanceAction.DependsOn
                  .Add(new MaintenanceActionDependency
                  {
                    DependsOn = e.SettledByStaticAction?.MaintenanceAction
                  });
                if (e.ParentEvolvingAction?.Status == ActionStatus.Settled)
                {
                  // TODO: what's this about - put a comment here
                  e.ParentEvolvingAction?
                    .SettledByStaticAction?
                    .MaintenanceAction
                    .DependsOn
                    .Add(new MaintenanceActionDependency
                    {
                      DependsOn = e.SettledByStaticAction?.MaintenanceAction
                    });
                }
              }
              else if (e is { Status: ActionStatus.Pending, IsFailed: true })
              {
                // TODO: what's this code path mean? - put a comment here
                foreach (var f in e.FailedByStaticActions)
                {
                  s.MaintenanceAction.DependsOn
                    .Add(new MaintenanceActionDependency { DependsOn = f.MaintenanceAction });
                }
              }
            }
          }
        }
        else
        {
          var m = s.MaintenanceAction;
          await context.FailAction(m, resultReasonMsg: s.Reason);
          // if this maint action existed before we started doing dependency resolution
          // it should be passed back, but in a failed state
          if (!s.InsertedByPrereq) maintActions.Add(m);
        }
      }
    }

    return maintActions;
  }

  /*
   * In the following comment, we use the following shorthand:
   *   - M<n> <=> The nth MaintenanceAction
   *   - S<n> <=> The nth Software
   *   - A<n> <=> The nth StaticAction
   *   - E<m><n> <=> The nth evolving action for the mth static action
   *
   * Each of the provided actions specifies a software and the state that that software will be in once the action is performed. They also specify the action to perform in order to get into the referenced state.
   * E.g. the list of MaintenanceActions (1): [
   *         M1 { state: S3 not installed; action: install S3 },
   *         M2 { state: S4 installed; action: none },
   *         M3 { state: S5 not installed; action: install S5 },
   *         M4 { state: S6 not installed; action: install S6 },
   *       ]
   *
   * Every software might have prerequisites that must be met before said software can be installed.
   * E.g. the list of Software (and their prerequisites) (2): [
   *         S1 [],
   *         S2 [],
   *         S3 [ { if NotInstalled(S1) then Install(S1) }, { if NotInstalled(S2) then Install(S2) } ],
   *         S4 [ { if NotInstalled(S3) then Install(S3) } ],
   *         S5 [ { if NotInstalled(S4) then Install(S4) } ],
   *         S6 [ { Installed(S1) then Uninstall(S1) } ],
   *       ]
   *
   * For those actions that would install a piece of software, we need to make sure the software's prereqs are met. We do this by adding an "EvolvingAction" for each prereq (and each prereq's prereqs, etc.) of each maintenance action, and linking that "EvolvingAction" to a "StaticAction" that represents the maintenance action from the initial list.
   * E.g. given (1) and (2), we construct the list of "IAction" objects (3):
   *       [
   *         E11 { if NotInstalled(S1) then Install(S1) | A1 }
   *         E12 { if NotInstalled(S2) then Install(S2) | A1 }
   *
   *         E31 { if NotInstalled(S1) then Install(S1) | A3 }
   *         E32 { if NotInstalled(S2) then Install(S2) | A3 }
   *         E33 { if NotInstalled(S3) then Install(S3) | A3 }
   *         E34 { if NotInstalled(S4) then Install(S4) | A3 }
   *
   *         E41 { if Installed(S1) then Uninstall(S1) | A4 }
   *       ]
   *
   * We then loop over (1) and insert each static action into (3) at the earliest location where it will resolve*, fulfill*, or fail* an evolving action. If it does resolve an evolving action, we mark the evolving action as resolved. If a static action makes an evolving action unresolvable*, we mark the evolving action as unresolvable. At the end, we loop over (3), removing any failed or resolved evolving actions, handling* any fulfilled evolving actions and killing* any unresolvable evolving actions and their linked static actions.
   *
   *   *fulfilled evolving action - a prerequisite having a Condition that is fulfilled by another maintenance action, thus meaning the ActionToPerform should be handled*
   *     E.g.: Let E := "if Installed(X) then Fail", and M := "Install(X)"; Then E is "fulfilled by" M
   *       - NB: E is "fulfilled", and E's fulfillment action is to "Fail" the maintenance action. That happens in the "handling fulfilled evolving actions" step detailed below
   *   *failed evolving action - a prerequisite having a Condition that is either 1) not met by any other maintenance action, or 2) guaranteed to be not-met by another maintenance action
   *     E.g.: Let E := "if NotInstalled(X) then Fail", and M := "Install(X)"; Then E is "failed by" M
   *   *resolved evolving action - a prerequisite having an ActionToPerform that is either 1) already completed (and won't be un-completed by another maintenance action) or 2) will be completed by another maintenance action
   *     E.g.: Let E := "if NotInstalled(X) then Install(X)", and M := "Install(X)"; Then E is "resolved by" M
   *   *unresolvable evolving action - a prerequisite having an ActionToPerform that is guaranteed to be not completed by another maintenance action
   *     E.g.: Let E := "if NotInstalled(X) then Install(X)", and M := "Uninstall(X)"; Then E is "made unresolvable by" M
   *   *handling fulfilled evolving actions - Performing a prerequisite's ActionToPerform when a prerequisite has been fulfilled and is not unresolvable. For ActionToPerforms of Install/Uninstall, this means inserting a new MaintenanceAction into the list; For ActionToPerform of Fail, this means "killing" the evolving action
   *     E.g.: Let E := "if Installed(X) then Install(y)", and M := "Install(X)"; Then E will result in a new MaintenanceAction M2 := "Install(Y)" being inserted before E in the list. This results in E being "resolved by" M2
   *   *killing evolving actions - Sets the evolving action's parent static action and all of the parent static actions evolving actions to "killed". This is done when the evolving action is fulfilled and has an ActionToPerform of Fail or when the evolving action is fulfilled and is "unresolvable". Killed static actions will result in the static action's MaintenanceAction being put in the "Failed" status
   */

  private async Task<ICollection<IAction>> GetFullActionsList(
    IStageRunContext context,
    ICollection<MaintenanceAction> actions)
  {
    var ret = new List<IAction>();

    // add all the prereq (evolving) actions first
    foreach (var action in actions)
    {
      switch (action.ActionType)
      {
        case MaintenanceActionType.Install:
        case MaintenanceActionType.Update:
        case MaintenanceActionType.Reinstall:
        case MaintenanceActionType.Downgrade:
        case MaintenanceActionType.NoAction when action.SoftwareType != null && action.DetectedVersion != null:
          var software = await context.Args.SoftwareActions
            .GetSoftware(action.SoftwareType!.Value, action.MaintenanceIdentifier, context.Args.StopProcessing, policyContext: context.Args.PolicyContext, cachePolicy: context.Args.CachePolicy);

          if (software is null)
          {
            context.AddLog($"Could not find software #{action.MaintenanceIdentifier} - {action.SoftwareType}");
            continue;
          }

          var prereqs = GetPrereqsForInstallingSoftware(software);
          var softwareSpecifier = new SoftwareSpecifierWithName(software.Identifier, software.SoftwareType, software.Name);
          await AddPrereqsToActionsList(context,
            prereqsToAdd: prereqs,
            actionsList: ret,
            parentMaintenanceAction: action,
            forSoftware: softwareSpecifier);
          break;
      }
    }

    // now loop over static (targetted) actions and add them one-by-one,
    // checking the evolving actions for fulfillment/failure as we go
    foreach (var action in actions)
    {
      var staticAction = new StaticAction { MaintenanceAction = action };
      staticAction.EvolvingActions = ret.Aggregate(
        new List<EvolvingAction>(),
        (agg, a) =>
        {
          if (a is EvolvingAction e && e.ParentAction == action)
          {
            e.ParentStaticAction = staticAction;
            agg.Add(e);
          }
          return agg;
        });

      InsertStaticAction(ret, staticAction);
    }

    // num of items in the list that are static actions, 'Killed', 'Settled'
    // or 'Failed'. When finishedCount equals the number of items in the
    // list, that means all dependencies are resolved
    int finishedCount;
    do
    {
      finishedCount = 0;
      foreach (var a in ret)
      {
        if (
          a is EvolvingAction e &&
          a.Status != ActionStatus.Killed &&
          a.Status != ActionStatus.Settled)
        {
          if (!e.IsFailed && !e.IsFulfilled)
          {
            if (e.ShouldKillStaticAction)
            {
              KillStaticActionDueToPrereqFailure(
                e,
                e.KillStaticActionReason ?? string.Empty);
              // Evolving action should now be killed, so
              // we know it won't get caught in the next pass of the loop, so
              // it's okay to start from the top again
              break;
            }

            try
            {
              await CheckPrereqConditionOnMachine(context, e);
            }
            catch (Exception ex) when (!ex.IsCancellationException())
            {
              KillStaticActionDueToPrereqFailure(
                e,
                $"Exception occurred while processing prerequisite: '{e.SoftwarePrerequisite.ToHumanString()}'",
                ex);
              // Evolving action should now be killed, so
              // we know it won't get caught in the next pass of the loop, so
              // it's okay to start from the top again
              break;
            }
          }

          if (e.ShouldKillStaticAction)
          {
            KillStaticActionDueToPrereqFailure(
              e,
              e.KillStaticActionReason ?? string.Empty);
            // Evolving action should now be killed, so
            // we know it won't get caught in the next pass of the loop, so
            // it's okay to start from the top again
            break;
          }

          if (e.ShouldInsertNewStaticAction && e.ActionableSoftware is not null)
          {
            var software = await context.Args.SoftwareActions.GetSoftware(
              e.ActionableSoftware.Type,
              e.ActionableSoftware.Identifier,
              policyContext: context.Args.PolicyContext,
              cachePolicy: context.Args.CachePolicy,
              token: context.Args.StopProcessing);

            if (software is null)
            {
              KillStaticActionDueToPrereqFailure(
                e,
               $"Could not find software #{ e.ActionableSoftware.Identifier} - { e.ActionableSoftware.Type}");
              continue;
            }

            var action = await _maintenanceActionResolver.BeginBuildingSoftwareMaintenanceAction(
              context,
              software,
              e.DesiredSoftwareState,
              specifiedSoftwareVersion: null);
            using var actionContext = await context.Extend(action);
            var detectionResult = await _immyDetectionResolver.RunDetectionForAction(actionContext);
            if (!detectionResult.Success)
            {
              context.AddLog($"Detection failed for action: {detectionResult.Reason}");
            }

            if (action.ActionType == MaintenanceActionType.NoAction && action.ActionResult != MaintenanceActionResult.Success )
            {
              KillStaticActionDueToPrereqFailure(e, $"The prerequisite {action.MaintenanceDisplayName} was unsuccessful. {action.ActionResultReasonMessage}");
            }
            else
            {
              var staticAction = new StaticAction
              {
                MaintenanceAction = action,
                InsertedByPrereq = true,
              };
              // inserting a static action to cover this fulfilled prereq
              // will mark the prereq as 'Settled'
              InsertStaticAction(ret, staticAction);
            }
            // Evolving action should now be settled, so
            // we know it won't get caught in the next pass of the loop, so
            // it's okay to start from the top again
            break;
          }
        }
        finishedCount++;
      }
    }
    while (finishedCount != ret.Count);

    return ret;
  }

  // This is a stub method for future extensions of the prereq
  // determination logic, e.g. when and if SoftwareVersions ever get to have
  // SoftwarePrerequisites
  //
  // Given a software and the version of the software to be installed,
  // return an ordered list of things that need to happen before
  // software can be installed
  private static IEnumerable<SoftwarePrerequisite> GetPrereqsForInstallingSoftware(
    Software software)
  {
    foreach (var p in software.SoftwarePrerequisites)
    {
      yield return p;
    }
  }

  /// <summary>
  /// Adds new EvolvingActions for each of the provided
  /// </summary>
  /// <param name="context"></param>
  /// <param name="prereqsToAdd"></param>
  /// <param name="actionsList">Will be mutated</param>
  /// <param name="parentMaintenanceAction"></param>
  /// <param name="forSoftware"></param>
  /// <param name="parentEvolvingAction"></param>
  private async Task<bool> AddPrereqsToActionsList(
    IRunContext context,
    IEnumerable<SoftwarePrerequisite> prereqsToAdd,
    List<IAction> actionsList,
    MaintenanceAction parentMaintenanceAction,
    SoftwareSpecifierWithName forSoftware,
    EvolvingAction? parentEvolvingAction = null)
  {
    bool prereqsWereSuccessful = true; // set to false if anything goes wrong
    foreach (var prereq in prereqsToAdd)
    {
      if (prereq.ActionToPerform == ActionToPerform.Fail)
      {
        actionsList.Add(new EvolvingAction
        {
          ParentAction = parentMaintenanceAction,
          SoftwarePrerequisite = prereq,
          DependencyForSoftware = forSoftware,
          ParentEvolvingAction = parentEvolvingAction,
        });
      }
      else
      {
        foreach (var obj in prereq.SoftwaresToPerformActionOn)
        {
          var s = await context.Args.SoftwareActions.GetSoftware(
            obj.SoftwareType,
            obj.SoftwareIdentifier,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);
          var evolvingAction = new EvolvingAction
          {
            ParentAction = parentMaintenanceAction,
            SoftwarePrerequisite = prereq,
            ActionableSoftware = new SoftwareSpecifierWithName(obj.SoftwareIdentifier, obj.SoftwareType, s?.Name ?? "Unknown"),
            DependencyForSoftware = forSoftware,
            ParentEvolvingAction = parentEvolvingAction,
          };
          // If this prereq is saying "install this other software before
          // installing me", then we need to also make sure the other
          // software's prereqs are met by adding its SoftwarePrerequisites
          // to the actionsList as well
          if (prereq.ActionToPerform == ActionToPerform.Install)
          {
            if (parentEvolvingAction != null && parentEvolvingAction.HasAncestorActionableSoftware(obj))
            {
              // one of our ancestors already says that it's trying to
              // install this software - we have an install dependency loop
              //
              // need to cancel this action entirely. We do so by
              // returning false, to indicate to
              // our parent that we didn't succeed in adding to the
              // actions list. Our parent will take it up the chain to
              // the top-most evolving action, which will be put into
              // a "killed" status with the appropriate reason

              return false;
            }
            var software = await context.Args.SoftwareActions
              .GetSoftware(obj.SoftwareType,
              obj.SoftwareIdentifier,
              policyContext: context.Args.PolicyContext,
              cachePolicy: context.Args.CachePolicy,
              token: context.Args.StopProcessing);

            if (software is null)
            {
              evolvingAction.Status = ActionStatus.Missing;
              prereqsWereSuccessful = false;
              actionsList.Add(evolvingAction);
              return false;
            }

            var nestedPrereqs = GetPrereqsForInstallingSoftware(software);
            var softwareSpecifier =
              new SoftwareSpecifierWithName(software.Identifier, software.SoftwareType, software.Name);
            var didSucceed = await AddPrereqsToActionsList(context,
              prereqsToAdd: nestedPrereqs,
              actionsList: actionsList,
              parentMaintenanceAction: parentMaintenanceAction,
              forSoftware: softwareSpecifier,
              parentEvolvingAction: evolvingAction);
            if (!didSucceed)
            {
              if (parentEvolvingAction == null)
              {
                // if we are the top of the evolving action tree, we need to
                // be killed because one of our dependencies says it didn't
                // succeed in adding itself or its own dependencies to the
                // actions list
                evolvingAction.Status = ActionStatus.HasInstallLoop;
                prereqsWereSuccessful = false;
                actionsList.Add(evolvingAction);
                break;
              }
              else
              {
                // if we are not the top of the evolving action tree, we need
                // to return false to indicate to our parent that one of our
                // dependencies didn't succeed in adding itself or its own
                // dependencies to the actions list
                return false;
              }
            }
          }
          actionsList.Add(evolvingAction);
        }
        if (!prereqsWereSuccessful) break;
      }
    }
    return prereqsWereSuccessful;
  }

  private static void KillStaticActionDueToPrereqFailure(
    EvolvingAction failedPrereq,
    string reason,
    Exception? ex = null)
  {
    if (failedPrereq.ParentStaticAction is not { } staticAction) return;
    var prereqs = staticAction.EvolvingActions ?? [];
    staticAction.Status = ActionStatus.Killed;
    staticAction.Reason = reason;
    staticAction.Exception = ex;

    foreach (var e in prereqs)
    {
      // want to remove the static actions that were added just to satisfy
      // this prereq unless the static action is also now satisfying other
      // (non-killed) prereqs. Also, when removing said static actions, we
      // want to make sure to set to pending any prereqs that were made
      // 'unresolvable' by static actions that we're removing
      var settledByStaticAction = e.SettledByStaticAction;
      if (settledByStaticAction != null)
      {
        settledByStaticAction.SettlesActions.Remove(e);
        if (
          settledByStaticAction.InsertedByPrereq &&
          !settledByStaticAction.SettlesActions.Any())
        {
          settledByStaticAction.Status = ActionStatus.Killed;
          foreach (var u in settledByStaticAction.MakesActionsUnresolvable)
          {
            u.MadeUnresolvableByStaticAction = null;
            if (u.Status == ActionStatus.UnresolvableAction)
            {
              // set previously unresolvable actions back to pending
              u.Status = ActionStatus.Pending;
            }
          }
        }
      }
      e.Status = ActionStatus.Killed;
    }
  }

  private static void AddPrerequisiteCheckLog(
    IRunContext context,
    string dependentName,
    string dependsOnName,
    Condition condition,
    ActionToPerform actionToPerform)
  {
    var conditionText = condition == Condition.Installed ? "installed" : "not installed";
    var actionText = actionToPerform.ToString().ToLower();
    context.AddLog($"{dependentName} depends on {dependsOnName} → If {conditionText} then {actionText}.");
  }

  private async Task CheckPrereqConditionOnMachine(
    IStageRunContext context,
    EvolvingAction e)
  {
    if (e.SoftwarePrerequisite.SubjectQualifier == SubjectQualifier.AllOf)
    {
      if (e.SoftwarePrerequisite.Condition == Condition.Installed)
      {
        foreach (var subj in e.SoftwarePrerequisite.SoftwaresForCondition)
        {
          var software = await context.Args.SoftwareActions.GetSoftware(subj.SoftwareType,
            subj.SoftwareIdentifier,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);

          if (software is null)
          {
            KillStaticActionDueToPrereqFailure(
              e,
             $"Could not find software #{ subj.SoftwareIdentifier} - { subj.SoftwareType}");
            return;
          }

          AddPrerequisiteCheckLog(context,
            e.ParentAction.MaintenanceDisplayName ?? string.Empty,
            software.Name,
            e.SoftwarePrerequisite.Condition,
            e.SoftwarePrerequisite.ActionToPerform);

          var detectionResult = await _immySoftwareOperations
            .DetectSoftwareVersion(context, software, false);
          if (detectionResult.Success && detectionResult.Result != null)
          {
            // condition was that all of the specified softwares must be
            // installed, and here we found one that is installed.
            //
            // => add this software to the 'fulfilled subjects' and see if
            //    the prereq is now fulfilled
            e.FulfilledSubjects.Add(subj);
            if (!e.NonFulfilledSubjects.Any())
            {
              // there's no more non-fulfilled software conditions
              //
              // => this prereq's condition is now fulfilled
              e.IsFulfilled = true;
              return;
            }
          }
          else
          {
            // Condition was that all of the specified softwares must be
            // installed, but here we found one that is not installed.
            //
            // => this condition should be 'failed'
            e.FailedSubjects.Add(subj);
            e.IsFailed = true;
            return;
          }
        }
      }
      else // condition: NotInstalled
      {
        foreach (var subj in e.SoftwarePrerequisite.SoftwaresForCondition)
        {
          var software = await context.Args.SoftwareActions.GetSoftware(
            subj.SoftwareType,
            subj.SoftwareIdentifier,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);

          if (software is null)
          {
            KillStaticActionDueToPrereqFailure(
              e,
             $"Could not find software #{ subj.SoftwareIdentifier} - { subj.SoftwareType}");
            return;
          }

          AddPrerequisiteCheckLog(context,
            e.ParentAction.MaintenanceDisplayName ?? string.Empty,
            software.Name,
            e.SoftwarePrerequisite.Condition,
            e.SoftwarePrerequisite.ActionToPerform);

          var detectionResult = await _immySoftwareOperations
            .DetectSoftwareVersion(context, software, false);
          if (detectionResult.Success && detectionResult.Result == null)
          {
            // condition was that all of the specified softwares must not be
            // installed, and here we found one that is not installed.
            //
            // => add this software to the 'fulfilled subjects' and see if
            //    the prereq is now fulfilled
            e.FulfilledSubjects.Add(subj);
            if (!e.NonFulfilledSubjects.Any())
            {
              // there's no more non-fulfilled software conditions
              //
              // => this prereq's condition is now fulfilled
              e.IsFulfilled = true;
              return;
            }
          }
          else
          {
            // condition was that all of the specified softwares must not be
            // installed, but here we found one that is installed.
            //
            // => this condition should be 'failed'
            e.FailedSubjects.Add(subj);
            e.IsFailed = true;
            return;
          }
        }
      }
    }
    else // Qualifier: AnyOf
    {
      if (e.SoftwarePrerequisite.Condition == Condition.Installed)
      {
        foreach (var subj in e.SoftwarePrerequisite.SoftwaresForCondition)
        {
          var software = await context.Args.SoftwareActions.GetSoftware(
            subj.SoftwareType,
            subj.SoftwareIdentifier,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);

          if (software is null)
          {
            KillStaticActionDueToPrereqFailure(
              e,
             $"Could not find software #{ subj.SoftwareIdentifier} - { subj.SoftwareType}");
            return;
          }

          AddPrerequisiteCheckLog(context,
            e.ParentAction.MaintenanceDisplayName ?? string.Empty,
            software.Name,
            e.SoftwarePrerequisite.Condition,
            e.SoftwarePrerequisite.ActionToPerform);

          var detectionResult = await _immySoftwareOperations
            .DetectSoftwareVersion(context, software, false);
          if (detectionResult.Success && detectionResult.Result != null)
          {
            // condition was that any of the specified softwares must be
            // installed, and here we found one that is installed.
            //
            // => this condition should be 'fulfilled'
            e.FulfilledSubjects.Add(subj);
            e.IsFulfilled = true;
            return;
          }
          else
          {
            // condition was that any of the specified softwares must be
            // installed, but here we found one that is not installed
            //
            // => add this software to the 'failed subjects' and see if the
            //    prereq is now failed
            e.FailedSubjects.Add(subj);
            if (!e.NonFailedSubjects.Any())
            {
              // there's no more non-failed software conditions
              //
              // => this prereq's condition is now failed
              e.IsFailed = true;
              return;
            }
          }
        }
      }
      else // condition: NotInstalled
      {
        foreach (var subj in e.SoftwarePrerequisite.SoftwaresForCondition)
        {
          var software = await context.Args.SoftwareActions.GetSoftware(
            subj.SoftwareType,
            subj.SoftwareIdentifier,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);

          if (software is null)
          {
            KillStaticActionDueToPrereqFailure(
              e,
             $"Could not find software #{subj.SoftwareIdentifier} - {subj.SoftwareType}");
            return;
          }

          AddPrerequisiteCheckLog(context,
            e.ParentAction.MaintenanceDisplayName ?? string.Empty,
            software.Name,
            e.SoftwarePrerequisite.Condition,
            e.SoftwarePrerequisite.ActionToPerform);

          var detectionResult = await _immySoftwareOperations
            .DetectSoftwareVersion(context, software, false);
          if (detectionResult.Success && detectionResult.Result == null)
          {
            // condition was that any of the specified softwares must be not
            // installed, and here we found one that is not installed.
            //
            // => this condition should be 'fulfilled'
            e.FulfilledSubjects.Add(subj);
            e.IsFulfilled = true;
            return;
          }
          else
          {
            // condition was that any of the specified softwares must be not
            // installed, but here we found one that is installed.
            //
            // => add this software to the 'failed subjects' and see if the
            //    prereq is now failed
            e.FailedSubjects.Add(subj);
            if (!e.NonFailedSubjects.Any())
            {
              // there's no more non-failed software conditions
              //
              // => this prereq's condition is now failed
              e.IsFailed = true;
              return;
            }
          }
        }
      }
    }
    // made it out of the loop which means it still isn't fulfilled or failed
    // Not sure how this can happen... Let's throw an error so we can debug
    // this easily in the future
    throw new NonExhaustivePrereqCheckerException("Some SoftwarePrerequisite is still in an un-fulfilled and un-failed state after checking all of its conditions");
  }

  private static void InsertStaticAction(
    List<IAction> ret,
    StaticAction staticAction)
  {
    var earliestIndex = ret.Count;
    var action = staticAction.MaintenanceAction;

    // if static action will result in a software being present,
    // then evolving actions that depend on the presence of the
    // software should be fulfilled, and evolving actions that depend
    // on the non-presence of the software should be failed. If static
    // action will result in the software being not present, then
    // evolving actions taht depend on the presence of the software
    // should be failed, and evolving actions that depend on the
    // non-presence of the software should be fulfilled
    var (failureCondition, fulfillCondition) =
      staticAction.ActionWouldResultIn == ActionResult.Installed
      ? (Condition.NotInstalled, Condition.Installed)
      : (Condition.Installed, Condition.NotInstalled);

    foreach (var a in ret)
    {
      var currentIndex = ret.IndexOf(a);
      if (
        a is not EvolvingAction e ||
        e.Status == ActionStatus.Settled ||
        e.Status == ActionStatus.HasInstallLoop ||
        e.Status == ActionStatus.Killed)
      {
        continue;
      }
      if (
        e.ActionableSoftware != null && staticAction.ActionableSoftware != null &&
        e.ActionableSoftware.Type == staticAction.ActionableSoftware.Type &&
        e.ActionableSoftware.Identifier == staticAction.ActionableSoftware.Identifier)
      {
        if (e.ActionWouldResultIn == staticAction.ActionWouldResultIn)
        {
          // evolving action can be settled now, without even worrying about
          // the condition
          e.Status = ActionStatus.Settled;
          staticAction.SettlesActions.Add(e);
          e.SettledByStaticAction = staticAction;
          if (currentIndex < earliestIndex) earliestIndex = currentIndex;

          // move to the next evolving action since this one is now settled
          continue;
        }
        else
        {
          // if this evolving action ever gets fulfilled, it should result in
          // an UnresolvablePrerequisite because we just found a static
          // action that directly contradicts the action that this prereq
          // will need to perform.

          // The edge-case here is if the static action is an update if found action and the
          // action was not found.  In this case, we want to allow the action to be installed
          if (staticAction.MaintenanceAction.DesiredSoftwareState is not DesiredSoftwareState.UpdateIfFound)
          {
            e.Status = ActionStatus.UnresolvableAction;
            staticAction.MakesActionsUnresolvable.Add(e);
            e.MadeUnresolvableByStaticAction = staticAction;
          }
        }
      }
      if (staticAction.ActionWouldResultIn == ActionResult.Nothing) continue;
      if (e.IsFulfilled || e.IsFailed) continue;
      if (e.SoftwarePrerequisite.Condition == failureCondition)
      {
        // If [Any|All] of SoftwaresForCondition ARE [Installed|NotInstalled]
        if (e.SoftwarePrerequisite.SubjectQualifier == SubjectQualifier.AllOf)
        {
          // all the softwares in `SoftwaresForCondition` must pass the
          // 'failureCondition'. So if any fail it, the prereq should get
          // 'IsFailed = true'
          var subj = e.SoftwarePrerequisite
            .SoftwaresForCondition
            .FirstOrDefault(s =>
              s.SoftwareType == action.SoftwareType &&
              s.SoftwareIdentifier == action.MaintenanceIdentifier);
          if (subj != null)
          {
            // this software must be installed, but there's a target
            // assignment saying that it can't be
            //
            // => this prereq's condition can't be fulfilled
            e.IsFailed = true;
            e.FailedSubjects.Add(subj);
            e.FailedByStaticActions.Add(staticAction);
          }
          else
          {
            // none of the evolving action's SoftwaresForCondition match the
            // new static action's software, so this evolving action's good,
            // for now
          }
        }
        else
        {
          // any of the softwares in `SoftwaresForCondition` must pass the
          // 'failureCondition'. So if they're all failed, the prereq should
          // get 'IsFailed = true'
          var subj = e.NonFailedSubjects
            .Find(s =>
            s.SoftwareType == action.SoftwareType &&
            s.SoftwareIdentifier == action.MaintenanceIdentifier);
          if (subj != null)
          {
            // one of the evolving action's SoftwaresForCondition is the new
            // static action's software
            //
            // => this particular SoftwaresForCondition software has failed,
            //    but it's possible another one will be fulfilled
            e.FailedSubjects.Add(subj);
            e.FailedByStaticActions.Add(staticAction);

            // See if this SoftwaresForCondition was the only remaining
            // unfailed one
            if (!e.NonFailedSubjects.Any())
            {
              // there's no more non-failed software conditions
              //
              // => this prereq's condition can't be fulfilled
              e.IsFailed = true;
            }
            else
            {
              // there's still at least one remaining non-failed software
              // condition, so this evolving action's good, for now
            }
          }
          else
          {
            // none of the evolving action's SoftwaresForCondition match the
            // new static action's software, so this evolving action's good,
            // for now
          }
        }
      }
      else if (e.SoftwarePrerequisite.Condition == fulfillCondition)
      {
        // If [Any|All] of SoftwaresForCondition ARE [NotInstalled|Installed]
        if (e.SoftwarePrerequisite.SubjectQualifier == SubjectQualifier.AnyOf)
        {
          // any of the softwares in `SoftwaresForCondition` must pass the
          // 'fulfillCondition'. So if any pass it, the prereq should get
          // 'IsFulfilled = true'
          var subj = e.SoftwarePrerequisite
            .SoftwaresForCondition
            .FirstOrDefault(s =>
              s.SoftwareType == action.SoftwareType &&
              s.SoftwareIdentifier == action.MaintenanceIdentifier);

          if (subj != null)
          {
            // the new static action says one of this evolving action's
            // SoftwaresForCondition is fulfilled
            //
            // => this prereq's condition is now fulfilled
            e.IsFulfilled = true;
            e.FulfilledSubjects.Add(subj);
            e.FulfilledByStaticActions.Add(staticAction);

            // make sure we insert `action` at least before this prereq
            if (currentIndex < earliestIndex) earliestIndex = currentIndex;
          }
          else
          {
            // none of the evolving action's SoftwaresForCondition match the
            // new static action's software, so this evolving action can't be
            // fulfilled, for now
          }
        }
        else
        {
          // all of the softwares in the `SoftwaresForCondition` must pass
          // the 'fulfillCondition'. So if they're all passed, the prereq
          // should get 'IsFulfilled = true'
          var subj = e.NonFulfilledSubjects
            .Find(s =>
              s.SoftwareType == action.SoftwareType &&
              s.SoftwareIdentifier == action.MaintenanceIdentifier);

          if (subj != null)
          {
            // the new static action says one of this evolving action's
            // SoftwaresForCondition is fulfilled
            //
            // => this particular SoftwaresForCondition software has passed,
            //    but it's possible another one isn't yet passed
            e.FulfilledSubjects.Add(subj);
            e.FulfilledByStaticActions.Add(staticAction);
            if (!e.NonFulfilledSubjects.Any())
            {
              // there's no more non-fulfilled software conditions
              //
              // => this prereq's condition is now fulfilled
              e.IsFulfilled = true;
            }
            else
            {
              // there's still at least one remaining non-fulfilled software
              // condition, so this evolving action can't be fulfilled,
              // for now
            }

            // make sure we insert `action` at least before this prereq
            if (currentIndex < earliestIndex) earliestIndex = currentIndex;
          }
          else
          {
            // none of the evolving action's SoftwaresForCondition match the
            // new static action's software, so this evolving action can't be
            // fulfilled, for now
          }
        }
      }
    }

    // after we're done updating the evolving actions, add the static action
    // to the list at the appropriate place
    ret.Insert(earliestIndex, staticAction);
  }
}
