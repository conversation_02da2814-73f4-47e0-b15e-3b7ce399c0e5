using System;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using System.Diagnostics;

namespace Immybot.Backend.Application.Maintenance;

#pragma warning disable S3881 // "IDisposable" should be implemented correctly
public class LogPhaseHandle : ILogPhaseHandle
#pragma warning restore S3881 // "IDisposable" should be implemented correctly
{
  private readonly RunContext _context;
  private readonly Activity? _activity;
  public SessionPhase Phase { get; }
  public Exception? PhaseException { get; set; }

  internal LogPhaseHandle(RunContext context, SessionPhase phase, Activity? activity)
  {
    _context = context;
    Phase = phase;
    _activity = activity;
  }

  public async Task SetResultState(SessionPhaseStatus resultState)
  {
    Phase.Status = resultState;
    Phase.DateCompletedUtc = DateTime.UtcNow;
    if (resultState == SessionPhaseStatus.Succeeded)
    {
      Phase.ProgressPercentComplete = 100;
      Phase.ProgressCompleted = true;
    }
    await _context.UpdatePhase(Phase);
  }

  public async Task SetProgressPercentComplete(int step, int totalSteps, string? progressStatus = null)
  {
    var percentComplete = (int)Math.Round((double)(100 * step) / totalSteps);
    if (Phase.ProgressPercentComplete != percentComplete || (progressStatus is string a && Phase.ProgressStatus != a))
    {
      Phase.ProgressPercentComplete = percentComplete;
      if (progressStatus != null) Phase.ProgressStatus = progressStatus;
      await _context.UpdatePhase(Phase);
    }
  }

  public async Task SetProgressPercentComplete(
    decimal? percentProgress,
    string? progressStatus = null,
    bool isManualProgressControl = false)
  {
    if (Phase.ProgressPercentComplete == percentProgress && (progressStatus is not string ps || Phase.ProgressStatus == ps))
      return; // nothing to do - no change to the progress

    // if the run context isn't an action run context
    // or the run context uses manual progress control and this is a manual update
    // or if the run context doesn't use manual progress control and this is not a manual update
    if (_context is not ActionRunContext a
      || (a.Action.UsesManualProgressControl == isManualProgressControl))
    {
      Phase.ProgressPercentComplete = percentProgress;
      if (progressStatus != null) Phase.ProgressStatus = progressStatus;
      await _context.UpdatePhase(Phase);
    }
  }

  public async ValueTask DisposeAsync()
  {
    if (PhaseException?.IsCancellationException() is true)
    {
      // do not update result state when a cancellation exception is thrown
    }
    else if (PhaseException is not null)
    {
      // fail the phase if an exception was thrown
      await SetResultState(SessionPhaseStatus.Failed);
    }
    else if (Phase.Status is SessionPhaseStatus.Running)
    {
      await SetResultState(SessionPhaseStatus.Succeeded);
    }

    if (_context is ActionRunContext a) a.CurrentActionPhase = null;
    else if (_context is StageRunContext s) s.CurrentStagePhase = null;

    _activity?.Dispose();
  }
}
