using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Ninite;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Shared.Abstractions.Device;
using Polly;

namespace Immybot.Backend.Application.Maintenance;

internal class RunContextArgs : IRunContextArgs
{
  // dependencies
  public IFindScriptByNameCmd FindScriptByNameCmd { get; }
  public ISoftwareActions SoftwareActions { get; }
  public IScriptActions ScriptActions { get; }
  public IScriptInvoker ScriptInvoker { get; }
  public IPreflightScriptInvoker PreflightScriptInvoker { get; }
  public IEphemeralAgentAcquisition EphemeralAgentAcquisition { get; }
  public ICachedSingleton<ApplicationPreferences> CachedAppPreferences { get; }
  public ICachedCollection<TenantPreferences> CachedTenantPreferences { get; }
  public IProviderActions ProviderActions { get; }
  public IRunContextActions RunContextActions { get; set; }
  public IMaintenanceTaskActions MaintenanceTaskActions { get; }
  public ITargetAssignmentActions TargetAssignmentActions { get; }
  public IMediaActions MediaActions { get; }
  public IMetascriptInvoker MetascriptInvoker { get; }
  public NiniteSoftwareCache NiniteSoftwareCache { get; }
  public IDomainEventEmitter DomainEventEmitter { get; }
  public IDomainEventReceiver DomainEventReceiver { get; }
  public IPowershellLoader PowershellLoader { get; }
  public IRunContextComputerActions ComputerActions { get; }
  public ITargetPopulator TargetPopulator { get; }
  public IEphemeralAgentSessionStore EphemeralAgentSessionHandler { get; }
  public IDynamicFormService DynamicFormService { get; }
  public IFeatureManager FeatureManager { get; }
  public IComputerMaintenanceDateOperations ComputerMaintenanceDateOperations { get; }
  public IMaintenanceActionDiagnosticEventHubService MaintenanceActionDiagnosticEventHubService { get; }
  public KeyedLocker KeyedLocker { get; }
  public ISystemTime SystemTime { get; }
  public ILicenseActions LicenseActions { get; }
  public ITenantActions TenantActions { get; }
  public IPersonActions PersonActions { get; }
  public IProviderLinkActions ProviderLinkActions { get; }
  public IMaintenanceItemOrderActions MaintenanceItemOrderActions { get; }
  public IScheduleActions ScheduleActions { get; }
  public IComputerInventoryActions ComputerInventoryActions { get; }
  public IMaintenanceActionActions MaintenanceActionActions { get; }

  public RunContextArgs(
    IFindScriptByNameCmd findScriptByNameCmd,
    ISoftwareActions softwareActions,
    IScriptActions scriptActions,
    IScriptInvoker scriptInvoker,
    IEphemeralAgentAcquisition ephemeralAgentAcquisition,
    IPreflightScriptInvoker preflightScriptInvoker,
    ICachedSingleton<ApplicationPreferences> cachedAppPreferences,
    ICachedCollection<TenantPreferences> cachedTenantPreferences,
    IProviderActions providerActions,
    IRunContextActionsList runContextActions,
    IMaintenanceTaskActions maintenanceTaskActions,
    ITargetAssignmentActions targetAssignmentActions,
    IMediaActions mediaActions,
    IMetascriptInvoker metascriptInvoker,
    NiniteSoftwareCache niniteSoftwareCache,
    IDomainEventEmitter domainEventEmitter,
    IDomainEventReceiver domainEventReceiver,
    IPowershellLoader powershellLoader,
    IRunContextComputerActions computerActions,
    ITargetPopulator targetPopulator,
    IEphemeralAgentSessionStore ephemeralAgentSessionHandler,
    IDynamicFormService dynamicFormService,
    IFeatureManager featureManager,
    IComputerMaintenanceDateOperations computerMaintenanceDateOperations,
    IMaintenanceActionDiagnosticEventHubService maintenanceActionDiagnosticEventHubService,
    ISystemTime systemTime,
    ILicenseActions licenseActions,
    ITenantActions tenantActions,
    IPersonActions personActions,
    IProviderLinkActions providerLinkActions,
    IMaintenanceItemOrderActions maintenanceItemOrderActions,
    IScheduleActions scheduleActions,
    IComputerInventoryActions computerInventoryActions,
    IMaintenanceActionActions maintenanceActionActions,
    KeyedLocker keyedLocker)
  {
    FindScriptByNameCmd = findScriptByNameCmd;
    SoftwareActions = softwareActions;
    ScriptActions = scriptActions;
    ScriptInvoker = scriptInvoker;
    PreflightScriptInvoker = preflightScriptInvoker;
    EphemeralAgentAcquisition = ephemeralAgentAcquisition;
    CachedAppPreferences = cachedAppPreferences;
    CachedTenantPreferences = cachedTenantPreferences;
    ProviderActions = providerActions;
    RunContextActions = runContextActions;
    MaintenanceTaskActions = maintenanceTaskActions;
    TargetAssignmentActions = targetAssignmentActions;
    MediaActions = mediaActions;
    MetascriptInvoker = metascriptInvoker;
    NiniteSoftwareCache = niniteSoftwareCache;
    DomainEventEmitter = domainEventEmitter;
    DomainEventReceiver = domainEventReceiver;
    PowershellLoader = powershellLoader;
    ComputerActions = computerActions;
    TargetPopulator = targetPopulator;
    EphemeralAgentSessionHandler = ephemeralAgentSessionHandler;
    DynamicFormService = dynamicFormService;
    FeatureManager = featureManager;
    ComputerMaintenanceDateOperations = computerMaintenanceDateOperations;
    MaintenanceActionDiagnosticEventHubService = maintenanceActionDiagnosticEventHubService;
    SystemTime = systemTime;
    LicenseActions = licenseActions;
    TenantActions = tenantActions;
    PersonActions = personActions;
    ProviderLinkActions = providerLinkActions;
    MaintenanceItemOrderActions = maintenanceItemOrderActions;
    ScheduleActions = scheduleActions;
    ComputerInventoryActions = computerInventoryActions;
    MaintenanceActionActions = maintenanceActionActions;
    KeyedLocker = keyedLocker;
  }

  // session arguments
  public CancellationToken StopProcessing { get; set; }
  public Computer? Computer { get; set; }
  public Person? Person { get; set; }

  private Tenant? _tenant;

  public Tenant Tenant
  {
    get
    {
      return _tenant ?? throw new InvalidOperationException("Tenant not set");
    }
    set
    {
      _tenant = value;
    }
  }

  public Priority Priority { get; set; } = Priority.Now;
  public int? ScheduleId { get; set; }
  public Schedule? Schedule { get; set; }
  public Guid? CacheGroupId { get; set; }
  public ICollection<Guid>? ExcludeProviderDeviceGroupTypes { get; set; }
  public Person? ManuallyTriggeredBy { get; set; }
  public bool InstallWindowsUpdates { get; set; }
  public RebootPreference RebootPreference { get; set; } = RebootPreference.Normal;
  public PromptTimeoutAction PromptTimeoutAction { get; set; } = PromptTimeoutAction.Suppress;
  public bool AutoConsentToReboots { get; set; }
  public bool HasPromptedPendingReboot { get; set; }
  public bool UserCancelledReboot { get; set; }
  public TimeSpan PromptTimeout { get; set; }
  public bool CacheOnly { get; set; }
  public RunContextRuntimeProperties Fields { get; set; } = new RunContextRuntimeProperties();
  public int? SpecifiedAssignmentId { get; set; }
  public DatabaseType? SpecifiedAssignmentType { get; set; }
  public IAsyncPolicy? CachePolicy { get; set; }
  public Context? PolicyContext { get; set; }
  public bool RerunningAction { get; set; }
  public MaintenanceItem? MaintenanceItem { get; set; }
  public MaintenanceEmailConfiguration MaintenanceEmailConfiguration { get; set; } = new MaintenanceEmailConfiguration();
  public MaintenanceOnboardingConfiguration MaintenanceOnboardingConfiguration { get; set; } = new MaintenanceOnboardingConfiguration();
  public MaintenanceAgentUpdatesConfiguration MaintenanceAgentUpdatesConfiguration { get; set; } = new MaintenanceAgentUpdatesConfiguration();

  public MaintenanceSchedulingConfiguration MaintenanceSchedulingConfiguration { get; set; } = new MaintenanceSchedulingConfiguration();
  public string? TimeZoneInfoId { get; set; }
  public bool Repair { get; set; }
  public bool RunInventoryInDetection { get; set; }
  public IMetascriptMessageHandler? MetascriptMessageHandler { get; set; }
  public ComputerOfflineMaintenanceSessionBehavior OfflineBehavior { get; set; } = ComputerOfflineMaintenanceSessionBehavior.Skip;
  public bool SuppressRebootsDuringBusinessHours { get; set; }
  public bool AppliedOnConnect { get; set; }
  public bool OnlyResolveOnboardingAssignments { get; set; }
  public int? ManuallyResumedByPersonId { get; set; }
  public bool ManuallyResumed { get; set; }
  public bool UseWinningDeployment { get; set; }
  public bool HasSessionFeatureBeenTracked { get; set; }
  public int? RerunFromScheduleId { get; set; }
  public Guid? SessionGroupId { get; set; }

  // active hours
  public string? ComputerTimeZoneInfoId { get; set; }
  public int? ActiveHoursStart { get; set; }
  public int? ActiveHoursEnd { get; set; }
}
