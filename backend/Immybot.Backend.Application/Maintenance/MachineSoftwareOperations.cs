using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management.Automation.Interpreter;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Bits;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.Ninite;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using ImmyBot.Backend.Application.Maintenance;
using Immybot.Shared.Scripts;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NuGet.Versioning;
using Polly;
using static Immybot.Backend.Application.Interface.Maintenance.IMachineSoftwareOperations;
using static Immybot.Backend.Domain.Helpers.ExceptionHelpers;
using GlobalScriptIds = Immybot.Backend.GlobalSoftwarePersistence.Interface.Constants.SystemScriptIds;

namespace Immybot.Backend.Application.Maintenance;

internal class MachineSoftwareOperations : IMachineSoftwareOperations
{
  private static readonly string BASE_IMMY_PATH = PathHelpers.Combine("Temp", "ImmyBot");
  public static readonly string UpgradeStrategyMissingMessage = "Upgrade failed: No Upgrade Strategy specified on the software.";
  public static readonly string InstallationScriptIsMissingMessage = "Could not find an installation script on the software version or a default install script on the software.  Cannot continue with the installation";
  public static readonly string UpgradeScriptIsMissingMessage = "Could not find an upgrade script on the software version or a default upgrade script on the software.  Cannot continue with the upgrade";
  public static readonly string SoftwareTestScriptMissingMessage = "A test script is required by the software, but a test script could not be found.";
  public static readonly string VersionTestScriptMissingMessage = "A test script is required by the software version but a test script could not be found.";
  public static readonly string TestScriptDidNotExplicitlyReturnFalseMessage = "The script output did not explicitly equal false.";
  public static readonly string TestPendingRebootScriptMissingMessage = "Could not find Test-PendingReboot script. Please contact immy.bot support for assistance.";

  // RebootComputer messages
  private const string _forceRebootMessage = "Reboot preference is 'Force' reboots.";
  private const string _forceRebootDueToSoftwareMessage = "Forced reboot due to software requirement.";
  private const string _suppressRebootMessage = "Reboot preference is 'Suppress' reboots.";
  private const string _pendingRebootNotFoundMessage = "A pending reboot was not found.";
  private const string _pendingRebootFoundMessage = "A pending reboot was found.";
  private const string _withinBusinessHoursMessage = "Reboot suppressed due to business hours.";
  private const string _autoConsentedRebootMessage = "Auto-consented reboot executed.";
  private const string _rebootCancelledOrSuppressedMessage = "Reboot cancelled by user or suppressed by timeout action.";
  private const string _rebootCancelledMessage = "Reboot cancelled by user.";
  private const string _promptedRebootMessage = "User prompted for reboot.";
  private const string _rebootActionMessage = "Operation succeeded with timeout action set to 'Reboot'.";
  private const string _rebootSuppressMessage = "Operation succeeded with timeout action set to 'Suppress'.";
  private const string _rebootFailedSessionMessage = "Operation failed with timeout action set to 'Fail Session'.";
  private const string _rebootFailedActionMessage = "Operation failed with timeout action set to 'Fail Action'.";
  private const string _unknownPromptActionMessage = "Operation succeeded with an unknown timeout action.";

  private readonly ILogger<MachineSoftwareOperations> _logger;
  private readonly IAzureBlobStorageSasService _azureBlobStorageSasService;
  private readonly IInventoryDeviceCmd _inventoryDeviceCmd;
  private readonly IPowershellLoader _powershellLoader;
  private readonly Func<ImmybotDbContext> _dbContextFactory;

  private static List<ChocoPackage> GetChocoPackagesFromScriptOutput(ICollection<object> scriptOutput)
  {
    if (scriptOutput == null) return [];

    var result = new List<ChocoPackage>();

    // skip the first line which shows something like, Chocolatey v0.11.2
    var outputToAnalyze = scriptOutput.Skip(1);

    foreach (var line in outputToAnalyze)
    {
      // if the line contains two items, then add it to the result
      var items = line?.ToString()?.Split(" ");

      if (items?.Length == 2)
      {
        result.Add(new ChocoPackage
        {
          DisplayName = items[0],
          DisplayVersion = items[1],
        });
      }
    }

    return result;
  }

  private async Task<List<ChocoPackage>?> GetBulkChocoPackages(IRunContext context)
  {
    if (context.Args.Fields.DidTryGetBulkChocoList) return context.Args.Fields.BulkChocoList;
    context.AddLog($"Gathering all software installed by Chocolatey");
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;

    // Long output from RunPowershell is often truncated.
    // CSV encoding this data reduces the string length from ~90000 to ~1200
    var script = _powershellLoader.GetPowerShellScript("Get-InstalledChocolateySoftware", null);
    script.OutputType = ScriptOutputType.Table;
    var result = await context.RunScript(
      script,
      scriptTimeouts.Detection,
      context.Args.StopProcessing);

    if (result.HadTerminatingException)
    {
      context.AddLog(result.GetErrorString());
      return null;
    }

    if (result.OutputAsCollection.Count <= 0) return null;

    var packages = GetChocoPackagesFromScriptOutput(result.OutputAsCollection);

    context.Args.Fields.DidTryGetBulkChocoList = true;
    try
    {
      context.Args.Fields.BulkChocoList.Clear();
      context.Args.Fields.BulkChocoList.AddRange(packages);
      return context.Args.Fields.BulkChocoList;
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      context.AddLog("Exception occurred while getting chocolatey software list from computer.");
      context.AddLog(ex.ToString());
      return null;
    }
  }

  private async Task<ImmyOperationResult> InstallChocolateyIfNotPresent(IStageRunContext context)
  {
    if (context.Args.Fields.HasChocolatey) return new ImmyOperationResult(true);
    if (context.Args.Fields.FailedToInstallChocolatey)
      return new ImmyOperationResult(false, "Failed to install Chocolatey.");

    ILogPhaseHandle? stagePhase = null;
    if (context is not IActionRunContext)
    {
      // only begin a new stage phase if we are not within an action
      stagePhase = await context.BeginNewStagePhase("Install Chocolatey");
    }
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    context.AddLog("Checking if Chocolatey is installed.");
    var chocoVersionScript = _powershellLoader.GetPowerShellScript("Get-InstalledChocolateyVersion", null);
    try
    {
      // check if it is already installed
      var verifyRes = await context.RunScript(
        chocoVersionScript,
        scriptTimeouts.Detection,
        context.Args.StopProcessing);

      if (!verifyRes.HadTerminatingException && NuGetVersion.TryParse(verifyRes.OutputAsObject?.ToString(), out var existingVer))
      {
        context.AddLog($"Found Chocolatey {existingVer.ToNormalizedString()}.");
      }
      else
      {
        // install chocolatey
        var installScript = await context.GetScriptById(GlobalScriptIds.ChocolateyIntallScriptId, DatabaseType.Global);
        if (installScript == null) return new ImmyOperationResult(false, "Could not retrieve choco install script");
        context.AddLog("Chocolatey was not found, and will be installed.");
        var res = await context.RunScript(
          installScript,
          scriptTimeouts.Install,
          context.Args.StopProcessing);

        if (res.HadTerminatingException)
        {
          if (stagePhase is not null)
            await stagePhase.SetResultState(SessionPhaseStatus.Failed);

          return new ImmyOperationResult(false, res.GetErrorString());
        }

        // verify it installed by checking its version
        var verifyInstalledSuccessfullyRes = await context.RunScript(
          chocoVersionScript,
          scriptTimeouts.Detection,
          context.Args.StopProcessing);

        if (verifyInstalledSuccessfullyRes.HadTerminatingException) return new ImmyOperationResult(false, reason: verifyRes.GetErrorString());

        if (NuGetVersion.TryParse(verifyInstalledSuccessfullyRes.OutputAsObject?.ToString(), out var ver))
        {
          context.AddLog($"Chocolatey {ver.ToNormalizedString()} installed.");
        }
        else
        {
          context.Args.Fields.FailedToInstallChocolatey = true;
          context.AddLog("Chocolatey failed to install.");
          if (stagePhase is not null)
            await stagePhase.SetResultState(SessionPhaseStatus.Failed);
          return new ImmyOperationResult(false, "Chocolatey failed to install");
        }
      }
      context.Args.Fields.HasChocolatey = true;
      return new ImmyOperationResult(true);
    }
    catch (CommandTimedOutException ex)
    {
      context.Args.Fields.FailedToInstallChocolatey = true;
      context.AddLog("Chocolatey failed to install due to the command timing out.");
      if (stagePhase is not null)
        await stagePhase.SetResultState(SessionPhaseStatus.Failed);
      return new ImmyOperationResult(false, ex.Message);
    }
    finally
    {
      if (stagePhase is not null) await stagePhase.DisposeAsync();
    }
  }

  private async Task<ImmyOperationResult<string?>> DetectSoftwareVersionStringForChocolatey(
    IStageRunContext context,
    Software software,
    bool ignoreCache)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    var installChocoResult = await InstallChocolateyIfNotPresent(context);
    if (!installChocoResult.Success)
      return new ImmyOperationResult<string?>(false, installChocoResult.Reason);
    var detectedVersion = "";
    context.AddLog($"{software.Name}: Getting installed version from Chocolatey [{software.Identifier}]");
    if (ignoreCache)
    {
      // Run normal one off choco detection
      try
      {
        if (software.DetectionScript == null)
          return new ImmyOperationResult<string?>(false, "Detection script was not found");
        var detectedVersionResult = await context.RunScript(
          software.DetectionScript,
          scriptTimeouts.Detection,
          context.Args.StopProcessing);
        if (detectedVersionResult.HadTerminatingException)
        {
          return new ImmyOperationResult<string?>(false, reason: detectedVersionResult.GetErrorString());
        }

        detectedVersion = detectedVersionResult.OutputAsObject?.ToString();
        return new ImmyOperationResult<string?>(true, result: detectedVersion);
      }
      catch (CommandTimedOutException ex)
      {
        return new ImmyOperationResult<string?>(false, ex.Message);
      }
    }
    else
    {
      var packages = await GetBulkChocoPackages(context);
      detectedVersion = packages?.Find(a => a.DisplayName == software.Identifier)?.DisplayVersion;
      return new ImmyOperationResult<string?>(true, result: detectedVersion);
    }
  }

  private async Task<ImmyOperationResult<bool>> VerifyRemoteHash(
    IRunContext context,
    string path,
    string? expectedMD5,
    bool throwIfFails = false)
  {
    if (string.IsNullOrEmpty(expectedMD5))
    {
      context.AddLog("No hash provided.  Skipping hash check.");
      return new ImmyOperationResult<bool>(true, result: false);
    }
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    context.AddLog($"Calculating hash of {path}.");
    try
    {
      var script = _powershellLoader.GetPowerShellScript("Compute-FileMd5", null, new()
      {
        { "path", PathHelpers.UseWindowsSeparators(path) },
      });
      var md5JsonResult = await context.RunScript(
        script,
        scriptTimeouts.Action,
        context.Args.StopProcessing);

      if (md5JsonResult.HadTerminatingException)
        return new ImmyOperationResult<bool>(false, md5JsonResult.GetErrorString());

      var md5 = md5JsonResult.OutputAsObject?.ToString();
      if (string.IsNullOrEmpty(md5))
      {
        context.AddLog($"Error retrieving hash for {path}.");
        if (throwIfFails)
          throw new DownloadFailedException("Remote hash verification failed");
        return new ImmyOperationResult<bool>(true, result: false);
      }


      if (md5.ToUpper() == expectedMD5.ToUpper())
      {
        context.AddLog($"Hashes match.");
        return new ImmyOperationResult<bool>(true, result: true);
      }
      else
      {
        context.AddLog($"Hash Mismatch. Expected: {expectedMD5} Actual: {md5}\n\nDownload may have been previously interrupted, or package hash we expect is incorrect");
        if (throwIfFails)
          throw new DownloadFailedException("Remote hash verification failed");
        return new ImmyOperationResult<bool>(true, result: false);
      }
    }
    catch (CommandTimedOutException ex)
    {
      if (throwIfFails) throw new DownloadFailedException($"Remote hash verification failed: {ex.Message}");
      return new ImmyOperationResult<bool>(false, ex.Message);
    }
  }

  public MachineSoftwareOperations(
    ILogger<MachineSoftwareOperations> logger,
    IAzureBlobStorageSasService azureBlobStorageSasService,
    IInventoryDeviceCmd inventoryDeviceCmd,
    IPowershellLoader powershellLoader,
    Func<ImmybotDbContext> dbContextFactory)
  {
    _logger = logger;
    _azureBlobStorageSasService = azureBlobStorageSasService;
    _inventoryDeviceCmd = inventoryDeviceCmd;
    _powershellLoader = powershellLoader;
    _dbContextFactory = dbContextFactory;
  }

  public async Task<ImmyOperationResult> InstallSoftware(
    IActionRunContext context,
    Software software,
    SoftwareVersion? version,
    ILogPhaseHandle? progressPhase = null)
  {
    // ensure choco is present if we are installing choco software
    if (software.SoftwareType == SoftwareType.Chocolatey || version?.SoftwareType == SoftwareType.Chocolatey)
    {
      var installChocoResult = await InstallChocolateyIfNotPresent(context);
      if (!installChocoResult.Success)
      {
        return installChocoResult;
      }
    }

    // ensure ninite is present if we are installing ninite software
    if (software.SoftwareType == SoftwareType.Ninite || version?.SoftwareType == SoftwareType.Ninite)
    {
      var installNiniteResult = await InstallNiniteIfNotPresent(context);
      if (!installNiniteResult.Success)
      {
        return installNiniteResult;
      }
    }

    context.AddLog($"Installing version {version?.SemanticVersion}.");

    context.Args.StopProcessing.ThrowIfCancellationRequested();

    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;

    Script installScript;

    if (version?.InstallScript is Script versionInstallScript)
    {
      // use the version install script if present
      context.AddLog("Will use the installation script found on the version.");
      installScript = versionInstallScript;
    }
    else if (software.InstallScript is Script softwareInstallScript)
    {
      // or use the default software install script
      context.AddLog("Will use the default install script found on the software.");
      installScript = softwareInstallScript;
    }
    else
    {
      // or fail because we don't have an install script
      context.AddLog(InstallationScriptIsMissingMessage);
      return new(false, reason: InstallationScriptIsMissingMessage);
    }

    try
    {
      if (software.SoftwareType == SoftwareType.Chocolatey)
      {
        context.AddLog($"Installing Chocolatey Software {software.Name}.");
        await context.UpdateAction(status: MaintenanceActionStatus.PerformingAction);
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.RunningInstallScript);

        var res = await context.RunScript(
          installScript,
          scriptTimeouts.Install,
          context.Args.StopProcessing,
          isPrimary: true);

        if (res.HadTerminatingException)
        {
          return new(false, reason: res.GetErrorString());
        }

        context.AddLog($"Rebooting the computer after installation.");
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
        await RebootComputer(context, treatForceAsIfNecessary: true);
      }
      else if (software.SoftwareType == SoftwareType.Ninite)
      {
        context.AddLog($"Installing Ninite software {software.Name}");
        await context.UpdateAction(status: MaintenanceActionStatus.PerformingAction);
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.RunningInstallScript);

        var res = await context.RunScript(
          installScript,
          scriptTimeouts.Install,
          context.Args.StopProcessing,
          isPrimary: true);

        if (res.HadTerminatingException)
        {
          return new(false, reason: res.GetErrorString());
        }

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
        await RebootComputer(context, treatForceAsIfNecessary: true);
      }
      else
      {
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.RunningInstallScript);
        var install = await RunScript(context, installScript, scriptTimeouts.Install,
          software: software,
          softwareVersion: version,
          isPrimary: true);
        if (!install.Success)
        {
          if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: install.Reason);
          return new(false, reason: install.Reason);
        }
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
        await RebootComputer(context, software.RebootNeeded, treatForceAsIfNecessary: true);
      }

      return new(true);
    }
    catch (CommandTimedOutException ex)
    {
      if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ex.Message);
      return new(false, reason: ex.Message);
    }
  }

  public async Task<ImmyOperationResult> RunRepairScript(
    IActionRunContext context,
    Software software,
    ILogPhaseHandle? progressPhase = null)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    // need to get custom script if it is not present
    var repairScript = software.RepairScript;
    if (repairScript == null && software.RepairScriptId.HasValue && software.RepairScriptType.HasValue)
      repairScript = await context.GetScriptById(software.RepairScriptId.Value, software.RepairScriptType.Value);

    if (repairScript == null)
    {
      return new(false, reason: "Could not find repair script");
    }

    context.AddLog(ActionProgressStatuses.RunningRepairScript);
    if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 1, progressStatus: ActionProgressStatuses.RunningRepairScript);
    var res = await context.RunScript(
      repairScript,
      scriptTimeouts.Install,
      context.Args.StopProcessing,
      isPrimary: true);

    if (res.HadTerminatingException)
    {
      return new(false, res.GetErrorString());
    }

    return new(true);
  }

  private async Task<ImmyOperationResult> RunUninstall(
    IActionRunContext context,
    Software software,
    SoftwareVersion? version = null,
    bool useDefaultSoftwareUninstallScript = false)
  {
    Script? uninstallScript;
    if (useDefaultSoftwareUninstallScript)
    {
      uninstallScript = software.UninstallScript;
    }
    else
    {
      uninstallScript = version?.UninstallScript;

      if (uninstallScript?.Action.ToLower().Contains("$installerfile") == true)
      {
        context.AddLog("This software version contains a reference to $installerfile in its script, so the installer will be downloaded (if it's not already).");
        await DownloadInstaller(context, version, throwIfFails: true);
      }
    }

    if (uninstallScript == null) return new ImmyOperationResult(false, "Could not find uninstall script");

    return await RunUninstallScript(context, uninstallScript, software, version: version);
  }
  private async Task<ImmyOperationResult> RunUninstallScript(
    IActionRunContext context,
    Script uninstallScript,
    Software software,
    SoftwareVersion? version = null)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    try
    {
      var uninstall = await RunScript(context, uninstallScript, scriptTimeouts.Uninstall,
        software: software,
        softwareVersion: version,
        isPrimary: true);
      if (!uninstall.Success)
        return new ImmyOperationResult(false, uninstall.Reason);
      await RebootComputer(context, software.RebootNeeded, treatForceAsIfNecessary: true);
      return new ImmyOperationResult(true);
    }
    catch (CommandTimedOutException ex)
    {
      return new ImmyOperationResult(false, ex.Message);
    }
  }

  public async Task<ImmyOperationResult> UninstallSoftware(
    IActionRunContext context,
    Software software,
    SoftwareVersion? detectedSoftwareVersion = null,
    ILogPhaseHandle? progressPhase = null)
  {
    context.Args.StopProcessing.ThrowIfCancellationRequested();
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    try
    {
      // if chocolatey or ninite, ensure that they are present on the machine.
      if (software.SoftwareType == SoftwareType.Chocolatey || detectedSoftwareVersion?.SoftwareType == SoftwareType.Chocolatey)
      {
        var installChocoResult = await InstallChocolateyIfNotPresent(context);
        if (!installChocoResult.Success) return installChocoResult;
      }

      if (software.SoftwareType == SoftwareType.Ninite || detectedSoftwareVersion?.SoftwareType == SoftwareType.Ninite)
      {
        var installNiniteResult = await InstallNiniteIfNotPresent(context);
        if (!installNiniteResult.Success) return installNiniteResult;
      }

      ScriptContextParameters populateSource = new ScriptContextParameters()
      {
        Software = software,
        SoftwareVersion = detectedSoftwareVersion,
        MaintenanceTask = await context.GetMaintenanceTask(),
        TargetAssignment = await context.GetTargetAssignment()
      };

      if (software.SoftwareType == SoftwareType.Chocolatey)
      {
        var version = context.DetectedSoftwareVersion ?? software.GetSoftwareVersions().FirstOrDefault();
        context.AddLog($"Attempting to uninstall {software.Name} {version?.SemanticVersionString} using the Chocolatey uninstall script.");
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 3, progressStatus: ActionProgressStatuses.RunningUninstallScript);
        var res = await context.RunScript(
          version?.UninstallScript!,
          scriptTimeouts.Uninstall,
          context.Args.StopProcessing,
          contextParameters: populateSource,
          isPrimary: true);

        if (res.HadTerminatingException)
        {
          var msg = $"Failed to uninstall due to exception: {res.GetErrorString()}";
          context.AddLog(msg);
          return new(false, reason: msg);
        }

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 3, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
        await RebootComputer(context, treatForceAsIfNecessary: true);
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 3, progressStatus: ActionProgressStatuses.Verifying);
        var result = await context.RunScript(
          software.DetectionScript!,
          scriptTimeouts.Detection,
          context.Args.StopProcessing,
          contextParameters: populateSource);

        if (result.HadTerminatingException)
        {
          var msg = $"Failed to uninstall due to exception: {result.GetErrorString()}";
          context.AddLog(msg);
          return new(false, reason: msg);
        }

        if (string.IsNullOrEmpty(result.OutputAsObject?.ToString()))
        {
          context.AddLog($"{software.Name} uninstalled successfully.");
          return new(true);
        }
        else
        {
          var msg = $"Failed to uninstall {software.Name} using the Chocolatey uninstall script.";
          context.AddLog(msg);
          return new(false, reason: msg);
        }
      }
      else if (software.SoftwareType == SoftwareType.Ninite)
      {
        var version = context.DetectedSoftwareVersion ?? software.GetSoftwareVersions().FirstOrDefault();
        context.AddLog($"Attempting to uninstall software {software.Name} {version?.SemanticVersionString} using the Ninite uninstall script..");
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 3, progressStatus: ActionProgressStatuses.RunningUninstallScript);
        var res = await context.RunScript(
          version?.UninstallScript!,
          scriptTimeouts.Uninstall,
          context.Args.StopProcessing,
          isPrimary: true);

        if (res.HadTerminatingException)
        {
          var msg = $"Failed to uninstall: {res.GetErrorString()}";
          context.AddLog(msg);
          return new(false, reason: msg);
        }

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 3, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
        await RebootComputer(context, treatForceAsIfNecessary: true);
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 3, progressStatus: ActionProgressStatuses.Verifying);
        var detectionResult = await DetectSoftwareVersionStringForNinite(context, software, ignoreCache: true);
        if (!detectionResult.Success)
        {
          var msg = $"Failed to uninstall: {detectionResult.Reason}";
          context.AddLog(msg);
          return new(false, reason: msg);
        }
        if (string.IsNullOrEmpty(detectionResult.Result))
        {
          context.AddLog($"{software.Name} uninstalled successfully.");
          return new(true);
        }
        else
        {
          var msg = $"Failed to uninstall {software.Name} using the Ninite uninstall script.";
          context.AddLog(msg);
          return new(false, reason: msg);
        }
      }
      else
      {
        var uninstallErrors = new HashSet<string>();

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 3, progressStatus: ActionProgressStatuses.TryingDetectedVersionUninstallScript);
        // go through the different uninstall procedures until one succeeds
        var res = await UninstallByDetectedVersionUninstallScript(context, software, detectedSoftwareVersion);
        if (res.Success) return res;
        uninstallErrors.Add(res.Reason ?? "");

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 3, progressStatus: ActionProgressStatuses.TryingDefaultUninstallScript);
        res = await UninstallByDefaultUninstallScript(context, software);
        if (res.Success) return res;
        uninstallErrors.Add(res.Reason ?? "");

        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 3, progressStatus: ActionProgressStatuses.TryingLatestVersionsUninstallScript);
        res = await UninstallByLatestVersionUninstallScript(context, software);
        if (res.Success) return res;
        uninstallErrors.Add(res.Reason ?? "");

        var filteredErrors = uninstallErrors.Where(e => !string.IsNullOrEmpty(e) && !e.Contains("Cannot attempt to uninstall")).ToList();
        var msg = "None of the software removal processes were successful.";
        if (uninstallErrors.TrueForAll(e => e.Contains("uninstall script because it is not present.")))
          msg = "Uninstall has failed as no uninstall scripts were found.";

        if (filteredErrors.Count > 0)
          msg += $"\n{string.Join("\n", filteredErrors)}";

        context.AddLog(msg);
        return new(false, reason: msg);
      }
    }
    catch (CommandTimedOutException ex)
    {
      return new(false, reason: ex.Message);
    }
  }

  public async Task<ImmyOperationResult> RunUpgradeScript(
    IActionRunContext context,
    Software software,
    SoftwareVersion? currentVersion,
    SoftwareVersion? desiredVersion,
    ILogPhaseHandle? progressPhase = null)
  {
    context.Args.StopProcessing.ThrowIfCancellationRequested();
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    try
    {
      // if chocolatey or ninite, ensure that they are present on the machine.
      bool isNinite = software.SoftwareType == SoftwareType.Ninite || desiredVersion?.SoftwareType == SoftwareType.Ninite;
      bool isChocolatey = software.SoftwareType == SoftwareType.Chocolatey || desiredVersion?.SoftwareType == SoftwareType.Chocolatey;

      if (isChocolatey)
      {
        var installChocoResult = await InstallChocolateyIfNotPresent(context);
        if (!installChocoResult.Success) return installChocoResult;
      }

      if (isNinite)
      {
        var installNiniteResult = await InstallNiniteIfNotPresent(context);
        if (!installNiniteResult.Success) return installNiniteResult;
      }

      Script upgradeScript;
      if (desiredVersion?.UpgradeScript is Script versionUpgradeScript)
      {
        // use the version install script if present
        context.AddLog("Using the upgrade script found on the version.");
        upgradeScript = versionUpgradeScript;
      }
      else if (software.UpgradeScript is Script softwareUpgradeScript)
      {
        // or use the default software install script
        context.AddLog("Using the default upgrade script found on the software.");
        upgradeScript = softwareUpgradeScript;
      }
      else
      {
        // or fail because we don't have an install script
        context.AddLog(UpgradeScriptIsMissingMessage);
        return new(false, reason: UpgradeScriptIsMissingMessage);
      }

      ScriptContextParameters scriptContextParameters = new ScriptContextParameters()
      {
        Software = software,
        MaintenanceTask = await context.GetMaintenanceTask(),
        SoftwareVersion = desiredVersion,
        TargetAssignment = await context.GetTargetAssignment(),
      };

      if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.RunningUpgradeScript);
      try
      {
        var result = ImmyOperationResult<string?>.FromMetaScriptConsoleTextResult(
          await context.RunScript(
            upgradeScript,
            scriptTimeouts.Upgrade,
            context.Args.StopProcessing,
            contextParameters: scriptContextParameters,
            isPrimary: true));
        if (!result.Success)
        {
          return new(false, reason: result.Reason);
        }
      }
      catch (Exception ex) when (!ex.IsCancellationException())
      {
        // coerce exceptions into operation failures
        return new(false, reason: ex.Message);
      }
      if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
      await RebootComputer(context, software.RebootNeeded, treatForceAsIfNecessary: true);
      return new(true);
    }
    catch (CommandTimedOutException ex)
    {
      return new(false, reason: ex.Message);
    }
  }

  public async Task<ImmyOperationResult<SemanticVersion?>> DetectSoftwareVersion(
    IStageRunContext context,
    Software software,
    bool ignoreCache,
    TargetAssignment? assignment = null)
  {
    try
    {
      context.Args.StopProcessing.ThrowIfCancellationRequested();

      var detectionResult = await DetectSoftwareVersionString(context, software, ignoreCache, assignment);
      if (!detectionResult.Success) return new ImmyOperationResult<SemanticVersion?>(false, detectionResult.Reason);
      if (string.IsNullOrEmpty(detectionResult.Result)) return new ImmyOperationResult<SemanticVersion?>(true);
      if (NuGetVersion.TryParse(detectionResult.Result, out var detectedVersion))
      {
        return new ImmyOperationResult<SemanticVersion?>(true, result: detectedVersion);
      }

      context.AddLog($"Unable to parse the result of the detection script into a semantic version. Detection script result:\n\n{detectionResult.Result}");
      return new ImmyOperationResult<SemanticVersion?>(false, "Unable to parse detected version");
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      return new ImmyOperationResult<SemanticVersion?>(false, ex.Message);
    }
  }

  public async Task<ImmyOperationResult<NiniteAuditResponse?>> GetNiniteAuditResponse(IRunContext context, bool ignoreCache = false)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    if (context.Args.Fields.NiniteAuditCache is null || ignoreCache)
    {
      try
      {
        var auditResponseStringResult = await context.RunScript(
          context.Args.SoftwareActions.NiniteDetectionAction,
          scriptTimeouts.Detection,
          context.Args.StopProcessing);
        if (auditResponseStringResult.HadTerminatingException) return new ImmyOperationResult<NiniteAuditResponse?>(success: false, reason: auditResponseStringResult.GetErrorString());

        var auditResponse = new NiniteAuditResponse(auditResponseStringResult.OutputAsCollection);
        if (auditResponse.FailureReason != null)
        {
          return new ImmyOperationResult<NiniteAuditResponse?>(
            success: false,
            reason: auditResponse.FailureReason,
            result: auditResponse);
        }
        context.Args.Fields.NiniteAuditCache = auditResponse;
      }
      catch (CommandTimedOutException ex)
      {
        return new ImmyOperationResult<NiniteAuditResponse?>(false, reason: ex.Message);
      }
    }

    return new ImmyOperationResult<NiniteAuditResponse?>(true, result: context.Args.Fields.NiniteAuditCache);
  }

  private async Task<ImmyOperationResult<string?>> DetectSoftwareVersionStringForNinite(
    IStageRunContext context,
    Software software,
    bool ignoreCache = false)
  {
    context.AddLog($"{software.Name}: Getting installed version from Ninite [{software.Identifier}]");

    var installNiniteResult = await InstallNiniteIfNotPresent(context);
    if (!installNiniteResult.Success) return new ImmyOperationResult<string?>(false, installNiniteResult.Reason);
    var auditResponse = await GetNiniteAuditResponse(context, ignoreCache: ignoreCache);
    if (!auditResponse.Success) return new ImmyOperationResult<string?>(false, auditResponse.Reason);
    var matchingSoftwareFromAudit = auditResponse.Result?.Items?.Find(a => a.SoftwareName == software.Identifier);
    return new ImmyOperationResult<string?>(true, result: matchingSoftwareFromAudit?.CurrentVersion?.ToString());
  }

  private async Task<ImmyOperationResult<string?>> DetectSoftwareVersionStringForGlobalAndLocal(
    IStageRunContext context,
    Software software,
    bool ignoreCache,
    TargetAssignment? assignment)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    var detectionAction = software.DetectionMethod switch
    {
      DetectionMethod.SoftwareTable => software.SoftwareTableName,
      DetectionMethod.CustomDetectionScript => software.DetectionScript?.Action,
      DetectionMethod.UpgradeCode => software.UpgradeCode,
      _ => throw new NotImplementedException(),
    };
    if (string.IsNullOrEmpty(detectionAction))
    {
      context.AddLog($"{software.Name} does not have a detection action.");
      return new ImmyOperationResult<string?>(true, result: null);
    }

    try
    {
      ScriptContextParameters scriptContextParameters = new()
      {
        Software = software,
        MaintenanceTask = context is IActionRunContext actionRunContext ? await actionRunContext.GetMaintenanceTask() : null,
        TargetAssignment = assignment,
      };

      switch (software.DetectionMethod)
      {
        case DetectionMethod.SoftwareTable:
        case DetectionMethod.UpgradeCode:
          var packageRes = await FindSoftwareInBulkDetection(context, detectionAction, software.DetectionMethod, ignoreCache, software.SoftwareTableNameSearchMode);
          if (!packageRes.Success)
          {
            return new ImmyOperationResult<string?>(success: false, reason: packageRes.Reason);
          }

          return new ImmyOperationResult<string?>(true, result: packageRes.Result?.DisplayVersion);
        case DetectionMethod.CustomDetectionScript:
          // Custom Detection Script
          var code = software.DetectionScript;
          var detectionResult = await context.RunScript(
            code!,
            scriptTimeouts.Detection,
            context.Args.StopProcessing,
            contextParameters: scriptContextParameters);
          if (detectionResult.HadTerminatingException)
            return new ImmyOperationResult<string?>(false, reason: detectionResult.GetErrorString());
          var version = TextHelpers.GetLastWordInString(detectionResult.OutputAsObject?.ToString());
          return new ImmyOperationResult<string?>(true, result: version);
        default:
          throw new NotSupportedException();
      }
    }
    catch (CommandTimedOutException ex)
    {
      return new ImmyOperationResult<string?>(false, ex.Message);
    }
  }

  private async Task<ImmyOperationResult<Package?>> FindSoftwareInBulkDetection(
    IRunContext context,
    string softwareFilter,
    DetectionMethod method,
    bool forceRefresh = false,
    SoftwareTableNameSearchMode? softwareTableNameSearchMode = null,
    bool parseVersion = true)
  {
    var bulkSoftware = await GetBulkSoftware(context, forceRefresh);
    if (!bulkSoftware.Success)
      return new ImmyOperationResult<Package?>(false, reason: bulkSoftware.Reason);

    IEnumerable<Package>? latestVersionQuery;
    var softwareFilterLower = softwareFilter.ToLower();
    switch (method)
    {
      case DetectionMethod.SoftwareTable:
        switch (softwareTableNameSearchMode)
        {
          case SoftwareTableNameSearchMode.Contains:
            context.AddLog(@$"Getting installed version from Windows Registry where display name contains ""{softwareFilter}"".");
            latestVersionQuery = bulkSoftware.Result?
              .Where(result => result.DisplayName?.ToLower().Contains(softwareFilter.ToLower()) == true);
            if (parseVersion)
            {
              latestVersionQuery = latestVersionQuery?.Where(result =>
                NuGetVersion.TryParse(result.DisplayVersion, out var _));
            }
            break;
          case SoftwareTableNameSearchMode.Regex:
            context.AddLog(@$"Getting installed version from Windows Registry where display name matches regex ""{softwareFilter}"".");
            var performRegexMatch = MakeRegexMatcher(softwareFilter);
            latestVersionQuery = bulkSoftware.Result?
              .Where(result =>
              {
                if (!performRegexMatch(result.DisplayName, out var displayVersion)) return false;
                if (!string.IsNullOrEmpty(displayVersion)) result.DisplayVersion = displayVersion;
                return true;
              }
            );
            if (parseVersion)
            {
              latestVersionQuery = latestVersionQuery?.Where(result =>
                NuGetVersion.TryParse(result.DisplayVersion, out var _));
            }
            break;
          case SoftwareTableNameSearchMode.Traditional:
            var regexMatch = TextHelpers.ConvertTraditionalTableNamePatternToRegex(softwareFilter);
            context.AddLog(@$"Getting installed version from Windows Registry where display name matches regex ""{regexMatch}"".");
            latestVersionQuery = bulkSoftware.Result?
              .Where(result =>
                Regex.IsMatch(result.DisplayName ?? "", regexMatch, RegexOptions.IgnoreCase));
            if (parseVersion)
            {
              latestVersionQuery = latestVersionQuery?.Where(result =>
                NuGetVersion.TryParse(result.DisplayVersion, out var _));
            }
            break;
          default:
            throw new NotSupportedException();
        }
        break;
      case DetectionMethod.UpgradeCode:
        context.AddLog($"Getting installed version from Registry using upgrade code \"{softwareFilter}\".");
        latestVersionQuery = bulkSoftware.Result?
          .Where(result => !string.IsNullOrEmpty(result.UpgradeCode) && result.UpgradeCode?.ToLower() == softwareFilterLower);
        break;
      case DetectionMethod.ProductCode:
        context.AddLog($"Getting installed version from Registry using product code \"{softwareFilter}\".");
        latestVersionQuery = bulkSoftware.Result?
          .Where(result => !string.IsNullOrEmpty(result.ProductCode) && result.ProductCode?.ToLower() == softwareFilterLower);
        break;
      default:
        throw new NotSupportedException();
    }

    var latestVersion = latestVersionQuery?
      .OrderBy(a => NuGetVersion.Parse(a.DisplayVersion ?? "0.0.0"))
      .LastOrDefault();

    if (latestVersion is null) context.AddLog("No version found");
    else context.AddLog($"Found \"{latestVersion.DisplayName}\" version \"{latestVersion.DisplayVersion}\".");

    return new ImmyOperationResult<Package?>(true, result: latestVersion);
  }

  public RegexMatchPerformer MakeRegexMatcher(string regexString)
  {
    var reg = new Regex(regexString, RegexOptions.IgnoreCase);
    bool PerformRegexMatch(string? displayName, out string? displayVersion)
    {
      displayVersion = null;
      // if this package doesn't match the search string regex, filter it out
      if (reg.Match(displayName ?? "") is not { Success: true } match)
        return false;

      if (match.Groups.TryGetValue("version", out var v))
        // if the search string has a capture group named "version", use that group to get the display version
        displayVersion = v.Value;
      else if (match.Groups.Values.Skip(1).FirstOrDefault(g => g.Success && NuGetVersion.TryParse(g.Value, out _)) is { } group)
        // search the capture groups for a valid semantic version and use that to get the display version
        displayVersion = group.Value;

      return true;
    }

    return PerformRegexMatch;
  }

  public async Task<ImmyOperationResult<List<Package>?>> GetBulkSoftware(
    IRunContext context,
    bool ignoreCache,
    bool strict = true)
  {
    try
    {
      if (!ignoreCache && context.Args.Fields.DidRetrieveBulkSoftwareList)
        return new ImmyOperationResult<List<Package>?>(true, result: context.Args.Fields.BulkSoftwareList);

      context.AddLog("Retrieving bulk software list.");
      InventoryDeviceCmdResponse? output;
      try
      {
        await context.EnsureComputerIsOnline();
        output = await _inventoryDeviceCmd.Run(
         new InventoryDeviceCmdPayload(
           context.Args.Computer!.DeviceId,
           null,
           new[] { InventoryKeys.WindowsSoftwareInventoryKey },
           false,
           strict: true),
         cancellationToken: context.Args.StopProcessing, runContext: context, onEphemeralAgentConnectionEvent: (eventText) =>
         {
           context.AddLog(eventText);
         });

        if (!output.Success)
        {
          return new ImmyOperationResult<List<Package>?>(false, output.FailureReason);
        }
      }
      catch (InventoryScriptErroredException ex)
      {
        if (strict) throw;
        return new ImmyOperationResult<List<Package>?>(false, ex.Message);
      }

      try
      {
        if (output.Output != null && output.Output.TryGetValue(InventoryKeys.WindowsSoftwareInventoryKey, out var softwareResponse) && softwareResponse.TryGetValue(InventoryKeys.OutputStreamKey, out var outputJson))
        {
          context.Args.Fields.BulkSoftwareList.Clear();
          var packages = JsonConvert.DeserializeObject<List<Package>>(outputJson);
          if (packages is not null)
          {
            context.Args.Fields.BulkSoftwareList.AddRange(packages);
            context.AddLog($"Retrieved {context.Args.Fields.BulkSoftwareList.Count} software.");
            context.Args.Fields.DidRetrieveBulkSoftwareList = true;
            return new ImmyOperationResult<List<Package>?>(true, result: context.Args.Fields.BulkSoftwareList);
          }
        }
        return new ImmyOperationResult<List<Package>?>(false, "Failed to retrieve software list from computer");
      }
      catch (Exception ex) when (!ex.IsCancellationException())
      {
        context.AddLog("Exception occurred while getting software list from computer");
        context.AddLog(ex.ToString());
        if (strict) throw;
        return new ImmyOperationResult<List<Package>?>(false, reason: ex.Message);
      }
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      if (strict) throw;
      return new ImmyOperationResult<List<Package>?>(false, reason: ex.Message);
    }
  }

  private async Task<ImmyOperationResult<string?>> DetectSoftwareVersionString(
    IStageRunContext context,
    Software software,
    bool ignoreCache,
    TargetAssignment? assignment)
  {
    context.Args.StopProcessing.ThrowIfCancellationRequested();
    return software.SoftwareType switch
    {
      SoftwareType.Chocolatey => await DetectSoftwareVersionStringForChocolatey(context, software, ignoreCache),
      SoftwareType.GlobalSoftware => await DetectSoftwareVersionStringForGlobalAndLocal(context, software, ignoreCache, assignment),
      SoftwareType.LocalSoftware => await DetectSoftwareVersionStringForGlobalAndLocal(context, software, ignoreCache, assignment),
      SoftwareType.Ninite => await DetectSoftwareVersionStringForNinite(context, software, ignoreCache),
      _ => new ImmyOperationResult<string?>(false, $"Detection of software type {software.SoftwareType} is not supported."),
    };
  }

  public async Task<ImmyOperationResult> InstallNiniteIfNotPresent(
    IStageRunContext context,
    bool ignoreCache = false)
  {
    if (context.Args.Fields.HasNiniteOneExe && !ignoreCache) return new ImmyOperationResult(true);
    if (context.Args.Fields.FailedToInstallNinite && !ignoreCache)
      return new ImmyOperationResult(false, "Failed to install Ninite");

    context.Args.StopProcessing.ThrowIfCancellationRequested();
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    ILogPhaseHandle? installNinitePhaseHandle = null;
    if (context is not IActionRunContext)
    {
      // only begin a new stage phase if we are not within an action
      installNinitePhaseHandle = await context.BeginNewStagePhase("Install Ninite");
    }

    try
    {
      var isInstalled = await context.RunScript<bool>(
        context.Args.SoftwareActions.NiniteTestExecutablePath,
        scriptTimeouts.Detection,
        context.Args.StopProcessing);
      if (isInstalled.HadTerminatingException)
      {
        if (installNinitePhaseHandle is not null)
          await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
        return new ImmyOperationResult(false, reason: isInstalled.GetErrorString());
      }

      if (isInstalled.OutputAsObject)
      {
        context.Args.Fields.HasNiniteOneExe = true;
        context.AddLog($"Found Ninite.");
      }
      else
      {
        try
        {
          await DownloadNiniteToComputer(context);
        }
        catch (DownloadFailedException)
        {
          context.Args.Fields.FailedToInstallNinite = true;
          if (installNinitePhaseHandle is not null)
            await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
          return new ImmyOperationResult(false, "Ninite failed to download.");
        }
        catch (NotSupportedException)
        {
          context.Args.Fields.FailedToInstallNinite = true;
          if (installNinitePhaseHandle is not null)
            await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
          return new ImmyOperationResult(false, "Ninite failed to install because Ninite is not present in the software table.");
        }
        isInstalled = await context.RunScript<bool>(
          context.Args.SoftwareActions.NiniteTestExecutablePath,
          scriptTimeouts.Detection,
          context.Args.StopProcessing);
        if (isInstalled.HadTerminatingException)
        {
          if (installNinitePhaseHandle is not null)
            await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
          return new ImmyOperationResult(false, reason: isInstalled.GetErrorString());
        }

        if (isInstalled.OutputAsObject)
        {
          context.AddLog($"Ninite installed.");
        }
        else
        {
          context.AddLog("Ninite failed to install.");
          if (installNinitePhaseHandle is not null)
            await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
          context.Args.Fields.FailedToInstallNinite = true;
          return new ImmyOperationResult(false, "Ninite failed to install");
        }
      }
      context.Args.Fields.HasNiniteOneExe = true;
      return new ImmyOperationResult(true);
    }
    catch (DownloadFailedException ex)
    {
      context.AddLog("Ninite failed to install due to the download failing.");
      if (installNinitePhaseHandle is not null)
        await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
      context.Args.Fields.FailedToInstallNinite = true;
      return new ImmyOperationResult(false, ex.Message);
    }
    catch (CommandTimedOutException ex)
    {
      context.AddLog("Ninite failed to install due to the command timing out.");
      if (installNinitePhaseHandle is not null)
        await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
      context.Args.Fields.FailedToInstallNinite = true;
      return new ImmyOperationResult(false, ex.Message);
    }
    catch (FormatException ex)
    {
      context.AddLog($"Ninite failed to install because the response could not be parsed.  Exception:\n\n{ex.Message}");
      if (installNinitePhaseHandle is not null)
        await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
      context.Args.Fields.FailedToInstallNinite = true;
      return new ImmyOperationResult(false, ex.ToString());
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      _logger.LogError(ex, "Failed to install ninite");
      context.AddLog($"Ninite failed to install.  Exception:\n\n{ex.Message}");
      if (installNinitePhaseHandle is not null)
        await installNinitePhaseHandle.SetResultState(SessionPhaseStatus.Failed);
      context.Args.Fields.FailedToInstallChocolatey = true;
      return new ImmyOperationResult(false, ex.ToString());
    }
    finally
    {
      if (installNinitePhaseHandle is not null) await installNinitePhaseHandle.DisposeAsync();
    }
  }

  private async Task DownloadNiniteToComputer(IRunContext context)
  {
    // fetch ninite software version
    var software = context.Args.SoftwareActions.GetNiniteSoftware();
    if (software == null || !software.SoftwareVersions.Any())
      throw new NotSupportedException("Ninite is not available in the software table.  Cannot download Ninite.");
    var version = software.SoftwareVersions.OrderByDescending(a => a.SemanticVersion).FirstOrDefault();
    if (version?.BlobName is null) throw new NotSupportedException("Ninite blob name was not specified.");
    var downloadUrl = new Uri(_azureBlobStorageSasService.GetLocalSoftwareDownloadUrl(version.BlobName));
    await DownloadFile(context, downloadUrl, context.Args.SoftwareActions.NiniteExecutablePath, throwIfFails: true, useBITS: true);
  }

  /// <summary>
  /// Returns true if everything is good.
  /// Otherwise returns false to indicate a reinstallation is required.
  /// For a test script to fail it must explicitly return false.
  /// </summary>
  /// <param name="context"></param>
  /// <param name="software"></param>
  /// <param name="softwareVersion"></param>
  /// <param name="targetAssignment"></param>
  /// <returns></returns>
  public async Task<ImmyOperationResult<bool>> TestSoftware(
    IActionRunContext context,
    Software software,
    SoftwareVersion? softwareVersion,
    TargetAssignment? targetAssignment = null,
    ILogPhaseHandle? progressPhase = null)
  {
    // short-circuit if a test script is not required
    if (!software.TestRequired && softwareVersion?.TestRequired == false)
    {
      context.AddLog("Software test is not required");
      return new(success: true, result: true);
    }

    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    Script? testScript;
    if (softwareVersion?.TestScript is Script versionTestScript)
    {
      testScript = versionTestScript;
    }
    else if (software.TestScript is Script softwareTestScript)
    {
      testScript = softwareTestScript;
    }
    else if (softwareVersion?.TestRequired == true)
    {
      return new(false, reason: VersionTestScriptMissingMessage, result: true);
    }
    else
    {
      return new(false, reason: SoftwareTestScriptMissingMessage, result: true);
    }

    context.Args.StopProcessing.ThrowIfCancellationRequested();

    if (string.IsNullOrEmpty(testScript.Action))
    {
      return new(success: false, reason: "Test script does not have any data");
    }

    if (testScript.Action.Contains("$LicenseFilePath"))
    {
      //download license
      context.AddLog("Test script mentions $LicenseFilePath. Downloading the license.");
      if (progressPhase is not null)
        await progressPhase.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.DownloadingLicense);
      await DetectAndDownloadLicense(context, softwareVersion, targetAssignment: targetAssignment);
    }
    context.AddLog($"Running test script for {software.Name} {softwareVersion?.SemanticVersionString}.");

    switch (testScript.ScriptLanguage)
    {
      case ScriptLanguage.CommandLine:
      case ScriptLanguage.PowerShell:
        try
        {
          ScriptContextParameters scriptContextParameters = new()
          {
            Software = software,
            MaintenanceTask = await context.GetMaintenanceTask(),
            SoftwareVersion = softwareVersion,
            TargetAssignment = targetAssignment,
          };
          if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RunningTestScript);
          var testResult = await context.RunScript<bool>(
            testScript,
            scriptTimeouts.Action,
            context.Args.StopProcessing,
            contextParameters: scriptContextParameters);

          if (testResult.HadTerminatingException)
          {
            var err = testResult.GetErrorString();
            if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 2, progressStatus: err);
            return new(true, reason: err, result: true);
          }

          return new(true, result: testResult.OutputAsObject);
        }
        catch (CommandTimedOutException ex)
        {
          if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 2, progressStatus: ex.Message);
          return new(false, ex.Message, result: true);
        }
      default:
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 2, progressStatus: ActionProgressStatuses.UnrecognizedScriptActionType);
        return new(true, ActionProgressStatuses.UnrecognizedScriptActionType, result: true);
    }
  }

  public async Task<ImmyOperationResult> DownloadInstaller(
    IActionRunContext context,
    SoftwareVersion? softwareVersion,
    bool includeLicense = false,
    bool throwIfFails = false,
    ILogPhaseHandle? progressPhase = null)
  {
    context.Args.StopProcessing.ThrowIfCancellationRequested();
    await context.UpdateAction(status: MaintenanceActionStatus.Downloading);

    var software = context.DesiredSoftware;
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;

    // download the license if we are including it
    if (includeLicense)
    {
      var license = context.SpecifiedLicense ?? (await context.GetTargetAssignment())?.License;
      if (license != null && software!.LicenseType == LicenseType.LicenseFile)
      {
        context.AddLog("License will be downloaded (if it isn't already).");
        if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(0, 4, "Downloading license");
        var licenseDownloadResult = await DetectAndDownloadLicense(context, softwareVersion, throwIfFails: throwIfFails);
        if (!licenseDownloadResult.Success)
        {
          return licenseDownloadResult;
        }
      }
    }

    context.AddLog("Installer will be downloaded (if it isn't already).");

    // populate the installer fields
    Uri? downloadUrl = null;
    string? installerFile = null;
    string? packageHash = null;
    PackageType packageType;
    string? relativeCacheSourcePath = null;

    // Checking if the software is using an integration to download the installer
    var softwareAgentTypeId = software?.AgentIntegrationTypeId;
    var assignment = await context.GetTargetAssignment();
    int? providerLinkId = assignment?.ProviderLinkIdForMaintenanceItem;

    // if the software is using dynamic versions, then run the dynamic version script to get the download url
    if (software?.UseDynamicVersions == true)
    {
      context.AddLog("Installer will be provided using dynamic versions.");
      if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 4, progressStatus: ActionProgressStatuses.GettingDynamicSoftwareVersion);
      var dynamicVersionRes = await GetDynamicSoftwareVersion(context, software, context.Action.DesiredVersion);
      if (dynamicVersionRes.Success && dynamicVersionRes.Result is DynamicSoftwareVersion dynamicVersion)
      {
        if (dynamicVersion.URL == null)
        {
          return new(false,
            reason: $"The url is missing from dynamic version {context.Action.DesiredVersion}");
        }

        if (string.IsNullOrEmpty(dynamicVersion.InstallerFile))
        {
          context.AddLog("The filename is missing from the version.  Will attempt to retrieve it by sending a HTTP HEAD request and pulling the filename from the Content-Disposition header.");
          if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(2, 4, progressStatus: ActionProgressStatuses.AttemptingToRetrieveMissingFilename);
          using var client = new HttpClient();
          var res = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, dynamicVersion.URL));
          if (res.IsSuccessStatusCode)
          {
            installerFile = res.Content.Headers.ContentDisposition?.FileName;
            if (string.IsNullOrEmpty(installerFile))
            {
              context.AddLog("The filename was not found in the Content-Disposition response header.");
            }
            else
            {
              context.AddLog($"The filename {installerFile} was found in the Content-Disposition response header.");
            }
          }
          else
          {
            context.AddLog($"The HTTP HEAD request was unsuccessful. {res.ReasonPhrase}");
          }

          // short-circuit if we could not determine a filename
          if (string.IsNullOrEmpty(installerFile))
          {
            return new(false,
              reason: $"The filename is missing from dynamic version {context.Action.DesiredVersion}, and we cannot continue.");
          }
        }
        else
        {
          installerFile = dynamicVersion.InstallerFile;
        }

        downloadUrl = new Uri(dynamicVersion.URL);
        packageHash = dynamicVersion.PackageHash;
        relativeCacheSourcePath = dynamicVersion.RelativeCacheSourcePath;
        packageType = dynamicVersion.PackageType;
      }
      else
      {
        var msg = $"An error occurred while fetching the download url for dynamic software {software!.Name}. {dynamicVersionRes.Reason}";
        context.AddLog(msg);
        return new(false, reason: msg);
      }
    }
    else
    {
      packageType = softwareVersion?.PackageType ?? PackageType.None;
      packageHash = softwareVersion?.PackageHash;
      relativeCacheSourcePath = softwareVersion?.RelativeCacheSourcePath;
      installerFile = string.IsNullOrWhiteSpace(softwareVersion?.InstallerFile) ? softwareVersion?.BlobName : softwareVersion?.InstallerFile;
      if (progressPhase is not null) await progressPhase.SetProgressPercentComplete(1, 4, progressStatus: ActionProgressStatuses.DeterminingSoftwareVersionInstallUrl);
      if (softwareVersion is { InstallerType: SoftwareVersionInstallerType.File, BlobName: not null })
      {
        context.AddLog(SessionLogMessages.FetchingVersionInstallerUrl);
        if (softwareVersion.SoftwareType == SoftwareType.GlobalSoftware)
          downloadUrl = new Uri(_azureBlobStorageSasService.GetGlobalSoftwareDownloadUrl(softwareVersion.BlobName));
        else
          downloadUrl = new Uri(_azureBlobStorageSasService.GetLocalSoftwareDownloadUrl(softwareVersion.BlobName));
      }
      else if (softwareVersion?.InstallerType == SoftwareVersionInstallerType.Url)
      {
        downloadUrl = new Uri(softwareVersion.URL!);
      }
    }

    var targetFolder = PathHelpers.Combine("$($env:SystemRoot)", BASE_IMMY_PATH);
    if (!string.IsNullOrEmpty(relativeCacheSourcePath))
      targetFolder = PathHelpers.Combine(targetFolder, relativeCacheSourcePath);
    var targetFile = PathHelpers.Combine(targetFolder, installerFile ?? "");

    if (softwareAgentTypeId is not null && context.ComputerId.HasValue && providerLinkId.HasValue)
    {
      // Fetch provider link
      var providerLink = await context.GetProviderLinkById(providerLinkId.Value);
      if (providerLink is null)
        return new ImmyOperationResult(false, reason: $"Provider link {providerLinkId} not found");

      // Fetch instance of provider
      var provider = await context.Args.ProviderActions.GetProvider(providerLink, context.Args.StopProcessing);
      if (provider is null)
        return new ImmyOperationResult(false, reason: $"Provider for link {providerLinkId} not found");

      // Verify provider implements ISupportsDownloadAgentInstaller
      if (provider is ISupportsDownloadAgentInstaller installerProvider)
      {
        // Verify downloadURL isn't null
        if (downloadUrl is null)
          return new ImmyOperationResult(false, reason: $"Unable to determine download url.");

        // Try to downlod the installer and return the result
        var downloadResult = await installerProvider.DownloadAgentInstaller(context, downloadUrl, targetFile, context.Args.StopProcessing);

        return new ImmyOperationResult(downloadResult.IsSuccess, reason: downloadResult.Reason);
      }
    }

    // if the software has a download installer script, then we use that instead.
    if (software?.DownloadInstallerScript != null)
    {
      context.AddLog("Using the software's download installer script");

      // add script parameters
      var script = Script.Copy(software.DownloadInstallerScript);
      script.Variables ??= [];
      if (downloadUrl is not null)
        script.Variables.TryAdd("Url", downloadUrl);
      if (packageHash is not null)
        script.Variables.TryAdd("PackageHash", packageHash);
      progressPhase?.SetProgressPercentComplete(3, 4, progressStatus: ActionProgressStatuses.Downloading);
      var res = await context.RunScript(
        script,
        scriptTimeouts.Action,
        context.Args.StopProcessing,
        contextParameters: new ScriptContextParameters
        {
          Software = software,
          MaintenanceTask = await context.GetMaintenanceTask(),
          SoftwareVersion = softwareVersion,
          TargetAssignment = await context.GetTargetAssignment()
        });

      if (res.HadTerminatingException)
      {
        return new ImmyOperationResult(false, "Script had a terminating error");
      }

      return new ImmyOperationResult(true, "Download Installer script finished");
    }
    else
    {
      switch (packageType)
      {
        case PackageType.EntireFolder:
          targetFile = targetFolder + ".zip";
          break;
        case PackageType.None:
          return new(true);
        default:
          break;
      }

      if (downloadUrl == null)
      {
        return new(false, reason: "Failed to retrieve the download url");
      }

      progressPhase?.SetProgressPercentComplete(3, 4, progressStatus: ActionProgressStatuses.Downloading);
      var downloadResult = await DownloadFile(
        context,
        downloadUrl,
        targetFile,
        packageHash,
        useBITS: true,
        throwIfFails: throwIfFails);

      if (!downloadResult.Success) return downloadResult;

      if (packageType == PackageType.EntireFolder)
      {
        context.AddLog(SessionLogMessages.ExtractingPackageFromFolder);
        try
        {
          var script = _powershellLoader.GetPowerShellScript("Expand-ZipFile", 3600, new()
          {
            { "path", targetFile },
            { "expandTo", PathHelpers.Combine("$($env:SystemRoot)", BASE_IMMY_PATH, relativeCacheSourcePath ?? "") },
          });
          progressPhase?.SetProgressPercentComplete(4, 5, progressStatus: ActionProgressStatuses.Unzipping);
          var result = await context.RunScript(
            script,
            scriptTimeouts.Action,
            context.Args.StopProcessing);
          if (result.HadTerminatingException)
          {
            context.AddLog("Extracting the package failed.");
            if (throwIfFails) throw new DownloadFailedException($"Unable to extract downloaded package; result: {result.ConsoleText}");
            return new(false,
              reason: $"Extracting the package failed; result: {result.ConsoleText}");
          }
        }
        catch (CommandTimedOutException ex)
        {
          if (throwIfFails) throw new DownloadFailedException($"Unable to extract downloaded package: {ex.Message}");
          return new(false, reason: ex.Message);
        }
      }
      return downloadResult;
    }
  }

  public async Task<ImmyOperationResult> DetectAndDownloadLicense(
    IActionRunContext context,
    SoftwareVersion? version,
    bool throwIfFails = false,
    TargetAssignment? targetAssignment = null)
  {
    var software = context.DesiredSoftware;
    if (software == null) return new ImmyOperationResult(false, reason: "The software object was missing when attempting to download the license.");

    if (software.LicenseRequirement == SoftwareLicenseRequirement.None
      || software.LicenseType == LicenseType.Key)
    {
      context.AddLog("Software does not require downloading a license.");
      return new ImmyOperationResult(true);
    }

    var license = context.SpecifiedLicense ?? (await context.GetTargetAssignment())?.License ?? targetAssignment?.License;
    if (license == null)
    {
      // no license on the context or target assignment
      context.AddLog("No applicable license could be found");
      if (software.LicenseRequirement == SoftwareLicenseRequirement.Optional)
      {
        // license is optional for this software anyway, so we're good
        context.AddLog("Licensing is optional for this software - continuing");
        return new ImmyOperationResult(true);
      }
      context.AddLog("Cannot continue because this software requires a license");
      var result = new ImmyOperationResult(false, "Unable to find license for software");
      if (throwIfFails) throw new DownloadFailedException("Unable to find license for software", result);
      return result;
    }

    try
    {
      var filename = license.GetFileName(software.LicenseType);
      if (filename is null) return new ImmyOperationResult(false, "File name for license could not be determined");

      var relativeCacheSourcePath = version?.RelativeCacheSourcePath ?? software.RelativeCacheSourcePath.ToString();
      var basePath = PathHelpers.Combine("$($env:SystemRoot)", BASE_IMMY_PATH);
      if (!string.IsNullOrEmpty(relativeCacheSourcePath))
      {
        basePath = PathHelpers.Combine(basePath, relativeCacheSourcePath);
      }
      string targetFile = PathHelpers.Combine(basePath, filename);
      var url = _azureBlobStorageSasService.GetLicenseDownloadUrl(license.LicenseValue);
      var downloadResult = await DownloadFile(context, new Uri(url), targetFile, throwIfFails: throwIfFails, overwriteExisting: true);
      if (!downloadResult.Success)
      {
        context.AddLog("Downloading license failed.");
        return new ImmyOperationResult(false, "Downloading license failed");
      }
    }
    catch (Exception ex) when (!(ex is DownloadFailedException && throwIfFails) && !ex.IsCancellationException())
    {
      context.AddLog($"Downloading license failed with exception:\n\n{ex.Message}");
      return new ImmyOperationResult(false, "Downloading license failed");
    }
    return new ImmyOperationResult(true);
  }

  public async Task<ImmyOperationResult> DownloadFile(
    IRunContext context,
    Uri uri,
    string localPath,
    string? expectedMd5 = null,
    bool useBITS = false,
    bool throwIfFails = false,
    bool overwriteExisting = false)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    try
    {
      context.AddLog($"Checking if {localPath} is present.");
      var script = _powershellLoader.GetPowerShellScript("Test-Path", null, new()
      {
        { "path", PathHelpers.UseWindowsSeparators(localPath) },
      });
      var testPathResult = await context.RunScript<bool>(
        script,
        scriptTimeouts.Detection,
        context.Args.StopProcessing);
      if (testPathResult.HadTerminatingException)
      {
        return new ImmyOperationResult(false, reason: testPathResult.GetErrorString());
      }

      if (testPathResult.OutputAsObject)
      {
        context.AddLog("File exists.");
        if (!overwriteExisting)
        {
          var verifyResult = await VerifyRemoteHash(context, localPath, expectedMd5);
          if (!verifyResult.Success) return new ImmyOperationResult(false, verifyResult.Reason);
          if (verifyResult.Result)
          {
            context.AddLog("Skipping download.");
            return new ImmyOperationResult(true, "File already exists");
          }
        }
        else
        {
          context.AddLog("Overwriting");
        }
      }
      else
      {
        context.AddLog("Download Required.");
      }
      bool bitsTransferFailed = false;
      var fileName = Path.GetFileName(PathHelpers.UseLinuxSeparators(localPath));
      if (useBITS)
      {
        var downloadBitsScript = _powershellLoader.GetPowerShellScript("Download-WithBitsTransfer", 300, new()
        {
          { "src", uri.ToString() },
          { "dest", PathHelpers.UseWindowsSeparators(localPath) },
        });

        var bitsJobId = string.Empty;
        try
        {
          context.AddLog($"Starting BITS download of file {uri}.");
          var bitsJobIdResult = await context.RunScript(
            downloadBitsScript,
            scriptTimeouts.Action,
            context.Args.StopProcessing,
            logCommand: false);

          if (bitsJobIdResult.HadTerminatingException || bitsJobIdResult.ErrorCollection.Any())
          {
            context.AddLog(bitsJobIdResult.GetErrorString());
            bitsTransferFailed = true;
          }
          else
          {
            bitsJobId = bitsJobIdResult.OutputAsObject?.ToString();
          }
        }
        catch (CommandTimedOutException)
        {
          bitsTransferFailed = true;
          // bits download timed out on creation fall back
          context.AddLog("BITS job creation script timed out - falling back to basic download");
        }
        context.AddLog("BITS job created successfully");
        if (Guid.TryParse(bitsJobId, out Guid bitsJobGuid) && !bitsTransferFailed)
        {
          var bitsProgressActivity = $"BITS Transfer - {fileName}";
          var bitsPercentScript = _powershellLoader.GetPowerShellScript("Get-BitsTransferAsJson", 120, new()
          {
            { "guid", bitsJobGuid },
          });

          var progressId = Guid.NewGuid();
          ulong lastBytesTransferred = 0;
          DateTime lastModified = DateTime.UtcNow;
          BitsJobProgress? job = null;
          do
          {
            try
            {
              context.Args.StopProcessing.ThrowIfCancellationRequested();
              string? bitsTransferString = null;
              try
              {
                var bitsTransferStringResult = await context.RunScript(
                  bitsPercentScript,
                  scriptTimeouts.Action,
                  context.Args.StopProcessing,
                  logCommand: false);

                if (bitsTransferStringResult.HadTerminatingException || bitsTransferStringResult.ErrorCollection.Any())
                {
                  context.AddLog(bitsTransferStringResult.GetErrorString());

                  // if we have a terminating exception
                  if (bitsTransferStringResult.TerminatingException != null)
                  {
                    break;
                  }
                }
                else
                {
                  bitsTransferString = bitsTransferStringResult.OutputAsObject?.ToString();
                }
              }
              catch (Exception ex) when (!ex.IsCancellationException())
              {
                // try one more time
                _logger.LogError(ex, "Erorr occurred while getting BITs progress");
                context.AddLog($"Error occurred while getting bits progress. Attempting again. {ex.Message}");
                var bitsTransferStringResult = await context.RunScript(
                  bitsPercentScript,
                  scriptTimeouts.Action,
                  context.Args.StopProcessing,
                  logCommand: false);
                if (bitsTransferStringResult.HadTerminatingException)
                {
                  context.AddLog(bitsTransferStringResult.GetErrorString());
                }
                else
                {
                  bitsTransferString = bitsTransferStringResult.OutputAsObject?.ToString();
                }
              }
              if (string.IsNullOrEmpty(bitsTransferString)) continue;
              job = JsonConvert.DeserializeObject<BitsJobProgress>(bitsTransferString);
              if (job is null)
              {
                context.AddLog($"Failed to deserialize bits job progress: {bitsTransferString}");
                continue;
              }

              if (job.JobState == BitsJobState.Error) break;

              var speed = job.GetSpeedSinceLastBytesTransferred(lastBytesTransferred, lastModified, inKBps: true);
              var secondsRemaining = job.GetSecondsRemaining(lastBytesTransferred, lastModified);
              context.AddLog(bitsProgressActivity,
                sessionLogType: SessionLogType.Progress,
                progressCorrelationId: progressId,
                progressActivity: $"{speed} KB/sec",
                progressPercentComplete: job.PercentComplete,
                progressStatus: job.JobState.ToString(),
                progressSecondsRemaining: Math.Round(secondsRemaining));

              lastBytesTransferred = job.BytesTransferredUInt64;
              lastModified = job.ModificationTime;
            }
            finally
            {
              await Task.Delay(5000);
            }
          } while (job?.IsRunning == true);
          if (job?.JobState == BitsJobState.Transferred)
          {
            context.AddLog(bitsProgressActivity,
              progressCorrelationId: progressId,
              sessionLogType: SessionLogType.Progress,
              progressStatus: job.JobState.ToString(),
              progressPercentComplete: 100,
              progressCompleted: true);

            var completeBitsScript = _powershellLoader.GetPowerShellScript("Complete-BitsTransfer", 300, new()
            {
              { "guid", bitsJobGuid },
            });
            await context.RunScript(
              completeBitsScript,
              scriptTimeouts.Action,
              context.Args.StopProcessing,
              logCommand: false);

            // only verify remote hash if we have an expected md5
            if (!string.IsNullOrEmpty(expectedMd5))
            {
              var verifyResult = await VerifyRemoteHash(context, localPath, expectedMd5, throwIfFails: throwIfFails);
              if (!verifyResult.Success) return new ImmyOperationResult(false, verifyResult.Reason);
              if (!verifyResult.Result)
              {
                context.AddLog($"Error verifying BITS transfer (jobId='{bitsJobId}').  Falling back to basic download.");
              }
            }

            return new ImmyOperationResult(true, "Download succeeded");
          }
          else
          {
            var statusSb = new StringBuilder(job?.JobState.ToString() ?? string.Empty);
            if (!string.IsNullOrEmpty((job?.ErrorDescription)))
            {
              statusSb.Append($" - {job.ErrorDescription}");
            }

            if (!string.IsNullOrEmpty(job?.ErrorContextDescription))
            {
              statusSb.Append($" - {job.ErrorContextDescription}");
            }
            context.AddLog("",
              progressCorrelationId: progressId,
              sessionLogType: SessionLogType.Progress,
              progressActivity: "BITS Transfer",
              progressStatus: statusSb.ToString(),
              progressCompleted: true);
            bitsTransferFailed = true;

            var removeBitsScript = _powershellLoader.GetPowerShellScript("Remove-BitsTransfer", 300, new()
            {
              { "guid", bitsJobGuid },
            });
            try
            {
              await context.RunScript(
                removeBitsScript,
                scriptTimeouts.Action,
                context.Args.StopProcessing);
            }
            catch (Exception ex) when (!(ex is CommandTimedOutException) && !ex.IsCancellationException())
            {
              _logger.LogError(ex, "Error occurred while trying to remove failed BITS transfer");
              context.AddLog("Error occurred while trying to remove the failed BITS transfer job.  Falling back to basic download.");
            }
          }
        }
        else
        {
          context.AddLog($"Error starting BITS transfer. Falling back to basic download.");
          bitsTransferFailed = true;
        }
      }
      context.AddLog($"Beginning basic download of file {uri}.");
      var targetFolder = Path.GetDirectoryName(PathHelpers.UseLinuxSeparators(localPath));
      if (targetFolder == null) return new ImmyOperationResult(false, $"Could not retrieve the folder path from '{localPath}'");
      var downloadFileToFolderScript = _powershellLoader.GetPowerShellScript("Download-FileToFolder", null, new()
      {
        { "targetFolder", PathHelpers.UseWindowsSeparators(targetFolder) },
        { "uri", uri.ToString() },
        { "localPath", PathHelpers.UseWindowsSeparators(localPath) },
      });
      var res = await context.RunScript(
        downloadFileToFolderScript,
        scriptTimeouts.Action,
        context.Args.StopProcessing);

      if (res.HadTerminatingException || res.ErrorCollection.Any())
      {
        return new(false, reason: res.GetErrorString());
      }

      var successResult = new ImmyOperationResult(true, bitsTransferFailed ? "BITS download failed, but basic download succeeded," : "Basic download succeeded.");
      if (!string.IsNullOrEmpty(expectedMd5))
      {
        var vResult = await VerifyRemoteHash(context, localPath, expectedMd5, throwIfFails: throwIfFails);
        if (!vResult.Success) return new ImmyOperationResult(false, vResult.Reason);
        if (vResult.Result)
        {
          return successResult;
        }
        var result = new ImmyOperationResult(false,
          bitsTransferFailed ? "BITS download failed, and basic download failed." : "Basic download failed.");

        if (throwIfFails)
          throw new DownloadFailedException("Download failed", result);

        return result;
      }

      return successResult;
    }
    catch (CommandTimedOutException ex)
    {
      if (throwIfFails) throw new DownloadFailedException($"Download failed: {ex.Message}");
      return new ImmyOperationResult(false, ex.Message);
    }
  }

  public async Task<ImmyOperationResult> VerifyExpectedDesiredState(
    IActionRunContext context,
    ILogPhaseHandle? progressPhase = null)
  {
    context.AddLog(SessionLogMessages.VerifyingDesiredState);
    progressPhase?.SetProgressPercentComplete(0, 1, progressStatus: ActionProgressStatuses.DetectingInstalledVersion);
    var detectionResult = await DetectSoftwareVersion(context, context.DesiredSoftware!, true,
      assignment: await context.GetTargetAssignment());
    if (!detectionResult.Success)
    {
      context.AddLog($"Detection failed{(detectionResult.Reason is { } reason ? $": {reason}" : "")}");
      progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: ActionProgressStatuses.DetectionFailed);
      return new(false, reason: detectionResult.Reason);
    }

    var actual = detectionResult.Result;

    // if the desired state is present, then verify the expected semantic version
    // otherwise if the desired state is not present, verify no version is found

    ImmyOperationResult ret;
    if (context.Action.DesiredSoftwareState == DesiredSoftwareState.NotPresent)
    {
      if (actual is null)
      {
        var msg = $"No version of {context.DesiredSoftware!.Name} was found.";
        context.AddLog(msg);
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        ret = new(true);
      }
      else
      {
        var msg = $"{context.DesiredSoftware!.Name} {actual} was still detected after uninstallaton.";
        context.AddLog(msg);
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        ret = new(false, reason: msg);
      }
    }
    else
    {
      var expected = context.DesiredSoftwareVersion?.SemanticVersion;
      if (actual == null)
      {
        var msg = $"No version of {context.DesiredSoftware?.Name} was found.";
        context.AddLog(msg);
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        ret = new(false, reason: msg);
      }
      else if (actual == expected)
      {
        var msg = $"Desired version {actual.ToNormalizedString()} found.";
        context.AddLog(msg);
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        ret = new(true);
      }
      else if (expected is not null && actual >= expected)
      {
        var msg = $"Newer version {actual.ToNormalizedString()} found.";
        context.AddLog(msg);
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        ret = new(true);
      }
      else
      {
        var msg = $"Desired version NOT found. Found {actual.ToNormalizedString()}";
        progressPhase?.SetProgressPercentComplete(1, 1, progressStatus: msg);
        context.AddLog(msg);
        ret = new(false, reason: msg);
      }
    }

    return ret;
  }

  public async Task<ImmyOperationResult> RebootComputer(
    IRunContext context,
    bool forceOnNormal = false,
    bool treatForceAsIfNecessary = false)
  {
    context.AddLog("Checking if computer needs to be rebooted.");
    context.Args.StopProcessing.ThrowIfCancellationRequested();

    var rebootPreference = context.Args.RebootPreference;
    var promptTimeoutAction = context.Args.PromptTimeoutAction;
    var promptTimeout = context.Args.PromptTimeout;
    var hasPromptedPendingReboot = context.Args.HasPromptedPendingReboot;
    var userCancelledReboot = context.Args.UserCancelledReboot;
    var autoConsentToReboots = context.Args.AutoConsentToReboots;
    var shouldSuppressDuringBusinessHours = context.Args.SuppressRebootsDuringBusinessHours;

    if (rebootPreference == RebootPreference.Suppress)
    {
      context.AddLog("Reboot preference is 'Suppress' reboots.");
      return new ImmyOperationResult(true, _suppressRebootMessage);
    }

    var shouldSuppressRebootDueToPreference =
        rebootPreference == RebootPreference.Prompt &&
        promptTimeoutAction != PromptTimeoutAction.Reboot;

    // Only check for business hours suppression if reboots aren't already suppressed due to action preference
    if (shouldSuppressDuringBusinessHours && !shouldSuppressRebootDueToPreference)
    {
      var withinBusinessHoursResult = await context.CheckIfDuringBusinessHours();

      // return if the check was unsuccessful or if we are within business hours
      if (!withinBusinessHoursResult.Success)
      {
        context.AddLog("Failed to determine if we are within business hours. Not allowing reboots.");
        return new ImmyOperationResult(true);
      }

      // within business hours
      if (withinBusinessHoursResult.Result)
      {
        return new ImmyOperationResult(true, reason: _withinBusinessHoursMessage);
      }
    }

    try
    {
      var pendingRebootResult = await HandlePendingRebootStatus(context);
      if (!pendingRebootResult.Success) return pendingRebootResult;
      var pendingRebootNotFound = pendingRebootResult.Reason == _pendingRebootNotFoundMessage;

      switch (rebootPreference)
      {
        case RebootPreference.Force when !treatForceAsIfNecessary:
          context.AddLog("Reboot preference is 'Force' reboot.");
          // computer should be forced to reboot
          await context.RebootComputer(force: true);
          return new ImmyOperationResult(true, _forceRebootMessage);

        case RebootPreference.Normal when forceOnNormal:
          context.AddLog("Reboot preference is 'If Necessary', but a reboot is required by the software. Forcing reboot.");
          // computer should be forced to reboot
          await context.RebootComputer(force: true);
          return new ImmyOperationResult(true, _forceRebootDueToSoftwareMessage);

        case RebootPreference.Prompt:
          if (pendingRebootNotFound) return new ImmyOperationResult(true, _pendingRebootNotFoundMessage);

          if (autoConsentToReboots && hasPromptedPendingReboot)
          {
            if (userCancelledReboot || promptTimeoutAction == PromptTimeoutAction.Suppress)
              return new ImmyOperationResult(true, _rebootCancelledOrSuppressedMessage);

            context.AddLog("Auto-Consent enabled. Rebooting automatically without additional prompts.");
            await context.RebootComputer(force: true);
            return new ImmyOperationResult(true, _autoConsentedRebootMessage);
          }

          if (!autoConsentToReboots && hasPromptedPendingReboot)
          {
            if (userCancelledReboot) return new ImmyOperationResult(true, _rebootCancelledMessage);

            context.AddLog("Prompting user again for additional reboot.");
            var promptResult = await context.RebootComputer(promptTimeout);
            CheckUserCancelledReboot(promptResult, context);
            return new ImmyOperationResult(true, _promptedRebootMessage);
          }

          // Call RebootComputer() for each option to prompt the user for action.
          // This is intentional to ensure the user sees the prompt, whether they confirm, cancel, or the prompt times out.
          // The response to the prompt is captured inside the "Restart-ComputerAndWait" function.
          switch (promptTimeoutAction)
          {
            case PromptTimeoutAction.Reboot:
              context.AddLog("Reboot preference is prompt user with 'Reboot' action selected.");
              var rebootResult = await context.RebootComputer(promptTimeout);
              CheckUserCancelledReboot(rebootResult, context);
              context.Args.HasPromptedPendingReboot = true;
              return new ImmyOperationResult(true, _rebootActionMessage);

            case PromptTimeoutAction.Suppress:
              context.AddLog("Reboot preference is prompt user with 'Suppress' action selected.");
              var suppressResult = await context.RebootComputer(promptTimeout);
              CheckUserCancelledReboot(suppressResult, context);
              context.Args.HasPromptedPendingReboot = true;
              return new ImmyOperationResult(true, _rebootSuppressMessage);

            case PromptTimeoutAction.FailSession:
              context.AddLog("Reboot preference is prompt user with 'Fail Session' action selected.");
              var failSessionResult = await context.RebootComputer(promptTimeout);
              CheckUserCancelledReboot(failSessionResult, context);
              context.Args.HasPromptedPendingReboot = true;
              return new ImmyOperationResult(false, _rebootFailedSessionMessage);

            case PromptTimeoutAction.FailAction:
              context.AddLog("Reboot preference is prompt user with 'Fail Action' action selected.");
              var failActionResult = await context.RebootComputer(promptTimeout);
              CheckUserCancelledReboot(failActionResult, context);
              context.Args.HasPromptedPendingReboot = true;
              return new ImmyOperationResult(false, _rebootFailedActionMessage);

            default:
              context.AddLog("Reboot preference is prompt user with an unknown option. No action taken.");
              return new ImmyOperationResult(true, _unknownPromptActionMessage);
          }
        default:
          return pendingRebootResult;
      }
    }
    catch (CommandTimedOutException ex)
    {
      context.AddLog("Rebooting the computer resulted in a timeout.");
      return new ImmyOperationResult(false, ex.Message);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      context.AddLog($"Rebooting the computer resulted in an exception. {ex.Message}");
      _logger.LogError(ex,
        "An exception occurred while attempting to reboot device with id={ComputerId}",
          context.ComputerId);
      return new ImmyOperationResult(false, "An exception occurred while attempting to reboot the device");
    }
  }

  /// <summary>
  /// Updates the reboot status based on the output of a script result and the current run context.
  /// </summary>
  /// <param name="result">The result of the script execution containing output data.</param>
  /// <param name="context">The run context that provides access to arguments and other settings.</param>
  /// <remarks>
  /// This method checks if the reboot was canceled by the user based on the output data provided
  /// in the <paramref name="result"/>. If the output contains a hashtable with a "Cancelled" key
  /// and the associated value is a boolean indicating cancellation, the method sets the
  /// <c>UserCancelledReboot</c> property of <paramref name="context"/> to true.
  /// </remarks>
  private static void CheckUserCancelledReboot(MetaScriptResult<object> result, IRunContext context)
  {
    bool userCancelledReboot = result?.OutputAsCollection?
        .OfType<Hashtable>()
        .Any(hashtable => hashtable.ContainsKey("Cancelled") && hashtable["Cancelled"] is bool cancelled && cancelled) ?? false;

    context.Args.UserCancelledReboot = userCancelledReboot;
  }

  private async Task<ImmyOperationResult> HandlePendingRebootStatus(IRunContext context)
  {
    var rebootPendingResult = await CheckIfRebootPending(context);
    if (!rebootPendingResult.Success)
    {
      context.AddLog($"Failed to determine if there is a pending reboot. {rebootPendingResult.Reason}");
      return new ImmyOperationResult(false, rebootPendingResult.Reason);
    }

    if (rebootPendingResult.Result)
    {
      context.AddLog(_pendingRebootFoundMessage);
      return new ImmyOperationResult(true);
    }

    context.AddLog(_pendingRebootNotFoundMessage);
    return new ImmyOperationResult(true, _pendingRebootNotFoundMessage);
  }

  public virtual async Task<ImmyOperationResult<bool>> CheckIfRebootPending(IRunContext context)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    try
    {
      var payload = new FindScriptByNameCmdPayload("Test-PendingReboot", ScriptCategory.Function);
      var script = await context.Args.FindScriptByNameCmd.RunAsync(
        payload,
        context.Args.StopProcessing,
        cachePolicy: context.Args.CachePolicy,
        policyContext: context.Args.PolicyContext);

      if (script is null) return new ImmyOperationResult<bool>(false, TestPendingRebootScriptMissingMessage);

      var result = await context.RunScript<bool>(
        script,
        scriptTimeouts.Action,
        context.Args.StopProcessing);
      if (result.HadTerminatingException)
        return new ImmyOperationResult<bool>(false, reason: result.GetErrorString());

      return new ImmyOperationResult<bool>(true, result: result.OutputAsObject);
    }
    catch (CommandTimedOutException ex)
    {
      return new ImmyOperationResult<bool>(false, ex.Message);
    }
  }

  public async Task<ImmyOperationResult<string?>> RunScript(
    IActionRunContext context,
    Script script,
    int defaultTimeout,
    Software? software = null,
    SoftwareVersion? softwareVersion = null,
    bool isPrimary = false)
  {
    try
    {
      ScriptContextParameters scriptContextParameters = new()
      {
        Software = software,
        MaintenanceTask = await context.GetMaintenanceTask(),
        SoftwareVersion = softwareVersion,
        TargetAssignment = await context.GetTargetAssignment()
      };

      var result = await context.RunScript(
        script,
        defaultTimeout,
        context.Args.StopProcessing,
        contextParameters: scriptContextParameters,
        isPrimary: isPrimary);
      return ImmyOperationResult<string?>.FromMetaScriptConsoleTextResult(result);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      // coerce exceptions into operation failures
      return new ImmyOperationResult<string?>(false, reason: ex.Message);
    }
  }

  public async Task<ImmyOperationResult> ApplyWindowsPatch(
    IActionRunContext context,
    WindowsPatch software,
    ILogPhaseHandle? progressPhase = null)
  {
    try
    {
      if (context.PreferredWindowsPatchProvider == null)
      {
        var msg = "The preferred run-script integration does not support Windows patching.";
        context.AddLog(msg);
        return new(false, reason: msg);
      }

      // since we are running the patch directly through the provider, let's first check if the computer is online.
      await context.EnsureComputerIsOnline();

      // timeout of 1 hour is what we currently have automate patching set to use.
      context.AddLog($"Applying {software.Name} with a timeout of 1 hour.");

      var (device, provider) = context.PreferredWindowsPatchProvider.Value;
      progressPhase?.SetProgressPercentComplete(0, 2, progressStatus: ActionProgressStatuses.Applying);
      var result = await provider.ApplyWindowsPatch(
        device.ExternalAgentId,
        software.MicrosoftUpdateId,
        context.Args.StopProcessing);
      progressPhase?.SetProgressPercentComplete(1, 2, progressStatus: ActionProgressStatuses.RebootingIfNecessary);
      await RebootComputer(context, forceOnNormal: true, treatForceAsIfNecessary: true);
      if (result) return new(true);
      return new(false, reason: "ApplyWindowsPatch did not return the expected result");
    }
    catch (CommandTimedOutException ex)
    {
      return new(false, reason: $"Windows update timed out: {ex.Message}");
    }
  }

  public async Task<ImmyOperationResult<int>> DetermineSkippedActions(
    IStageRunContext context,
    ICollection<MaintenanceAction> actions)
  {
    try
    {
      int numSkipped = 0;
      if (context.Args.RebootPreference == RebootPreference.Suppress)
      {
        foreach (var action in actions)
        {
          context.Args.StopProcessing.ThrowIfCancellationRequested();
          if (
            (action.MaintenanceType is MaintenanceType.GlobalSoftware or MaintenanceType.LocalSoftware)
            && (action.ActionType != MaintenanceActionType.NoAction || action.ActionResult == MaintenanceActionResult.Pending)
          )
          {
            var software = await context.Args.SoftwareActions.GetSoftware(
              action.SoftwareType!.Value,
              action.MaintenanceIdentifier,
              context.Args.StopProcessing,
              cachePolicy: context.Args.CachePolicy,
              policyContext: context.Args.PolicyContext);
            if (software?.RebootNeeded is true)
            {
              context.AddLog($"Skipping {action.MaintenanceDisplayName}, since it requires a reboot and will not get one.");
              numSkipped++;
              action.ActionStatus = MaintenanceActionStatus.Skipped;
              await context.FailAction(action, resultReason: MaintenanceActionResultReason.RebootsSuppressed, resultReasonMsg: "This software needs a reboot, but reboots are suppressed");
            }
          }
        }
      }
      return new ImmyOperationResult<int>(true, result: numSkipped);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      return new ImmyOperationResult<int>(false, ex.Message);
    }
  }

  /// <summary>
  /// Uninstall a software using a detected version's uninstall script if present
  /// </summary>
  /// <param name="context"></param>
  /// <param name="software"></param>
  /// <param name="detectedVersion"></param>
  /// <returns></returns>
  private async Task<ImmyOperationResult> UninstallByDetectedVersionUninstallScript(IActionRunContext context, Software software, SoftwareVersion? detectedVersion)
  {
    try
    {
      if (string.IsNullOrEmpty(detectedVersion?.UninstallScript?.Action))
      {
        var msg = "Cannot attempt to uninstall using the detected version's uninstall script because it is not present.";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }

      var scriptTypeMsg = detectedVersion.SoftwareType switch
      {
        SoftwareType.Chocolatey => "Chocolatey",
        SoftwareType.Ninite => "Ninite",
        _ => "detected version's"
      };

      context.AddLog(
        $"Attempting to uninstall {software.Name} {detectedVersion.SemanticVersionString} using the {scriptTypeMsg} uninstall script.");

      var uninstallResult = await RunUninstall(context, software, detectedVersion);
      if (!uninstallResult.Success)
        return new ImmyOperationResult(false, uninstallResult.Reason);

      var detectionResult = await DetectSoftwareVersionString(context, software, true, await context.GetTargetAssignment());
      if (!detectionResult.Success)
        return new ImmyOperationResult(false, detectionResult.Reason);

      if (string.IsNullOrEmpty(detectionResult.Result))
      {
        context.AddLog($"{software.Name} uninstalled successfully.");
        return new ImmyOperationResult(true);
      }
      else
      {
        var msg =
          $"Failed to uninstall {software.Name} {detectedVersion.SemanticVersionString} using the {scriptTypeMsg} uninstall script.\n\nDetected: {detectionResult.Result}";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }
    }
    catch (DownloadFailedException)
    {
      var msg = "Failed to download license.";
      context.AddLog(msg);
      return new ImmyOperationResult(false, msg);
    }
  }

  /// <summary>
  /// Uninstall a software using the software's default uninstall script if present
  /// </summary>
  /// <param name="context"></param>
  /// <param name="software"></param>
  /// <param name="detectedVersion"></param>
  /// <returns></returns>
  private async Task<ImmyOperationResult> UninstallByDefaultUninstallScript(IActionRunContext context, Software software, SoftwareVersion? detectedVersion = null)
  {
    try
    {
      if (string.IsNullOrEmpty(software.UninstallScript?.Action))
      {
        var msg = "Cannot attempt to uninstall using the software's default uninstall script because it is not present.";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }

      context.AddLog($"Attempting to uninstall {software.SoftwareType} {software.Name} using the software's default uninstall script.");

      var uninstallResult = await RunUninstall(context, software, detectedVersion, useDefaultSoftwareUninstallScript: true);
      if (!uninstallResult.Success)
        return new ImmyOperationResult(false, uninstallResult.Reason);

      var detectionResult = await DetectSoftwareVersionString(context, software, true, await context.GetTargetAssignment());
      if (!detectionResult.Success)
        return new ImmyOperationResult(false, detectionResult.Reason);

      if (string.IsNullOrEmpty(detectionResult.Result))
      {
        context.AddLog($"{software.Name} uninstalled successfully.");
        return new ImmyOperationResult(true);
      }
      else
      {
        var msg = $"Failed to uninstall {software.Name} {detectedVersion?.SemanticVersionString} using the software's default uninstall script.\n\nDetected: {detectionResult.Result}";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }
    }
    catch (DownloadFailedException)
    {
      var msg = "Failed to download license.";
      context.AddLog(msg);
      return new ImmyOperationResult(false, msg);
    }
  }

  /// <summary>
  /// Uninstall a software using the software's latest version's uninstall script if present
  /// </summary>
  /// <param name="context"></param>
  /// <param name="software"></param>
  /// <returns></returns>
  private async Task<ImmyOperationResult> UninstallByLatestVersionUninstallScript(IActionRunContext context, Software software)
  {
    try
    {
      var latest = await context.Args.SoftwareActions.GetLatestSoftwareVersion(
        software.SoftwareType,
        software.Identifier,
        policyContext: context.Args.PolicyContext,
        cachePolicy: context.Args.CachePolicy,
        token: context.Args.StopProcessing);

      if (string.IsNullOrEmpty(latest?.UninstallScript?.Action))
      {
        var msg = "Cannot attempt to uninstall using the latest version's uninstall script because it is not present.";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }

      context.AddLog($"Attempting to uninstall {software.Name} using the latest version's uninstall script.");

      var uninstallResult = await RunUninstall(context, software, latest);
      if (!uninstallResult.Success)
        return new ImmyOperationResult(false, uninstallResult.Reason);

      var detectionResult = await DetectSoftwareVersionString(context, software, true, await context.GetTargetAssignment());
      if (!detectionResult.Success)
        return new ImmyOperationResult(false, detectionResult.Reason);

      if (string.IsNullOrEmpty(detectionResult.Result))
      {
        context.AddLog($"[{software.Name}] uninstalled successfully.");
        return new ImmyOperationResult(true);
      }
      else
      {
        var msg = $"Failed to uninstall {software.Name} using the latest version's uninstall script.\n\nDetected: {detectionResult.Result}";
        context.AddLog(msg);
        return new ImmyOperationResult(false, msg);
      }
    }
    catch (DownloadFailedException)
    {
      var msg = "Unable to download license";
      context.AddLog(msg);
      return new ImmyOperationResult(false, msg);
    }
  }

  public async Task<ImmyOperationResult> PostInstallSoftware(
    IActionRunContext context,
    ILogPhaseHandle? progressPhase = null)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    var desiredVersion = context.DesiredSoftwareVersion;
    var software = context.DesiredSoftware;

    Script? postInstallScript;
    if (desiredVersion?.PostInstallScript is Script versionPostInstallScript)
    {
      postInstallScript = versionPostInstallScript;
    }
    else if (software?.PostInstallScript is Script softwarePostInstallScript)
    {
      postInstallScript = softwarePostInstallScript;
    }
    else
    {
      postInstallScript = null;
    }

    if (postInstallScript == null) return new(true);

    try
    {
      progressPhase?.SetProgressPercentComplete(0, 1, progressStatus: ActionProgressStatuses.RunningPostInstallScript);
      var res = await RunScript(
        context,
        postInstallScript,
        scriptTimeouts.Install,
        software: software,
        softwareVersion: desiredVersion);

      if (!res.Success)
      {
        return new(false, reason: res.Reason);
      }
      return new(true);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      return new(false, reason: ex.Message);
    }
  }

  public async Task<ImmyOperationResult> PostUninstallSoftware(
    IActionRunContext context,
    ILogPhaseHandle? progressPhase = null)
  {
    var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
    var detectedVersion = context.DetectedSoftwareVersion;
    var software = context.DesiredSoftware;

    Script? postUninstallScript;
    if (detectedVersion?.PostUninstallScript is Script versionPostInstallScript)
    {
      postUninstallScript = versionPostInstallScript;
    }
    else if (software?.PostUninstallScript is Script softwarePostInstallScript)
    {
      postUninstallScript = softwarePostInstallScript;
    }
    else
    {
      postUninstallScript = null;
    }

    if (postUninstallScript == null) return new(true);


    try
    {
      progressPhase?.SetProgressPercentComplete(0, 1, progressStatus: ActionProgressStatuses.RunningPostUninstallScript);
      var res = await RunScript(
        context,
        postUninstallScript,
        scriptTimeouts.Install,
        software: software,
        softwareVersion: detectedVersion);

      if (!res.Success)
      {
        return new(false, reason: res.Reason);
      }

      return new(true);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      return new(false, reason: ex.Message);
    }
  }

  private static async Task<(string? ErrorMessage, List<DynamicSoftwareVersion>)> RunDynamicVersionScript(
    IRunContext context,
    Software software,
    Func<ImmybotDbContext> _dbContextFactory)
  {
    string? retErrorMessage = null;
    var policyContext = context.Args.PolicyContext ?? [];
    var cachePolicy = context.Args.CachePolicy;
    if (cachePolicy != null)
    {
      // cache script per tenant
      var type = software is GlobalSoftware ? DatabaseType.Global : software.DynamicVersionsScriptType;
      policyContext.SetCacheKey(new[] { "dynamic-version-script", software.Identifier, software.DynamicVersionsScriptId.ToString(), type.ToString(), context.TenantId.ToString() });
    }
    else
    {
      cachePolicy = Policy.NoOpAsync();
    }

    //Determine if the software uses a provider that implements ISupportsDynamicVersions
    //Get the provider link id for the maintenance item
    // if session is not adhoc, use the provider link id from the SpecifiedTargetAssignment
    // if session is adhoc, use the provider link id from the session args
    // fetch provider link
    // if software has providerTypeId and only one integration exists for that providerTypeId, use it
    ProviderLink? providerLink = null;
    int? providerLinkId = context.SpecifiedTargetAssignment?.ProviderLinkIdForMaintenanceItem ?? context.Args.MaintenanceItem?.ProviderLinkIdForMaintenanceItem;

    if ((software.AgentIntegrationTypeId is not null || providerLinkId is not null))
    {
      if (providerLinkId.HasValue)
      {
        await using var ctx = _dbContextFactory();
        providerLink = ctx.GetProviderLink(providerLinkId.Value, includeClients: true);
      }
      else
      {
        await using var ctx = _dbContextFactory();
        var providerLinks = ctx.GetProviderLinks(providerTypeId: software.AgentIntegrationTypeId, includeClients: true).ToList();
        switch (providerLinks.Count)
        {
          case 1:
            providerLink = providerLinks[0];
            break;
          case > 1:
            context.AddLog(
              $"Unable to automatically determine integration instance as there are multiple active integrations with TypeId: {software.AgentIntegrationTypeId}");
            break;
          default:
            context.AddLog($"No active integrations with TypeId {software.AgentIntegrationTypeId} found");
            break;
        }
      }

      if (providerLink is null) context.AddLog($"Provider with id {providerLinkId} not found");

      if (providerLink is not null && await context.Args.ProviderActions.GetProvider(providerLink, context.Args.StopProcessing) is ISupportsDynamicVersions dynamicVersionProvider)
      {
        var externalClientId = providerLink.ProviderClients.FirstOrDefault(a => a.LinkedToTenantId == context.TenantId)
          ?.ExternalClientId;

        // ensure the provider client exists if the provider does not support listing clients
        // e.g. immy agent
        if (externalClientId is null && dynamicVersionProvider is not ISupportsListingClients)
        {
          externalClientId = context.TenantId.ToString();
          context.Args.ProviderActions.EnsureProviderClientExists(
            providerLink.Id,
            externalClientId,
            supportsListingExternalClients: false);
        }

        // Match our Immy tenant to our provider tenant
        if (externalClientId is null)
          return ($"Provider {providerLink.Name} does not have a client linked to tenant {context.TenantId}", new List<DynamicSoftwareVersion>());

        // Get the dynamic versions
        // cache script per tenant
        var type = software is GlobalSoftware ? DatabaseType.Global : software.DynamicVersionsScriptType;
        policyContext.SetCacheKey(new[] { "integration-dynamic-versions", software.Identifier, software.DynamicVersionsScriptId.ToString(), type.ToString(), context.TenantId.ToString() });
        var dynamicVersions = await cachePolicy.ExecuteAsync(async (ctx, t) =>
          await dynamicVersionProvider.GetDynamicVersions(externalClientId, context.Args.StopProcessing),
          policyContext, context.Args.StopProcessing);
        if (!dynamicVersions.Any())
          return ($"Provider {providerLink.Name} did not return a version", new List<DynamicSoftwareVersion>());

        var versions = new List<DynamicSoftwareVersion>();
        // Make a list of DynamicSoftwareVersion(s) from the dynamic versions
        foreach (var version in dynamicVersions)
        {
          var semver = version.GetSemanticVersion();
          if (semver is null) continue;
          var dependsOnSemver = version.GetDependsOnSemanticVersion();
          versions.Add(new DynamicSoftwareVersion(software.SoftwareType, software.Identifier)
          {
            InstallerFile = version.FileName,
            PackageType = version.PackageType,
            PackageHash = version.PackageHash,
            SemanticVersion = semver,
            DisplayName = software.Name,
            DisplayVersion = version.Version,
            URL = version.Url,
            InstallerType = SoftwareVersionInstallerType.Url,
            RelativeCacheSourcePath = software.RelativeCacheSourcePath.ToString(),
            Architecture = version.Architecture,
            DependsOnSemanticVersion = dependsOnSemver
          });
        }

        var charOrChars = dynamicVersions.Length == 1 ? "" : "s";
        var wasOrWere = dynamicVersions.Length == 1 ? "was" : "were";
        context.AddLog($"{dynamicVersions.Count()} version{charOrChars} {wasOrWere} returned from the dynamic versions script.");

        return ("", versions);
      }
    }
    if (software.DynamicVersionsScript is null)
      return ($"{software.Name} does not have a dynamic version script", new List<DynamicSoftwareVersion>());

    var dynamicVersionScriptRes = await cachePolicy.ExecuteAsync(async (ctx, t) =>
    {
      var scriptTimeouts = context.ApplicationPreferences.DefaultScriptTimeouts;
      TargetAssignment? targetAssignment;
      MaintenanceTask? task = null;
      SoftwareVersion? detectedSoftwareVersion = null;
      if (context is IActionRunContext actionRunContext)
      {
        targetAssignment = await actionRunContext.GetTargetAssignment();
        task = await actionRunContext.GetMaintenanceTask();
        if (actionRunContext.DetectedVersion is not null)
        {
          detectedSoftwareVersion = new DynamicSoftwareVersion(software.SoftwareType, software.Identifier)
          {
            SemanticVersion = actionRunContext.DetectedVersion
          };
        }
      }
      else
      {
        targetAssignment = context.SpecifiedTargetAssignment;
      }

      return await context.RunScript<DynamicVersionsScriptResponse>(
        software.DynamicVersionsScript,
        scriptTimeouts.DynamicVersions,
        context.Args.StopProcessing,
        contextParameters: new ScriptContextParameters
        {
          Software = software,
          MaintenanceTask = task,
          TargetAssignment = targetAssignment,
          SoftwareVersion = detectedSoftwareVersion,
        });
    }, policyContext, context.Args.StopProcessing);

    if (dynamicVersionScriptRes.HadTerminatingException)
    {
      var errMsg = dynamicVersionScriptRes.GetErrorString();
      context.AddLog(errMsg);
      retErrorMessage = errMsg;
      return (retErrorMessage, new List<DynamicSoftwareVersion>());
    }

    if (dynamicVersionScriptRes.OutputAsObject?.Versions is null or { Count: 0 }) return (retErrorMessage ?? "No versions were returned", []);

    var dynamicVersionObjects = dynamicVersionScriptRes.OutputAsObject;
    var dynamicSoftwareVersions = new List<DynamicSoftwareVersion>();

    var someVersionsExcluded = false;
    var excludedCount = 0;

    foreach (var v in dynamicVersionObjects.Versions)
    {
      var semver = v.GetSemanticVersion();
      var dependsOnSemver = v.GetDependsOnSemanticVersion();
      if (semver == null || string.IsNullOrEmpty((v.Url)))
      {
        someVersionsExcluded = true;
        excludedCount++;
        continue;
      }

      dynamicSoftwareVersions.Add(new DynamicSoftwareVersion(software.SoftwareType, software.Identifier)
      {
        InstallerFile = v.FileName,
        PackageType = v.PackageType,
        PackageHash = v.PackageHash,
        SemanticVersion = semver,
        DisplayName = software.Name,
        DisplayVersion = v.Version,
        URL = v.Url,
        InstallerType = SoftwareVersionInstallerType.Url,
        RelativeCacheSourcePath = software.RelativeCacheSourcePath.ToString(),
        Architecture = v.Architecture,
        DependsOnSemanticVersion = dependsOnSemver
      });
    }

    var sChar = dynamicVersionObjects.Versions.Count == 1 ? "" : "s";
    var wasWere = dynamicVersionObjects.Versions.Count == 1 ? "was" : "were";
    context.AddLog($"{dynamicVersionObjects.Versions.Count} version{sChar} {wasWere} returned from the dynamic versions script.");

    if (someVersionsExcluded)
    {
      var sCharExcluded = excludedCount == 1 ? "" : "s";
      var wasWereExcluded = excludedCount == 1 ? "was" : "were";
      var itThey = excludedCount == 1 ? "it" : "they";
      context.AddLog($"{excludedCount} version{sCharExcluded} from the dynamic versions script {wasWereExcluded} excluded because {itThey} {wasWereExcluded} missing a version or url property.");
    }

    return (retErrorMessage, dynamicSoftwareVersions ?? []);
  }

  public async Task<ImmyOperationResult<DynamicSoftwareVersion?>> GetLatestDynamicSoftwareVersion(
    IActionRunContext context,
    Software software,
    ILogPhaseHandle? progressPhase = null)
  {
    // if integration is built-in use dynamic versions script, otherwise use integration.
    var versionSource = software.AgentIntegrationTypeId.HasValue ? "agent integration" : "dynamic versions script";

    context.AddLog($"Attempting to retrieve latest software version using the software's {versionSource}.");
    var result = await GetDynamicSoftwareVersion(context, software, progressPhase: progressPhase);
    if (!result.Success)
    {
      context.AddLog($"Retrieving the latest dynamic software version failed: {result.Reason}");
    }
    return result;
  }

  public async Task<ImmyOperationResult<DynamicSoftwareVersion?>> GetDynamicSoftwareVersion(
    IActionRunContext context,
    Software software,
    SemanticVersion? semver = null,
    ILogPhaseHandle? progressPhase = null)
  {
    try
    {
      // When we have a integration, we don't have a dynover script so this throws
      // If we don't have a dynover script but we have an integration, we can continue
      // short-circuit if we don't have a dynamic version script
      if (software.DynamicVersionsScript is null && software.AgentIntegrationTypeId is null)
      {
        return new ImmyOperationResult<DynamicSoftwareVersion?>(false,
          reason: "Software does not have a dynamic versions script set");
      }

      progressPhase?.SetProgressPercentComplete(0, 2,
        progressStatus: ActionProgressStatuses.RunningDynamicVersionScript);
      // retrieve the dynamic versions from the script
      var (errorMessage, versions) = await RunDynamicVersionScript(context, software, _dbContextFactory);

      if (!string.IsNullOrEmpty(errorMessage))
      {
        return new ImmyOperationResult<DynamicSoftwareVersion?>(false, reason: errorMessage);
      }

      var dynamicVersionQuery = versions
        .OrderByDescending(a => a.SemanticVersion)
        .ThenByDescending(a => a.Architecture)
        .ToList();

      if (semver != null)
      {
        // if we want a specific version then filter to that version
        var semverQuery = dynamicVersionQuery.Where(a => a.SemanticVersion == semver).ToList();
        // If we don't have any versions at the requested version, then check if we have any that are greater than the requested version.
        // If we do, then it's likely that a newer version has been released since the last time the dynamic version script was run.
        if (!semverQuery.Any() && dynamicVersionQuery.Exists(a => a.SemanticVersion > semver))
        {
          return new ImmyOperationResult<DynamicSoftwareVersion?>(false,
            reason: $"The dynamic version script is returning a newer version than what was retrieved the last time the script was ran. " +
                    $"If so, re-deploy this action. We will automatically handle this scenario in a future release.");
        }

        dynamicVersionQuery = semverQuery.ToList();
      }

      progressPhase?.SetProgressPercentComplete(1, 2,
        progressStatus: ActionProgressStatuses.DeterminingComputerArchitecture);
      // retrieve the computer's architecture
      var architecture = await context.GetComputerArchitecture();

      // if we don't have an architecture, then let's attempt to populate it in inventory
      if (architecture == null)
      {
        context.AddLog(SessionLogMessages.CouldNotDetermineArch);
        if (context.Args.Computer is null)
        {
          var msg = "The computer object is missing from the session";
          context.AddLog(msg);
          throw new MaintenanceSessionException(msg);
        }
        var res = await _inventoryDeviceCmd.Run(
        new InventoryDeviceCmdPayload(
          context.Args.Computer.DeviceId,
          null,
          new[] { InventoryKeys.WindowsSystemInfo },
          false
        ), CancellationToken.None,
        runContext: context);

        try
        {
          if (res.Success && res.Output?.Any() is true)
          {
            var valueJson = res.Output.FirstOrDefault(a => a.Key == InventoryKeys.WindowsSystemInfo)
              .Value.FirstOrDefault(a => a.Key == InventoryKeys.OutputStreamKey).Value;
            if (!string.IsNullOrEmpty(valueJson))
            {
              var parsedObject = JObject.Parse(valueJson);
              if (parsedObject.GetValue("Architecture") is JToken archProperty)
              {
                var archString = archProperty.ToString();
                context.AddLog($"Architecture found: {archString}");
                architecture = ArchitectureHelpers.GetArchitectureFromString(archString);
              }
            }
          }

          if (!string.IsNullOrEmpty(res.FailureReason))
          {
            context.AddLog(res.FailureReason);
          }
        }
        catch (Exception ex)
        {
          context.AddLog(GenerateDetailedExceptionMessage(ex));
        }

        if (architecture is null)
        {
          // wait a few seconds for the database to get updated before we try fetching again
          await Task.Delay(TimeSpan.FromSeconds(10));
          architecture = await context.GetComputerArchitecture();
        }
      }

      DynamicSoftwareVersion? dynamicVersion;
      if (!architecture.HasValue)
      {
        if (context.Args.Computer is null)
        {
          var msg = "The computer object is missing from the session";
          context.AddLog(msg);
          throw new Exception(msg);
        }
        context.AddLog($"Could not determine the architecture of \"{context.Args.Computer.ComputerName}\".  Only versions without an architecture will be considered.");

        dynamicVersion = dynamicVersionQuery
          .Find(a => a.Architecture == null);
      }
      else
      {
        var isX64Arch = architecture == Architecture.X64;
        // if we have the computer's architecture, then find the most applicable version for it
        // x64 computers can use x86 versions
        // any computer arch can use a version that has a null arch
        dynamicVersion = dynamicVersionQuery
          .Find(a =>
            a.Architecture == architecture
            || (isX64Arch && a.Architecture == Architecture.X86)
            || a.Architecture == null);
      }

      if (dynamicVersion == null)
      {
        var archMessage = architecture.HasValue ? $"for computer architecture {architecture}" : "without a specific architecture";
        var versionMessage = semver != null ? $"version {semver}" : "the latest version";
        return new ImmyOperationResult<DynamicSoftwareVersion?>(false,
          reason: $"Could not retrieve {versionMessage} {archMessage} from the dynamic version script response.");
      }

      // if the installer file is not provided let's try to get it
      return new ImmyOperationResult<DynamicSoftwareVersion?>(true, result: dynamicVersion);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      _logger.LogError(ex,
          "Session #{SessionId} - An uncaught exception occurred while running the dynamic software version script. script id:{ScriptId}, script type: {ScriptType}",
          context.SessionId,
          software.DynamicVersionsScriptId,
          software.DynamicVersionsScriptType);
      return new ImmyOperationResult<DynamicSoftwareVersion?>(false,
        reason: $"An unhandled exception occurred while retrieving the dynamic software version. {ex.Message}");
    }
  }
}
