using System.Net.WebSockets;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Http;

namespace Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;

public interface IEphemeralAgentSessionHttpHandler
{
  public bool IsConnected { get; }
  public bool IsPendingReconnection { get; }
  /// <summary>
  /// Returns false if we are already connecting
  /// </summary>
  /// <returns></returns>
  public Task<bool> TryStartConnectingAsync(CancellationToken token);
  public Task<OpResult> HandleWSConnection(HttpContext request, WebSocket websocket, CancellationToken token);
}
