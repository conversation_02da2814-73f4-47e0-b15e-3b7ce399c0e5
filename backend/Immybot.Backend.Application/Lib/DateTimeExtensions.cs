using System;

namespace Immybot.Backend.Application.Lib;

public static class DateTimeExtensions
{
  /// <summary>
  /// https://stackoverflow.com/questions/58440682/schedule-a-hangfire-job-at-specific-time-of-the-day-based-on-time-zone?rq=1
  /// https://stackoverflow.com/questions/57878720/c-sharp-handling-ranges-of-prevailing-times-on-dst-transition-days-the-suppl/57961386#57961386
  /// </summary>
  /// <param name="dt"></param>
  /// <param name="tz"></param>
  /// <returns></returns>
  public static DateTimeOffset ToDateTimeOffset(this DateTime dt, TimeZoneInfo tz)
  {
    if (dt.Kind != DateTimeKind.Unspecified)
    {
      // Handle UTC or Local kinds (regular and hidden 4th kind)
      DateTimeOffset dto = new DateTimeOffset(dt.ToUniversalTime(), TimeSpan.Zero);
      return TimeZoneInfo.ConvertTime(dto, tz);
    }

    if (tz.IsAmbiguousTime(dt))
    {
      // Prefer the daylight offset, because it comes first sequentially (1:30 ET becomes 1:30 EDT)
      TimeSpan[] offsets = tz.GetAmbiguousTimeOffsets(dt);
      TimeSpan offset = offsets[0] > offsets[1] ? offsets[0] : offsets[1];
      return new DateTimeOffset(dt, offset);
    }

    if (tz.IsInvalidTime(dt))
    {
      // Advance by the gap, and return with the daylight offset  (2:30 ET becomes 3:30 EDT)
      TimeSpan[] offsets = [tz.GetUtcOffset(dt.AddDays(-1)), tz.GetUtcOffset(dt.AddDays(1))];
      TimeSpan gap = offsets[1] - offsets[0];
      return new DateTimeOffset(dt.Add(gap), offsets[1]);
    }

    // Simple case
    return new DateTimeOffset(dt, tz.GetUtcOffset(dt));
  }
}
