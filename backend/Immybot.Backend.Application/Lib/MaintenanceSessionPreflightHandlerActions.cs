using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Lib;

public interface IMaintenanceSessionPreflightHandlerActions
{
  Task<List<int>> GetAllPreflightSessions(CancellationToken cancellationToken);
  Task<ISessionRunContext> GetSessionRunContext(int sessionId, CancellationToken cancellationToken);
  Task<IEphemeralAgentSession> GetEphemeralAgentSession(IRunContext runContext, CancellationToken cancellationToken);
  Task<OpResult> ExecutePreflight(IRunContext runContext, CancellationToken cancellationToken);
}

internal class MaintenanceSessionPreflightHandlerActions : IMaintenanceSessionPreflightHandlerActions
{
  private readonly Func<ImmybotDbContext> _immyBotDbContextFactory;
  private readonly IEphemeralAgentAcquisition _ephemeralAgentAcquisition;
  private readonly IRunContextFactory _runContextFactory;
  private readonly IPreflightScriptInvoker _preflightScriptInvoker;

  public MaintenanceSessionPreflightHandlerActions(
    Func<ImmybotDbContext> immyBotDbContextFactory,
    IEphemeralAgentAcquisition ephemeralAgentAcquisition,
    IRunContextFactory runContextFactory,
    IPreflightScriptInvoker preflightScriptInvoker)
  {
    _immyBotDbContextFactory = immyBotDbContextFactory;
    _ephemeralAgentAcquisition = ephemeralAgentAcquisition;
    _runContextFactory = runContextFactory;
    _preflightScriptInvoker = preflightScriptInvoker;
  }
  public Task<OpResult> ExecutePreflight(IRunContext runContext, CancellationToken cancellationToken) =>
    _preflightScriptInvoker.ExecutePreflight(runContext, runContext.Args.Computer!, cancellationToken);

  public Task<List<int>> GetAllPreflightSessions(CancellationToken cancellationToken)
  {
    return _immyBotDbContextFactory.With(ctx => ctx
      .ActiveSessions
      .AsNoTracking()
      .Where(a => a.SessionStatus == SessionStatus.PendingPreflight && a.MaintenanceSession!.Computer != null)
      .Select(a => a.MaintenanceSessionId)
      .ToListAsync(cancellationToken));
  }

  public async Task<IEphemeralAgentSession> GetEphemeralAgentSession(IRunContext runContext, CancellationToken cancellationToken)
  {
    var providers = await runContext.GetAgentsAndProvidersAsync(null, cancellationToken);

    return await _ephemeralAgentAcquisition.AcquireEphemeralAgentWithRetryAsync(
      providers.ToDictionary(a => a.Key.ProviderLinkId, a => a.Value),
      computerId: runContext.ComputerId,
      providerAgentId: null,
      cancellationToken,
      computerCircuitBreakerPolicy: ComputerCircuitBreakerPolicy.BypassEphemeral,
      agentConnectionWaitTimeoutSeconds: 60);
  }

  public Task<ISessionRunContext> GetSessionRunContext(int sessionId, CancellationToken cancellationToken) =>
    _runContextFactory.GenerateSessionRunContext(sessionId, null, cancellationToken);
}
