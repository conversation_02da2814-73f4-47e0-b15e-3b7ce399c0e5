using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Lib;

internal class MaintenanceSessionPendingConnectivityHandler
  : IMaintenanceSessionPendingConnectivityHandler
  , IDisposable
{
  private readonly ILogger<MaintenanceSessionPendingConnectivityHandler> _logger;
  private readonly Func<ImmybotDbContext> _contextFactory;
  private readonly IDomainEventReceiver _eventReceiver;
  private readonly IServiceScopeFactory _serviceScopeFactory;

  private readonly ConcurrentQueue<(int ComputerId, HashSet<int> ExcludedSessionIds)> _queue = new();

  /// <summary>
  /// ensures that we cannot concurrently modify the queue
  /// </summary>
  private readonly SemaphoreSlim _queueLocker = new(1, 1);

  /// <summary>
  /// ensures that we we do not create multiple _loopRunner tasks
  /// </summary>
  private readonly object _loopRunnerLocker = new();

  private Task? _loopRunner;
  private bool _initialized;
  private bool _disposedValue;
  private readonly CancellationToken _applicationStoppingToken;

  private IDisposable? _computerOnlineEventReceiverGuid;
  private IDisposable? _replaceExistingAgentIdentificationResolutionEventGuid;
  private IDisposable? _replaceExistingComputerIdentificationResolutionEventGuid;
  private IDisposable? _keepBothAgentsIdentificationResolutionEvent;

  private readonly TimeSpan _loopDelay = TimeSpan.FromSeconds(5);

  public MaintenanceSessionPendingConnectivityHandler(
    ILogger<MaintenanceSessionPendingConnectivityHandler> logger,
    Func<ImmybotDbContext> contextFactory,
    IDomainEventReceiver eventReceiver,
    IServiceScopeFactory serviceScopeFactory,
    IHostApplicationLifetime appLifetime)
  {
    _logger = logger;
    _contextFactory = contextFactory;
    _eventReceiver = eventReceiver;
    _serviceScopeFactory = serviceScopeFactory;
    _applicationStoppingToken = appLifetime.ApplicationStopping;
  }

  private void TriggerLoop()
  {
    lock (_loopRunnerLocker)
    {
      if (_loopRunner != null) return;
      try
      {
        // if we are not processing, then trigger a new loop
        _logger.LogTrace("Triggering a new loop");
        _loopRunner = Task.Run(async () =>
        {
          var computerIds = await DequeueComputers();
          await ProcessComputers(computerIds);
        }, _applicationStoppingToken);

#pragma warning disable VSTHRD105
        _ = _loopRunner.ContinueWith(async t =>
#pragma warning restore VSTHRD105
        {
          _applicationStoppingToken.ThrowIfCancellationRequested();

          if (t.IsFaulted)
          {
            _logger.LogError(t.Exception, "Exception occurred in LoopRunner");
          }

          bool isQueueEmpty;

          await _queueLocker.WaitAsync(_applicationStoppingToken);
          _applicationStoppingToken.ThrowIfCancellationRequested();

          try
          {
            _loopRunner = null;
            isQueueEmpty = _queue.IsEmpty;
          }
          finally
          {
            _queueLocker.Release();
          }

          if (!isQueueEmpty)
          {
            _logger.LogTrace("The queue is not empty after triggering loop, triggering a new loop.");
            TriggerLoop();
          }
        }, _applicationStoppingToken);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "An exception occurred in the maintenance session pending connectivity handler");
      }
    }
  }

  private async Task<Dictionary<int, HashSet<int>>> DequeueComputers()
  {
    _applicationStoppingToken.ThrowIfCancellationRequested();
    await _queueLocker.WaitAsync(_applicationStoppingToken);
    try
    {
      var computerIds = new Dictionary<int, HashSet<int>>();

      while (_queue.TryDequeue(out var ev))
      {
        if (!computerIds.TryGetValue(ev.ComputerId, out var sessionIds))
        {
          sessionIds = [];
          computerIds.TryAdd(ev.ComputerId, sessionIds);
        }
        ev.ExcludedSessionIds.ForEach(s => sessionIds.Add(s));
      }

      return computerIds;
    }
    finally
    {
      _queueLocker.Release();
    }
  }

  private async Task ProcessComputers(Dictionary<int, HashSet<int>> computerIds)
  {
    foreach (var (computerId, excludedSessionIds) in computerIds)
    {
      await TryEnqueuePendingConnectivityMaintenanceSession(computerId, excludedSessionIds);
    }
  }

  /// <summary>
  /// Exposed for testing purposes since this contains the main logic of the service.
  /// This should really be brought into a separate class and tested in isolation.
  /// </summary>
  /// <param name="computerId"></param>
  /// <param name="excludedSessionIds"></param>
  internal async Task TryEnqueuePendingConnectivityMaintenanceSession(
    int computerId,
    HashSet<int> excludedSessionIds)
  {
    await using var ctx = _contextFactory();

    // fetch all sessions pending connectivity for this computer
    var sessionsQueryable = ctx.ActiveSessions
      .AsNoTracking()
      .Include(a => a.MaintenanceSession)
      .Where(a =>
        a.MaintenanceSession!.ComputerId == computerId &&
        (a.SessionStatus == SessionStatus.PendingConnectivity || a.SessionStatus == SessionStatus.Running));

    if (excludedSessionIds.Any())
    {
      sessionsQueryable = sessionsQueryable.Where(a => !excludedSessionIds.Contains(a.MaintenanceSessionId));
    }

    var sessions = await sessionsQueryable.OrderByDescending(a => a.MaintenanceSessionId).ToListAsync();

    if (!sessions.Any()) return;

    if (sessions.Exists(s => s.SessionStatus == SessionStatus.Running)) return;

    var sessionsToConsiderEnqueueing = new List<ActiveSession>();
    var missedSessions = new List<int>();

    // find all pending connectivity sessions for schedules or full maintenance
    var groupedBySchedule = sessions.GroupBy(a => a.MaintenanceSession?.ScheduledId);

    foreach (var group in groupedBySchedule)
    {
      // consider all adhoc sessions for re-enqueuing
      if (group.Key is null)
      {
        sessionsToConsiderEnqueueing.AddRange(group.ToList());
        continue;
      }

      var scheduleSessions = group.ToList();
      var latest = scheduleSessions.OrderByDescending(a => a.MaintenanceSessionId).First();
      // Take only the latest session for each schedule; cancel the rest
      sessionsToConsiderEnqueueing.Add(latest);
      scheduleSessions.Remove(latest);
      missedSessions.AddRange(scheduleSessions.Select(a => a.MaintenanceSessionId));
    }

    // find all ad-hoc pending connectivity sessions with specified maintenance items
    var groupedByItem = sessions
      .Where(a => a.MaintenanceSession?.ScheduledId == null && a.MaintenanceSession?.JobArgs.MaintenanceItem != null)
      .GroupBy(a => new { a.MaintenanceSession?.JobArgs.MaintenanceItem!.MaintenanceIdentifier, a.MaintenanceSession?.JobArgs.MaintenanceItem?.MaintenanceType });

    foreach (var group in groupedByItem)
    {
      var itemSessions = group.ToList();
      var latest = itemSessions.OrderByDescending(a => a.MaintenanceSessionId).First();
      // Take only the latest session for each maintenance item; cancel the rest
      sessionsToConsiderEnqueueing.Add(latest);
      itemSessions.Remove(latest);
      missedSessions.AddRange(itemSessions.Select(a => a.MaintenanceSessionId));
    }

    if (missedSessions.Count != 0)
    {
      await ctx.MaintenanceSessions
        .Where(a => missedSessions.Contains(a.Id))
        .ExecuteUpdateAsync(a =>
            a.SetProperty(b => b.SessionStatus, SessionStatus.Missed),
          _applicationStoppingToken);
    }

    if (sessionsToConsiderEnqueueing.Count != 0)
    {
      // enqueue the earliest valid session
      var sessionToEnqueue = sessionsToConsiderEnqueueing.OrderBy(a => a.MaintenanceSessionId).First();
      using var scope = _serviceScopeFactory.CreateScope();
      var immyServiceJob = scope.ServiceProvider.GetRequiredService<IImmyServiceJob>();
      try
      {
        immyServiceJob.EnqueueConnectivityTriggered(
          new EnqueueExistingSessionPayload(sessionToEnqueue.MaintenanceSessionId));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to enqueue session {sessionId} for computer {computerId}", sessionToEnqueue.MaintenanceSessionId, computerId);
      }
    }
  }

  public async Task FindAndEnqueuePendingMaintenanceSessionsForComputer(
    int computerId,
    HashSet<int> sessionIdsToExclude,
    bool withDelay = false)
  {
    if (withDelay) await Task.Delay(_loopDelay, _applicationStoppingToken);
    await _queueLocker.WaitAsync(_applicationStoppingToken);
    _applicationStoppingToken.ThrowIfCancellationRequested();
    try
    {
      _queue.Enqueue((computerId, sessionIdsToExclude));
    }
    finally
    {
      _queueLocker.Release();
    }
    TriggerLoop();
  }

  public void Init()
  {
    if (_initialized) return;

    // whenever a computer comes online,
    // check for pending connectivity sessions that need to be run
    _computerOnlineEventReceiverGuid = _eventReceiver.Subscribe<AgentConnectedSupportingScriptExecutionEvent>(async ev =>
    {
      if (ev.ComputerId is null) return;

      _logger.LogTrace($"Received online agent event");
      await FindAndEnqueuePendingMaintenanceSessionsForComputer(ev.ComputerId.Value, []);
    });

    // whenever a conflicting agent is resolved to a computer,
    // check for pending connectivity sessions that need to be run
    _replaceExistingAgentIdentificationResolutionEventGuid = _eventReceiver.Subscribe<ReplaceExistingAgentIdentificationResolutionEvent>(async ev =>
    {
      _logger.LogTrace("Received {event}", nameof(ReplaceExistingAgentIdentificationResolutionEvent));
      await FindAndEnqueuePendingMaintenanceSessionsForComputer(ev.ComputerId, []);
    });

    _replaceExistingComputerIdentificationResolutionEventGuid = _eventReceiver.Subscribe<ReplaceExistingComputerIdentificationResolutionEvent>(async ev =>
    {
      _logger.LogTrace("Received {event}", nameof(ReplaceExistingComputerIdentificationResolutionEvent));
      await FindAndEnqueuePendingMaintenanceSessionsForComputer(ev.ComputerId, []);
    });

    _keepBothAgentsIdentificationResolutionEvent = _eventReceiver.Subscribe<KeepBothAgentsIdentificationResolutionEvent>(async ev =>
    {
      _logger.LogTrace("Received {event}", nameof(KeepBothAgentsIdentificationResolutionEvent));
      await FindAndEnqueuePendingMaintenanceSessionsForComputer(ev.ComputerId, []);
    });

    _initialized = true;
  }

  protected virtual void Dispose(bool disposing)
  {
    if (!_disposedValue)
    {
      if (disposing)
      {
        _queueLocker.Dispose();
        _computerOnlineEventReceiverGuid?.Dispose();
        _replaceExistingAgentIdentificationResolutionEventGuid?.Dispose();
        _replaceExistingComputerIdentificationResolutionEventGuid?.Dispose();
        _keepBothAgentsIdentificationResolutionEvent?.Dispose();
      }

      _disposedValue = true;
    }
  }

  public void Dispose()
  {
    // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
    Dispose(disposing: true);
    GC.SuppressFinalize(this);
  }
}
