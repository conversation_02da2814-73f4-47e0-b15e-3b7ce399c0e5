
using Immybot.Shared.Primitives;

namespace Immybot.Backend.Application.Lib;

internal class DisposableSemaphoreSlim : IDisposable
{
  private readonly SemaphoreSlim _semaphore;
  private bool _disposedValue;

  public DisposableSemaphoreSlim(int initial, int max)
  {
    _semaphore = new(initial, max);
  }

  public async Task<IDisposable> WaitAsync(CancellationToken token = default)
  {
    await _semaphore.WaitAsync(token);
    return Disposable.Create(() => { if (!_disposedValue) _semaphore.Release(); });
  }

  protected virtual void Dispose(bool disposing)
  {
    if (!_disposedValue)
    {
      if (disposing)
      {
        // dispose managed state (managed objects)
        _semaphore.Dispose();
      }

      //  free unmanaged resources (unmanaged objects) and override finalizer
      // set large fields to null
      _disposedValue = true;
    }
  }

  public void Dispose()
  {
    // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
    Dispose(disposing: true);
    GC.SuppressFinalize(this);
  }
}
