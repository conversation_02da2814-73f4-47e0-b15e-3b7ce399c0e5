namespace Immybot.Backend.Application.Lib.Azure;

public class GraphApiResult<T>
{
  public string PrincipalId { get; }
  public bool Successful { get; }
  public T? Result { get; }
  public string? FailureMessage { get; }
  public GraphApiResult(string principalId, T? result, bool successful = true, string? failureMessage = null)
  {
    PrincipalId = principalId;
    Successful = successful;
    Result = result;
    FailureMessage = failureMessage;
  }
}
