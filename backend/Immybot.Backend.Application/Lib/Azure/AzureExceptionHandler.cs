using System;
using System.Text.Json;
using Azure.Identity;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Oauth;
using Microsoft.Graph;
using Microsoft.Graph.Models.ODataErrors;
using Refit;

namespace Immybot.Backend.Application.Lib.Azure;

public interface IAzureExceptionHandler
{
  bool NotificationEventEmittingEnabled { get; }

  string GetFormattedErrorMessage(Exception ex);
  AzureErrorLogItem HandleAzureException(
    Exception ex,
    string tenantPrincipalId,
    string sourceMessage);
  AzureErrorLogItem HandleAzureException(
    string exceptionMessage,
    string tenantPrincipalId,
    string sourceMessage);
  AzureErrorLogItem HandleAzureException(
    Exception ex,
    AzureTenantTokenCredential credential,
    string sourceMessage);
  AzureErrorLogItem HandlePartnerCenterException(
    Exception ex,
    string sourceMessage,
    string? partnerPrincipalId = null,
    int? oauth2AccessTokenId = null);
  bool IsAzureError(Exception ex);
  void DisableNotificationEventEmitting();
  void EnableNotificationEventEmitting();
}

internal class AzureExceptionHandler : IAzureExceptionHandler
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
  private readonly IDomainEventEmitter _eventEmitter;

  public bool NotificationEventEmittingEnabled { get; private set; } = true;

  public AzureExceptionHandler(
    IDomainEventEmitter eventEmitter)
  {
    _eventEmitter = eventEmitter;
  }

  public void DisableNotificationEventEmitting()
  {
    NotificationEventEmittingEnabled = false;
  }

  public void EnableNotificationEventEmitting()
  {
    NotificationEventEmittingEnabled = true;
  }

  public AzureErrorLogItem HandleAzureException(
    Exception ex,
    AzureTenantTokenCredential credential,
    string sourceMessage)
  {
    // Attach the credential details to the AzureError, to allow better diagnosis on the frontend
    var azError = FromException(ex) with
    {
      CredentialDetails = new(
        credential.TenantPrincipalId,
        credential.PartnerPrincipalId,
        credential.GotAccessTokenFrom,
        credential.ResolvedClientId,
        credential.OnlyUsePartnerCenterRefresh,
        credential.TenantPreferredAzureClientId,
        credential.TenantAzurePermissionLevel),
    };
    var logItem = new AzureErrorLogItem
    {
      Id = Guid.NewGuid(),
      AzureError = azError,
      SourceMessage = sourceMessage,
      TenantPrincipalId = credential.TenantPrincipalId,
      CreatedDateUtc = DateTime.UtcNow,
    };
    TrackAzureError(logItem);
    return logItem;
  }

  public AzureErrorLogItem HandleAzureException(
    string exceptionMessage,
    string tenantPrincipalId,
    string sourceMessage)
  {
    var azError = new AzureError(exceptionMessage);
    var logItem = new AzureErrorLogItem
    {
      Id = Guid.NewGuid(),
      AzureError = azError,
      SourceMessage = sourceMessage,
      TenantPrincipalId = tenantPrincipalId,
      CreatedDateUtc = DateTime.UtcNow,
    };
    TrackAzureError(logItem);
    return logItem;
  }

  public AzureErrorLogItem HandleAzureException(
    Exception ex,
    string tenantPrincipalId,
    string sourceMessage)
  {
    var azError = FromException(ex);
    var logItem = new AzureErrorLogItem
    {
      Id = Guid.NewGuid(),
      AzureError = azError,
      SourceMessage = sourceMessage,
      TenantPrincipalId = tenantPrincipalId,
      CreatedDateUtc = DateTime.UtcNow,
    };
    TrackAzureError(logItem);
    return logItem;
  }

  public AzureErrorLogItem HandlePartnerCenterException(
    Exception ex,
    string sourceMessage,
    string? partnerPrincipalId = null,
    int? oauth2AccessTokenId = null)
  {
    var azError = FromPartnerCenterException(ex);
    var logItem = new AzureErrorLogItem
    {
      Id = Guid.NewGuid(),
      AzureError = azError,
      SourceMessage = sourceMessage,
      TenantPrincipalId = partnerPrincipalId,
      Oauth2AccessTokenId = oauth2AccessTokenId,
      CreatedDateUtc = DateTime.UtcNow,
    };
    TrackAzureError(logItem);
    return logItem;
  }

  public string GetFormattedErrorMessage(Exception ex)
  {
    return FromException(ex).FormattedErrorMessage;
  }

  public bool IsAzureError(Exception ex)
    => ex is MissingAccessTokenException or AuthenticationFailedException
          or EntityNotFoundException or ODataError or ApiException or OauthException;
  public static AzureError FromPartnerCenterException(Exception ex)
  {
    return ex switch
    {
      ApiException r => new AzureError(r.Message,
        PartnerCenterApiResponseDetails: new(r.HttpMethod.ToString(),
          r.Uri?.ToString(),
          (int)r.StatusCode,
          r.Content ?? string.Empty)),
      _ => FromException(ex),
    };
  }
  public static AzureError FromException(Exception ex)
  {
    return ex switch
    {
      ODataError o => FromODataError(o),
      ServiceException s => FromServiceException(s),
      AuthenticationFailedException a => FromAuthFailedException(a),
      MissingAccessTokenException { RequiredScopes: not null } m
        => new AzureError(m.Message, MissingAccessToken: new(m.TenantId, m.RequiredScopes)),
      ApiException r => new AzureError(r.Message,
        ApiResponseContent: new(r.HttpMethod.ToString(),
          r.Uri?.ToString(),
          (int)r.StatusCode,
          r.Content ?? string.Empty)),
      OauthException o => new AzureError(o.Message, OauthAccessTokenErrorResponse: o.ErrorResponse),
      _ => new AzureError(ex.Message)
    };
  }
  public static AzureError FromODataError(ODataError ex)
  {
    if (ex.Error is MainError e)
    {
      if (e.InnerError is { Date: null, AdditionalData: { } additionalData }
        && additionalData.TryGetValue("date", out var d))
      {
        // Date is not present in the InnerError, but it's present in the AdditionalData
        // (this is a text-case-matching bug in the Graph SDK - 'date' vs. 'Date')
        if (d is DateTimeOffset o) e.InnerError.Date = o;
        else if (d is DateTime dd) e.InnerError.Date = dd.ToDateTimeOffset(TimeZoneInfo.Local);
      }

      // ODataError dates don't contain timezone information, so when they're deserialized
      // they're assumed to be local, but in fact they're UTC. This causes the date to be
      // shifted by the local timezone offset, so we need to shift it back to UTC.
      if (e.InnerError?.Date is { Offset.TotalHours: { } totalHours and not 0 })
        e.InnerError.Date = e.InnerError.Date.Value.AddHours(totalHours);
      return new AzureError(e.Message ?? ex.Message,
        ODataError: new(e.Code ?? string.Empty,
          e.InnerError?.Date,
          e.InnerError?.RequestId ?? string.Empty,
          e.InnerError?.ClientRequestId ?? string.Empty,
          e.InnerError?.OdataType ?? string.Empty));
    }
    else
      return new AzureError(ex.Message);
  }

  public static AzureError FromServiceException(ServiceException ex)
  {
    if (ex.RawResponseBody.Contains("\"innererror\":", StringComparison.InvariantCultureIgnoreCase))
    {
      // parse as odata error
      try
      {
        var e = JsonSerializer.Deserialize<ODataError>(ex.RawResponseBody, _jsonSerializerOptions);
        if (e is not null) return FromODataError(e);
      }
      catch
      {
        // ignore deserialization errors
      }
    }
    return new AzureError(ex.Message, ApiResponseContent: new(null, null, ex.ResponseStatusCode, ex.RawResponseBody));
  }

  public static AzureError FromAuthFailedException(AuthenticationFailedException ex)
  {
    if (ex.InnerException is Microsoft.Identity.Client.MsalServiceException mse)
    {
      try
      {
        if (mse.ResponseBody is not null
          && JsonSerializer.Deserialize<Oauth2AccessTokenErrorResponse>(mse.ResponseBody) is { Error.Length: > 0 } r)
        {
          return new AzureError(r.ErrorDescription,
            MsalError: new(r.Error, r.ErrorDescription, r.ErrorCodes, r.Timestamp, r.TraceId, r.CorrelationId));
        }
      }
      catch
      {
        // ignore deserialization errors
      }
      return new AzureError(mse.Message);
    }
    return new AzureError(ex.Message);
  }

  private void TrackAzureError(AzureErrorLogItem ev)
  {
    _eventEmitter.EmitEvent(new AzureErrorEvent(ev, EmitNotificationEvent: NotificationEventEmittingEnabled));
  }
}
