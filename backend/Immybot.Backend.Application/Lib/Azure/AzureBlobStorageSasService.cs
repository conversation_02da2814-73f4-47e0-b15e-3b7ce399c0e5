using Immybot.Backend.Application.Interface.Azure;
using System;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Immybot.Backend.Azure.Domain.BlobStorage;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.Azure;

internal class AzureBlobStorageSasService(
  IOptionsMonitor<DatabaseOptions> _dbOpts,
  LocalBlobServiceClient _localBlobServiceClient,
  GlobalBlobServiceClient _globalBlobServiceClient)
  : IAzureBlobStorageSasService
{
  /// <param name="blobName"></param>
  /// <param name="policyName"></param>
  /// <returns>Returns a short-lived url to retrieve a file from the licenses azure blob</returns>
  public string GetLicenseDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetLocalBlobDownloadUrl(dbOpts.LicenseContainerName, blobName, policyName);
  }

  /// <param name="blobName"></param>
  /// <param name="policyName"></param>
  /// <returns>Returns a short-lived url to retrieve a file from the local software azure blob</returns>
  public string GetLocalSoftwareDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetLocalBlobDownloadUrl(dbOpts.LocalSoftwareContainerName, blobName, policyName);
  }

  /// <param name="blobName"></param>
  /// <param name="policyName"></param>
  /// <returns>Returns a short-lived url to retrieve a file from the local software azure blob</returns>
  public string GetGlobalSoftwareDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetGlobalBlobDownloadUrl(dbOpts.GlobalSoftwareContainerName, blobName, policyName);
  }

  /// <param name="blobName"></param>
  /// <param name="policyName"></param>
  /// <returns>Returns a short-lived url to retrieve a file from the support files/attachments azure blob</returns>
  public string GetSupportMediaDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetLocalBlobDownloadUrl(dbOpts.SupportFileContainerName, blobName, policyName);
  }

  public string GetLocalMediaDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetLocalBlobDownloadUrl(dbOpts.LocalMediaContainerName, blobName, policyName);
  }

  public string GetGlobalMediaDownloadUrl(string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    return GetGlobalBlobDownloadUrl(dbOpts.GlobalMediaContainerName, blobName, policyName);
  }

  /// <summary>
  /// https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-service-sas-create-dotnet#create-a-service-sas-for-a-blob
  /// </summary>
  /// <param name="blobServiceClient"></param>
  /// <param name="containerName"></param>
  /// <param name="blobName"></param>
  /// <param name="policyName"></param>
  /// <returns></returns>
  private static string GetBlobSasUri(
    BlobServiceClient blobServiceClient,
    string containerName,
    string blobName,
    string? policyName = null)
  {
    try
    {
      // Get a reference to the container
      var containerClient = blobServiceClient.GetBlobContainerClient(containerName);

      // Get a reference to the blob
      var blobClient = containerClient.GetBlobClient(blobName);

      // Generate Sas Uri using the blob client, container name, blob name, and policy name if provided.
      return blobClient.GenerateSasUri(containerName, blobName, policyName).ToString();
    }
    catch (Exception ex)
    {
      throw new InvalidOperationException(
        "Failed to generate SAS token. Ensure an environment variable with your " +
        "connection string has been defined in the system environment variables. : " +
        ex);
    }
  }

  /// <summary>
  /// Generates a SAS uri for a blob and replaces the scheme, host, and port with the local blob storage endpoint.
  /// Necessary for local development - proxying to Azurite.
  /// </summary>
  private string GetLocalBlobDownloadUrl(string containerName, string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    var endpointUri = new Uri(dbOpts.LocalBlobStorageEndpoint);
    var uri = GetBlobSasUri(
      _localBlobServiceClient,
      containerName,
      blobName,
      policyName);

    return new UriBuilder(uri) { Scheme = endpointUri.Scheme, Host = endpointUri.Host, Port = endpointUri.Port }
      .ToString();
  }

  /// <summary>
  /// Generates a SAS uri for a blob and replaces the scheme, host, and port with the global blob storage endpoint.
  /// Necessary for local development - proxying to Azurite.
  /// </summary>
  private string GetGlobalBlobDownloadUrl(string containerName, string blobName, string? policyName = null)
  {
    var dbOpts = _dbOpts.CurrentValue;
    var endpointUri = new Uri(dbOpts.GlobalBlobStorageEndpoint);
    var uri = GetBlobSasUri(
      _globalBlobServiceClient,
      containerName,
      blobName,
      policyName);

    return new UriBuilder(uri) { Scheme = endpointUri.Scheme, Host = endpointUri.Host, Port = endpointUri.Port }
      .ToString();
  }
}
