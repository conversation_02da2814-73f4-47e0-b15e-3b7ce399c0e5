{"delegatedScopesList": [{"adminConsentDescription": "Allows the application to read and update the organization's federated token validation policy on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's federated token validation policy", "id": "be1be369-4540-4ac9-8928-79de99f70d8f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update the organization's federated token validation policy on your behalf.", "consentDisplayName": "Read and write your organization's federated token validation policy", "value": "Policy.ReadWrite.FedTokenValidation"}, {"adminConsentDescription": "Allows an app to read user metrics insights, such as daily and monthly active users, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user metrics insights", "id": "7d249730-51a3-4180-8ec1-214f144f1bff", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows an app to read user metrics insights, such as daily and monthly active users, on your behalf.", "consentDisplayName": "Read user metrics insights", "value": "Insights-UserMetric.Read.All"}, {"adminConsentDescription": "Allow the app to create data classification sessions, on behalf of the signed-in user.", "adminConsentDisplayName": "Create data classification sessions", "id": "2dd78a34-0a00-4157-82e2-2b614b875b3e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allow the app to create data classification sessions, on your behalf.", "consentDisplayName": "Create data classification sessions", "value": "DataClassificationSession.Create"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in user accounts, and manage its permission grants for accessing those specific users' data, on behalf of the signed-in user.", "adminConsentDisplayName": "Allow the Teams app to manage itself and its permission grants in user accounts", "id": "7a349935-c54d-44ab-ab66-1b460d315be7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in your account, and manage its permission grants for accessing your data, on your behalf.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants on your user account", "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForUser"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in user accounts, on behalf of the signed-in user. Gives the ability to manage permission grants for accessing those specific users' data.", "adminConsentDisplayName": "Manage installation and permission grants of Teams apps in users' personal scope", "id": "2da62c49-dfbd-40df-ba16-fef3529d391c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps for your account, on your behalf. Gives the ability to manage permission grants for accessing your data.", "consentDisplayName": "Manage installation and permission grants of Teams apps on your user account", "value": "TeamsAppInstallation.ReadWriteAndConsentForUser"}, {"adminConsentDescription": "Allows the app to read the resource specific permissions granted on a user account, on behalf of the signed-in user.", "adminConsentDisplayName": "Read resource specific permissions granted on a user account", "id": "f1d91a8f-88e7-4774-8401-b668d5bca0c5", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the resource specific permission granted on your account, on your behalf.", "consentDisplayName": "Read resource specific permissions granted on your user account", "value": "ResourceSpecificPermissionGrant.ReadForUser"}, {"adminConsentDescription": "Allows the app to read and write your organization’s identity protection policy on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization’s identity protection policy ", "id": "7256e131-3efb-4323-9854-cf41c6021770", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization’s identity protection policy on your behalf.", "consentDisplayName": "Read and write your organization’s identity protection policy.", "value": "Policy.ReadWrite.IdentityProtection"}, {"adminConsentDescription": "Allows the app to read your organization’s identity protection policy on behalf of the signed-in user. ", "adminConsentDisplayName": "Read your organization’s identity protection policy", "id": "d146432f-b803-4ed4-8d42-ba74193a6ede", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization’s identity protection policy on your behalf.", "consentDisplayName": "Read your organization’s identity protection policy", "value": "Policy.Read.IdentityProtection"}, {"adminConsentDescription": "Allows the app to read and write organization-wide Microsoft 365 apps installation settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization-wide Microsoft 365 apps installation settings", "id": "1ff35e91-19eb-42d8-aa2d-cc9891127ae5", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write organization-wide Microsoft 365 apps installation settings on your behalf.", "consentDisplayName": "Read and write organization-wide Microsoft 365 apps installation settings", "value": "OrgSettings-Microsoft365Install.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read organization-wide Microsoft 365 apps installation settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read organization-wide Microsoft 365 apps installation settings", "id": "8cbdb9f6-9c2e-451a-814d-ec606e5d0212", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read organization-wide Microsoft 365 apps installation settings on your behalf.", "consentDisplayName": "Read organization-wide Microsoft 365 apps installation settings", "value": "OrgSettings-Microsoft365Install.Read.All"}, {"adminConsentDescription": "Allows the app to read and write insights related to guest activity, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write insights related to guest activity", "id": "7e23346e-6931-45b2-b04e-6332b3905b8a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write insights related to guest activity, on your behalf.", "consentDisplayName": "Read and write insights related to guest activity", "value": "Insights-GuestActivity.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read insights related to guest activity, on behalf of the signed-in user.", "adminConsentDisplayName": "Read insights related to guest activity", "id": "bdd9425e-296d-4e4b-9c15-0a288b4b12e1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read insights related to guest activity, on your behalf.", "consentDisplayName": "Read insights related to guest activity", "value": "Insights-GuestActivity.Read.All"}, {"adminConsentDescription": "Allows the app to read audit logs for events that contain information about custom security attributes, on behalf of the signed-in user.", "adminConsentDisplayName": "Read custom security attribute audit logs", "id": "1fcdeaab-b519-44dd-bffc-ed1fd15a24e0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read custom security attributes audit logs on your behalf.", "consentDisplayName": "Read custom security attribute audit logs", "value": "CustomSecAttributeAuditLogs.Read.All"}, {"adminConsentDescription": "Allows the app to read the trusted certificate authority configuration which can be used to restrict application certificates based on their issuing authority, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the trusted certificate authority configuration for applications", "id": "af281d3a-030d-4122-886e-146fb30a0413", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the trusted certificate authority configuration which can be used to restrict application certificates based on their issuing authority, on your behalf.", "consentDisplayName": "Read the trusted certificate authority configuration for applications", "value": "AppCertTrustConfiguration.Read.All"}, {"adminConsentDescription": "Allows the app to create, read, update and delete the trusted certificate authority configuration which can be used to restrict application certificates based on their issuing authority, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write the trusted certificate authority configuration for applications", "id": "4bae2ed4-473e-4841-a493-9829cfd51d48", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to to create, read, update and delete the trusted certificate authority configuration which can be used to restrict application certificates based on their issuing authority, on your behalf.", "consentDisplayName": "Read and write the trusted certificate authority configuration for applications", "value": "AppCertTrustConfiguration.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read delegated permission grants, on behalf of the signed in user.", "adminConsentDisplayName": "Read delegated permission grants", "id": "a197cdc4-a8e8-4d49-9d35-4ca7c83887b4", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read delegated permission grants, on your behalf.", "consentDisplayName": "Read delegated permission grants", "value": "DelegatedPermissionGrant.Read.All"}, {"adminConsentDescription": "Allows the app to read and write multi-tenant organization details and tenants on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write multi-tenant organization details and tenants", "id": "77af1528-84f3-4023-8d90-d219cd433108", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write multi-tenant organization details and tenants on your behalf.", "consentDisplayName": "Read and write multi-tenant organization details and tenants", "value": "MultiTenantOrganization.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read multi-tenant organization details and tenants on behalf of the signed-in user.", "adminConsentDisplayName": "Read multi-tenant organization details and tenants", "id": "526aa72a-5878-49fe-bf4e-357973af9b06", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read multi-tenant organization details and tenants on your behalf.", "consentDisplayName": "Read multi-tenant organization details and tenants", "value": "MultiTenantOrganization.Read.All"}, {"adminConsentDescription": "Allows the app to read multi-tenant organization basic details and active tenants on behalf of the signed-in user.", "adminConsentDisplayName": "Read multi-tenant organization basic details and active tenants", "id": "225db56b-15b2-4daa-acb3-0eec2bbe4849", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read multi-tenant organization basic details and active tenants on your behalf.", "consentDisplayName": "Read multi-tenant organization basic details and active tenants", "value": "MultiTenantOrganization.ReadBasic.All"}, {"adminConsentDescription": "Allows the app to read and write user's modules and resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write the user's class modules and resources", "id": "4793c53b-df34-44fd-8d26-d15c517732f5", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write modules and resources on your behalf.", "consentDisplayName": "Read and write your class modules and resources", "value": "EduCurricula.ReadWrite"}, {"adminConsentDescription": "Allows the app to read the user's modules and resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read the user's class modules and resources", "id": "484859e8-b9e2-4e92-b910-84db35dadd29", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read modules and resources on your behalf.", "consentDisplayName": "Read your class modules and resources", "value": "EduCurricula.Read"}, {"adminConsentDescription": "Allows the app to upload bulk user data to the identity synchronization service, on behalf of the signed-in user.", "adminConsentDisplayName": "Upload user data to the identity synchronization service", "id": "1a2e7420-4e92-4d2b-94cb-fb2952e9ddf7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to upload bulk user data to the identity synchronization service, on your behalf.", "consentDisplayName": "Bulk upload user data to identity synchronization service", "value": "SynchronizationData-User.Upload"}, {"adminConsentDescription": "Allows the app to read organization-wide apps and services settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read organization-wide apps and services settings", "id": "1e9b7a7e-4d64-44ff-acf5-2e9651c1519f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read organization-wide apps and services settings on your behalf.", "consentDisplayName": "Read organization-wide apps and services settings", "value": "OrgSettings-AppsAndServices.Read.All"}, {"adminConsentDescription": "Allows the app to read organization-wide Microsoft Forms settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read organization-wide Microsoft Forms settings", "id": "210051a0-1ffc-435c-ae76-02d226d05752", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read organization-wide Microsoft Forms settings on your behalf.", "consentDisplayName": "Read organization-wide Microsoft Forms settings", "value": "OrgSettings-Forms.Read.All"}, {"adminConsentDescription": "Allows the app to read organization-wide Dynamics customer voice settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read organization-wide Dynamics customer voice settings", "id": "9862d930-5aec-4a98-8d4f-7277a8db9bcb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read organization-wide Dynamics customer voice settings on your behalf.", "consentDisplayName": "Read organization-wide dynamics customer voice settings", "value": "OrgSettings-DynamicsVoice.Read.All"}, {"adminConsentDescription": "Allows the app to read and write organization-wide apps and services settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization-wide apps and services settings", "id": "c167b0e7-47c0-48e8-9eee-9892f58018fa", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write organization-wide apps and services settings on your behalf.", "consentDisplayName": "Read and write organization-wide apps and services settings", "value": "OrgSettings-AppsAndServices.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write organization-wide Microsoft To Do settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization-wide Microsoft To Do settings", "id": "087502c2-5263-433e-abe3-8f77231a0627", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write organization-wide Microsoft To Do settings on your behalf.", "consentDisplayName": "Read and write organization-wide Microsoft To Do settings", "value": "OrgSettings-Todo.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write organization-wide Dynamics customer voice settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization-wide Dynamics customer voice settings", "id": "4cea26fb-6967-4234-82c4-c044414743f8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write organization-wide Dynamics customer voice settings on your behalf.", "consentDisplayName": "Read and write organization-wide Dynamics customer voice settings", "value": "OrgSettings-DynamicsVoice.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write organization-wide Microsoft Forms settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization-wide Microsoft Forms settings", "id": "346c19ff-3fb2-4e81-87a0-bac9e33990c1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write organization-wide Microsoft Forms settings on your behalf.", "consentDisplayName": "Read and write organization-wide Microsoft Forms settings", "value": "OrgSettings-Forms.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read organization-wide Microsoft To Do settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read organization-wide Microsoft To Do settings", "id": "7ff96f41-f022-45ba-acd8-ef3f03063d6b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read organization-wide Microsoft To Do settings on your behalf.", "consentDisplayName": "Read organization-wide Microsoft To Do settings", "value": "OrgSettings-Todo.Read.All"}, {"adminConsentDescription": "Allows the app to read policies in Privileged Identity Management for Groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all policies in PIM for Groups", "id": "7e26fdff-9cb1-4e56-bede-211fe0e420e8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read policies in Privileged Identity Management for Groups, on your behalf.", "consentDisplayName": "Read all policies in PIM for Groups", "value": "RoleManagementPolicy.Read.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read, update, and delete policies in Privileged Identity Management for Groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read, update, and delete all policies in PIM for Groups", "id": "0da165c7-3f15-4236-b733-c0b0f6abe41d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update, and delete policies in Privileged Identity Management for Groups, on your behalf.", "consentDisplayName": "Read, update, and delete all policies in PIM for Groups", "value": "RoleManagementPolicy.ReadWrite.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read details of discovered cloud apps in the organization, on behalf of the signed in user.", "adminConsentDisplayName": "Read discovered cloud applications data", "id": "ad46d60e-1027-4b75-af88-7c14ccf43a19", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read details of discovered cloud apps in the organization, on your behalf.", "consentDisplayName": "Read discovered cloud application data", "value": "CloudApp-Discovery.Read.All"}, {"adminConsentDescription": "Allows the app to read data for the learner's assignments in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user's assignments", "id": "ac08cdae-e845-41db-adf9-5899a0ec9ef6", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read data for your assignments in the organization's directory, on your behalf.", "consentDisplayName": "Read your assignments", "value": "LearningAssignedCourse.Read"}, {"adminConsentDescription": "Allows the app to read data for the learner's self-initiated courses in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user's self-initiated courses", "id": "f6403ef7-4a96-47be-a190-69ba274c3f11", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read data for your self-initiated courses in the organization's directory, on your behalf.", "consentDisplayName": "Read your self-initiated courses", "value": "LearningSelfInitiatedCourse.Read"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for your organization's Exchange Online service, on behalf of the signed-in user. This includes reading Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "adminConsentDisplayName": "Read Exchange Online RBAC configuration", "id": "3bc15058-7858-4141-b24f-ae43b4e80b52", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the role-based access control (RBAC) settings for your organization's Exchange Online service, on your behalf. This includes reading Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "consentDisplayName": "Read Exchange Online RBAC configuration", "value": "RoleManagement.Read.Exchange"}, {"adminConsentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your organization's Exchange Online service, on behalf of the signed-in user. This includes reading, creating, updating, and deleting Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "adminConsentDisplayName": "Read and write Exchange Online RBAC configuration", "id": "c1499fe0-52b1-4b22-bed2-7a244e0e879f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your organization's Exchange Online service, on your behalf. This includes reading, creating, updating, and deleting Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "consentDisplayName": "Read and write Exchange Online RBAC configuration", "value": "RoleManagement.ReadWrite.Exchange"}, {"adminConsentDescription": "Allows the app to read, create, and delete time-based eligibility schedules for access to Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read, create, and delete eligibility schedules for access to Azure AD groups", "id": "ba974594-d163-484e-ba39-c330d5897667", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, create, and delete time-based eligibility schedules for access to Azure AD groups, on your behalf.", "consentDisplayName": "Read, create, and delete eligibility schedules for access to Azure AD groups", "value": "PrivilegedEligibilitySchedule.ReadWrite.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read, create, and delete time-based assignment schedules for access to Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read, create, and delete assignment schedules for access to Azure AD groups", "id": "06dbc45d-6708-4ef0-a797-f797ee68bf4b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, create, and delete time-based assignment schedules for access to Azure AD groups, on your behalf.", "consentDisplayName": "Read, create, and delete assignment schedules for access to Azure AD groups", "value": "PrivilegedAssignmentSchedule.ReadWrite.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read time-based assignment schedules for access to Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read assignment schedules for access to Azure AD groups", "id": "02a32cc4-7ab5-4b58-879a-0586e0f7c495", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time-based assignment schedules for access to Azure AD groups, on your behalf.", "consentDisplayName": "Read assignment schedules for access to Azure AD groups", "value": "PrivilegedAssignmentSchedule.Read.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read time-based eligibility schedules for access to Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read eligibility schedules for access to Azure AD groups", "id": "8f44f93d-ecef-46ae-a9bf-338508d44d6b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time-based eligibility schedules for access to Azure AD groups, on your behalf.", "consentDisplayName": "Read eligibility schedules for access to Azure AD groups", "value": "PrivilegedEligibilitySchedule.Read.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read threat intelligence information, such as indicators, observations, and articles, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all threat intelligence information", "id": "f266d9c0-ccb9-4fb8-a228-01ac0d8d6627", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read threat intelligence information, such as indicators, observations, and articles, on your behalf.", "consentDisplayName": "Read threat intelligence Information", "value": "ThreatIntelligence.Read.All"}, {"adminConsentDescription": "Allows the app to delete and recover deleted chats, on behalf of the signed-in user.", "adminConsentDisplayName": "Delete and recover deleted chats", "id": "bb64e6fc-6b6d-4752-aea0-dd922dbba588", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to delete and recover deleted chats, on your behalf.", "consentDisplayName": "Delete and recover deleted chats", "value": "Chat.ManageDeletion.All"}, {"adminConsentDescription": "Allows the app to read virtual events created by the you", "adminConsentDisplayName": "Read your virtual events", "id": "6b616635-ae58-433a-a918-8c45e4f304dc", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read virtual events created by the you", "consentDisplayName": "Read your virtual events", "value": "VirtualEvent.Read"}, {"adminConsentDescription": "Allows the application to read tenant-wide people settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read tenant-wide people settings", "id": "ec762c5f-388b-4b16-8693-ac1efbc611bc", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read tenant-wide people settings on your behalf.", "consentDisplayName": "Read tenant-wide people settings", "value": "PeopleSettings.Read.All"}, {"adminConsentDescription": "Allows the app to read and write other apps' remote desktop security configuration, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write the remote desktop security configuration for apps", "id": "ffa91d43-2ad8-45cc-b592-09caddeb24bb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write other apps' remote desktop security configuration, on your behalf.", "consentDisplayName": "Read and write the remote desktop security configuration for apps", "value": "Application-RemoteDesktopConfig.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the configurations applicable to the signed-in user for protecting organizational data, on behalf of the signed-in user.", "adminConsentDisplayName": "Read configurations for protecting organizational data applicable to the user", "id": "12f4bffb-b598-413c-984b-db99728f8b54", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the configurations applicable to you for protecting organizational data, on your behalf.", "consentDisplayName": "Read configurations for protecting organizational data applicable to you", "value": "InformationProtectionConfig.Read"}, {"adminConsentDescription": "Allows the application to read and write tenant-wide people settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write tenant-wide people settings", "id": "e67e6727-c080-415e-b521-e3f35d5248e9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and write tenant-wide people settings on your behalf.", "consentDisplayName": "Read and write tenant-wide people settings", "value": "PeopleSettings.ReadWrite.All"}, {"adminConsentDescription": "Read email metadata, security detection details, and execute remediation actions like deleting an email, on behalf of the signed in user.", "adminConsentDisplayName": "Read metadata, detection details, and execute remediation actions on emails in your organization", "id": "48eb8c83-6e58-46e7-a6d3-8805822f5940", "isEnabled": true, "isAdmin": true, "consentDescription": "Read email metadata, security detection details, and execute remediation actions like deleting an email, on your behalf.", "consentDisplayName": "Read metadata, detection details, and execute remediation actions on emails in your organization", "value": "SecurityAnalyzedMessage.ReadWrite.All"}, {"adminConsentDescription": "Read email metadata and security detection details on behalf of the signed in user.", "adminConsentDisplayName": "Read metadata and detection details for emails in your organization", "id": "53e6783e-b127-4a35-ab3a-6a52d80a9077", "isEnabled": true, "isAdmin": true, "consentDescription": "Read email metadata and security detection details on your behalf.", "consentDisplayName": "Read metadata and detection details for emails in your organization", "value": "SecurityAnalyzedMessage.Read.All"}, {"adminConsentDescription": "Allows the app to enable and disable users' accounts, on behalf of the signed-in user.", "adminConsentDisplayName": "Enable and disable user accounts", "id": "f92e74e7-2563-467f-9dd0-902688cb5863", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to enable and disable users' accounts, on your behalf.", "consentDisplayName": "Enable and disable user accounts", "value": "User.EnableDisableAccount.All"}, {"adminConsentDescription": "Allows the app to read and manage the role-based access control (RBAC) alerts for your company's directory, on behalf of the signed-in user. This includes managing alert settings, initiating alert scans, dimissing alerts, remediating alert incidents, and reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "adminConsentDisplayName": "Read all alert data, configure alerts, and take actions on all alerts for your company's directory", "id": "435644c6-a5b1-40bf-8f52-fe8e5b53e19c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the role-based access control (RBAC) alerts for your company's directory, on your behalf. This includes managing alert settings, initiating alert scans, dimissing alerts, remediating alert incidents, and reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "consentDisplayName": "Read all alert data, configure alerts, and take actions on all alerts for your company's directory", "value": "RoleManagementAlert.ReadWrite.Directory"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) alerts for your company's directory, on behalf of the signed-in user. This includes reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "adminConsentDisplayName": "Read all alert data for your company's directory", "id": "cce71173-f76d-446e-97ff-efb2d82e11b1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the role-based access control (RBAC) alerts for your company's directory, on your behalf. This includes reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "consentDisplayName": "Read all alert data for your company's directory", "value": "RoleManagementAlert.Read.Directory"}, {"adminConsentDescription": "Allows the app to read, create, and update attack simulation and training data for an organization for the signed-in user.", "adminConsentDisplayName": "Read, create, and update attack simulation data of an organization", "id": "27608d7c-2c66-4cad-a657-951d575f5a60", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, create, and update attack simulation and training data for an organization on your behalf.", "consentDisplayName": "Read, create, and update attack simulation data of an organization", "value": "AttackSimulation.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to upload data files to a data connector on behalf of the signed-in user.", "adminConsentDisplayName": "Upload files to a data connector", "id": "fc47391d-ab2c-410f-9059-5600f7af660d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to upload data files to a data connector on your behalf.", "consentDisplayName": "Upload files to a data connector", "value": "IndustryData-DataConnector.Upload"}, {"adminConsentDescription": "Allows the app to read and write source system definitions on behalf of the signed-in user.", "adminConsentDisplayName": "Manage source system definitions", "id": "9599f005-05d6-4ea7-b1b1-4929768af5d0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write source system definitions on your behalf.", "consentDisplayName": "Manage source system definitions", "value": "IndustryData-SourceSystem.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all on-premises directory synchronization information for the organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all on-premises directory synchronization information", "id": "f6609722-4100-44eb-b747-e6ca0536989d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all on-premises directory synchronization information for the organization, on your behalf.", "consentDisplayName": "Read all on-premises directory synchronization information", "value": "OnPremDirectorySynchronization.Read.All"}, {"adminConsentDescription": "Allows the app to read and write time period definitions on behalf of the signed-in user.", "adminConsentDisplayName": "Manage time period definitions", "id": "b6d56528-3032-4f9d-830f-5a24a25e6661", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write time period definitions on your behalf.", "consentDisplayName": "Manage time period definitions", "value": "IndustryData-TimePeriod.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read time period definitions on behalf of the signed-in user.", "adminConsentDisplayName": "Read time period definitions", "id": "c9d51f28-8ccd-42b2-a836-fd8fe9ebf2ae", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time period definitions on your behalf.", "consentDisplayName": "Read time period definitions", "value": "IndustryData-TimePeriod.Read.All"}, {"adminConsentDescription": "Allows the app to read source system definitions on behalf of the signed-in user.", "adminConsentDisplayName": "View source system definitions", "id": "49b7016c-89ae-41e7-bd6f-b7170c5490bf", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read source system definitions on your behalf.", "consentDisplayName": "View source system definitions", "value": "IndustryData-SourceSystem.Read.All"}, {"adminConsentDescription": "Allows the app to read reference definitions on behalf of the signed-in user.", "adminConsentDisplayName": "View reference definitions", "id": "a3f96ffe-cb84-40a8-ac85-582d7ef97c2a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read reference definitions on your behalf.", "consentDisplayName": "View reference definitions", "value": "IndustryData-ReferenceDefinition.Read.All"}, {"adminConsentDescription": "Allows the app to read basic Industry Data service and resource information on behalf of the signed-in user.", "adminConsentDisplayName": "Read basic Industry Data service and resource definitions", "id": "60382b96-1f5e-46ea-a544-0407e489e588", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read Industry Data basic service and resource information on your behalf.", "consentDisplayName": "Read basic Industry Data service and resource definitions", "value": "IndustryData.ReadBasic.All"}, {"adminConsentDescription": "Allows the app to read and write all on-premises directory synchronization information for the organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all on-premises directory synchronization information", "id": "c2d95988-7604-4ba1-aaed-38a5f82a51c7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all on-premises directory synchronization information for the organization, on your behalf.", "consentDisplayName": "Read and write all on-premises directory synchronization information", "value": "OnPremDirectorySynchronization.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read current and previous IndustryData runs on behalf of the signed-in user.", "adminConsentDisplayName": "View current and previous runs", "id": "92685235-50c4-4702-b2c8-36043db6fa79", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read current and previous IndustryData runs on your behalf.", "consentDisplayName": "View current and previous runs", "value": "IndustryData-Run.Read.All"}, {"adminConsentDescription": "Allows the app to read and write data connectors on behalf of the signed-in user.", "adminConsentDisplayName": "Manage data connector definitions", "id": "5ce933ac-3997-4280-aed0-cc072e5c062a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write data connectors on your behalf.", "consentDisplayName": "Manage data connector definitions", "value": "IndustryData-DataConnector.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read data connectors on behalf of the signed-in user.", "adminConsentDisplayName": "View data connector definitions", "id": "d19c0de5-7ecb-4aba-b090-da35ebcd5425", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read data connectors on your behalf.", "consentDisplayName": "View data connector definitions", "value": "IndustryData-DataConnector.Read.All"}, {"adminConsentDescription": "Allows the app to read inbound data flows on behalf of the signed-in user.", "adminConsentDisplayName": "View inbound flow definitions", "id": "cb0774da-a605-42af-959c-32f438fb38f4", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read inbound data flows on your behalf.", "consentDisplayName": "View inbound flow definitions", "value": "IndustryData-InboundFlow.Read.All"}, {"adminConsentDescription": "Allows the app to read and write inbound data flows on behalf of the signed-in user.", "adminConsentDisplayName": "Manage inbound flow definitions", "id": "97044676-2cec-40ee-bd70-38df444c9e70", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write inbound data flows on your behalf.", "consentDisplayName": "Manage inbound flow definitions", "value": "IndustryData-InboundFlow.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your organization's branches for network access on behalf of the signed-in user.", "adminConsentDisplayName": "Read properties of branches for network access", "id": "4051c7fc-b429-4804-8d80-8f1f8c24a6f7", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your organization's branches for network access on your behalf.", "consentDisplayName": "Read properties of branches for network access", "value": "NetworkAccessBranch.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization's security and routing network access policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read security and routing policies for network access", "id": "ba22922b-752c-446f-89d7-a2d92398fceb", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your organization's security and routing network access policies on your behalf.", "consentDisplayName": "Read security and routing policies for network access", "value": "NetworkAccessPolicy.Read.All"}, {"adminConsentDescription": "Allows the app to read events in user calendars, except for properties such as body, attachments, and extensions.", "adminConsentDisplayName": "Read basic details of user calendars", "id": "662d75ba-a364-42ad-adee-f5f880ea4878", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read events in your calendars, except for properties such as body, attachments, and extensions.", "consentDisplayName": "Read basic details of your calendars", "value": "Calendars.ReadBasic"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats the signed-in user can access, and manage its permission grants for accessing those specific chats' data.", "adminConsentDisplayName": "Allow the Teams app to manage itself and its permission grants in chats", "id": "a0e0e18b-8fb2-458f-8130-da2d7cab9c75", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats the signed-in user can access, and manage its permission grants for accessing those specific chats' data.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants in chats", "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForChat"}, {"adminConsentDescription": "Allows the app to create new business scenarios and fully manage the configurations of scenarios it owns, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write business scenario configurations this app creates or owns", "id": "b3b7fcff-b4d4-4230-bf6f-90bd91285395", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create new business scenarios and fully manage the configurations of scenarios it owns, on your behalf.", "consentDisplayName": "Read and write business scenario configurations this app creates or owns", "value": "BusinessScenarioConfig.ReadWrite.OwnedBy"}, {"adminConsentDescription": "Allows the app to read mail the signed-in user can access, including their own and shared mail, except for body, bodyPreview, uniqueBody, attachments, extensions, and any extended properties.", "adminConsentDisplayName": "Read user and shared basic mail", "id": "b11fa0e7-fdb7-4dc9-b1f1-59facd463480", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read mail you can access, including shared mail except body, previewBody, uniqueBody, attachments, extensions, and any extended properties. ", "consentDisplayName": "Read basic mail you can access", "value": "Mail.ReadBasic.Shared"}, {"adminConsentDescription": "Allows the app to read and write your organization's security and routing network access policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write security and routing policies for network access", "id": "b1fbad0f-ef6e-42ed-8676-bca7fa3e7291", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's security and routing network access policies on your behalf.", "consentDisplayName": "Read and write security and routing policies for network access", "value": "NetworkAccessPolicy.ReadWrite.All"}, {"adminConsentDescription": "Allows an application to read and write virtual appointments for the signed-in user. Only an organizer or participant user can read and write their virtual appointments. ", "adminConsentDisplayName": "Read and write a user's virtual appointments  ", "id": "2ccc2926-a528-4b17-b8bb-860eed29d64c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write virtual appointments on your behalf.  ", "consentDisplayName": "Read and write your virtual appointments", "value": "VirtualAppointment.ReadWrite"}, {"adminConsentDescription": "Allows the app to read the configurations of business scenarios it owns, on behalf of the signed-in user.", "adminConsentDisplayName": "Read business scenario configurations this app creates or owns", "id": "c47e7b6e-d6f1-4be9-9ffd-1e00f3e32892", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the configurations of business scenarios it owns, on your behalf.", "consentDisplayName": "Read business scenario configurations this app creates or owns", "value": "BusinessScenarioConfig.Read.OwnedBy"}, {"adminConsentDescription": "Allows the app to read and write the billing configuration on all applications on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write application billing configuration", "id": "2bf6d319-dfca-4c22-9879-f88dcfaee6be", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the billing configuration on all applications on your behalf", "consentDisplayName": "Read and write application billing configuration", "value": "BillingConfiguration.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write your organization's branches for network access on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write properties of branches for network access", "id": "b8a36cc2-b810-461a-baa4-a7281e50bd5c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's branches for network access on your behalf.", "consentDisplayName": "Read and write properties of branches for network access", "value": "NetworkAccessBranch.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the configurations of your organization's business scenarios, on behalf of the signed-in user.", "adminConsentDisplayName": "Read business scenario configurations", "id": "d16480b2-e469-4118-846b-d3d177327bee", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the configurations of your organization's business scenarios, on your behalf.", "consentDisplayName": "Read business scenario configurations", "value": "BusinessScenarioConfig.Read.All"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams the signed-in user can access. Gives the ability to manage permission grants for accessing those specific teams' data.", "adminConsentDisplayName": "Manage installed Teams apps in teams", "id": "946349d5-2a9d-4535-abc0-7<PERSON><PERSON><PERSON>1d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams the signed-in user can access. Gives the ability to manage permission grants for accessing those specific teams' data.", "consentDisplayName": "Manage installation and permission grants of Teams apps in teams", "value": "TeamsAppInstallation.ReadWriteAndConsentForTeam"}, {"adminConsentDescription": "Allows the app to read and write your organization's security defaults policy on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's security defaults policy", "id": "0b2a744c-2abf-4f1e-ad7e-17a087e2be99", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's security defaults policy on your behalf.", "consentDisplayName": "Read and write your organization's security defaults policy", "value": "Policy.ReadWrite.SecurityDefaults"}, {"adminConsentDescription": "Allows the app to read all data associated with the business scenarios it owns. Data access will be attributed to the signed-in user.", "adminConsentDisplayName": "Read all data for business scenarios this app creates or owns", "id": "25b265c4-5d34-4e44-952d-b567f6d3b96d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all data associated with the business scenarios it owns. Data access will be attributed to you.", "consentDisplayName": "Read data for business scenarios this app creates or owns", "value": "BusinessScenarioData.Read.OwnedBy"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats the signed-in user can access. Gives the ability to manage permission grants for accessing those specific chats' data.", "adminConsentDisplayName": "Manage installed Teams apps in chats", "id": "e1408a66-8f82-451b-a2f3-3c3e38f7413f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats the signed-in user can access. Gives the ability to manage permission grants for accessing those specific chats' data.", "consentDisplayName": "Manage installation and permission grants of Teams apps in chats", "value": "TeamsAppInstallation.ReadWriteAndConsentForChat"}, {"adminConsentDescription": "Allows the app to read and write the configurations of your organization's business scenarios, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write business scenario configurations", "id": "755e785b-b658-446f-bb22-5a46abd029ea", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the configurations of your organization's business scenarios, on your behalf.", "consentDisplayName": "Read and write business scenario configurations", "value": "BusinessScenarioConfig.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read device local credential properties excluding passwords, on behalf of the signed-in user.", "adminConsentDisplayName": "Read device local credential properties", "id": "9917900e-410b-4d15-846e-42a357488545", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read device local credential properties excluding passwords, on your behalf.", "consentDisplayName": "Read device local credential properties", "value": "DeviceLocalCredential.ReadBasic.All"}, {"adminConsentDescription": "Allows the application to read and update the organization's external identities policy on behalf of the signed-in user.  For example, external identities policy controls if users invited to access resources in your organization via B2B collaboration or B2B direct connect are allowed to self-service leave.", "adminConsentDisplayName": "Read and write your organization's external identities policy", "id": "b5219784-1215-45b5-b3f1-88fe1081f9c0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update the organization's external identities policy on your behalf.  For example, external identities policy controls if users invited to access resources in your organization via B2B collaboration or B2B direct connect are allowed to self-service leave.", "consentDisplayName": "Read and write your organization's external identities policy", "value": "Policy.ReadWrite.ExternalIdentities"}, {"adminConsentDescription": "Allows the app to configure the Azure AD synchronization service, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all Azure AD synchronization data", "id": "7bb27fa3-ea8f-4d67-a916-87715b6188bd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to configure the Azure AD synchronization service, on your behalf.", "consentDisplayName": "Read and write all Azure AD synchronization data", "value": "Synchronization.ReadWrite.All"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in teams the signed-in user can access, and manage its permission grants for accessing those specific teams' data.", "adminConsentDisplayName": "Allow the Teams app to manage itself and its permission grants in teams", "id": "4a6bbf29-a0e1-4a4d-a7d1-cef17f772975", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in teams the signed-in user can access, and manage its permission grants for accessing those specific teams' data.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants in teams", "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForTeam"}, {"adminConsentDescription": "Allows the app to read device local credential properties including passwords, on behalf of the signed-in user.", "adminConsentDisplayName": "Read device local credential passwords", "id": "280b3b69-0437-44b1-bc20-3b2fca1ee3e9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read device local credential properties including passwords, on your behalf.", "consentDisplayName": "Read device local credential passwords", "value": "DeviceLocalCredential.Read.All"}, {"adminConsentDescription": "Allows an app to read all question and answer sets that the signed-in user can access.", "adminConsentDisplayName": "Read all Questions and Answers that the user can access.", "id": "f73fa04f-b9a5-4df9-8843-993ce928925e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read all question and answer sets that you can access.", "consentDisplayName": "Read all Questions and Answers that you can access.", "value": "QnA.Read.All"}, {"adminConsentDescription": "Allows the app to read Azure AD synchronization information, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all Azure AD synchronization data", "id": "7aa02aeb-824f-4fbe-a3f7-611f751f5b55", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read Azure AD synchronization information, on your behalf.", "consentDisplayName": "Read all Azure AD synchronization data", "value": "Synchronization.Read.All"}, {"adminConsentDescription": "Allows an application to read virtual appointments for the signed-in user. Only an organizer or participant user can read their virtual appointments.  ", "adminConsentDisplayName": "Read a user's virtual appointments", "id": "27470298-d3b8-4b9c-aad4-6334312a3eac", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read virtual appointments on your behalf.  ", "consentDisplayName": "Read your virtual appointments ", "value": "VirtualAppointment.Read"}, {"adminConsentDescription": "Allows the app to fully manage all data associated with the business scenarios it owns. Data access and changes will be attributed to the signed-in user.", "adminConsentDisplayName": "Read and write all data for business scenarios this app creates or owns", "id": "19932d57-2952-4c60-8634-3655c79fc527", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to fully manage all data associated with the business scenarios it owns. These changes will be attributed to you.", "consentDisplayName": "Read and write data for business scenarios this app creates or owns", "value": "BusinessScenarioData.ReadWrite.OwnedBy"}, {"adminConsentDescription": "Allows the app to manage self-service entitlement management resources on behalf of the signed-in user.  This includes operations such as requesting access and approving access of others.", "adminConsentDisplayName": "Read and write entitlement management resources related to self-service operations", "id": "e9fdcbbb-8807-410f-b9ec-8d5468c7c2ac", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to create and manage self-service entitlement management resources that you have access to.  This includes operations such as requesting access and approving access for others.", "consentDisplayName": "Read and write self-service entitlement management resources", "value": "EntitlementMgmt-SubjectAccess.ReadWrite"}, {"adminConsentDescription": "Allows the app to read the available Teams templates, on behalf of the signed-in user.", "adminConsentDisplayName": "Read available Teams templates", "id": "cd87405c-5792-4f15-92f7-debc0db6d1d6", "isEnabled": true, "isAdmin": false, "consentDescription": "Read available Teams templates, on your behalf.", "consentDisplayName": "Read available Teams templates", "value": "TeamTemplates.Read"}, {"adminConsentDescription": "Allows an app to read all acronyms that the signed-in user can access.", "adminConsentDisplayName": "Read all acronyms that the user can access", "id": "9084c10f-a2d6-4713-8732-348def50fe02", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read all acronyms you can access.", "consentDisplayName": "Read all acronyms that you have access to", "value": "Acronym.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the Teams app settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write Teams app settings", "id": "87c556f0-2bd9-4eed-bd74-5dd8af6eaf7e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the Teams app settings on your behalf.", "consentDisplayName": "Read and write Teams app settings", "value": "TeamworkAppSettings.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read all bookmarks that the signed-in user can access.", "adminConsentDisplayName": "Read all bookmarks that the user can access", "id": "98b17b35-f3b1-4849-a85f-9f13733002f0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read all bookmarks you can access.", "consentDisplayName": "Read all bookmarks that you have access to", "value": "Bookmark.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the lifecycle information like employeeLeaveDateTime of users in your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all users' lifecycle information", "id": "7ee7473e-bd4b-4c9f-987c-bd58481f5fa2", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the lifecycle information like employeeLeaveDateTime of users in your organization, on behalf of the signed-in user.", "consentDisplayName": "Read and write all users' lifecycle information", "value": "User-LifeCycleInfo.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read and write the browser site lists configured for your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write browser site lists for your organization", "id": "83b34c85-95bf-497b-a04e-b58eca9d49d0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read and write the browser site lists configured for your organization, on your behalf.", "consentDisplayName": "Read and write browser site lists for your organization", "value": "BrowserSiteLists.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the lifecycle information like employeeLeaveDateTime of users in your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all users' lifecycle information", "id": "ed8d2a04-0374-41f1-aefe-da8ac87ccc87", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the lifecycle information like employeeLeaveDateTime of users in your organization, on behalf of the signed-in user.", "consentDisplayName": "Read all users' lifecycle information", "value": "User-LifeCycleInfo.Read.All"}, {"adminConsentDescription": "Allows the app to read the Teams app settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read Teams app settings", "id": "44e060c4-bbdc-4256-a0b9-dcc0396db368", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the Teams app settings on your behalf.", "consentDisplayName": "Read Teams app settings", "value": "TeamworkAppSettings.Read.All"}, {"adminConsentDescription": "Allows an app to read the browser site lists configured for your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read browser site lists for your organization", "id": "fb9be2b7-a7fc-4182-aec1-eda4597c43d5", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read the browser site lists configured for your organization, on your behalf.", "consentDisplayName": "Read browser site lists for your organization", "value": "BrowserSiteLists.Read.All"}, {"adminConsentDescription": "Allows the app to list and read all workflows, tasks and related lifecycle workflows resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read all lifecycle workflows resources", "id": "9bcb9916-765a-42af-bf77-02282e26b01a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to list and read all workflows, tasks and related lifecycle workflows resources on your behalf.", "consentDisplayName": "Read all lifecycle workflows resources", "value": "LifecycleWorkflows.Read.All"}, {"adminConsentDescription": "Allows the app to create, update, list, read and delete all workflows, tasks and related lifecycle workflows resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all lifecycle workflows resources", "id": "84b9d731-7db8-4454-8c90-fd9e95350179", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create, update, list, read and delete all workflows, tasks and related lifecycle workflows resources on your behalf.", "consentDisplayName": "Read and write all lifecycle workflows resources", "value": "LifecycleWorkflows.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and update admin report settings, such as whether to display concealed information in reports, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write admin report settings", "id": "b955410e-7715-4a88-a940-dfd551018df3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update admin report settings, such as whether to display concealed information in reports, on your behalf.", "consentDisplayName": "Read and write admin report settings", "value": "ReportSettings.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read admin report settings, such as whether to display concealed information in reports, on behalf of the signed-in user", "adminConsentDisplayName": "Read admin report settings", "id": "84fac5f4-33a9-4100-aa38-a20c6d29e5e7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read admin report settings, such as whether to display concealed information in reports, on your behalf.", "consentDisplayName": "Read admin report settings", "value": "ReportSettings.Read.All"}, {"adminConsentDescription": "Allows the app to read data for the learning provider in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read learning provider", "id": "dd8ce36f-9245-45ea-a99e-8ac398c22861", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read data for the learning provider in the organization's directory, on your behalf.", "consentDisplayName": "Read learning provider", "value": "LearningProvider.Read"}, {"adminConsentDescription": "Allows the app to read learning content in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read learning content", "id": "ea4c1fd9-6a9f-4432-8e5d-86e06cc0da77", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read learning content in the organization's directory, on your behalf.", "consentDisplayName": "Read learning content", "value": "LearningContent.Read.All"}, {"adminConsentDescription": "Allows the app to create, update, read, and delete data for the learning provider in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage learning provider", "id": "40c2eb57-abaf-49f5-9331-e90fd01f7130", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create, update, read, and delete data for the learning provider in the organization's directory, on your behalf.", "consentDisplayName": "Manage learning provider", "value": "LearningProvider.ReadWrite"}, {"adminConsentDescription": "Allows the app to manage learning content in the organization's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage learning content", "id": "53cec1c4-a65f-4981-9dc1-ad75dbf1c077", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage learning content in the organization's directory, on your behalf.", "consentDisplayName": "Manage learning content", "value": "LearningContent.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read attack simulation and training data for an organization for the signed-in user.", "adminConsentDisplayName": "Read attack simulation data of an organization", "id": "104a7a4b-ca76-4677-b7e7-2f4bc482f381", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read attack simulation and training data for an organization on your behalf.", "consentDisplayName": "Read attack simulation data of an organization", "value": "AttackSimulation.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization's custom authentication extensions on behalf of the signed-in user.", "adminConsentDisplayName": "Read your oganization's custom authentication extensions", "id": "b2052569-c98c-4f36-a5fb-43e5c111e6d0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's custom authentication extensions on your behalf.", "consentDisplayName": "Read your organization's custom authentication extensions", "value": "CustomAuthenticationExtension.Read.All"}, {"adminConsentDescription": "Allows the app to read or write your organization's custom authentication extensions on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's custom authentication extensions", "id": "8dfcf82f-15d0-43b3-bc78-a958a13a5792", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read or write your organization's custom authentication extensions on your behalf.", "consentDisplayName": "Read and write your organization's custom authentication extensions", "value": "CustomAuthenticationExtension.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read or write your organization's authentication event listeners on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's authentication event listeners", "id": "d11625a6-fe21-4fc6-8d3d-063eba5525ad", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read or write your organization's authentication event listeners on your behalf.", "consentDisplayName": "Read and write your organization's authentication event listeners", "value": "EventListener.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your organization's authentication event listeners on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization's authentication event listeners", "id": "f7dd3bed-5eec-48da-bc73-1c0ef50bc9a1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's authentication event listeners on your behalf.", "consentDisplayName": "Read your organization's authentication event listeners", "value": "EventListener.Read.All"}, {"adminConsentDescription": "Allows the app to read and write channel messages, on behalf of the signed-in user. This doesn't allow the app to edit the policyViolation of a channel message.", "adminConsentDisplayName": "Read and write user channel messages", "id": "5922d31f-46c8-4404-9eaf-2117e390a8a4", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write channel messages, on your behalf. This doesn't allow the app to edit the policyViolation of a channel message.", "consentDisplayName": "Read and write user channel messages", "value": "ChannelMessage.ReadWrite"}, {"adminConsentDescription": "Allows the app to read and write your organization's directory access review default policy on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's directory access review default policy", "id": "4f5bc9c8-ea54-4772-973a-9ca119cb0409", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's directory access review default policy on your behalf.", "consentDisplayName": "Read and write your organization's directory access review default policy", "value": "Policy.ReadWrite.AccessReview"}, {"adminConsentDescription": "Allows the app to read your organization's threat submissions and threat submission policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read all threat submissions", "id": "7083913a-4966-44b6-9886-c5822a5fd910", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's threat submissions and threat submission policies on your behalf.", "consentDisplayName": "Read all threat submissions", "value": "ThreatSubmission.Read.All"}, {"adminConsentDescription": "Allows the app to read the threat submissions and threat submission policies owned by the signed-in user. Also allows the app to create new threat submissions on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write threat submissions", "id": "68a3156e-46c9-443c-b85c-921397f082b5", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the threat submissions and threat submission policies that you own. Also allows the app to create new threat submissions on your behalf.", "consentDisplayName": "Read and write threat submissions", "value": "ThreatSubmission.ReadWrite"}, {"adminConsentDescription": "Allows the app to read the threat submissions and threat submission policies owned by the signed-in user.", "adminConsentDisplayName": "Read threat submissions", "id": "fd5353c6-26dd-449f-a565-c4e16b9fce78", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the threat submissions and threat submission policies that you own on your behalf.", "consentDisplayName": "Read threat submissions", "value": "ThreatSubmission.Read"}, {"adminConsentDescription": "Allows the app to read your organization's threat submission policies on behalf of the signed-in user. Also allows the app to create new threat submission policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all threat submission policies", "id": "059e5840-5353-4c68-b1da-666a033fc5e8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's threat submission policies  on your behalf. Also allows the app to create new threat submission policies  on your behalf.", "consentDisplayName": "Read and write all threat submission policies", "value": "ThreatSubmissionPolicy.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your organization's threat submissions and threat submission policies on behalf of the signed-in user. Also allows the app to create new threat submissions on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all threat submissions", "id": "8458e264-4eb9-4922-abe9-768d58f13c7f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's threat submissions and threat submission policies on your behalf. Also allows the app to create new threat submissions on your behalf.", "consentDisplayName": "Read and write all threat submissions", "value": "ThreatSubmission.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and update all authentication context information in your organization on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all authentication context information", "id": "ba6d575a-1344-4516-b777-1404f5593057", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update all authentication context information in your organization on your behalf.", "consentDisplayName": "Read and write all authentication context information", "value": "AuthenticationContext.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all authentication context information in your organization on behalf of the signed-in user.", "adminConsentDisplayName": "Read all authentication context information", "id": "57b030f1-8c35-469c-b0d9-e4a077debe70", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all authentication context information in your organization on your behalf.", "consentDisplayName": "Read all authentication context information", "value": "AuthenticationContext.Read.All"}, {"adminConsentDescription": "Allow the application to create, update and delete any data from Records Management, such as configuration, labels, and policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write Records Management configuration, labels, and policies", "id": "f2833d75-a4e6-40ab-86d4-6dfe73c97605", "isEnabled": true, "isAdmin": true, "consentDescription": "Allow the application to create, update and delete any data from Records Management, such as configuration, labels, and policies on your behalf.", "consentDisplayName": "Read and write Records Management configuration, labels, and policies", "value": "RecordsManagement.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to read any data from Records Management, such as configuration, labels, and policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read Records Management configuration, labels, and policies", "id": "07f995eb-fc67-4522-ad66-2b8ca8ea3efd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read any data from Records Management, such as configuration, labels and policies on your behalf.", "consentDisplayName": "Read Records Management configuration, labels, and policies", "value": "RecordsManagement.Read.All"}, {"adminConsentDescription": "Allows the app to read all transcripts of online meetings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all transcripts of online meetings. ", "id": "30b87d18-ebb1-45db-97f8-82ccb1f0190c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all transcripts of online meetings, on your behalf.", "consentDisplayName": "Read all transcripts of online meetings.", "value": "OnlineMeetingTranscript.Read.All"}, {"adminConsentDescription": "Allows an app to manage license assignments for users and groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage all license assignments", "id": "f55016cc-149c-447e-8f21-7cf3ec1d6350", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage all license assignments, on your behalf.", "consentDisplayName": "Manage all license assignments", "value": "LicenseAssignment.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all recordings of online meetings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all recordings of online meetings.", "id": "190c2bb6-1fdd-4fec-9aa2-7d571b5e1fe3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all recordings of online meetings, on your behalf. ", "consentDisplayName": "Read all recordings of online meetings. ", "value": "OnlineMeetingRecording.Read.All"}, {"adminConsentDescription": "Allows the application to read the tenant-level settings in SharePoint and OneDrive on behalf of the signed-in user.", "adminConsentDisplayName": "Read SharePoint and OneDrive tenant settings", "id": "2ef70e10-5bfd-4ede-a5f6-67720500b258", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read the tenant-level settings in SharePoint and OneDrive on your behalf.", "consentDisplayName": "Read SharePoint and OneDrive tenant settings", "value": "SharePointTenantSettings.Read.All"}, {"adminConsentDescription": "Allows the application to read and change the tenant-level settings of SharePoint and OneDrive on behalf of the signed-in user.", "adminConsentDisplayName": "Read and change SharePoint and OneDrive tenant settings", "id": "aa07f155-3612-49b8-a147-6c590df35536", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and change the tenant-level settings of SharePoint and OneDrive on your behalf.", "consentDisplayName": "Read and change SharePoint and OneDrive tenant settings", "value": "SharePointTenantSettings.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant on behalf of the signed-in user.  It also permits the application to export external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant on behalf of the signed-in user.", "adminConsentDisplayName": "Read all shared cross-tenant user profiles and export their data", "id": "759dcd16-3c90-463c-937e-abf89f991c18", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant on your behalf.  It also permits the application to export external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant on your behalf.", "consentDisplayName": "Read any shared cross-tenant user profiles and export data", "value": "CrossTenantUserProfileSharing.Read.All"}, {"adminConsentDescription": "Allows the app to read and update Azure AD recommendations, on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and update Azure AD recommendations", "id": "f37235e8-90a0-4189-93e2-e55b53867ccd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update Azure AD recommendations, on your behalf.", "consentDisplayName": "Read and update Azure AD recommendations", "value": "DirectoryRecommendations.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to list and query user profile information associated with the current tenant on behalf of the signed-in user.  It also permits the application to export external user data (e.g. customer content or system-generated logs), associated with the current tenant on behalf of the signed-in user.", "adminConsentDisplayName": "Read shared cross-tenant user profile and export data", "id": "cb1ba48f-d22b-4325-a07f-74135a62ee41", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to list and query shared user profile information associated with the current tenant on your behalf.  It also permits the application to export your external user data (e.g. customer content or system-generated logs), associated with the current tenant on your behalf.", "consentDisplayName": "Read shared cross-tenant user profile and export data", "value": "CrossTenantUserProfileSharing.Read"}, {"adminConsentDescription": "Allows the application to obtain basic tenant information about another target tenant within the Azure AD ecosystem on behalf of the signed-in user.", "adminConsentDisplayName": "Read cross-tenant basic information", "id": "81594d25-e88e-49cf-ac8c-fecbff49f994", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to obtain basic tenant information about another target tenant within the Azure AD ecosystem on your behalf.", "consentDisplayName": "Read cross-tenant basic information", "value": "CrossTenantInformation.ReadBasic.All"}, {"adminConsentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant on behalf of the signed-in user.  It also permits the application to export and remove external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant on behalf of the signed-in user.", "adminConsentDisplayName": "Read all shared cross-tenant user profiles and export or delete their data", "id": "64dfa325-cbf8-48e3-938d-51224a0cac01", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant on your behalf.  It also permits the application to export and remove external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant on your behalf.", "consentDisplayName": "Read any shared cross-tenant user profiles and export or delete data", "value": "CrossTenantUserProfileSharing.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to manage restricted resources based on the other permissions granted to the app, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage restricted resources in the directory", "id": "cba5390f-ed6a-4b7f-b657-0efc2210ed20", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage restricted resources based on the other permissions granted to the app, on your behalf.", "consentDisplayName": "Manage restricted resources in the directory", "value": "Directory.Write.Restricted"}, {"adminConsentDescription": "Allows the application to list and query user profile information associated with the current tenant on behalf of the signed-in user.  It also permits the application to export and remove external user data (e.g. customer content or system-generated logs), associated with the current tenant on behalf of the signed-in user.", "adminConsentDisplayName": "Read shared cross-tenant user profile and export or delete data", "id": "eed0129d-dc60-4f30-8641-daf337a39ffd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to list and query shared user profile information associated with the current tenant on your behalf.  It also permits the application to export and remove your external user data (e.g. customer content or system-generated logs), associated with the current tenant on your behalf.", "consentDisplayName": "Read shared cross-tenant user profile and export or delete data", "value": "CrossTenantUserProfileSharing.ReadWrite"}, {"adminConsentDescription": "Allows the app to read Azure AD recommendations, on behalf of the signed-in user.", "adminConsentDisplayName": "Read Azure AD recommendations", "id": "34d3bd24-f6a6-468c-b67c-0c365c1d6410", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read Azure AD recommendations, on your behalf.", "consentDisplayName": "Read Azure AD recommendations", "value": "DirectoryRecommendations.Read.All"}, {"adminConsentDescription": "Allows the app to manage (create-update-terminate) Delegated Admin relationships with customers as well as role assignments to security groups for active Delegated Admin relationships on behalf of the signed-in user.", "adminConsentDisplayName": "Manage Delegated Admin relationships with customers", "id": "885f682f-a990-4bad-a642-36736a74b0c7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage (create-update-terminate) Delegated Admin relationships with customers and role assignments to security groups for active Delegated Admin relationships on your behalf.", "consentDisplayName": "Manage Delegated Admin relationships with customers", "value": "DelegatedAdminRelationship.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read details of delegated admin relationships with customers like access details (that includes roles) and the duration as well as specific role assignments to security groups on behalf of the signed-in user.", "adminConsentDisplayName": "Read Delegated Admin relationships with customers", "id": "0c0064ea-477b-4130-82a5-4c2cc4ff68aa", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read details of Delegated Admin relationships with customers like access details (that includes roles) and the duration as well as specific role assignments to security groups on your behalf.", "consentDisplayName": "Read Delegated Admin relationships with customers", "value": "DelegatedAdminRelationship.Read.All"}, {"adminConsentDescription": "Allows the app to read and write all external items on behalf of a signed-in user. The signed-in user must be an administrator.", "adminConsentDisplayName": "Read and write all external items", "id": "b02c54f8-eb48-4c50-a9f0-a149e5a2012f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all external items on your behalf. The signed-in user must be an administrator.", "consentDisplayName": "Read and write all external items", "value": "ExternalItem.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write external items on behalf of a signed-in user. The signed-in user must be an administrator. The app can only read external items of the connection that it is authorized to.", "adminConsentDisplayName": "Read and write external items", "id": "4367b9d7-cee7-4995-853c-a0bdfe95c1f9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write external items on your behalf. The signed-in user must be an administrator. The app can only read external items of the connection that it is authorized to.", "consentDisplayName": "Read and write external items", "value": "ExternalItem.ReadWrite.OwnedBy"}, {"adminConsentDescription": "Allows the app to read and write all external connections on behalf of a signed-in user. The signed-in user must be an administrator.", "adminConsentDisplayName": "Read and write all external connections", "id": "bbbbd9b3-3566-4931-ac37-2b2180d9e334", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all external connections on your behalf. The signed-in user must be an administrator.", "consentDisplayName": "Read and write all external connections", "value": "ExternalConnection.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all external connections on behalf of a signed-in user. The signed-in user must be an administrator.", "adminConsentDisplayName": "Read all external connections", "id": "a38267a5-26b6-4d76-9493-935b7599116b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all external connections on your behalf. The signed-in user must be an administrator.", "consentDisplayName": "Read all external connections", "value": "ExternalConnection.Read.All"}, {"adminConsentDescription": "Allows the app to read and write settings of external connections on behalf of a signed-in user. The signed-in user must be an administrator. The app can only read and write settings of connections that it is authorized to.", "adminConsentDisplayName": "Read and write external connections", "id": "4082ad95-c812-4f02-be92-780c4c4f1830", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write external connections on your behalf. The signed-in user must be an administrator. The app can only read and write external connections that it is authorized to, or it can create new external connections. ", "consentDisplayName": "Read and write external connections", "value": "ExternalConnection.ReadWrite.OwnedBy"}, {"adminConsentDescription": "Allows the app to read the Cloud PC role-based access control (RBAC) settings, on behalf of the signed-in user.  This includes reading Cloud PC role definitions and role assignments.", "adminConsentDisplayName": "Read Cloud PC RBAC settings", "id": "9619b88a-8a25-48a7-9571-d23be0337a79", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the Cloud PC role-based access control (RBAC) settings, on your behalf.  This includes reading Cloud PC role definitions and role assignments.", "consentDisplayName": "Read Cloud PC RBAC settings", "value": "RoleManagement.Read.CloudPC"}, {"adminConsentDescription": "Allows the app to read and manage the Cloud PC role-based access control (RBAC) settings, on behalf of the signed-in user. This includes reading and managing Cloud PC role definitions and role assignments.", "adminConsentDisplayName": "Read and write Cloud PC RBAC settings", "id": "501d06f8-07b8-4f18-b5c6-c191a4af7a82", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the Cloud PC role-based access control (RBAC) settings, on your behalf. This includes reading and managing Cloud PC role definitions and memberships.", "consentDisplayName": "Read and write Cloud PC RBAC settings", "value": "RoleManagement.ReadWrite.CloudPC"}, {"adminConsentDescription": "Allows the app to read all managed tenant information on behalf of the signed-in user.", "adminConsentDisplayName": "Read all managed tenant information", "id": "dc34164e-6c4a-41a0-be89-3ae2fbad7cd3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all managed tenant information on your behalf.", "consentDisplayName": "Read all managed tenant information", "value": "ManagedTenants.Read.All"}, {"adminConsentDescription": "Allows the app to read and write all managed tenant information on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all managed tenant information", "id": "b31fa710-c9b3-4d9e-8f5e-8036eecddab9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all managed tenant information on your behalf.", "consentDisplayName": "Read and write all managed tenant information", "value": "ManagedTenants.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read online meeting artifacts on behalf of the signed-in user.", "adminConsentDisplayName": "Read user's online meeting artifacts", "id": "110e5abb-a10c-4b59-8b55-9b4daa4ef743", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read online meeting artifacts on your behalf.", "consentDisplayName": "Read user's online meeting artifacts", "value": "OnlineMeetingArtifact.Read.All"}, {"adminConsentDescription": "Allows the app to read search configuration, on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization's search configuration", "id": "7d307522-aa38-4cd0-bd60-90c6f0ac50bd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read search configuration, on your behalf.", "consentDisplayName": "Read your organization's search configuration", "value": "SearchConfiguration.Read.All"}, {"adminConsentDescription": "Allows the app to read and write search configuration, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's search configuration", "id": "b1a7d408-cab0-47d2-a2a5-a74a3733600d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write search configuration, on your behalf.", "consentDisplayName": "Read and write your organization's search configuration", "value": "SearchConfiguration.ReadWrite.All"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs in chats the signed-in user can access.", "adminConsentDisplayName": "Allow the Teams app to manage only its own tabs in chats", "id": "0c219d04-3abf-47f7-912d-5cca239e90e6", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs in chats you can access.", "consentDisplayName": "Allow the Teams app to manage only its own tabs in chats", "value": "TeamsTab.ReadWriteSelfForChat"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs for the signed-in user.", "adminConsentDisplayName": "Allow the Teams app to manage only its own tabs for a user", "id": "395dfec1-a0b9-465f-a783-8250a430cb8c", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs for you.", "consentDisplayName": "Allow the Teams app to manage only its own tabs for you", "value": "TeamsTab.ReadWriteSelfForUser"}, {"adminConsentDescription": "Allows the app to read and write subject rights requests on behalf of the signed-in user", "adminConsentDisplayName": "Read and write subject rights requests", "id": "2b8fcc74-bce1-4ae3-a0e8-60c53739299d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write subject rights requests on your behalf.", "consentDisplayName": "Read and write data subject requests", "value": "SubjectRightsRequest.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read subject rights requests on behalf of the signed-in user", "adminConsentDisplayName": "Read subject rights requests", "id": "9c3af74c-fd0f-4db4-b17a-71939e2a9d77", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read subject rights requests on your behalf.", "consentDisplayName": "Read data subject requests", "value": "SubjectRightsRequest.Read.All"}, {"adminConsentDescription": "Allows the app to read the presence information and write activity and availability on behalf of the signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "adminConsentDisplayName": "Read and write a user's presence information", "id": "8d3c54a7-cf58-4773-bf81-c0cd6ad522bb", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the presence information and write activity and availability on your behalf. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "consentDisplayName": "Read and write your presence information", "value": "Presence.ReadWrite"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs to teams the signed-in user can access.", "adminConsentDisplayName": "Allow the Teams app to manage only its own tabs in teams", "id": "f266662f-120a-4314-b26a-99b08617c7ef", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs to teams you can access.", "consentDisplayName": "Allow the Teams app to manage only its own tabs in teams", "value": "TeamsTab.ReadWriteSelfForTeam"}, {"adminConsentDescription": "Allows the app to read and update identity risky service principal information for all service principals in your organization, on behalf of the signed-in user. Update operations include dismissing risky service principals.", "adminConsentDisplayName": "Read and write all identity risky service principal information", "id": "bb6f654c-d7fd-4ae3-85c3-fc380934f515", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update identity risky service principal information for all service principals in your organization, on your behalf. Update operations include dismissing risky service principals.", "consentDisplayName": "Read and write all identity risky service principal information", "value": "IdentityRiskyServicePrincipal.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all identity risky service principal information for your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all identity risky service principal information", "id": "ea5c4ab0-5a73-4f35-8272-5d5337884e5d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all identity risky service principal information for your organization, on your behalf.", "consentDisplayName": "Read all identity risky service principal information", "value": "IdentityRiskyServicePrincipal.Read.All"}, {"adminConsentDescription": "Allow the app to read and write the management data for Teams devices on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write Teams devices", "id": "ddd97ecb-5c31-43db-a235-0ee20e635c40", "isEnabled": true, "isAdmin": true, "consentDescription": "Allow the app to read and write the management data for Teams devices on your behalf.", "consentDisplayName": "Read and write Teams devices", "value": "TeamworkDevice.ReadWrite.All"}, {"adminConsentDescription": "Allow the app to read the management data for Teams devices on behalf of the signed-in user.", "adminConsentDisplayName": "Read Teams devices", "id": "b659488b-9d28-4208-b2be-1c6652b3c970", "isEnabled": true, "isAdmin": true, "consentDescription": "Allow the app to read the management data for Teams devices on your behalf.", "consentDisplayName": "Read Teams devices", "value": "TeamworkDevice.Read.All"}, {"adminConsentDescription": "Allows the app to run hunting queries, on behalf of the signed-in user.", "adminConsentDisplayName": "Run hunting queries", "id": "b152eca8-ea73-4a48-8c98-1a6742673d99", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to run hunting queries that you can execute.", "consentDisplayName": "Run hunting queries", "value": "ThreatHunting.Read.All"}, {"adminConsentDescription": "Allows the app to update service announcement messages' user status on behalf of the signed-in user. The message status can be marked as read, archive, or favorite.", "adminConsentDisplayName": "Update user status on service announcement messages", "id": "636e1b0b-1cc2-4b1c-9aa9-4eeed9b9761b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to update service announcement messages' status on your behalf. Your status for messages can be marked as read, archive, or favorite.", "consentDisplayName": "Update your user status on service announcement messages", "value": "ServiceMessageViewpoint.Write"}, {"adminConsentDescription": "Allows the app to read all security alerts, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all security alerts", "id": "bc257fb8-46b4-4b15-8713-01e91bfbe4ea", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all security alerts that you have access to.", "consentDisplayName": "Read all alerts", "value": "SecurityAlert.Read.All"}, {"adminConsentDescription": "Allows the app to read and write to all security alerts, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write to all security alerts", "id": "471f2a7f-2a42-4d45-a2bf-594d0838070d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all alerts that you have access to.", "consentDisplayName": "Read and write all alerts", "value": "SecurityAlert.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read security incidents, on behalf of the signed-in user.", "adminConsentDisplayName": "Read incidents", "id": "b9abcc4f-94fc-4457-9141-d20ce80ec952", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all security incidents that you have access to.", "consentDisplayName": "Read security incidents", "value": "SecurityIncident.Read.All"}, {"adminConsentDescription": "Allows the app to read and write security incidents, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write to incidents", "id": "128ca929-1a19-45e6-a3b8-435ec44a36ba", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write to all security incidents that you have access to.", "consentDisplayName": "Read and write to security incidents", "value": "SecurityIncident.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read tags in Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Read tags in Teams", "id": "57587d0b-8399-45be-b207-8050cec54575", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read tags in Teams, on your behalf.", "consentDisplayName": "Read tags in Teams", "value": "TeamworkTag.Read"}, {"adminConsentDescription": "Allows the app to read and write tags in Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write tags in Teams", "id": "539dabd7-b5b6-4117-b164-d60cd15a8671", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write tags in Teams, on your behalf.", "consentDisplayName": "Read and write tags in Teams", "value": "TeamworkTag.ReadWrite"}, {"adminConsentDescription": "Allows the app to read and write your organization's cross tenant access policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's cross tenant access policies", "id": "014b43d0-6ed4-4fc6-84dc-4b6f7bae7d85", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's cross tenant access policies on your behalf.", "consentDisplayName": "Read and write your organization's cross tenant access policies", "value": "Policy.ReadWrite.CrossTenantAccess"}, {"adminConsentDescription": "Allows the app to read custom security attribute definitions for the tenant on behalf of a signed in user.", "adminConsentDisplayName": "Read custom security attribute definitions", "id": "ce026878-a0ff-4745-a728-d4fedd086c07", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read custom security attribute definitions for the tenant on your behalf.", "consentDisplayName": "Read custom security attribute definitions", "value": "CustomSecAttributeDefinition.Read.All"}, {"adminConsentDescription": "Allows the app to read custom security attribute assignments for all principals in the tenant on behalf of a signed in user.", "adminConsentDisplayName": "Read custom security attribute assignments", "id": "b46ffa80-fe3d-4822-9a1a-c200932d54d0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read custom security attribute assignments for all principals in the tenant on your behalf.", "consentDisplayName": "Read custom security attribute assignments", "value": "CustomSecAttributeAssignment.Read.All"}, {"adminConsentDescription": "Allows the app to read and manage the active role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes managing active directory role membership, and reading directory role templates, directory roles and active memberships.", "adminConsentDisplayName": "Read, update, and delete all active role assignments for your company's directory", "id": "8c026be3-8e26-4774-9372-8d5d6f21daff", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the active role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes managing active directory role membership, and reading directory role templates, directory roles and active memberships.", "consentDisplayName": "Read, update, and delete all active role assignments for your company's directory", "value": "RoleAssignmentSchedule.ReadWrite.Directory"}, {"adminConsentDescription": "Allows the app to read and manage the eligible role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes managing eligible directory role membership, and reading directory role templates, directory roles and eligible memberships.", "adminConsentDisplayName": "Read, update, and delete  all eligible role assignments for your company's directory", "id": "62ade113-f8e0-4bf9-a6ba-5acb31db32fd", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the eligible role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes managing eligible directory role membership, and reading directory role templates, directory roles and eligible memberships.", "consentDisplayName": "Read, update, and delete  all eligible role assignments for your company's directory", "value": "RoleEligibilitySchedule.ReadWrite.Directory"}, {"adminConsentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory", "id": "1ff1be21-34eb-448c-9ac9-ce1f506b2a68", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, on your behalf.", "consentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory", "value": "RoleManagementPolicy.ReadWrite.Directory"}, {"adminConsentDescription": "Allows the app to read the active role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes reading directory role templates, and directory roles.", "adminConsentDisplayName": "Read all active role assignments for your company's directory", "id": "344a729c-0285-42c6-9014-f12b9b8d6129", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the active role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes reading directory role templates, and directory roles.", "consentDisplayName": "Read all active role assignments for your company's directory", "value": "RoleAssignmentSchedule.Read.Directory"}, {"adminConsentDescription": "Allows the app to read the eligible role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes reading directory role templates, and directory roles.", "adminConsentDisplayName": "Read all eligible role assignments for your company's directory", "id": "eb0788c2-6d4e-4658-8c9e-c0fb8053f03d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the eligible role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes reading directory role templates, and directory roles.", "consentDisplayName": "Read all eligible role assignments for your company's directory", "value": "RoleEligibilitySchedule.Read.Directory"}, {"adminConsentDescription": "Allows the app to read policies for privileged role-based access control (RBAC) assignments of your company's directory, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all policies for privileged role assignments of your company's directory", "id": "3de2cdbe-0ff5-47d5-bdee-7f45b4749ead", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read policies for privileged role-based access control (RBAC) assignments of your company's directory, on your behalf.", "consentDisplayName": "Read all policies for privileged role assignments of your company's directory", "value": "RoleManagementPolicy.Read.Directory"}, {"adminConsentDescription": "Allows the app to read and write all Windows update deployment settings for the organization on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all Windows update deployment settings", "id": "11776c0c-6138-4db3-a668-ee621bea2555", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all Windows update deployment settings for the organization on your behalf.", "consentDisplayName": "Read and write all Windows update deployment settings", "value": "WindowsUpdates.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write your organization's mobility management policies on behalf of the signed-in user.  For example, a mobility management policy can set the enrollment scope for a given mobility management application.", "adminConsentDisplayName": "Read and write your organization's mobility management policies", "id": "a8ead177-1889-4546-9387-f25e658e2a79", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's mobility management policies on your behalf.  For example, a mobility management policy can set the enrollment scope for a given mobility management application.", "consentDisplayName": "Read and write your organization's mobility management policies", "value": "Policy.ReadWrite.MobilityManagement"}, {"adminConsentDescription": "Allows the app to read basic unified group properties, memberships and owners of the group the signed-in guest is a member of.", "adminConsentDisplayName": "Read unified group memberships as guest", "id": "73e75199-7c3e-41bb-9357-167164dbb415", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read basic unified group properties, memberships and owners of the group you are a member of.", "consentDisplayName": "Read unified group memberships as guest", "value": "UnifiedGroupMember.Read.AsGuest"}, {"adminConsentDescription": "Allows the app to update service principal endpoints", "adminConsentDisplayName": "Read and update service principal endpoints", "id": "7297d82c-9546-4aed-91df-3d4f0a9b3ff0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to update service principal endpoints", "consentDisplayName": "Read and update service principal endpoints", "value": "ServicePrincipalEndpoint.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read service principal endpoints", "adminConsentDisplayName": "Read service principal endpoints", "id": "9f9ce928-e038-4e3b-8faf-7b59049a8ddc", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read service principal endpoints", "consentDisplayName": "Read service principal endpoints", "value": "ServicePrincipalEndpoint.Read.All"}, {"adminConsentDescription": "Allows the app to create new notifications in users' teamwork activity feeds on behalf of the signed in user. These notifications may not be discoverable or be held or governed by compliance policies.", "adminConsentDisplayName": "Send a teamwork activity as the user", "id": "7ab1d787-bae7-4d5d-8db6-37ea32df9186", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to create new activities in your teamwork activity feed, and send new activities to other users' activity feed, on your behalf.", "consentDisplayName": "Send a teamwork activity", "value": "TeamsActivity.Send"}, {"adminConsentDescription": "Allows the app to read and write eDiscovery objects such as cases, custodians, review sets and other related objects on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all eDiscovery objects", "id": "acb8f680-0834-4146-b69e-4ab1b39745ad", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write eDiscovery objects such as cases, custodians, review sets and other related objects on your behalf.", "consentDisplayName": "Read and write all eDiscovery objects", "value": "eDiscovery.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read eDiscovery objects such as cases, custodians, review sets and other related objects on behalf of the signed-in user.", "adminConsentDisplayName": "Read all eDiscovery objects", "id": "99201db3-7652-4d5a-809a-bdb94f85fe3c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read eDiscovery objects such as cases, custodians, review sets and other related objects on your behalf.", "consentDisplayName": "Read all eDiscovery objects", "value": "eDiscovery.Read.All"}, {"adminConsentDescription": "Allows the app to read and write custom security attribute assignments for all principals in the tenant on behalf of a signed in user.", "adminConsentDisplayName": "Read and write custom security attribute assignments", "id": "ca46335e-8453-47cd-a001-8459884efeae", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write custom security attribute assignments for all principals in the tenant on your behalf.", "consentDisplayName": "Read and write custom security attribute assignments", "value": "CustomSecAttributeAssignment.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write custom security attribute definitions for the tenant on behalf of a signed in user.", "adminConsentDisplayName": "Read and write custom security attribute definitions", "id": "8b0160d4-5743-482b-bb27-efc0a485ca4a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write custom security attribute definitions for the tenant on your behalf.", "consentDisplayName": "Read and write custom security attribute definitions", "value": "CustomSecAttributeDefinition.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to create print jobs on behalf of the signed-in user and upload document content to print jobs that the signed-in user created.", "adminConsentDisplayName": "Create print jobs", "id": "21f0d9c0-9f13-48b3-94e0-b6b231c7d320", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to create print jobs on your behalf and upload document content to print jobs  that you created.", "consentDisplayName": "Create your print jobs", "value": "PrintJob.Create"}, {"adminConsentDescription": "Allows the application to read basic information about printer shares on behalf of the signed-in user. Does not allow reading access control information.", "adminConsentDisplayName": "Read basic information about printer shares", "id": "5fa075e9-b951-4165-947b-c63396ff0a37", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read basic information about printer shares on your behalf.", "consentDisplayName": "Read basic information about printer shares", "value": "PrinterShare.ReadBasic.All"}, {"adminConsentDescription": "Allows the application to read print connectors on behalf of the signed-in user.", "adminConsentDisplayName": "Read print connectors", "id": "d69c2d6d-4f72-4f99-a6b9-663e32f8cf68", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read print connectors on your behalf.", "consentDisplayName": "Read print connectors", "value": "PrintConnector.Read.All"}, {"adminConsentDescription": "Allows the application to read and write print connectors on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write print connectors", "id": "79ef9967-7d59-4213-9c64-4b10687637d8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and write print connectors on your behalf.", "consentDisplayName": "Read and write print connectors", "value": "PrintConnector.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to read tenant-wide print settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read tenant-wide print settings", "id": "490f32fd-d90f-4dd7-a601-ff6cdc1a3f6c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read tenant-wide print settings on your behalf.", "consentDisplayName": "Read tenant-wide print settings", "value": "PrintSettings.Read.All"}, {"adminConsentDescription": "Allows the application to read and write tenant-wide print settings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write tenant-wide print settings", "id": "9ccc526a-c51c-4e5c-a1fd-74726ef50b8f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and write tenant-wide print settings on your behalf.", "consentDisplayName": "Read and write tenant-wide print settings", "value": "PrintSettings.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to create chats on behalf of the signed-in user.", "adminConsentDisplayName": "Create chats", "id": "38826093-1258-4dea-98f0-00003be2b8d0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to create chats on your behalf. ", "consentDisplayName": "Create chats", "value": "Chat.Create"}, {"adminConsentDescription": "Add and remove members from chats, on behalf of the signed-in user.", "adminConsentDisplayName": "Add and remove members from chats", "id": "dea13482-7ea6-488f-8b98-eb5bbecf033d", "isEnabled": true, "isAdmin": true, "consentDescription": "Add and remove members from chats, on your behalf.", "consentDisplayName": "Add and remove members from chats", "value": "ChatMember.ReadWrite"}, {"adminConsentDescription": "Read the members of chats, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the members of chats", "id": "c5a9e2b1-faf6-41d4-8875-d381aa549b24", "isEnabled": true, "isAdmin": true, "consentDescription": "Read the members of chats, on your behalf.", "consentDisplayName": "Read the members of chats", "value": "ChatMember.Read"}, {"adminConsentDescription": "Allows the app to read, create and manage the API connectors used in user authentication flows, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write API connectors for authentication flows", "id": "c67b52c5-7c69-48b6-9d48-7b3af3ded914", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, create and manage the API connectors used in user authentication flows, on your behalf.", "consentDisplayName": "Read and write API connectors for authentication flows", "value": "APIConnectors.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the API connectors used in user authentication flows, on behalf of the signed-in user.", "adminConsentDisplayName": "Read API connectors for authentication flows", "id": "1b6ff35f-31df-4332-8571-d31ea5a4893f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the API connectors used in user authentication flows, on your behalf.", "consentDisplayName": "Read API connectors for authentication flows", "value": "APIConnectors.Read.All"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for the signed-in user.", "adminConsentDisplayName": "Allow the Teams app to manage all tabs for a user", "id": "c37c9b61-7762-4bff-a156-afc0005847a0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for you.", "consentDisplayName": "Allow the Teams app to manage all tabs for you", "value": "TeamsTab.ReadWriteForUser"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs to teams the signed-in user can access.", "adminConsentDisplayName": "Allow the Teams app to manage all tabs in teams", "id": "c975dd04-a06e-4fbb-9704-62daad77bb49", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs to teams you can access.", "consentDisplayName": "Allow the app to manage all tabs in teams", "value": "TeamsTab.ReadWriteForTeam"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in chats the signed-in user can access.", "adminConsentDisplayName": "Allow the Teams app to manage all tabs in chats", "id": "ee928332-e9c2-4747-b4a0-f8c164b68de6", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in chats you can access.", "consentDisplayName": "Allow the Teams app to manage all tabs in chats", "value": "TeamsTab.ReadWriteForChat"}, {"adminConsentDescription": "Allows an app to read one-to-one and group chat messages, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user chat messages", "id": "cdcdac3a-fd45-410d-83ef-554db620e5c7", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read one-to-one or group chat messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Read user chat messages", "value": "ChatMessage.Read"}, {"adminConsentDescription": "Allows the app to read the signed-in user’s tasks and task lists, including any shared with the user. Doesn't include permission to create, delete, or update anything.", "adminConsentDisplayName": "Read user's tasks and task lists", "id": "f45671fb-e0fe-4b4b-be20-3d3ce43f1bcb", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your tasks and task lists, including any shared with you. Doesn't include permission to create, delete, or update anything.", "consentDisplayName": "Read your tasks and task lists", "value": "Tasks.Read"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete the signed-in user's tasks and task lists, including any shared with the user.", "adminConsentDisplayName": "Create, read, update, and delete user’s tasks and task lists", "id": "2219042f-cab5-40cc-b0d2-16b1540b4c5f", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to create, read, update, and delete your tasks and task lists, including any shared with you.", "consentDisplayName": "Create, read, update, and delete your tasks and task lists", "value": "Tasks.ReadWrite"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats the signed-in user can access. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Manage installed Teams apps in chats", "id": "aa85bf13-d771-4d5d-a9e6-bca04ce44edf", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats you can access. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage installed Teams apps in chats", "value": "TeamsAppInstallation.ReadWriteForChat"}, {"adminConsentDescription": "Allows the app to read the properties of Cloud PCs on behalf of the signed-in user.", "adminConsentDisplayName": "Read Cloud PCs", "id": "5252ec4e-fd40-4d92-8c68-89dd1d3c6110", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the properties of Cloud PCs, on your behalf.", "consentDisplayName": "Read Cloud PCs", "value": "CloudPC.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the properties of Cloud PCs on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write Cloud PCs", "id": "9d77138f-f0e2-47ba-ab33-cd246c8b79d1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the properties of Cloud PCs, on your behalf.", "consentDisplayName": "Read and write Cloud PCs", "value": "CloudPC.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read the members and descriptions of one-to-one and group chat threads, on behalf of the signed-in user.", "adminConsentDisplayName": "Read names and members of user chat threads", "id": "9547fcb5-d03f-419d-9948-5928bbf71b0f", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read the members and descriptions of one-to-one and group chat threads, on your behalf.", "consentDisplayName": "Read names and members of your chat threads", "value": "<PERSON><PERSON><PERSON>"}, {"adminConsentDescription": "Allows an app to send one-to-one and group chat messages in Microsoft Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Send user chat messages", "id": "116b7235-7cc6-461e-b163-8e55691d839e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to send one-to-one and group chat messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Send chat messages", "value": "ChatMessage.Send"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for all RBAC providers, on behalf of the signed-in user.  This includes reading role definitions and role assignments.", "adminConsentDisplayName": "Read role management data for all RBAC providers", "id": "48fec646-b2ba-4019-8681-8eb31435aded", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the role-based access control (RBAC) settings for all RBAC providers, on your behalf.  This includes reading role definitions and role assignments.", "consentDisplayName": "Read role management data for all RBAC providers", "value": "RoleManagement.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization's conditional access policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization's conditional access policies", "id": "633e0fce-8c58-4cfb-9495-12bbd5a24f7c", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your organization's conditional access policies on your behalf.", "consentDisplayName": "Read your organization's conditional access policies", "value": "Policy.Read.ConditionalAccess"}, {"adminConsentDescription": "Allows the app to read, create, edit, and delete short notes of a signed-in user.", "adminConsentDisplayName": "Read, create, edit, and delete short notes of the signed-in user", "id": "328438b7-4c01-4c07-a840-e625a749bb89", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, create, edit, and delete your short notes.", "consentDisplayName": "Read, create, edit, and delete your short notes", "value": "ShortNotes.ReadWrite"}, {"adminConsentDescription": "Allows the app to read all the short notes a sign-in user has access to.", "adminConsentDisplayName": "Read short notes of the signed-in user", "id": "50f66e47-eb56-45b7-aaa2-75057d9afe08", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your short notes.", "consentDisplayName": "Read your short notes", "value": "ShortNotes.Read"}, {"adminConsentDescription": "Allows the app to read your tenant's service health information on behalf of the signed-in user. Health information may include service issues or service health overviews.", "adminConsentDisplayName": "Read service health", "id": "55896846-df78-47a7-aa94-8d3d4442ca7f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your tenant's service health information on your behalf.Health information may include service issues or service health overviews.", "consentDisplayName": "Read service health", "value": "ServiceHealth.Read.All"}, {"adminConsentDescription": "Allows the app to read your tenant's service announcement messages on behalf of the signed-in user. Messages may include information about new or changed features.", "adminConsentDisplayName": "Read service announcement messages", "id": "eda39fa6-f8cf-4c3c-a909-432c683e4c9b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your tenant's service announcement messages on your behalf. Messages may include information about new or changed features.", "consentDisplayName": "Read service messages", "value": "ServiceMessage.Read.All"}, {"adminConsentDescription": "Allows the app to read or modify data that the signed-in user has access to. This includes all sets, groups and terms in the term store.", "adminConsentDisplayName": "Read and write term store data", "id": "6c37c71d-f50f-4bff-8fd3-8a41da390140", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read or modify data that you have access to. This includes all sets, groups and terms in the term store.", "consentDisplayName": "Read and write term store data", "value": "TermStore.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the term store data that the signed-in user has access to. This includes all sets, groups and terms in the term store.", "adminConsentDisplayName": "Read term store data", "id": "297f747b-0005-475b-8fef-c890f5152b38", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the term store data that you have access to. This includes all sets, groups and terms in the term store.", "consentDisplayName": "Read term store data", "value": "TermStore.Read.All"}, {"adminConsentDescription": "Add and remove members from all teams, on behalf of the signed-in user. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "adminConsentDisplayName": "Add and remove members with non-owner role for all teams", "id": "2104a4db-3a2f-4ea0-9dba-143d457dc666", "isEnabled": true, "isAdmin": true, "consentDescription": "Add and remove members from all teams, on your behalf. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "consentDisplayName": "Add and remove members with non-owner role for all teams", "value": "TeamMember.ReadWriteNonOwnerRole.All"}, {"adminConsentDescription": "Allows the app to create teams on behalf of the signed-in user.", "adminConsentDisplayName": "Create teams", "id": "7825d5d6-6049-4ce7-bdf6-3b8d53f4bcd0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to create teams on your behalf. ", "consentDisplayName": "Create teams", "value": "Team.Create"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps installed for the signed-in user. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Manage user's installed Teams apps", "id": "093f8818-d05f-49b8-95bc-9d2a73e9a43c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps installed for you. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage your installed Teams apps", "value": "TeamsAppInstallation.ReadWriteForUser"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for the signed-in user.", "adminConsentDisplayName": "Allow the Teams app to manage itself for a user", "id": "207e0cb1-3ce7-4922-b991-5a760c346ebc", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for you.", "consentDisplayName": "Allow the Teams app to manage itself for you", "value": "TeamsAppInstallation.ReadWriteSelfForUser"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself to teams the signed-in user can access.", "adminConsentDisplayName": "Allow the app to manage itself in teams", "id": "0f4595f7-64b1-4e13-81bc-11a249df07a9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself to teams you can access.", "consentDisplayName": "Allow the Teams app to manage itself in teams", "value": "TeamsAppInstallation.ReadWriteSelfForTeam"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats the signed-in user can access.", "adminConsentDisplayName": "Allow the Teams app to manage itself in chats", "id": "0ce33576-30e8-43b7-99e5-62f8569a4002", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats you can access.", "consentDisplayName": "Allow the Teams app to manage itself in chats", "value": "TeamsAppInstallation.ReadWriteSelfForChat"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams the signed-in user can access. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Manage installed Teams apps in teams", "id": "2e25a044-2580-450d-8859-42eeb6e996c0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams you can access. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage installed Teams apps in teams", "value": "TeamsAppInstallation.ReadWriteForTeam"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed for the signed-in user. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Read user's installed Teams apps", "id": "c395395c-ff9a-4dba-bc1f-8372ba9dca84", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the Teams apps that are installed for you. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read your installed Teams apps", "value": "TeamsAppInstallation.ReadForUser"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed in teams the signed-in user can access. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Read installed Teams apps in teams", "id": "5248dcb1-f83b-4ec3-9f4d-a4428a961a72", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the Teams apps that are installed in teams that you can access. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read installed Teams apps in teams", "value": "TeamsAppInstallation.ReadForTeam"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed in chats the signed-in user can access. Does not give the ability to read application-specific settings.", "adminConsentDisplayName": "Read installed Teams apps in chats", "id": "bf3fbf03-f35f-4e93-963e-47e4d874c37a", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read the Teams apps that are installed in chats that you can access. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read installed Teams apps in chats", "value": "TeamsAppInstallation.ReadForChat"}, {"adminConsentDescription": "Allows the app to submit application packages to the catalog and cancel submissions that are pending review on behalf of the signed-in user.", "adminConsentDisplayName": "Submit application packages to the catalog and cancel pending submissions", "id": "3db89e36-7fa6-4012-b281-85f3d9d9fd2e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to submit application packages to the catalog and cancel submissions that are pending review on your behalf.", "consentDisplayName": "Submit application packages to your organization's catalog and cancel pending submissions", "value": "AppCatalog.Submit"}, {"adminConsentDescription": "Allows the app to read and write your organization's device configuration policies on behalf of the signed-in user.  For example, device registration policy can limit initial provisioning controls using quota restrictions, additional authentication and authorization checks.", "adminConsentDisplayName": "Read and write your organization's device configuration policies", "id": "40b534c3-9552-4550-901b-23879c90bcf9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's device configuration policies on your behalf.  For example, device registration policy can limit initial provisioning controls using quota restrictions, additional authentication and authorization checks.", "consentDisplayName": "Read and write your organization's device configuration policies", "value": "Policy.ReadWrite.DeviceConfiguration"}, {"adminConsentDescription": "Allows the application to read and update the metadata of print jobs on behalf of the signed-in user. Does not allow access to print job document content.", "adminConsentDisplayName": "Read and write basic information of print jobs", "id": "3a0db2f6-0d2a-4c19-971b-49109b19ad3d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update the metadata of print jobs on your behalf. Does not allow access to print job document content.", "consentDisplayName": "Read and write basic information of print jobs", "value": "PrintJob.ReadWriteBasic.All"}, {"adminConsentDescription": "Allows the application to read and update the metadata of print jobs that the signed-in user created. Does not allow access to print job document content.", "adminConsentDisplayName": "Read and write basic information of user's print jobs", "id": "6f2d22f2-1cb6-412c-a17c-3336817eaa82", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read and update the metadata of print jobs that you created. Does not allow access to print job document content.", "consentDisplayName": "Read and write basic information of your print jobs", "value": "PrintJob.ReadWriteBasic"}, {"adminConsentDescription": "Allows the application to read and update the metadata and document content of print jobs on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write print jobs", "id": "036b9544-e8c5-46ef-900a-0646cc42b271", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update the metadata and document content of print jobs on your behalf. ", "consentDisplayName": "Read and update print jobs", "value": "PrintJob.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to read and update the metadata and document content of print jobs that the signed-in user created.", "adminConsentDisplayName": "Read and write user's print jobs", "id": "b81dd597-8abb-4b3f-a07a-820b0316ed04", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read and update the metadata and document content of print jobs that you created.", "consentDisplayName": "Read and update your print jobs", "value": "PrintJob.ReadWrite"}, {"adminConsentDescription": "Allows the application to read the metadata of print jobs on behalf of the signed-in user. Does not allow access to print job document content.", "adminConsentDisplayName": "Read basic information of print jobs", "id": "04ce8d60-72ce-4867-85cf-6d82f36922f3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read the metadata of print jobs on your behalf. Does not allow access to print job document content.", "consentDisplayName": "Read basic information of print jobs", "value": "PrintJob.ReadBasic.All"}, {"adminConsentDescription": "Allows the application to read the metadata of print jobs that the signed-in user created. Does not allow access to print job document content.", "adminConsentDisplayName": "Read basic information of user's print jobs", "id": "6a71a747-280f-4670-9ca0-a9cbf882b274", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read the metadata of print jobs that you created. Does not allow access to print job document content.", "consentDisplayName": "Read basic information of your print jobs", "value": "PrintJob.ReadBasic"}, {"adminConsentDescription": "Allows the application to read the metadata and document content of print jobs on behalf of the signed-in user. ", "adminConsentDisplayName": "Read print jobs", "id": "afdd6933-a0d8-40f7-bd1a-b5d778e8624b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read the metadata and document content of print jobs on your behalf. ", "consentDisplayName": "Read print jobs", "value": "PrintJob.Read.All"}, {"adminConsentDescription": "Allows the application to read the metadata and document content of print jobs that the signed-in user created.", "adminConsentDisplayName": "Read user's print jobs", "id": "248f5528-65c0-4c88-8326-876c7236df5e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read the metadata and document content of print jobs that you created.", "consentDisplayName": "Read your print jobs", "value": "PrintJob<PERSON>"}, {"adminConsentDescription": "Allows the application to read and update printer shares on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write printer shares", "id": "06ceea37-85e2-40d7-bec3-91337a46038f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update printer shares on your behalf. ", "consentDisplayName": "Read and update printer shares", "value": "PrinterShare.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to read printer shares on behalf of the signed-in user. ", "adminConsentDisplayName": "Read printer shares", "id": "ed11134d-2f3f-440d-a2e1-411efada2502", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the application to read printer shares on your behalf. ", "consentDisplayName": "Read printer shares", "value": "PrinterShare.Read.All"}, {"adminConsentDescription": "Allows the application to read and update printers on behalf of the signed-in user. Does not allow creating (registering) or deleting (unregistering) printers.", "adminConsentDisplayName": "Read and update printers", "id": "89f66824-725f-4b8f-928e-e1c5258dc565", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read and update printers on your behalf. Does not allow creating (registering) or deleting (unregistering) printers.", "consentDisplayName": "Read and update printers", "value": "Printer.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to read printers on behalf of the signed-in user. ", "adminConsentDisplayName": "Read printers", "id": "3a736c8a-018e-460a-b60c-863b2683e8bf", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to read printers on your behalf. ", "consentDisplayName": "Read printers", "value": "Printer.Read.All"}, {"adminConsentDescription": "Allows the application to create (register), read, update, and delete (unregister) printers on behalf of the signed-in user. ", "adminConsentDisplayName": "Register, read, update, and unregister printers", "id": "93dae4bd-43a1-4a23-9a1a-92957e1d9121", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to create (register), read, update, and delete (unregister) printers on your behalf.  ", "consentDisplayName": "Register, read, update, and unregister printers", "value": "Printer.FullControl.All"}, {"adminConsentDescription": "Allows the application to create (register) printers on behalf of the signed-in user. ", "adminConsentDisplayName": "Register printers  ", "id": "90c30bed-6fd1-4279-bf39-714069619721", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the application to create (register) printers on your behalf. ", "consentDisplayName": "Register printers  ", "value": "Printer.Create"}, {"adminConsentDescription": "Allows the app to manage policies related to consent and permission grants for applications, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage consent and permission grant policies", "id": "2672f8bb-fd5e-42e0-85e1-ec764dd2614e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage policies related to consent and permission grants for applications, on behalf of the signed-in user.", "consentDisplayName": "Manage consent and permission grant policies", "value": "Policy.ReadWrite.PermissionGrant"}, {"adminConsentDescription": "Allows the app to read policies related to consent and permission grants for applications, on behalf of the signed-in user.", "adminConsentDisplayName": "Read consent and permission grant policies", "id": "414de6ea-2d92-462f-b120-6e2a809a6d01", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read policies related to consent and permission grants for applications, on your behalf.", "consentDisplayName": "Read consent and permission grant policies", "value": "Policy.Read.PermissionGrant"}, {"adminConsentDescription": "Allows the app to read and write your organization's authorization policy on behalf of the signed-in user.  For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "adminConsentDisplayName": "Read and write your organization's authorization policy", "id": "edd3c878-b384-41fd-95ad-e7407dd775be", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's authorization policy on your behalf. For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "consentDisplayName": "Read and write your organization's authorization policy", "value": "Policy.ReadWrite.Authorization"}, {"adminConsentDescription": "Allows the app to read and write the authentication method policies, on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write authentication method policies", "id": "7e823077-d88e-468f-a337-e18f1f0e6c7c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the authentication method policies for your tenant, on your behalf.", "consentDisplayName": "Read and write your authentication method policies ", "value": "Policy.ReadWrite.AuthenticationMethod"}, {"adminConsentDescription": "Allows the app to read the apps in the app catalogs.", "adminConsentDisplayName": "Read all app catalogs", "id": "88e58d74-d3df-44f3-ad47-e89edf4472e4", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read apps in the app catalogs.", "consentDisplayName": "Read all app catalogs", "value": "AppCatalog.Read.All"}, {"adminConsentDescription": "Allows an app to read a channel's messages in Microsoft Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user channel messages", "id": "767156cb-16ae-4d10-8f8b-41b657c8c8c8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read a channel's messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Read your channel messages", "value": "ChannelMessage.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the authentication flow policies, on behalf of the signed-in user. ", "adminConsentDisplayName": "Read and write authentication flow policies", "id": "edb72de9-4252-4d03-a925-451deef99db7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the authentication flow policies for your tenant, on your behalf.", "consentDisplayName": "Read and write your authentication flow policies", "value": "Policy.ReadWrite.AuthenticationFlows"}, {"adminConsentDescription": "Add and remove members from channels, on behalf of the signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "adminConsentDisplayName": "Add and remove members from channels", "id": "0c3e411a-ce45-4cd1-8f30-f99a3efa7b11", "isEnabled": true, "isAdmin": true, "consentDescription": "Add and remove members from channels, on your behalf. Also allows changing a member's role, for example from owner to non-owner.", "consentDisplayName": "Add and remove members from teams and channels", "value": "ChannelMember.ReadWrite.All"}, {"adminConsentDescription": "Read the members of channels, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the members of channels", "id": "2eadaff8-0bce-4198-a6b9-2cfc35a30075", "isEnabled": true, "isAdmin": true, "consentDescription": "Read the members of channels, on your behalf.", "consentDisplayName": "Read the members of teams and channels", "value": "ChannelMember.Read.All"}, {"adminConsentDescription": "Allows the app to read presence information of all users in the directory on behalf of the signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "adminConsentDisplayName": "Read presence information of all users in your organization", "id": "9c7a330d-35b3-4aa1-963d-cb2b9f927841", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read presence information of all users in the directory on your behalf. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "consentDisplayName": "Read presence information of all users in your organization", "value": "Presence.Read.All"}, {"adminConsentDescription": "Allows the app to read presence information on behalf of the signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "adminConsentDisplayName": "Read user's presence information", "id": "76bc735e-aecd-4a1d-8b4c-2b915deabb79", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your presence information on your behalf. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "consentDisplayName": "Read your presence information", "value": "Presence.Read"}, {"adminConsentDescription": "Allows the app to read and write your organization's consent requests policy on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write consent request policy", "id": "4d135e65-66b8-41a8-9f8b-081452c91774", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's consent request policy on your behalf.", "consentDisplayName": "Read and write consent request policy", "value": "Policy.ReadWrite.ConsentRequest"}, {"adminConsentDescription": "Allows the app to read app consent requests and approvals, and deny or approve those requests on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write consent requests", "id": "497d9dfa-3bd1-481a-baab-90895e54568c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read app consent requests for your approval, and deny or approve those request on your behalf.", "consentDisplayName": "Read and write consent requests", "value": "ConsentRequest.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read consent requests and approvals on behalf of the signed-in user.", "adminConsentDisplayName": "Read consent requests", "id": "f3bfad56-966e-4590-a536-82ecf548ac1e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read consent requests and approvals, on your behalf.", "consentDisplayName": "Read consent requests", "value": "ConsentRequest.Read.All"}, {"adminConsentDescription": "Add and remove members from teams, on behalf of the signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "adminConsentDisplayName": "Add and remove members from teams", "id": "4a06efd2-f825-4e34-813e-82a57b03d1ee", "isEnabled": true, "isAdmin": true, "consentDescription": "Add and remove members from teams, on your behalf. Also allows changing a member's role, for example from owner to non-owner.", "consentDisplayName": "Add and remove members from teams and channels", "value": "TeamMember.ReadWrite.All"}, {"adminConsentDescription": "Read the members of teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the members of teams", "id": "2497278c-d82d-46a2-b1ce-39d4cdde5570", "isEnabled": true, "isAdmin": true, "consentDescription": "Read the members of teams, on your behalf.", "consentDisplayName": "Read the members of teams", "value": "TeamMember.Read.All"}, {"adminConsentDescription": "Read and change all teams' settings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and change teams' settings", "id": "39d65650-9d3e-4223-80db-a335590d027e", "isEnabled": true, "isAdmin": true, "consentDescription": "Read and change all teams' settings, on your behalf.", "consentDisplayName": "Read and change teams' settings", "value": "TeamSettings.ReadWrite.All"}, {"adminConsentDescription": "Read all teams' settings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read teams' settings", "id": "48638b3c-ad68-4383-8ac4-e6880ee6ca57", "isEnabled": true, "isAdmin": true, "consentDescription": "Read all teams' settings, on your behalf.", "consentDisplayName": "Read teams' settings", "value": "TeamSettings.Read.All"}, {"adminConsentDescription": "Read channel names and channel descriptions, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the names and descriptions of channels", "id": "9d8982ae-4365-4f57-95e9-d6032a4c0b87", "isEnabled": true, "isAdmin": false, "consentDescription": "Read channel names and channel descriptions, on your behalf.", "consentDisplayName": "Read the names and descriptions of channels", "value": "Channel.ReadBasic.All"}, {"adminConsentDescription": "Read the names and  descriptions of teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the names and descriptions of teams", "id": "485be79e-c497-4b35-9400-0e3fa7f2a5d4", "isEnabled": true, "isAdmin": false, "consentDescription": "Read the names and  descriptions of teams, on your behalf.", "consentDisplayName": "Read the names and descriptions of teams", "value": "Team.ReadBasic.All"}, {"adminConsentDescription": "Allows the app to read all webhook subscriptions on behalf of the signed-in user.", "adminConsentDisplayName": "Read all webhook subscriptions ", "id": "5f88184c-80bb-4d52-9ff2-757288b2e9b7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all webhook subscriptions on your behalf.", "consentDisplayName": "Read all webhook subscriptions ", "value": "Subscription.Read.All"}, {"adminConsentDescription": "Read and write the names, descriptions, and settings of all channels, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write the names, descriptions, and settings of channels", "id": "d649fb7c-72b4-4eec-b2b4-b15acf79e378", "isEnabled": true, "isAdmin": true, "consentDescription": "Read and write the names, descriptions, and settings of all channels, on your behalf.", "consentDisplayName": "Read and write the names, descriptions, and settings of channels", "value": "ChannelSettings.ReadWrite.All"}, {"adminConsentDescription": "Read all channel names, channel descriptions, and channel settings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read the names, descriptions, and settings of channels", "id": "233e0cf1-dd62-48bc-b65b-b38fe87fcf8e", "isEnabled": true, "isAdmin": true, "consentDescription": "Read all channel names, channel descriptions, and channel settings, on your behalf.", "consentDisplayName": "Read the names, descriptions, and settings of channels", "value": "ChannelSettings.Read.All"}, {"adminConsentDescription": "Delete channels in any team, on behalf of the signed-in user.", "adminConsentDisplayName": "Delete channels", "id": "cc83893a-e232-4723-b5af-bd0b01bcfe65", "isEnabled": true, "isAdmin": true, "consentDescription": "Delete channels in any team, on your behalf.", "consentDisplayName": "Delete channels", "value": "Channel.Delete.All"}, {"adminConsentDescription": "Create channels in any team, on behalf of the signed-in user.", "adminConsentDisplayName": "Create channels", "id": "101147cf-4178-4455-9d58-02b5c164e759", "isEnabled": true, "isAdmin": true, "consentDescription": "Create channels in any team, on your behalf.", "consentDisplayName": "Create channels", "value": "Channel.Create"}, {"adminConsentDescription": "Allows the app to read access packages and related entitlement management resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read all entitlement management resources", "id": "5449aa12-1393-4ea2-a7c7-d0e06c1a56b2", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read access packages and related entitlement management resources that you have access to.", "consentDisplayName": "Read all entitlement management resources", "value": "EntitlementManagement.Read.All"}, {"adminConsentDescription": "Allows the app to read, update and delete identities that are associated with a user's account that the signed-in user has access to. This controls the identities users can sign-in with.", "adminConsentDisplayName": "Manage  user identities", "id": "637d7bec-b31e-4deb-acc9-24275642a2c9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update and delete identities that are associated with a user's account that you have access to. This controls the identities users can sign-in with.", "consentDisplayName": "Manage  user identities", "value": "User.ManageIdentities.All"}, {"adminConsentDescription": "Allows the app to read your organization's devices' configuration information on behalf of the signed-in user.", "adminConsentDisplayName": "Read all devices", "id": "951183d1-1a61-466f-a6d1-1fde911bfd95", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read devices' configuration information on your behalf.", "consentDisplayName": "Read all devices", "value": "Device.Read.All"}, {"adminConsentDescription": "Allows the app to read and write your organization's application configuration policies on behalf of the signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy,  tokenIssuancePolicy and tokenLifetimePolicy.", "adminConsentDisplayName": "Read and write your organization's application configuration policies", "id": "b27add92-efb2-4f16-84f5-8108ba77985c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's application configuration policies on your behalf.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy, tokenIssuancePolicy  and tokenLifetimePolicy.", "consentDisplayName": "Read and write your organization's application configuration policies", "value": "Policy.ReadWrite.ApplicationConfiguration"}, {"adminConsentDescription": "Allows the app to read and write all domain properties on behalf of the signed-in user. Also allows the app to add, verify and remove domains.", "adminConsentDisplayName": "Read and write domains", "id": "0b5d694c-a244-4bde-86e6-eb5cd07730fe", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write all domain properties on your behalf. Also allows the app to add, verify and remove domains.", "consentDisplayName": "Read and write domains", "value": "Domain.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all domain properties on behalf of the signed-in user.", "adminConsentDisplayName": "Read domains.", "id": "2f9ee017-59c1-4f1d-9472-bd5529a7b311", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all domain properties on your behalf.", "consentDisplayName": "Read domains.", "value": "Domain.Read.All"}, {"adminConsentDescription": "Allows the app to be able to send emails from the user’s mailbox using the SMTP AUTH client submission protocol.", "adminConsentDisplayName": "Send emails from mailboxes using SMTP AUTH.", "id": "258f6531-6087-4cc4-bb90-092c5fb3ed3f", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to send emails on your behalf from your mailbox.", "consentDisplayName": "Access to sending emails from your mailbox.", "value": "SMTP.Send"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via POP protocol.", "adminConsentDisplayName": "Read and write access to mailboxes via POP.", "id": "d7b7f2d9-0f45-4ea1-9d42-e50810c06991", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail.", "consentDisplayName": "Read and write access to your mail.", "value": "POP.AccessAsUser.All"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via IMAP protocol.", "adminConsentDisplayName": "Read and write access to mailboxes via IMAP.", "id": "652390e4-393a-48de-9484-05f9b1212954", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail.", "consentDisplayName": "Read and write access to your mail.", "value": "IMAP.AccessAsUser.All"}, {"adminConsentDescription": "Read and write tabs in any team in Microsoft Teams, on behalf of the signed-in user. This does not give access to the content inside the tabs.", "adminConsentDisplayName": "Read and write tabs in Microsoft Teams.", "id": "b98bfd41-87c6-45cc-b104-e2de4f0dafb9", "isEnabled": true, "isAdmin": true, "consentDescription": "Read and write tabs in any team in Microsoft Teams, on your behalf. This does not give access to the content inside the tabs.", "consentDisplayName": "Read and write tabs in Microsoft Teams.", "value": "TeamsTab.ReadWrite.All"}, {"adminConsentDescription": "Read the names and settings of tabs inside any team in Microsoft Teams, on behalf of the signed-in user. This does not give access to the content inside the tabs.", "adminConsentDisplayName": "Read tabs in Microsoft Teams.", "id": "59dacb05-e88d-4c13-a684-59f1afc8cc98", "isEnabled": true, "isAdmin": true, "consentDescription": "Read the names and settings of tabs inside any team in Microsoft Teams, on your behalf. This does not give access to the content inside the tabs.", "consentDisplayName": "Read tabs in Microsoft Teams.", "value": "TeamsTab.Read.All"}, {"adminConsentDescription": "Allows the app to create tabs in any team in Microsoft Teams, on behalf of the signed-in user. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "adminConsentDisplayName": "Create tabs in Microsoft Teams.", "id": "a9ff19c2-f369-4a95-9a25-ba9d460efc8e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create tabs in any team in Microsoft Teams, on your behalf. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "consentDisplayName": "Create tabs in Microsoft Teams.", "value": "TeamsTab.Create"}, {"adminConsentDescription": "Allows the app to read the signed-in user's authentication methods, including phone numbers and Authenticator app settings. This does not allow the app to see secret information like the signed-in user's passwords, or to sign-in  or otherwise use the signed-in user's authentication methods.", "adminConsentDisplayName": "Read user authentication methods.", "id": "1f6b61c5-2f65-4135-9c9f-31c0f8d32b52", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your authentication methods, including phone numbers and Authenticator app settings. This does not allow the app to see secret information like your passwords, or to sign-in or otherwise use your authentication methods.", "consentDisplayName": "Read your authentication methods.", "value": "UserAuthenticationMethod.Read"}, {"adminConsentDescription": "Allows the app to read authentication methods of all users in your organization that the signed-in user has access to. Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "adminConsentDisplayName": "Read all users' authentication methods", "id": "aec28ec7-4d02-4e8c-b864-50163aea77eb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read authentication methods of all users you have access to in your organization. Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "consentDisplayName": "Read all users' authentication methods", "value": "UserAuthenticationMethod.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the signed-in user's authentication methods, including phone numbers and Authenticator app settings.                       This does not allow the app to see secret information like the signed-in user's passwords, or                      to sign-in or otherwise use the signed-in user's authentication methods.  ", "adminConsentDisplayName": "Read and write user authentication methods", "id": "48971fc1-70d7-4245-af77-0beb29b53ee2", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your authentication methods, including phone numbers and Authenticator app settings.This does not allow the app to see secret information like your passwords, or to sign-in or otherwise use your authentication methods.", "consentDisplayName": "Read and write your authentication methods", "value": "UserAuthenticationMethod.ReadWrite"}, {"adminConsentDescription": " Allows the app to read and write authentication methods of all users in your organization that the signed-in user has access to.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This                      does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "adminConsentDisplayName": "Read and write all users' authentication methods.", "id": "b7887744-6746-4312-813d-72daeaee7e2d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write authentication methods of all users you have access to in your organization.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow                      the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "consentDisplayName": "Read and write all users' authentication methods", "value": "UserAuthenticationMethod.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to manage schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write user schedule items", "id": "63f27281-c9d9-4f29-94dd-6942f7f1feb0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on your behalf.", "consentDisplayName": "Read and write your schedule items", "value": "Schedule.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on behalf of the signed-in user.", "adminConsentDisplayName": "Read user schedule items", "id": "fccf6dd8-5706-49fa-811f-69e2e1b585d0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on your behalf.", "consentDisplayName": "Read your schedule items", "value": "Schedule.Read.All"}, {"adminConsentDescription": "Allows an app to read your organization's threat assessment requests on behalf of the signed-in user. Also allows the app to create new requests to assess threats received by your organization on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write threat assessment requests", "id": "cac97e40-6730-457d-ad8d-4852fddab7ad", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows an app to read your organization's threat assessment requests on your behalf. Also allows the app to create new requests to assess threats received by your organization on your behalf.", "consentDisplayName": "Read and write threat assessment requests", "value": "ThreatAssessment.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to list groups, read basic properties, read and update the membership of the groups the signed-in user has access to. Group properties and owners cannot be updated and groups cannot be deleted.", "adminConsentDisplayName": "Read and write group memberships", "id": "f81125ac-d3b7-4573-a3b2-7099cc39df9e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to list groups, read basic properties, read and update the membership of your groups. Group properties and owners cannot be updated and groups cannot be deleted.", "consentDisplayName": "Read and write group memberships", "value": "GroupMember.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to list groups, read basic group properties and read membership of all groups the signed-in user has access to.", "adminConsentDisplayName": "Read group memberships", "id": "bc024368-1153-4739-b217-4326f2e966d0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to list groups, read basic group properties and read membership of all your groups.", "consentDisplayName": "Read group memberships", "value": "GroupMember.Read.All"}, {"adminConsentDescription": "Allows the app to read basic BitLocker key properties on behalf of the signed-in user, for their owned devices. Does not allow read of the recovery key itself.", "adminConsentDisplayName": "Read BitLocker keys basic information", "id": "5a107bfc-4f00-4e1a-b67e-66451267bc68", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read basic BitLocker key properties for your owned devices. Does not allow read of the recovery key itself.", "consentDisplayName": "Read your BitLocker keys basic information", "value": "BitlockerKey.ReadBasic.All"}, {"adminConsentDescription": "Allows the app to read BitLocker keys on behalf of the signed-in user, for their owned devices. Allows read of the recovery key.", "adminConsentDisplayName": "Read BitLocker keys", "id": "b27a61ec-b99c-4d6a-b126-c4375d08ae30", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read BitLocker keys for your owned devices. Allows read of the recovery key.", "consentDisplayName": "Read your BitLocker keys", "value": "BitlockerKey.Read.All"}, {"adminConsentDescription": "Allows the app to create, read, update and delete applications and service principals on behalf of the signed-in user. Does not allow management of consent grants.", "adminConsentDisplayName": "Read and write all applications", "id": "bdfbf15f-ee85-4955-8675-146e8e5296b5", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create, read, update and delete applications and service principals on your behalf. Does not allow management of consent grants.", "consentDisplayName": "Read and write applications", "value": "Application.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read applications and service principals on behalf of the signed-in user.", "adminConsentDisplayName": "Read applications", "id": "c79f8feb-a9db-4090-85f9-90d820caa0eb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read applications and service principals on your behalf.", "consentDisplayName": "Read applications", "value": "Application.Read.All"}, {"adminConsentDescription": "Allows the app to send, read, update and delete user’s notifications.", "adminConsentDisplayName": "Deliver and manage user's notifications", "id": "26e2f3e8-b2a1-47fc-9620-89bb5b042024", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to send, read, update and delete your app-specific notifications.", "consentDisplayName": "Deliver and manage your notifications", "value": "UserNotification.ReadWrite.CreatedByApp"}, {"adminConsentDescription": "Allows the app to request access to and management of access packages and related entitlement management resources on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write entitlement management resources", "id": "ae7a573d-81d7-432b-ad44-4ed5c9d89038", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to request access to and management of access packages and related entitlement management resources that you have access to.", "consentDisplayName": "Read and write entitlement management resources", "value": "EntitlementManagement.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to manage organization places (conference rooms and room lists) for calendar events and other applications, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write organization places", "id": "4c06a06a-098a-4063-868e-5dfee3827264", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage organization places (conference rooms and room lists) for calendar events and other applications, on your behalf.", "consentDisplayName": "Read and write organization places", "value": "Place.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to send channel messages in Microsoft Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Send channel messages", "id": "ebf0f66e-9fb1-49e4-a278-222f76911cf4", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to send channel messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Send channel messages", "value": "ChannelMessage.Send"}, {"adminConsentDescription": "Allows an app to edit channel messages in Microsoft Teams, on behalf of the signed-in user.", "adminConsentDisplayName": "Edit user's channel messages", "id": "2b61aa8a-6d36-4b2f-ac7b-f29867937c53", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to edit channel messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Edit your channel messages", "value": "ChannelMessage.Edit"}, {"adminConsentDescription": "Allow the app to read external datasets and content, on behalf of the signed-in user.", "adminConsentDisplayName": "Read items in external datasets", "id": "922f9392-b1b7-483c-a4be-0089be7704fb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read external datasets and content that you have access to.", "consentDisplayName": "Read items in external datasets", "value": "ExternalItem.Read.All"}, {"adminConsentDescription": "Allows the app to read all the indicators for your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all threat indicators", "id": "9cc427b4-2004-41c5-aa22-757b755e9796", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all the indicators for your organization, on your behalf.", "consentDisplayName": "Read all threat indicators", "value": "ThreatIndicators.Read.All"}, {"adminConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write privileged access to Azure AD groups", "id": "32531c59-1f32-461f-b8df-6f8a3b89f73b", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on your behalf.", "consentDisplayName": "Read and write privileged access to Azure AD groups", "value": "PrivilegedAccess.ReadWrite.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) on behalf of the signed-in user.", "adminConsentDisplayName": "Read privileged access to Azure resources", "id": "1d89d70c-dcac-4248-b214-903c457af83a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) on your behalf.", "consentDisplayName": "Read privileged access to your Azure resources", "value": "PrivilegedAccess.Read.AzureResources"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Read privileged access to Azure AD groups", "id": "d329c81c-20ad-4772-abf9-3f6fdb7e5988", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on your behalf.", "consentDisplayName": "Read privileged access to Azure AD groups", "value": "PrivilegedAccess.Read.AzureADGroup"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles, on behalf of the signed-in user.", "adminConsentDisplayName": "Read privileged access to Azure AD", "id": "b3a539c9-59cb-4ad5-825a-041ddbdc2bdb", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles, on your behalf.", "consentDisplayName": "Read privileged access to Azure AD", "value": "PrivilegedAccess.Read.AzureAD"}, {"adminConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation of user privileges to manage Azure resources (like subscriptions, resource groups, storage, compute) on behalf of the signed-in users.", "adminConsentDisplayName": "Read and write privileged access to Azure resources", "id": "a84a9652-ffd3-496e-a991-22ba5529156a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation of user privileges to manage  your Azure resources (like your subscriptions, resource groups, storage, compute) on your behalf.", "consentDisplayName": "Read and write privileged access to Azure resources", "value": "PrivilegedAccess.ReadWrite.AzureResources"}, {"adminConsentDescription": "Allows the app to read the signed-in user's teamwork activity feed.", "adminConsentDisplayName": "Read user's teamwork activity feed", "id": "0e755559-83fb-4b44-91d0-4cc721b9323e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your teamwork activity feed.", "consentDisplayName": "Read your teamwork activity feed", "value": "TeamsActivity.Read"}, {"adminConsentDescription": "Allows the app to read and create online meetings on behalf of the signed-in user.", "adminConsentDisplayName": "Read and create user's online meetings", "id": "a65f2972-a4f8-4f5e-afd7-69ccb046d5dc", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read and create online meetings on your behalf.", "consentDisplayName": "Read and create your online meetings", "value": "OnlineMeetings.ReadWrite"}, {"adminConsentDescription": "Allows the app to read online meeting details on behalf of the signed-in user.", "adminConsentDisplayName": "Read user's online meetings", "id": "9be106e1-f4e3-4df5-bdff-e4bc531cbe43", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read online meeting details on your behalf.", "consentDisplayName": "Read your online meetings", "value": "OnlineMeetings.Read"}, {"adminConsentDescription": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), on behalf of the signed in user.", "adminConsentDisplayName": "Manage all delegated permission grants", "id": "41ce6ca6-6826-4807-84f1-1c82854f7ee5", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), on your behalf. ", "consentDisplayName": "Manage all delegated permission grants", "value": "DelegatedPermissionGrant.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage app permission grants and app role assignments", "id": "84bccea3-f856-4a8a-967b-dbe0a3d53a64", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, on your behalf.", "consentDisplayName": "Manage app permission grants and app role assignments", "value": "AppRoleAssignment.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all organizational contacts on behalf of the signed-in user.  These contacts are managed by the organization and are different from a user's personal contacts.", "adminConsentDisplayName": "Read organizational contacts", "id": "08432d1b-5911-483c-86df-7980af5cdee0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read all organizational contacts on your behalf.  These contacts are managed by the organization and are different from your personal contacts.", "consentDisplayName": "Read organizational contacts", "value": "OrgContact.Read.All"}, {"adminConsentDescription": "Allows the app to read or write your organization's user flows, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all identity user flows", "id": "281892cc-4dbf-4e3a-b6cc-b21029bb4e82", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read or write your organization's user flows, on your behalf.", "consentDisplayName": "Read and write all identity user flows", "value": "IdentityUserFlow.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your organization's user flows, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all identity user flows", "id": "2903d63d-4611-4d43-99ce-a33f3f52e343", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's user flows, on your behalf.", "consentDisplayName": "Read all identity user flows", "value": "IdentityUserFlow.Read.All"}, {"adminConsentDescription": "Allows the app to read and write financials data on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write financials data", "id": "f534bf13-55d4-45a9-8f3c-c92fe64d6131", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read and write financials data on your behalf.", "consentDisplayName": "Read and write financials data", "value": "Financials.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via Exchange ActiveSync.", "adminConsentDisplayName": "Access mailboxes via Exchange ActiveSync", "id": "ff91d191-45a0-43fd-b837-bd682c4a0b0f", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app full access to your mailboxes on your behalf.", "consentDisplayName": "Access your mailboxes", "value": "EAS.AccessAsUser.All"}, {"adminConsentDescription": "Allows an app to read, write and manage bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user.", "adminConsentDisplayName": "Manage bookings information", "id": "7f36b48e-542f-4d3b-9bcb-8406f0ab9fdb", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read, write and manage bookings appointments, businesses, customers, services, and staff on your behalf.", "consentDisplayName": "Manage bookings information", "value": "Bookings.Manage.All"}, {"adminConsentDescription": "Allows an app to read and write bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user. Does not allow create, delete and publish of booking businesses.", "adminConsentDisplayName": "Read and write bookings information", "id": "948eb538-f19d-4ec5-9ccc-f059e1ea4c72", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read and write Bookings appointments, businesses, customers, services, and staff on your behalf. Does not allow create, delete and publish of booking businesses.", "consentDisplayName": "Read and write bookings information", "value": "Bookings.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read and write bookings appointments and customers, and additionally allows read businesses information, services, and staff on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write booking appointments", "id": "02a5a114-36a6-46ff-a102-954d89d9ab02", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read and write bookings appointments and customers, and additionally allows read businesses information, services, and staff on your behalf.", "consentDisplayName": "Read and write booking appointments", "value": "BookingsAppointment.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user.", "adminConsentDisplayName": "Read bookings information", "id": "33b1df99-4b29-4548-9339-7a7b83eaeebc", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read bookings appointments, businesses, customers, services, and staff on your behalf.", "consentDisplayName": "Read bookings information", "value": "Bookings.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization’s identity (authentication) providers’ properties on behalf of the user.", "adminConsentDisplayName": "Read identity providers", "id": "43781733-b5a7-4d1b-98f4-e8edff23e1a9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization’s identity (authentication) providers’ properties on your behalf.", "consentDisplayName": "Read identity providers", "value": "IdentityProvider.Read.All"}, {"adminConsentDescription": "Allows the app to read and write your organization’s identity (authentication) providers’ properties on behalf of the user.", "adminConsentDisplayName": "Read and write identity providers", "id": "f13ce604-1677-429f-90bd-8a10b9f01325", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization’s identity (authentication) providers’ properties on your behalf.", "consentDisplayName": "Read and write identity providers", "value": "IdentityProvider.ReadWrite.All"}, {"adminConsentDescription": "Allows the application to have full control of all site collections on behalf of the signed-in user.", "adminConsentDisplayName": "Have full control of all site collections", "id": "5a54b8b3-347c-476d-8f8e-42d5c7424d29", "isEnabled": true, "isAdmin": true, "consentDescription": "Allow the application to have full control of all site collections on your behalf.", "consentDisplayName": "Have full control of all your site collections", "value": "Sites.FullControl.All"}, {"adminConsentDescription": "Allows the application to create or delete document libraries and lists in all site collections on behalf of the signed-in user.", "adminConsentDisplayName": "Create, edit, and delete items and lists in all site collections", "id": "65e50fdc-43b7-4915-933e-e8138f11f40a", "isEnabled": true, "isAdmin": false, "consentDescription": "Allow the application to create or delete document libraries and lists in all site collections on your behalf.", "consentDisplayName": "Create, edit, and delete items and lists in all your site collections", "value": "Sites.Manage.All"}, {"adminConsentDescription": "Allows the app to read a ranked list of relevant people of the signed-in user. The list includes local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "adminConsentDisplayName": "Read users' relevant people lists", "id": "ba47897c-39ec-4d83-8086-ee8256fa737d", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read a list of people in the order that's most relevant to you. This includes your local contacts, your contacts from social networking, people listed in your organization's directory, and people from recent communications.", "consentDisplayName": "Read your relevant people list", "value": "People.Read"}, {"adminConsentDescription": "Allows the app to read the memberships of hidden groups and administrative units on behalf of the signed-in user, for those hidden groups and administrative units that the signed-in user has access to.", "adminConsentDisplayName": "Read hidden memberships", "id": "f6a3db3e-f7e8-4ed2-a414-557c8c9830be", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the memberships of hidden groups or administrative units on your behalf, for those hidden groups or adminstrative units that you have access to.", "consentDisplayName": "Read your hidden memberships", "value": "Member<PERSON><PERSON><PERSON>"}, {"adminConsentDescription": "Allows the app to read identity risk event information for all users in your organization on behalf of the signed-in user. ", "adminConsentDisplayName": "Read identity risk event information", "id": "8f6a01e7-0391-4ee5-aa22-a3af122cef27", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read identity risk event information for all users in your organization on behalf of the signed-in user. ", "consentDisplayName": "Read identity risk event information", "value": "IdentityRiskEvent.Read.All"}, {"adminConsentDescription": "Allows the app to see your users' basic profile (e.g., name, picture, user name, email address)", "adminConsentDisplayName": "View users' basic profile", "id": "14dad69e-099b-42c9-810b-d002981feec1", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to see your basic profile (e.g., name, picture, user name, email address)", "consentDisplayName": "View your basic profile", "value": "profile"}, {"adminConsentDescription": "Allows the app to read your users' primary email address", "adminConsentDisplayName": "View users' email address", "id": "64a6cdd6-aab1-4aaf-94b8-3cc8405e90d0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your primary email address", "consentDisplayName": "View your email address", "value": "email"}, {"adminConsentDescription": "Allows users to sign in to the app with their work or school accounts and allows the app to see basic user profile information.", "adminConsentDisplayName": "Sign users in", "id": "37f7f235-527c-4136-accd-4a02d197296e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows you to sign in to the app with your work or school account and allows the app to read your basic profile information.", "consentDisplayName": "Sign in as you", "value": "openid"}, {"adminConsentDescription": "Allows the application to read documents and list  items in all site collections on behalf of the signed-in user", "adminConsentDisplayName": "Read items in all site collections", "id": "205e70e5-aba6-4c52-a976-6d2d46c48043", "isEnabled": true, "isAdmin": false, "consentDescription": "Allow the application to read documents and list items in all site collections on your behalf", "consentDisplayName": "Read items in all site collections", "value": "Sites.Read.All"}, {"adminConsentDescription": "Allows the app to read, create, update and delete all files the signed-in user can access.", "adminConsentDisplayName": "Have full access to all files user can access", "id": "863451e7-0667-486c-a5d6-d135439485f0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, create, update and delete all files that you can access.", "consentDisplayName": "Have full access to all files you have access to", "value": "Files.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read all files the signed-in user can access.", "adminConsentDisplayName": "Read all files that user can access", "id": "df85f4d6-205c-4ac5-a5ea-6bf408dba283", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read all files you can access.", "consentDisplayName": "Read all files that you have access to", "value": "Files.Read.All"}, {"adminConsentDescription": "Allows the app to read, create, update and delete the signed-in user's files.", "adminConsentDisplayName": "Have full access to user files", "id": "5c28f0bf-8a70-41f1-8ab2-9032436ddb65", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, create, update, and delete your files.", "consentDisplayName": "Have full access to your files", "value": "Files.ReadWrite"}, {"adminConsentDescription": "Allows the app to read the signed-in user's files.", "adminConsentDisplayName": "Read user files", "id": "10465720-29dd-4523-a11a-6a75c743c9d9", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your files.", "consentDisplayName": "Read your files", "value": "Files.Read"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete user contacts. ", "adminConsentDisplayName": "Have full access to user contacts ", "id": "d56682ec-c09e-4743-aaf4-1a3aac4caa21", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete contacts in your contact folders. ", "consentDisplayName": "Have full access of your contacts ", "value": "Contacts.ReadWrite"}, {"adminConsentDescription": "Allows the app to read user contacts.  ", "adminConsentDisplayName": "Read user contacts ", "id": "ff74d97f-43af-4b68-9f2a-b77ee6968c5d", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read contacts in your contact folders. ", "consentDisplayName": "Read your contacts ", "value": "Contacts.Read"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete events in user calendars. ", "adminConsentDisplayName": "Have full access to user calendars ", "id": "1ec239c2-d7c9-4623-a91a-a9775856bb36", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete events in your calendars. ", "consentDisplayName": "Have full access to your calendars  ", "value": "Calendars.ReadWrite"}, {"adminConsentDescription": "Allows the app to read events in user calendars . ", "adminConsentDisplayName": "Read user calendars ", "id": "465a38f9-76ea-45b9-9f34-9e8b0d4b0b42", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read events in your calendars. ", "consentDisplayName": "Read your calendars ", "value": "Calendars.Read"}, {"adminConsentDescription": "Allows the app to send mail as users in the organization. ", "adminConsentDisplayName": "Send mail as a user ", "id": "e383f46e-2787-4529-855e-0e479a3ffac0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to send mail as you. ", "consentDisplayName": "Send mail as you ", "value": "Mail.Send"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete email in user mailboxes. Does not include permission to send mail. ", "adminConsentDisplayName": "Read and write access to user mail ", "id": "024d486e-b451-40bb-833d-3e66d98c5c73", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail. ", "consentDisplayName": "Read and write access to your mail ", "value": "Mail.ReadWrite"}, {"adminConsentDescription": "Allows the app to have the same access to information in the directory as the signed-in user.", "adminConsentDisplayName": "Access directory as the signed in user", "id": "0e263e50-5827-48a4-b97c-d940288653c7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to have the same access to information in your work or school directory as you do.", "consentDisplayName": "Access the directory as you", "value": "Directory.AccessAsUser.All"}, {"adminConsentDescription": "Allows the app to read and write data in your organization's directory, such as users, and groups.  It does not allow the app to delete users or groups, or reset user passwords.", "adminConsentDisplayName": "Read and write directory data", "id": "c5366453-9fb0-48a5-a156-24f0c49a4b84", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write data in your organization's directory, such as other users, groups.  It does not allow the app to delete users or groups, or reset user passwords.", "consentDisplayName": "Read and write directory data", "value": "Directory.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read data in your organization's directory, such as users, groups and apps.", "adminConsentDisplayName": "Read directory data", "id": "06da0dbc-49e2-44d2-8312-53f166ab848a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read data in your organization's directory.", "consentDisplayName": "Read directory data", "value": "Directory.Read.All"}, {"adminConsentDescription": "Allows the app to create groups and read all group properties and memberships on behalf of the signed-in user.  Additionally allows group owners to manage their groups and allows group members to update group content.", "adminConsentDisplayName": "Read and write all groups", "id": "4e46008b-f24c-477d-8fff-7bb4ec7aafe0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create groups and read all group properties and memberships on your behalf.  Additionally allows the app to manage your groups and to update group content for groups you are a member of.", "consentDisplayName": "Read and write all groups", "value": "Group.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to list groups, and to read their properties and all group memberships on behalf of the signed-in user.  Also allows the app to read calendar, conversations, files, and other group content for all groups the signed-in user can access. ", "adminConsentDisplayName": "Read all groups", "id": "5f8c59db-677d-491f-a6b8-5f174b11ec1d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to list groups, and to read their properties and all group memberships on your behalf.  Also allows the app to read calendar, conversations, files, and other group content for all groups you can access.  ", "consentDisplayName": "Read all groups", "value": "Group.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the full set of profile properties, reports, and managers of other users in your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all users' full profiles", "id": "204e0828-b5ca-4ad8-b9f3-f32a958e7cc4", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the full set of profile properties, reports, and managers of other users in your organization, on your behalf.", "consentDisplayName": "Read and write all users' full profiles", "value": "User.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the full set of profile properties, reports, and managers of other users in your organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all users' full profiles", "id": "a154be20-db9c-4678-8ab7-66f6cc099a59", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the full set of profile properties, reports, and managers of other users in your organization, on your behalf.", "consentDisplayName": "Read all users' full profiles", "value": "User.Read.All"}, {"adminConsentDescription": "Allows the app to read a basic set of profile properties of other users in your organization on behalf of the signed-in user. This includes display name, first and last name, email address and photo.", "adminConsentDisplayName": "Read all users' basic profiles", "id": "b340eb25-3456-403f-be2f-af7a0d370277", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read a basic set of profile properties of other users in your organization on your behalf. Includes display name, first and last name, email address and photo.", "consentDisplayName": "Read all users' basic profiles", "value": "User.ReadBasic.All"}, {"adminConsentDescription": "Allows the app to read your profile. It also allows the app to update your profile information on your behalf.", "adminConsentDisplayName": "Read and write access to user profile", "id": "b4e74841-8e56-480b-be8b-910348b18b4c", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your profile, and discover your group membership, reports and manager. It also allows the app to update your profile information on your behalf.", "consentDisplayName": "Read and update your profile", "value": "User.ReadWrite"}, {"adminConsentDescription": "Allows users to sign-in to the app, and allows the app to read the profile of signed-in users. It also allows the app to read basic company information of signed-in users.", "adminConsentDisplayName": "Sign in and read user profile", "id": "e1fe6dd8-ba31-4d61-89e7-88639da4683d", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows you to sign in to the app with your organizational account and let the app read your profile. It also allows the app to read basic company information.", "consentDisplayName": "Sign you in and read your profile", "value": "User.Read"}, {"adminConsentDescription": "Allows the app to read mail a user can access, including their own and shared mail.", "adminConsentDisplayName": "Read user and shared mail", "id": "7b9103a5-4610-446b-9670-80643382c1fa", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read mail you can access, including shared mail.", "consentDisplayName": "Read mail you can access", "value": "Mail.Read.Shared"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete mail a user has permission to access, including their own and shared mail. Does not include permission to send mail.", "adminConsentDisplayName": "Read and write user and shared mail", "id": "5df07973-7d5d-46ed-9847-1271055cbd51", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create, and delete mail you have permission to access, including your own and shared mail. Does not allow the app to send mail on your behalf.", "consentDisplayName": "Read and write mail you can access", "value": "Mail.ReadWrite.Shared"}, {"adminConsentDescription": "Allows the app to send mail as the signed-in user, including sending on-behalf of others.", "adminConsentDisplayName": "Send mail on behalf of others", "id": "a367ab51-6b49-43bf-a716-a1fb06d2a174", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to send mail as you or on-behalf of someone else.", "consentDisplayName": "Send mail on behalf of others or yourself", "value": "Mail.Send.Shared"}, {"adminConsentDescription": "Allows the app to read events in all calendars that the user can access, including delegate and shared calendars.", "adminConsentDisplayName": "Read user and shared calendars", "id": "2b9c4092-424d-4249-948d-b43879977640", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read events in all calendars that you can access, including delegate and shared calendars. ", "consentDisplayName": "Read calendars you can access", "value": "Calendars.Read.Shared"}, {"adminConsentDescription": "Allows the app to create, read, update and delete events in all calendars in the organization user has permissions to access. This includes delegate and shared calendars.", "adminConsentDisplayName": "Read and write user and shared calendars", "id": "12466101-c9b8-439a-8589-dd09ee67e8e9", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create and delete events in all calendars in your organization you have permissions to access. This includes delegate and shared calendars.", "consentDisplayName": "Read and write to your and shared calendars", "value": "Calendars.ReadWrite.Shared"}, {"adminConsentDescription": "Allows the app to read contacts a user has permissions to access, including their own and shared contacts.", "adminConsentDisplayName": "Read user and shared contacts", "id": "242b9d9e-ed24-4d09-9a52-f43769beb9d4", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read contacts you have permissions to access, including your own and shared contacts.", "consentDisplayName": "Read your and shared contacts", "value": "Contacts.Read.Shared"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete contacts a user has permissions to, including their own and shared contacts.", "adminConsentDisplayName": "Read and write user and shared contacts", "id": "afb6c84b-06be-49af-80bb-8f3f77004eab", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create, and delete contacts you have permissions to access, including your own and shared contacts.", "consentDisplayName": "Read and write to your and shared contacts", "value": "Contacts.ReadWrite.Shared"}, {"adminConsentDescription": "Allows the app to read tasks a user has permissions to access, including their own and shared tasks.", "adminConsentDisplayName": "Read user and shared tasks", "id": "88d21fd4-8e5a-4c32-b5e2-4a1c95f34f72", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read tasks you have permissions to access, including your own and shared tasks.", "consentDisplayName": "Read your and shared tasks", "value": "Tasks.Read.Shared"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete tasks a user has permissions to, including their own and shared tasks.", "adminConsentDisplayName": "Read and write user and shared tasks", "id": "c5ddf11b-c114-4886-8558-8a4e557cd52b", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create, and delete tasks you have permissions to access, including your own and shared tasks.", "consentDisplayName": "Read and write to your and shared tasks", "value": "Tasks.ReadWrite.Shared"}, {"adminConsentDescription": "Allows the application to edit or delete documents and list items in all site collections on behalf of the signed-in user.", "adminConsentDisplayName": "Edit or delete items in all site collections", "id": "89fe6a52-be36-487e-b7d8-d061c450a026", "isEnabled": true, "isAdmin": false, "consentDescription": "Allow the application to edit or delete documents and list items in all site collections on your behalf.", "consentDisplayName": "Edit or delete items in all site collections", "value": "Sites.ReadWrite.All"}, {"adminConsentDescription": "Allows an app to read all service usage reports on behalf of the signed-in user.  Services that provide usage reports include Office 365 and Azure Active Directory.", "adminConsentDisplayName": "Read all usage reports", "id": "02e97553-ed7b-43d0-ab3c-f8bace0d040c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows an app to read all service usage reports on your behalf. Services that provide usage reports include Office 365 and Azure Active Directory.", "consentDisplayName": "Read all usage reports", "value": "Reports.Read.All"}, {"adminConsentDescription": "(Preview) Allows the app to read, create, update and delete files in the application's folder.", "adminConsentDisplayName": "Have full access to the application's folder (preview)", "id": "8019c312-3263-48e6-825e-2b833497195b", "isEnabled": true, "isAdmin": false, "consentDescription": "(Preview) Allows the app to read, create, update and delete files in the application's folder.", "consentDisplayName": "Have full access to the application's folder", "value": "Files.ReadWrite.AppFolder"}, {"adminConsentDescription": "(Preview) Allows the app to read and write files that the user selects. The app has access for several hours after the user selects a file.", "adminConsentDisplayName": "Read and write files that the user selects (preview)", "id": "17dde5bd-8c17-420f-a486-969730c1b827", "isEnabled": true, "isAdmin": false, "consentDescription": "(Preview) Allows the app to read and write files that you select. After you select a file, the app has access to the file for several hours.", "consentDisplayName": "Read and write selected files", "value": "Files.ReadWrite.Selected"}, {"adminConsentDescription": "(Preview) Allows the app to read files that the user selects. The app has access for several hours after the user selects a file.", "adminConsentDisplayName": "Read files that the user selects (preview)", "id": "5447fe39-cb82-4c1a-b977-520e67e724eb", "isEnabled": true, "isAdmin": false, "consentDescription": "(Preview) Allows the app to read files that you select. After you select a file, the app has access to the file for several hours.", "consentDisplayName": "Read selected files", "value": "Files.Read.Selected"}, {"adminConsentDescription": "Allows the app to the read user's mailbox settings. Does not include permission to send mail.", "adminConsentDisplayName": "Read user mailbox settings", "id": "87f447af-9fa4-4c32-9dfa-4a57a73d18ce", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your mailbox settings.", "consentDisplayName": "Read your mailbox settings", "value": "MailboxSettings.Read"}, {"adminConsentDescription": "Allows the app to invite guest users to the organization, on behalf of the signed-in user.", "adminConsentDisplayName": "Invite guest users to the organization", "id": "63dd7cd9-b489-4adf-a28c-ac38b9a0f962", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to invite guest users to the organization, on your behalf.", "consentDisplayName": "Invite guest users to the organization", "value": "User.Invite.All"}, {"adminConsentDescription": "Allows the app to read the titles of OneNote notebooks and sections and to create new pages, notebooks, and sections on behalf of the signed-in user.", "adminConsentDisplayName": "Create user OneNote notebooks", "id": "9d822255-d64d-4b7a-afdb-833b9a97ed02", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to view the titles of your OneNote notebooks and sections and to create new pages, notebooks, and sections on your behalf.", "consentDisplayName": "Create your OneNote notebooks", "value": "Notes.Create"}, {"adminConsentDescription": "This is deprecated!  Do not use! This permission no longer has any effect. You can safely consent to it. No additional privileges will be granted to the app.", "adminConsentDisplayName": "Limited notebook access (deprecated)", "id": "ed68249d-017c-4df5-9113-e684c7f8760b", "isEnabled": true, "isAdmin": false, "consentDescription": "This permission no longer has any effect. You can safely consent to it. No additional privileges will be granted to the app.", "consentDisplayName": "Limited access to your OneNote notebooks for this app (preview)", "value": "Notes.ReadWrite.CreatedByApp"}, {"adminConsentDescription": "Allows the app to read OneNote notebooks on behalf of the signed-in user.", "adminConsentDisplayName": "Read user OneNote notebooks", "id": "371361e4-b9e2-4a3f-8315-2a301a3b0a3d", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read OneNote notebooks on your behalf.", "consentDisplayName": "Read your OneNote notebooks", "value": "Notes.Read"}, {"adminConsentDescription": "Allows the app to read, share, and modify OneNote notebooks on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write user OneNote notebooks", "id": "615e26af-c38a-4150-ae3e-c3b0d4cb1d6a", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, share, and modify OneNote notebooks on your behalf.", "consentDisplayName": "Read and write your OneNote notebooks", "value": "Notes.ReadWrite"}, {"adminConsentDescription": "Allows the app to read OneNote notebooks that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Read all OneNote notebooks that user can access", "id": "dfabfca6-ee36-4db2-8208-7a28381419b3", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read all the OneNote notebooks that you have access to.", "consentDisplayName": "Read all OneNote notebooks that you can access", "value": "Notes.Read.All"}, {"adminConsentDescription": "Allows the app to read, share, and modify OneNote notebooks that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Read and write all OneNote notebooks that user can access", "id": "64ac0503-b4fa-45d9-b544-71a463f05da0", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, share, and modify all the OneNote notebooks that you have access to.", "consentDisplayName": "Read and write all OneNote notebooks that you can access", "value": "Notes.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read a user's list of devices on behalf of the signed-in user.", "adminConsentDisplayName": "Read user devices", "id": "11d4cd79-5ba5-460f-803f-e22c8ab85ccd", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to see your list of devices.", "consentDisplayName": "View your list of devices", "value": "Device.Read"}, {"adminConsentDescription": "Allows the app to launch another app or communicate with another app on a user's device on behalf of the signed-in user.", "adminConsentDisplayName": "Communicate with user devices", "id": "bac3b9c2-b516-4ef4-bd3b-c2ef73d8d804", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to launch another app or communicate with another app on a device that you own.", "consentDisplayName": "Communicate with your other devices", "value": "Device.Command"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete user's mailbox settings. Does not include permission to send mail.", "adminConsentDisplayName": "Read and write user mailbox settings", "id": "818c620a-27a9-40bd-a6a5-d96f7d610b4b", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read, update, create, and delete your mailbox settings.", "consentDisplayName": "Read and write to your mailbox settings", "value": "MailboxSettings.ReadWrite"}, {"adminConsentDescription": "Allows the app to report the signed-in user's app activity information to Microsoft Timeline.", "adminConsentDisplayName": "Write app activity to users' timeline", "id": "367492fc-594d-4972-a9b5-0d58c622c91c", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to report your app activity information to Microsoft Timeline.", "consentDisplayName": "Write app activity to your timeline", "value": "UserTimelineActivity.Write.CreatedByApp"}, {"adminConsentDescription": "Allows the app to read a limited subset of the properties from the structure of schools and classes in an organization's roster and a limited subset of properties about users to be read on behalf of the user. Includes name, status, education role, email address and photo.", "adminConsentDisplayName": "Read a limited subset of users' view of the roster", "id": "5d186531-d1bf-4f07-8cea-7c42119e1bd9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view minimal  information about both schools and classes in your organization and education-related information about you and other users on your behalf.", "consentDisplayName": "View a limited subset of your school, class and user information", "value": "EduRoster.ReadBasic"}, {"adminConsentDescription": "Allows the app to read the structure of schools and classes in an organization's roster and education-specific information about users to be read on behalf of the user.", "adminConsentDisplayName": "Read users' view of the roster", "id": "a4389601-22d9-4096-ac18-36a927199112", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view information about schools and classes in your organization and education-related information about you and other users on your behalf.", "consentDisplayName": "View your school, class and user information", "value": "EduRoster.Read"}, {"adminConsentDescription": "Allows the app to read and write the structure of schools and classes in an organization's roster and education-specific information about users to be read and written on behalf of the user.", "adminConsentDisplayName": "Read and write users' view of the roster", "id": "359e19a6-e3fa-4d7f-bcab-d28ec592b51e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view and modify information about schools and classes in your organization and education-related information about you and other users on your behalf.", "consentDisplayName": "View and modify your school, class and user information", "value": "EduRoster.ReadWrite"}, {"adminConsentDescription": "Allows the app to read assignments without grades on behalf of the user.", "adminConsentDisplayName": "Read users' class assignments without grades", "id": "c0b0103b-c053-4b2e-9973-9f3a544ec9b8", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view your assignments on your behalf without seeing grades.", "consentDisplayName": "View your assignments without grades", "value": "EduAssignments.ReadBasic"}, {"adminConsentDescription": "Allows the app to read and write assignments without grades on behalf of the user.", "adminConsentDisplayName": "Read and write users' class assignments without grades", "id": "2ef770a1-622a-47c4-93ee-28d6adbed3a0", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view and modify your assignments on your behalf without seeing grades.", "consentDisplayName": "View and modify your assignments without grades", "value": "EduAssignments.ReadWriteBasic"}, {"adminConsentDescription": "Allows the app to read assignments and their grades on behalf of the user.", "adminConsentDisplayName": "Read users' class assignments and their grades", "id": "091460c9-9c4a-49b2-81ef-1f3d852acce2", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view your assignments on your behalf including grades.", "consentDisplayName": "View your assignments and grades", "value": "EduAssignments.Read"}, {"adminConsentDescription": "Allows the app to read and write assignments and their grades on behalf of the user.", "adminConsentDisplayName": "Read and write users' class assignments and their grades", "id": "2f233e90-164b-4501-8bce-31af2559a2d3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view and modify your assignments on your behalf including  grades.", "consentDisplayName": "View and modify your assignments and grades", "value": "EduAssignments.ReadWrite"}, {"adminConsentDescription": "Read the state and settings of all Microsoft education apps on behalf of the user.", "adminConsentDisplayName": "Read education app settings", "id": "8523895c-6081-45bf-8a5d-f062a2f12c9f", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to view the state and settings of all Microsoft education apps on your behalf.", "consentDisplayName": "View your education app settings", "value": "EduAdministration.Read"}, {"adminConsentDescription": "Manage the state and settings of all Microsoft education apps on behalf of the user.", "adminConsentDisplayName": "Manage education app settings", "id": "63589852-04e3-46b4-bae9-15d5b1050748", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage the state and settings of all Microsoft education apps on your behalf.", "consentDisplayName": "Manage your education app settings", "value": "EduAdministration.ReadWrite"}, {"adminConsentDescription": "Allows the app to read a scored list of relevant people of the signed-in user or other users in the signed-in user's organization. The list can include local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "adminConsentDisplayName": "Read all users' relevant people lists", "id": "b89f9189-71a5-4e70-b041-9887f0bc7e4a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read a list of people in the order that is most relevant to you. Allows the app to read a list of people in the order that is most relevant to another user in your organization. These can include local contacts, contacts from social networking, people listed in your organization’s directory, and people from recent communications.", "consentDisplayName": "Read all users’ relevant people lists", "value": "People.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization’s security events on behalf of the signed-in user. Also allows the app to update editable properties in security events on behalf of the signed-in user.", "adminConsentDisplayName": "Read and update your organization’s security events", "id": "6aedf524-7e1c-45a7-bd76-ded8cab8d0fc", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization’s security events on your behalf. Also allows you to update editable properties in security events.", "consentDisplayName": "Read and update your organization’s security events", "value": "SecurityEvents.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your organization’s security events on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization’s security events", "id": "64733abd-851e-478a-bffb-e47a14b18235", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization’s security events on your behalf.", "consentDisplayName": "Read your organization’s security events", "value": "SecurityEvents.Read.All"}, {"adminConsentDescription": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration.", "adminConsentDisplayName": "Read Microsoft Intune configuration", "id": "8696daa5-bce5-4b2e-83f9-51b6defc4e1e", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration.", "consentDisplayName": "Read Microsoft Intune configuration", "value": "DeviceManagementServiceConfig.Read.All"}, {"adminConsentDescription": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration.", "adminConsentDisplayName": "Read and write Microsoft Intune configuration", "id": "662ed50a-ac44-4eef-ad86-62eed9be2a29", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration.", "consentDisplayName": "Read and write Microsoft Intune configuration", "value": "DeviceManagementServiceConfig.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune.", "adminConsentDisplayName": "Perform user-impacting remote actions on Microsoft Intune devices", "id": "3404d2bf-2b13-457e-a330-c24615765193", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune.", "consentDisplayName": "Perform user-impacting remote actions on Microsoft Intune devices", "value": "DeviceManagementManagedDevices.PrivilegedOperations.All"}, {"adminConsentDescription": "Allows the app to read and write the properties of devices managed by Microsoft Intune. Does not allow high impact operations such as remote wipe and password reset on the device’s owner.", "adminConsentDisplayName": "Read and write Microsoft Intune devices", "id": "44642bfe-8385-4adc-8fc6-fe3cb2c375c3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the properties of devices managed by Microsoft Intune. Does not allow high impact operations such as remote wipe and password reset on the device’s owner.", "consentDisplayName": "Read and write Microsoft Intune devices", "value": "DeviceManagementManagedDevices.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the properties of devices managed by Microsoft Intune.", "adminConsentDisplayName": "Read Microsoft Intune devices", "id": "314874da-47d6-4978-88dc-cf0d37f0bb82", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the properties of devices managed by Microsoft Intune.", "consentDisplayName": "Read devices Microsoft Intune devices", "value": "DeviceManagementManagedDevices.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "adminConsentDisplayName": "Read and write Microsoft Intune RBAC settings", "id": "0c5e8a55-87a6-4556-93ab-adc52c4d862d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "consentDisplayName": "Read and write Microsoft Intune RBAC settings", "value": "DeviceManagementRBAC.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "adminConsentDisplayName": "Read Microsoft Intune RBAC settings", "id": "49f0cc30-024c-4dfd-ab3e-82e137ee5431", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "consentDisplayName": "Read Microsoft Intune RBAC settings", "value": "DeviceManagementRBAC.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "adminConsentDisplayName": "Read and write Microsoft Intune apps", "id": "7b3f05d5-f68c-4b8d-8c59-a2ecd12f24af", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "consentDisplayName": "Read and write Microsoft Intune apps", "value": "DeviceManagementApps.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "adminConsentDisplayName": "Read Microsoft Intune apps", "id": "4edf5f54-4666-44af-9de9-0144fb4b6e8c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "consentDisplayName": "Read Microsoft Intune apps", "value": "DeviceManagementApps.Read.All"}, {"adminConsentDescription": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "adminConsentDisplayName": "Read and write Microsoft Intune Device Configuration and Policies", "id": "0883f392-0a7a-443d-8c76-16a6d39c7b63", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "consentDisplayName": "Read and write Microsoft Intune Device Configuration and Policies", "value": "DeviceManagementConfiguration.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "adminConsentDisplayName": "Read Microsoft Intune Device Configuration and Policies", "id": "f1493658-876a-4c87-8fa7-edb559b3476a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "consentDisplayName": "Read Microsoft Intune Device Configuration and Policies", "value": "DeviceManagementConfiguration.Read.All"}, {"adminConsentDescription": "Allows the app to read and report the signed-in user's activity in the app.", "adminConsentDisplayName": "Read and write app activity to users' activity feed", "id": "47607519-5fb1-47d9-99c7-da4b48f369b1", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read and report your activity in the app.", "consentDisplayName": "Read and write app activity to your activity feed", "value": "UserActivity.ReadWrite.CreatedByApp"}, {"adminConsentDescription": "Allows the app to read and query your audit log activities, on behalf of the signed-in user.", "adminConsentDisplayName": "Read audit log data", "id": "e4c9e354-4dc5-45b8-9e7c-e1393b0b1a20", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and query your audit log activities, on your behalf.", "consentDisplayName": "Read audit log data", "value": "AuditLog.Read.All"}, {"adminConsentDescription": "Allows the app to read terms of use acceptance statuses on behalf of the signed-in user.", "adminConsentDisplayName": "Read terms of use acceptance statuses that user can access", "id": "a66a5341-e66e-4897-9d52-c2df58c2bfb9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read terms of use acceptance statuses on your behalf.", "consentDisplayName": "Read all terms of use acceptance statuses", "value": "AgreementAcceptance.Read.All"}, {"adminConsentDescription": "Allows the app to read terms of use acceptance statuses on behalf of the signed-in user.", "adminConsentDisplayName": "Read user terms of use acceptance statuses", "id": "0b7643bb-5336-476f-80b5-18fbfbc91806", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your terms of use acceptance statuses.", "consentDisplayName": "Read your terms of use acceptance statuses", "value": "AgreementAcceptance.Read"}, {"adminConsentDescription": "Allows the app to read and write terms of use agreements on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write all terms of use agreements", "id": "ef4b5d93-3104-4664-9053-a5c49ab44218", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write terms of use agreements on your behalf.", "consentDisplayName": "Read and write all terms of use agreements", "value": "Agreement.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read terms of use agreements on behalf of the signed-in user.", "adminConsentDisplayName": "Read all terms of use agreements", "id": "af2819c9-df71-4dd3-ade7-4d7c9dc653b7", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read terms of use agreements on your behalf.", "consentDisplayName": "Read all terms of use agreements", "value": "Agreement.Read.All"}, {"adminConsentDescription": "Allows the app to request and manage just in time elevation (including scheduled elevation) of users to Azure AD built-in administrative roles, on behalf of signed-in users.", "adminConsentDisplayName": "Read and write privileged access to Azure AD", "id": "3c3c74f5-cdaa-4a97-b7e0-4e788bfcfb37", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to request and manage just in time elevation (including scheduled elevation) of users to Azure AD built-in administrative roles, on your behalf.", "consentDisplayName": "Read and write privileged access to Azure AD", "value": "PrivilegedAccess.ReadWrite.AzureAD"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete apps in the app catalogs.", "adminConsentDisplayName": "Read and write to all app catalogs", "id": "1ca167d5-1655-44a1-8adf-1414072e1ef9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create, read, update, and delete apps in the app catalogs.", "consentDisplayName": "Read and write to all app catalogs", "value": "AppCatalog.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on programs and program controls that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Manage all programs that user can access", "id": "50fd364f-9d93-4ae1-b170-300e87cccf84", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update and perform action on programs and program controls that you have access to.", "consentDisplayName": "Manage programs that you can access", "value": "ProgramControl.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read programs and program controls that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Read all programs that user can access", "id": "c492a2e1-2f8f-4caa-b076-99bbf6e40fe4", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read information on programs and program controls that you have access to.", "consentDisplayName": "Read programs that you can access", "value": "ProgramControl.Read.All"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Manage all access reviews that user can access", "id": "e4aa47b9-9a69-4109-82ed-36ec70d85ff1", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update and perform action on access reviews, reviewers, decisions and settings that you have access to.", "consentDisplayName": "Manage access reviews that you can access", "value": "AccessReview.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read access reviews, reviewers, decisions and settings that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Read all access reviews that user can access", "id": "ebfcd32b-babb-40f4-a14b-42706e83bd28", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read information on access reviews, reviewers, decisions and settings that you have access to.", "consentDisplayName": "Read access reviews that you can access", "value": "AccessReview.Read.All"}, {"adminConsentDescription": "Allows the app to read your organization's policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization's policies", "id": "572fea84-0151-49b2-9301-11cb16974376", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your organization's policies on your behalf.", "consentDisplayName": "Read your organization's policies", "value": "Policy.Read.All"}, {"adminConsentDescription": "Allows the app to read and write your organization's conditional access policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's conditional access policies", "id": "ad902697-1014-4ef5-81ef-2b4301988e8c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's conditional access policies on your behalf.", "consentDisplayName": "Read and write your organization's conditional access policies", "value": "Policy.ReadWrite.ConditionalAccess"}, {"adminConsentDescription": "Allows the app to deliver its notifications on behalf of signed-in users. Also allows the app to read, update, and delete the user's notification items for this app.", "adminConsentDisplayName": "Deliver and manage user notifications for this app", "id": "89497502-6e42-46a2-8cb2-427fd3df970a", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to deliver its notifications, on your behalf. Also allows the app to read, update, and delete your notification items for this app.", "consentDisplayName": "Deliver and manage your notifications for this app", "value": "Notifications.ReadWrite.CreatedByApp"}, {"adminConsentDescription": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "adminConsentDisplayName": "Export user's data", "id": "405a51b5-8d8d-430b-9842-8be4b0e9f324", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "consentDisplayName": "Export user's data", "value": "User.Export.All"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via Exchange Web Services.", "adminConsentDisplayName": "Access mailboxes as the signed-in user via Exchange Web Services", "id": "9769c687-087d-48ac-9cb3-c37dde652038", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app full access to your mailboxes on your behalf.", "consentDisplayName": "Access your mailboxes", "value": "EWS.AccessAsUser.All"}, {"adminConsentDescription": "Allows the app to see and update the data you gave it access to, even when users are not currently using the app. This does not give the app any additional permissions.", "adminConsentDisplayName": "Maintain access to data you have given it access to", "id": "7427e0e9-2fba-42fe-b0c0-848c9e6a8182", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to see and update the data you gave it access to, even when you are not currently using the app. This does not give the app any additional permissions.", "consentDisplayName": "Maintain access to data you have given it access to", "value": "offline_access"}, {"adminConsentDescription": "Allows the app to read the signed-in user's activity statistics, such as how much time the user has spent on emails, in meetings, or in chat sessions.", "adminConsentDisplayName": "Read user activity statistics", "id": "e03cf23f-8056-446a-8994-7d93dfc8b50e", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your activity statistics, such as how much time you've spent on emails, in meetings, or in chat sessions.", "consentDisplayName": "Read your activity statistics", "value": "Analytics.Read"}, {"adminConsentDescription": "Allows the app to read identity risky user information for all users in your organization on behalf of the signed-in user.", "adminConsentDisplayName": "Read identity risky user information", "id": "d04bb851-cb7c-4146-97c7-ca3e71baf56c", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read identity risky user information for all users in your organization on behalf of the signed-in user.", "consentDisplayName": "Read identity risky user information", "value": "IdentityRiskyUser.Read.All"}, {"adminConsentDescription": "Allows the app to read the signed-in user's mailbox.", "adminConsentDisplayName": "Read user mail ", "id": "570282fd-fa5c-430d-a7fd-fc8dc98a9dca", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read email in your mailbox. ", "consentDisplayName": "Read your mail ", "value": "Mail.Read"}, {"adminConsentDescription": "Allows the app to read and update identity risky user information for all users in your organization on behalf of the signed-in user. Update operations include dismissing risky users.", "adminConsentDisplayName": "Read and write risky user information", "id": "e0a7cdbb-08b0-4697-8264-0069786e9674", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update identity risky user information for all users in your organization on your behalf. Update operations include dismissing risky users.", "consentDisplayName": "Read and write identity risky user information", "value": "IdentityRiskyUser.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and update identity risk event information for all users in your organization on behalf of the signed-in user. Update operations include confirming risk event detections. ", "adminConsentDisplayName": "Read and write risk event information", "id": "9e4862a5-b68f-479e-848a-4e07e25c9916", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update identity risk event information for all users in your organization on your behalf. Update operations include confirming risk event detections. ", "consentDisplayName": "Read and write risk event information", "value": "IdentityRiskEvent.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read and write trust framework key set properties on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write trust framework key sets", "id": "39244520-1e7d-4b4a-aee0-57c65826e427", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read or write trust framework key sets, on your behalf.", "consentDisplayName": "Read and write trust framework key sets", "value": "TrustFrameworkKeySet.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read trust framework key set properties on behalf of the signed-in user.", "adminConsentDisplayName": "Read trust framework key sets", "id": "7ad34336-f5b1-44ce-8682-31d7dfcd9ab9", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read trust framework key sets, on your behalf.", "consentDisplayName": "Read trust framework key sets", "value": "TrustFrameworkKeySet.Read.All"}, {"adminConsentDescription": "Allows the app to read and write your organization's trust framework policies on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write your organization's trust framework policies", "id": "cefba324-1a70-4a6e-9c1d-fd670b7ae392", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's trust framework policies on your behalf.", "consentDisplayName": "Read and write trust framework policies", "value": "Policy.ReadWrite.TrustFramework"}, {"adminConsentDescription": "Allows an app to read and write 1 on 1 or group chats threads, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write user chat messages", "id": "9ff7295e-131b-4d94-90e1-69fde507ac11", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read and write your 1 on 1 or group chat messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Read and write your chat messages", "value": "Chat.ReadWrite"}, {"adminConsentDescription": "Allows an app to read 1 on 1 or group chats threads, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user chat messages", "id": "f501c180-9344-439a-bca0-6cbf209fd270", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read your 1 on 1 or group chat messages in Microsoft Teams, on your behalf.", "consentDisplayName": "Read your chat messages", "value": "<PERSON><PERSON><PERSON>"}, {"adminConsentDescription": "Allows the app to read security actions, on behalf of the signed-in user.", "adminConsentDisplayName": "Read your organization's security actions", "id": "1638cddf-07a4-4de2-8645-69c96cacad73", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read security actions, on your behalf.", "consentDisplayName": "Read your organization's security actions", "value": "SecurityActions.Read.All"}, {"adminConsentDescription": "Allows the app to read or update security actions, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and update your organization's security actions", "id": "dc38509c-b87d-4da0-bd92-6bec988bac4a", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and update security actions, on your behalf.", "consentDisplayName": "Read and update your organization's security actions", "value": "SecurityActions.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), on behalf of the signed-in user.  It cannot update any threat indicators it does not own.", "adminConsentDisplayName": "Manage threat indicators this app creates or owns", "id": "91e7d36d-022a-490f-a748-f8e011357b42", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), on your behalf.  It cannot update any threat indicators that it is not an owner of.", "consentDisplayName": "Manage threat indicators this app creates or owns", "value": "ThreatIndicators.ReadWrite.OwnedBy"}, {"adminConsentDescription": "Allows the app to read your family information, members and their basic profile.", "adminConsentDisplayName": "Read your family info", "id": "3a1e4806-a744-4c70-80fc-223bf8582c46", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read your family information, members and their basic profile.", "consentDisplayName": "Read your family info", "value": "Family.Read"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write administrative units", "id": "7b8a2d34-6b3f-4542-a343-54651608ad81", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership on your behalf.", "consentDisplayName": "Read and write administrative units", "value": "AdministrativeUnit.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read administrative units and administrative unit membership on behalf of the signed-in user.", "adminConsentDisplayName": "Read administrative units", "id": "3361d15d-be43-4de6-b441-3c746d05163d", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read administrative units and administrative unit membership on your behalf.", "consentDisplayName": "Read administrative units", "value": "AdministrativeUnit.Read.All"}, {"adminConsentDescription": "Allows an app to read information protection sensitivity labels and label policy settings, on behalf of the signed-in user.", "adminConsentDisplayName": "Read user sensitivity labels and label policies.", "id": "4ad84827-5578-4e18-ad7a-86530b12f884", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows an app to read information protection sensitivity labels and label policy settings, on behalf of the signed-in user.", "consentDisplayName": "Read user sensitivity labels and label policies.", "value": "InformationProtectionPolicy.Read"}, {"adminConsentDescription": "Allows the app to manage hybrid identity service configuration by creating, viewing, updating and deleting on-premises published resources, on-premises agents and agent groups, on behalf of the signed-in user.", "adminConsentDisplayName": "Manage on-premises published resources", "id": "8c4d5184-71c2-4bf8-bb9d-bc3378c9ad42", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage hybrid identity service configuration by creating, viewing, updating and deleting on-premises published resources, on-premises agents and agent groups, on your behalf.", "consentDisplayName": "Manage on-premises published resources", "value": "OnPremisesPublishingProfiles.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings for group and app memberships that the signed-in user has access to in the organization.", "adminConsentDisplayName": "Manage access reviews for group and app memberships", "id": "5af8c3f5-baca-439a-97b0-ea58a435e269", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read, update and perform action on access reviews, reviewers, decisions and settings that you have access to.", "consentDisplayName": "Manage access reviews for group and app memberships", "value": "AccessReview.ReadWrite.Membership"}, {"adminConsentDescription": "Allows the app to read workforce integrations, to synchronize data from Microsoft Teams Shifts, on behalf of the signed-in user.", "adminConsentDisplayName": "Read workforce integrations", "id": "f1ccd5a7-6383-466a-8db8-1a656f7d06fa", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read workforce integrations, to synchronize data from Microsoft Teams Shifts, on your behalf.", "consentDisplayName": "Read workforce integrations", "value": "WorkforceIntegration.Read.All"}, {"adminConsentDescription": "Allows the app to manage workforce integrations, to synchronize data from Microsoft Teams Shifts, on behalf of the signed-in user.", "adminConsentDisplayName": "Read and write workforce integrations", "id": "08c4b377-0d23-4a8b-be2a-23c1c1d88545", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to manage workforce integrations, to synchronize data from Microsoft Teams Shifts, on your behalf.", "consentDisplayName": "Read and write workforce integrations", "value": "WorkforceIntegration.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read your company's places (conference rooms and room lists) for calendar events and other applications, on behalf of the signed-in user.", "adminConsentDisplayName": "Read all company places", "id": "cb8f45a0-5c2e-4ea1-b803-84b870a7d7ec", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read your company's places (conference rooms and room lists) for calendar events and other applications, on your behalf.", "consentDisplayName": "Read all company places", "value": "Place.Read.All"}, {"adminConsentDescription": "Allows the app to read the organization and related resources, on behalf of the signed-in user. Related resources include things like subscribed skus and tenant branding information.", "adminConsentDisplayName": "Read organization information", "id": "4908d5b9-3fb2-4b1e-9336-1888b7937185", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the organization and related resources, on your behalf. Related resources include things like subscribed skus and tenant branding information.", "consentDisplayName": "Read organization information", "value": "Organization.Read.All"}, {"adminConsentDescription": "Allows the app to read and write the organization and related resources, on behalf of the signed-in user. Related resources include things like subscribed skus and tenant branding information.", "adminConsentDisplayName": "Read and write organization information", "id": "46ca0847-7e6b-426e-9775-ea810a948356", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write the organization and related resources, on your behalf. Related resources include things like subscribed skus and tenant branding information.", "consentDisplayName": "Read and write organization information", "value": "Organization.ReadWrite.All"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, on behalf of the signed-in user.  This includes reading directory role templates, directory roles and memberships.", "adminConsentDisplayName": "Read directory RBAC settings", "id": "741c54c3-0c1e-44a1-818b-3f97ab4e8c83", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, on your behalf.  This includes reading directory role templates, directory roles and memberships.", "consentDisplayName": "Read directory RBAC settings", "value": "RoleManagement.Read.Directory"}, {"adminConsentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, on behalf of the signed-in user. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "adminConsentDisplayName": "Read and write directory RBAC settings", "id": "d01b97e9-cbc0-49fe-810a-750afd5527a3", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, on your behalf. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "consentDisplayName": "Read and write directory RBAC settings", "value": "RoleManagement.ReadWrite.Directory"}, {"adminConsentDescription": "Allows the app to read and write your organization's feature rollout policies on behalf of the signed-in user. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "adminConsentDisplayName": "Read and write your organization's feature rollout policies", "id": "92a38652-f13b-4875-bc77-6e1dbb63e1b2", "isEnabled": true, "isAdmin": true, "consentDescription": "Allows the app to read and write your organization's feature rollout policies on your behalf. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "consentDisplayName": "Read and write your organization's feature rollout policies", "value": "Policy.ReadWrite.FeatureRollout"}, {"adminConsentDescription": "Allows the app to read email in the signed-in user's mailbox except body, previewBody, attachments and any extended properties.", "adminConsentDisplayName": "Read user basic mail", "id": "a4b8392a-d8d1-4954-a029-8e668a39a170", "isEnabled": true, "isAdmin": false, "consentDescription": "Allows the app to read email in the signed-in user's mailbox except body, previewBody, attachments and any extended properties.", "consentDisplayName": "Read user basic mail", "value": "Mail.ReadBasic"}], "applicationScopesList": [{"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update the organization's federated token validation policy without a signed-in user.", "consentDisplayName": "Read and write your organization's federated token validation policy", "id": "90bbca0b-227c-4cdc-8083-1c6cfb95bac6", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.FedTokenValidation"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all user metrics insights, such as daily and monthly active users, without a signed-in user.", "consentDisplayName": "Read all user metrics insights", "id": "34cbd96c-d824-4755-90d3-1008ef47efc1", "isEnabled": true, "isAdmin": false, "value": "Insights-UserMetric.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the app to create data classification sessions, without a signed-in user.", "consentDisplayName": "Create data classification sessions", "id": "50bbb34b-8df4-4148-8e59-239c00555340", "isEnabled": true, "isAdmin": false, "value": "DataClassificationSession.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization’s identity protection policy without a signed-in user.", "consentDisplayName": "Read and write your organization’s identity protection policy ", "id": "2dcf8603-09eb-4078-b1ec-d30a1a76b873", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.IdentityProtection"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization’s identity protection policy without a signed-in user. ", "consentDisplayName": "Read your organization’s identity protection policy", "id": "b21b72f6-4e6a-4533-9112-47eea9f97b28", "isEnabled": true, "isAdmin": false, "value": "Policy.Read.IdentityProtection"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write organization-wide Microsoft 365 apps installation settings, without a signed-in user. ", "consentDisplayName": "Read and write organization-wide Microsoft 365 apps installation settings", "id": "83f7232f-763c-47b2-a097-e35d2cbe1da5", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Microsoft365Install.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read organization-wide Microsoft 365 apps installation settings, without a signed-in user.", "consentDisplayName": "Read organization-wide Microsoft 365 apps installation settings", "id": "6cdf1fb1-b46f-424f-9493-07247caa22e2", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Microsoft365Install.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all insights related to guest activity, without a signed-in user.", "consentDisplayName": "Read and write all insights related to guest activity", "id": "681ded65-ad9c-4d3d-8f7d-962f84cc13d8", "isEnabled": true, "isAdmin": false, "value": "Insights-GuestActivity.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all insights related to guest activity, without a signed-in user.", "consentDisplayName": "Read all insights related to guest activity", "id": "e93bd8c0-c267-45ea-9722-9d3376a7e302", "isEnabled": true, "isAdmin": false, "value": "Insights-GuestActivity.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read call event information for all users in your organizatio, without a signed-in user.", "consentDisplayName": "Read all call events", "id": "1abb026f-7572-49f6-9ddd-ad61cbba181e", "isEnabled": true, "isAdmin": false, "value": "CallEvents.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all audit logs for events that contain information about custom security attributes, without a signed-in user.", "consentDisplayName": "Read all custom security attribute audit logs", "id": "2a4f026d-e829-4e84-bdbf-d981a2703059", "isEnabled": true, "isAdmin": false, "value": "CustomSecAttributeAuditLogs.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all delegated permission grants, without a signed-in user.", "consentDisplayName": "Read all delegated permission grants", "id": "81b4724a-58aa-41c1-8a55-84ef97466587", "isEnabled": true, "isAdmin": false, "value": "DelegatedPermissionGrant.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all resource specific permissions granted on user accounts, without a signed-in user.", "consentDisplayName": "Read all resource specific permissions granted on user accounts", "id": "acfca4d5-f49f-40ed-9648-84068b474c73", "isEnabled": true, "isAdmin": false, "value": "ResourceSpecificPermissionGrant.ReadForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for any user account, without a signed-in user, and manage its permission grants for accessing those specific users' data.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants in all user accounts", "id": "a87076cf-6abd-4e56-8559-4dbdf41bef96", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in any user account, without a signed-in user. Gives the ability to manage permission grants for accessing those specific users' data.", "consentDisplayName": "Manage installation and permission grants of Teams apps in a user account", "id": "32ca478f-f89e-41d0-aaf8-101deb7da510", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all multi-tenant organization details and tenants, without a signed-in user.", "consentDisplayName": "Read all multi-tenant organization details and tenants", "id": "4f994bc0-31bb-44bb-b480-7a7c1be8c02e", "isEnabled": true, "isAdmin": false, "value": "MultiTenantOrganization.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all multi-tenant organization details and tenants, without a signed-in user.", "consentDisplayName": "Read and write all multi-tenant organization details and tenants", "id": "920def01-ca61-4d2d-b3df-105b46046a70", "isEnabled": true, "isAdmin": false, "value": "MultiTenantOrganization.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read multi-tenant organization basic details and active tenants, without a signed-in user.", "consentDisplayName": "Read multi-tenant organization basic details and active tenants", "id": "f9c2b2a7-3895-4b2e-80f6-c924b456e50b", "isEnabled": true, "isAdmin": false, "value": "MultiTenantOrganization.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all modules and resources, without a signed-in user.", "consentDisplayName": "Read all class modules and resources", "id": "6cdb464c-3a03-40f8-900b-4cb7ea1da9c0", "isEnabled": true, "isAdmin": false, "value": "EduCurricula.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all modules and resources, without a signed-in user.", "consentDisplayName": "Read and write all class modules and resources", "id": "6a0c2318-d59d-4c7d-bf2e-5f3902dc2593", "isEnabled": true, "isAdmin": false, "value": "EduCurricula.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to upload bulk user data to the identity synchronization service, without a signed-in user.", "consentDisplayName": "Upload user data to the identity synchronization service", "id": "db31e92a-b9ea-4d87-bf6a-75a37a9ca35a", "isEnabled": true, "isAdmin": false, "value": "SynchronizationData-User.Upload"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read organization-wide Dynamics customer voice settings, without a signed-in user.", "consentDisplayName": "Read organization-wide Dynamics customer voice settings", "id": "c18ae2dc-d9f3-4495-a93f-18980a0e159f", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-DynamicsVoice.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read organization-wide apps and services settings, without a signed-in user.", "consentDisplayName": "Read organization-wide apps and services settings", "id": "56c84fa9-ea1f-4a15-90f2-90ef41ece2c9", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-AppsAndServices.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write organization-wide Microsoft To Do settings, without a signed-in user.", "consentDisplayName": "Read and write organization-wide Microsoft To Do settings", "id": "5febc9da-e0d0-4576-bd13-ae70b2179a39", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Todo.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write organization-wide Microsoft Forms settings, without a signed-in user.", "consentDisplayName": "Read and write organization-wide Microsoft Forms settings", "id": "2cb92fee-97a3-4034-8702-24a6f5d0d1e9", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Forms.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write organization-wide Dynamics customer voice settings, without a signed-in user.", "consentDisplayName": "Read and write organization-wide Dynamics customer voice settings", "id": "c3f1cc32-8bbd-4ab6-bd33-f270e0d9e041", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-DynamicsVoice.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read organization-wide Microsoft To Do settings, without a signed-in user.", "consentDisplayName": "Read organization-wide Microsoft To Do settings", "id": "e4d9cd09-d858-4363-9410-abb96737f0cf", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Todo.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read organization-wide Microsoft Forms settings, without a signed-in user.", "consentDisplayName": "Read organization-wide Microsoft Forms settings", "id": "434d7c66-07c6-4b1f-ab21-417cf2cdaaca", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-Forms.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write organization-wide apps and services settings, without a signed-in user.", "consentDisplayName": "Read and write organization-wide apps and services settings", "id": "4a8e4191-c1c8-45f8-b801-f9a1a5ee6ad3", "isEnabled": true, "isAdmin": false, "value": "OrgSettings-AppsAndServices.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, and delete policies in Privileged Identity Management for Groups, without a signed-in user.", "consentDisplayName": "Read, update, and delete all policies in PIM for Groups", "id": "b38dcc4d-a239-4ed6-aa84-6c65b284f97c", "isEnabled": true, "isAdmin": false, "value": "RoleManagementPolicy.ReadWrite.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all on-premises directory synchronization information for the organization, without a signed-in user.", "consentDisplayName": "Read all on-premises directory synchronization information", "id": "bb70e231-92dc-4729-aff5-697b3f04be95", "isEnabled": true, "isAdmin": false, "value": "OnPremDirectorySynchronization.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all on-premises directory synchronization information for the organization, without a signed-in user.", "consentDisplayName": "Read and write all on-premises directory synchronization information", "id": "c22a92cc-79bf-4bb1-8b6c-e0a05d3d80ce", "isEnabled": true, "isAdmin": false, "value": "OnPremDirectorySynchronization.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read policies in Privileged Identity Management for Groups, without a signed-in user.", "consentDisplayName": "Read all policies in PIM for Groups", "id": "69e67828-780e-47fd-b28c-7b27d14864e6", "isEnabled": true, "isAdmin": false, "value": "RoleManagementPolicy.Read.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all details of discovered cloud apps in the organization, without a signed-in user.", "consentDisplayName": "Read all discovered cloud applications data", "id": "64a59178-dad3-4673-89db-84fdcd622fec", "isEnabled": true, "isAdmin": false, "value": "CloudApp-Discovery.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, update, read and delete all assignments in the organization's directory, without a signed-in user.", "consentDisplayName": "Read and write all assignments", "id": "236c1cbd-1187-427f-b0f5-b1852454973b", "isEnabled": true, "isAdmin": false, "value": "LearningAssignedCourse.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, update, read and delete all self-initiated courses in the organization's directory, without a signed-in user.", "consentDisplayName": "Read and write all self-initiated courses", "id": "7654ed61-8965-4025-846a-0856ec02b5b0", "isEnabled": true, "isAdmin": false, "value": "LearningSelfInitiatedCourse.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read data for all self-initiated courses in the organization's directory, without a signed-in user.", "consentDisplayName": "Read all self-initiated courses", "id": "467524fc-ed22-4356-a910-af61191e3503", "isEnabled": true, "isAdmin": false, "value": "LearningSelfInitiatedCourse.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read data for all assignments in the organization's directory, without a signed-in user.", "consentDisplayName": "Read all assignments", "id": "535e6066-2894-49ef-ab33-e2c6d064bb81", "isEnabled": true, "isAdmin": false, "value": "LearningAssignedCourse.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time-based eligibility schedules for access to Azure AD groups, without a signed-in user.", "consentDisplayName": "Read eligibility schedules for access to Azure AD groups", "id": "edb419d6-7edc-42a3-9345-509bfdf5d87c", "isEnabled": true, "isAdmin": false, "value": "PrivilegedEligibilitySchedule.Read.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time-based assignment schedules for access to Azure AD groups, without a signed-in user.", "consentDisplayName": "Read assignment schedules for access to Azure AD groups", "id": "cd4161cb-f098-48f8-a884-1eda9a42434c", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAssignmentSchedule.Read.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create, and delete time-based assignment schedules for access to Azure AD groups, without a signed-in user.", "consentDisplayName": "Read, create, and delete assignment schedules for access to Azure AD groups", "id": "41202f2c-f7ab-45be-b001-85c9728b9d69", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAssignmentSchedule.ReadWrite.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the role-based access control (RBAC) configuration for your organization's Exchange Online service, without a signed-in user. This includes reading Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "consentDisplayName": "Read Exchange Online RBAC configuration", "id": "c769435f-f061-4d0b-8ff1-3d39870e5f85", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.Read.Exchange"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your organization's Exchange Online service, without a signed-in user. This includes reading, creating, updating, and deleting Exchange management role definitions, role groups, role group membership, role assignments, management scopes, and role assignment policies.", "consentDisplayName": "Read and write Exchange Online RBAC configuration", "id": "025d3225-3f02-4882-b4c0-cd5b541a4e80", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.ReadWrite.Exchange"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create, and delete time-based eligibility schedules for access to Azure AD groups, without a signed-in user.", "consentDisplayName": "Read, create, and delete eligibility schedules for access to Azure AD groups", "id": "618b6020-bca8-4de6-99f6-ef445fa4d857", "isEnabled": true, "isAdmin": false, "value": "PrivilegedEligibilitySchedule.ReadWrite.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read threat intellgence information, such as indicators, observations, and and articles, without a signed in user.", "consentDisplayName": "Read all Threat Intelligence Information", "id": "e0b77adb-e790-44a3-b0a0-257d06303687", "isEnabled": true, "isAdmin": false, "value": "ThreatIntelligence.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to delete and recover deleted chats, without a signed-in user.", "consentDisplayName": "Delete and recover deleted chats", "id": "9c7abde0-eacd-4319-bf9e-35994b1a1717", "isEnabled": true, "isAdmin": false, "value": "Chat.ManageDeletion.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all virtual events without a signed-in user.", "consentDisplayName": "Read all users' virtual events", "id": "1dccb351-c4e4-4e09-a8d1-7a9ecbf027cc", "isEnabled": true, "isAdmin": false, "value": "VirtualEvent.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and write tenant-wide people settings without a signed-in user.", "consentDisplayName": "Read and write all tenant-wide people settings", "id": "b6890674-9dd5-4e42-bb15-5af07f541ae1", "isEnabled": true, "isAdmin": false, "value": "PeopleSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read tenant-wide people settings without a signed-in user.", "consentDisplayName": "Read all tenant-wide people settings", "id": "ef02f2e7-e22d-4c77-8614-8f765683b86e", "isEnabled": true, "isAdmin": false, "value": "PeopleSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all configurations applicable to users for protecting organizational data, without a signed-in user.", "consentDisplayName": "Read all configurations for protecting organizational data applicable to users", "id": "14f49b9f-4bf2-4d24-b80e-b27ec58409bd", "isEnabled": true, "isAdmin": false, "value": "InformationProtectionConfig.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the remote desktop security configuration for all apps in your organization, without a signed-in user.", "consentDisplayName": "Read and write the remote desktop security configuration for all apps", "id": "3be0012a-cc4e-426b-895b-f9c836bf6381", "isEnabled": true, "isAdmin": false, "value": "Application-RemoteDesktopConfig.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and manage the eligible role-based access control (RBAC) assignments and schedules for your company's directory, without a signed-in user. This includes managing eligible directory role membership, and reading directory role templates, directory roles and eligible memberships.", "consentDisplayName": "Read, update, and delete all eligible role assignments and schedules for your company's directory", "id": "fee28b28-e1f3-4841-818e-2704dc62245f", "isEnabled": true, "isAdmin": false, "value": "RoleEligibilitySchedule.ReadWrite.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read policies for privileged role-based access control (RBAC) assignments of your company's directory, without a signed-in user.", "consentDisplayName": "Read all policies for privileged role assignments of your company's directory", "id": "fdc4c997-9942-4479-bfcb-75a36d1138df", "isEnabled": true, "isAdmin": false, "value": "RoleManagementPolicy.Read.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, without a signed-in user.", "consentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory", "id": "31e08e0a-d3f7-4ca2-ac39-7343fb83e8ad", "isEnabled": true, "isAdmin": false, "value": "RoleManagementPolicy.ReadWrite.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the eligible role-based access control (RBAC) assignments and schedules for your company's directory, without a signed-in user. This includes reading directory role templates, and directory roles.", "consentDisplayName": "Read all eligible role assignments and role schedules for your company's directory", "id": "ff278e11-4a33-4d0c-83d2-d01dc58929a5", "isEnabled": true, "isAdmin": false, "value": "RoleEligibilitySchedule.Read.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the active role-based access control (RBAC) assignments and schedules for your company's directory, without a signed-in user. This includes reading directory role templates, and directory roles.", "consentDisplayName": "Read all active role assignments and role schedules for your company's directory", "id": "d5fe8ce8-684c-4c83-a52c-46e882ce4be1", "isEnabled": true, "isAdmin": false, "value": "RoleAssignmentSchedule.Read.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, without a signed-in user.", "consentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory", "id": "dd199f4a-f148-40a4-a2ec-f0069cc799ec", "isEnabled": true, "isAdmin": false, "value": "RoleAssignmentSchedule.ReadWrite.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read email metadata and security detection details, and execute remediation actions like deleting an email, without a signed-in user.", "consentDisplayName": "Read metadata, detection details, and execute remediation actions on all emails in your organization", "id": "04c55753-2244-4c25-87fc-704ab82a4f69", "isEnabled": true, "isAdmin": false, "value": "SecurityAnalyzedMessage.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read email metadata and security detection details, without a signed-in user.  ", "consentDisplayName": "Read metadata and detection details for all emails in your organization", "id": "b48f7ac2-044d-4281-b02f-75db744d6f5f", "isEnabled": true, "isAdmin": false, "value": "SecurityAnalyzedMessage.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to enable and disable users' accounts, without a signed-in user.", "consentDisplayName": "Enable and disable user accounts", "id": "3011c876-62b7-4ada-afa2-506cbbecc68c", "isEnabled": true, "isAdmin": false, "value": "User.EnableDisableAccount.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and manage all role-based access control (RBAC) alerts for your company's directory, without a signed-in user. This includes managing alert settings, initiating alert scans, dimissing alerts, remediating alert incidents, and reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "consentDisplayName": "Read all alert data, configure alerts, and take actions on all alerts for your company's directory", "id": "********-d6a6-4851-98ed-509268489c4a", "isEnabled": true, "isAdmin": false, "value": "RoleManagementAlert.ReadWrite.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all role-based access control (RBAC) alerts for your company's directory, without a signed-in user. This includes reading alert statuses, alert definitions, alert configurations and incidents that lead to an alert.", "consentDisplayName": "Read all alert data for your company's directory", "id": "ef31918f-2d50-4755-8943-b8638c0a077e", "isEnabled": true, "isAdmin": false, "value": "RoleManagementAlert.Read.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create, and update attack simulation and training data for an organization without a signed-in user.", "consentDisplayName": "Read, create, and update all attack simulation data of an organization", "id": "e125258e-8c8a-42a8-8f55-ab502afa52f3", "isEnabled": true, "isAdmin": false, "value": "AttackSimulation.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read inbound data flows without a signed-in user.", "consentDisplayName": "View inbound flow definitions", "id": "305f6ba2-049a-4b1b-88bb-fe7e08758a00", "isEnabled": true, "isAdmin": false, "value": "IndustryData-InboundFlow.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time period definitions without a signed-in user.", "consentDisplayName": "Read time period definitions", "id": "7c55c952-b095-4c23-a522-022bce4cc1e3", "isEnabled": true, "isAdmin": false, "value": "IndustryData-TimePeriod.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read basic service and resource information without a signed-in user.", "consentDisplayName": "View basic service and resource information", "id": "4f5ac95f-62fd-472c-b60f-125d24ca0bc5", "isEnabled": true, "isAdmin": false, "value": "IndustryData.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write inbound data flows without a signed-in user.", "consentDisplayName": "Manage inbound flow definitions", "id": "e688c61f-d4c6-4d64-a197-3bcf6ba1d6ad", "isEnabled": true, "isAdmin": false, "value": "IndustryData-InboundFlow.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write source system definitions without a signed-in user.", "consentDisplayName": "Manage source system definitions", "id": "7d866958-e06e-4dd6-91c6-a086b3f5cfeb", "isEnabled": true, "isAdmin": false, "value": "IndustryData-SourceSystem.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read current and previous IndustryData runs without a signed-in user.", "consentDisplayName": "View current and previous runs", "id": "f6f5d10b-3024-4d1d-b674-aae4df4a1a73", "isEnabled": true, "isAdmin": false, "value": "IndustryData-Run.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read reference definitions without a signed-in user.", "consentDisplayName": "View reference definitions", "id": "6ee891c3-74a4-4148-8463-0c834375dfaf", "isEnabled": true, "isAdmin": false, "value": "IndustryData-ReferenceDefinition.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to upload data files to a data connector without a signed-in user.", "consentDisplayName": "Upload files to a data connector", "id": "9334c44b-a7c6-4350-8036-6bf8e02b4c1f", "isEnabled": true, "isAdmin": false, "value": "IndustryData-DataConnector.Upload"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write data connectors without a signed-in user.", "consentDisplayName": "Manage data connector definitions", "id": "eda0971c-482e-4345-b28f-69c309cb8a34", "isEnabled": true, "isAdmin": false, "value": "IndustryData-DataConnector.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read data connectors without a signed-in user.", "consentDisplayName": "View data connector definitions", "id": "7ab52c2f-a2ee-4d98-9ebc-725e3934aae2", "isEnabled": true, "isAdmin": false, "value": "IndustryData-DataConnector.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read source system definitions without a signed-in user.", "consentDisplayName": "View source system definitions", "id": "bc167a60-39fe-4865-8b44-78400fc6ed03", "isEnabled": true, "isAdmin": false, "value": "IndustryData-SourceSystem.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write time period definitions without a signed-in user.", "consentDisplayName": "Manage time period definitions", "id": "7afa7744-a782-4a32-b8c2-e3db637e8de7", "isEnabled": true, "isAdmin": false, "value": "IndustryData-TimePeriod.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's network access braches, without a signed-in user.", "consentDisplayName": "Read and write properties of all branches for network access", "id": "8137102d-ec16-4191-aaf8-7aeda8026183", "isEnabled": true, "isAdmin": false, "value": "NetworkAccessBranch.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's network access policies, without a signed-in user.", "consentDisplayName": "Read and write all security and routing policies for network access", "id": "f0c341be-8348-4989-8e43-************", "isEnabled": true, "isAdmin": false, "value": "NetworkAccessPolicy.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and write virtual appointments for all users, without a signed-in user. The app must also be authorized to access an individual user’s data by the online meetings application access policy.", "consentDisplayName": "Read-write all virtual appointments for users, as authorized by online meetings app access policy", "id": "bf46a256-f47d-448f-ab78-f226fff08d40", "isEnabled": true, "isAdmin": false, "value": "VirtualAppointment.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in any chat, without a signed-in user. Gives the ability to manage permission grants for accessing those specific chats' data.", "consentDisplayName": "Manage installation and permission grants of Teams apps for all chats", "id": "6e74eff9-4a21-45d6-bc03-3a20f61f8281", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the data associated with the business scenarios it owns, without a signed-in user.", "consentDisplayName": "Read data for all business scenarios this app creates or owns", "id": "6c0257fd-cffe-415b-8239-2d0d70fdaa9c", "isEnabled": true, "isAdmin": false, "value": "BusinessScenarioData.Read.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all question and answers, without a signed-in user.", "consentDisplayName": "Read all Question and Answers ", "id": "ee49e170-1dd1-4030-b44c-61ad6e98f743", "isEnabled": true, "isAdmin": false, "value": "QnA.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to configure the Azure AD synchronization service, without a signed-in user.", "consentDisplayName": "Read and write all Azure AD synchronization data. ", "id": "9b50c33d-700f-43b1-b2eb-87e89b703581", "isEnabled": true, "isAdmin": false, "value": "Synchronization.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create new business scenarios and fully manage the configurations of scenarios it owns, without a signed-in user.", "consentDisplayName": "Read and write all business scenario configurations this app creates or owns", "id": "bbea195a-4c47-4a4f-bff2-cba399e11698", "isEnabled": true, "isAdmin": false, "value": "BusinessScenarioConfig.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read device local credential properties excluding passwords, without a signed-in user.", "consentDisplayName": "Read device local credential properties", "id": "db51be59-e728-414b-b800-e0f010df1a79", "isEnabled": true, "isAdmin": false, "value": "DeviceLocalCredential.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read device local credential properties including passwords, without a signed-in user.", "consentDisplayName": "Read device local credential passwords", "id": "884b599e-4d48-43a5-ba94-15c414d00588", "isEnabled": true, "isAdmin": false, "value": "DeviceLocalCredential.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for any team, without a signed-in user, and manage its permission grants for accessing those specific teams' data.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants for all teams", "id": "1e4be56c-312e-42b8-a2c9-009600d732c0", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in any team, without a signed-in user. Gives the ability to manage permission grants for accessing those specific teams' data.", "consentDisplayName": "Manage installation and permission grants of Teams apps for all teams", "id": "b0c13be0-8e20-4bc5-8c55-963c23a39ce9", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read Azure AD synchronization information, without a signed-in user.", "consentDisplayName": "Read all Azure AD synchronization data. ", "id": "5ba43d2f-fa88-4db2-bd1c-a67c5f0fb1ce", "isEnabled": true, "isAdmin": false, "value": "Synchronization.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read virtual appointments for all users, without a signed-in user. The app must also be authorized to access an individual user’s data by the online meetings application access policy.", "consentDisplayName": "Read all virtual appointments for users, as authorized by online meetings application access policy", "id": "d4f67ec2-59b5-4bdc-b4af-d78f6f9c1954", "isEnabled": true, "isAdmin": false, "value": "VirtualAppointment.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update the organization's external identities policy without a signed-in user.  For example, external identities policy controls if users invited to access resources in your organization via B2B collaboration or B2B direct connect are allowed to self-service leave.", "consentDisplayName": "Read and write your organization's external identities policy", "id": "03cc4f92-788e-4ede-b93f-199424d144a5", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.ExternalIdentities"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's network access braches, without a signed-in user.", "consentDisplayName": "Read properties of all branches for network access", "id": "39ae4a24-1ef0-49e8-9d63-2a66f5c39edd", "isEnabled": true, "isAdmin": false, "value": "NetworkAccessBranch.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's network access policies, without a signed-in user.", "consentDisplayName": "Read all security and routing policies for network access", "id": "8a3d36bf-cb46-4bcc-bec9-8d92829dab84", "isEnabled": true, "isAdmin": false, "value": "NetworkAccessPolicy.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read events of all calendars, except for properties such as body, attachments, and extensions, without a signed-in user.", "consentDisplayName": "Read basic details of calendars in all mailboxes ", "id": "8ba4a692-bc31-4128-9094-475872af8a53", "isEnabled": true, "isAdmin": false, "value": "Calendars.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for any chat, without a signed-in user, and manage its permission grants for accessing those specific chats' data.", "consentDisplayName": "Allow the Teams app to manage itself and its permission grants for all chats", "id": "ba1ba90b-2d8f-487e-9f16-80728d85bb5c", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteAndConsentSelfForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the configurations of business scenarios it owns, without a signed-in user.", "consentDisplayName": "Read all business scenario configurations this app creates or owns", "id": "acc0fc4d-2cd6-4194-8700-1768d8423d86", "isEnabled": true, "isAdmin": false, "value": "BusinessScenarioConfig.Read.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's security defaults policy, without a signed-in user.", "consentDisplayName": "Read and write your organization's security defaults policy", "id": "1c6e93a6-28e2-4cbb-9f64-1a46a821124d", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.SecurityDefaults"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to fully manage the data associated with the business scenarios it owns, without a signed-in user.", "consentDisplayName": "Read and write data for all business scenarios this app creates or owns", "id": "f2d21f22-5d80-499e-91cc-0a8a4ce16f54", "isEnabled": true, "isAdmin": false, "value": "BusinessScenarioData.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the billing configuration on all applications without a signed-in user. ", "consentDisplayName": "Read and write application billing configuration", "id": "9e8be751-7eee-4c09-bcfd-d64f6b087fd8", "isEnabled": true, "isAdmin": false, "value": "BillingConfiguration.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all available Teams Templates, without a signed-user.", "consentDisplayName": "Read all available Teams Templates", "id": "6323133e-1f6e-46d4-9372-ac33a0870636", "isEnabled": true, "isAdmin": false, "value": "TeamTemplates.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all acronyms without a signed-in user.", "consentDisplayName": "Read all acronyms", "id": "8c0aed2c-0c61-433d-b63c-6370ddc73248", "isEnabled": true, "isAdmin": false, "value": "Acronym.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the lifecycle information like employeeLeaveDateTime of users in your organization, without a signed-in user.", "consentDisplayName": "Read all users' lifecycle information", "id": "8556a004-db57-4d7a-8b82-97a13428e96f", "isEnabled": true, "isAdmin": false, "value": "User-LifeCycleInfo.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all browser site lists configured for your organization, without a signed-in user.", "consentDisplayName": "Read all browser site lists for your organization", "id": "c5ee1f21-fc7f-4937-9af0-c91648ff9597", "isEnabled": true, "isAdmin": false, "value": "BrowserSiteLists.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the lifecycle information like employeeLeaveDateTime of users in your organization, without a signed-in user.", "consentDisplayName": "Read and write all users' lifecycle information", "id": "925f1248-0f97-47b9-8ec8-538c54e01325", "isEnabled": true, "isAdmin": false, "value": "User-LifeCycleInfo.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all bookmarks without a signed-in user.", "consentDisplayName": "Read all bookmarks", "id": "be95e614-8ef3-49eb-8464-1c9503433b86", "isEnabled": true, "isAdmin": false, "value": "Bookmark.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read and write all browser site lists configured for your organization, without a signed-in user.", "consentDisplayName": "Read and write all browser site lists for your organization", "id": "8349ca94-3061-44d5-9bfb-33774ea5e4f9", "isEnabled": true, "isAdmin": false, "value": "BrowserSiteLists.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to list and read all workflows, tasks and related lifecycle workflows resources without a signed-in user.", "consentDisplayName": "Read all lifecycle workflows resources", "id": "7c67316a-232a-4b84-be22-cea2c0906404", "isEnabled": true, "isAdmin": false, "value": "LifecycleWorkflows.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read names and members of all one-to-one and group chats in Microsoft Teams where the associated Teams application is installed, without a signed-in user.", "consentDisplayName": "Read names and members of all chat threads where the associated Teams application is installed.", "id": "818ba5bd-5b3e-4fe0-bbe6-aa4686669073", "isEnabled": true, "isAdmin": false, "value": "Chat.ReadBasic.WhereInstalled"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all chat messages in Microsoft Teams for chats where the associated Teams application is installed, without a signed-in user.", "consentDisplayName": "Read and write all chat messages for chats where the associated Teams application is installed.", "id": "ad73ce80-f3cd-40ce-b325-df12c33df713", "isEnabled": true, "isAdmin": false, "value": "Chat.ReadWrite.WhereInstalled"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all one-to-one or group chat messages in Microsoft Teams for chats where the associated Teams application is installed, without a signed-in user.", "consentDisplayName": "Read all chat messages for chats where the associated Teams application is installed.", "id": "1c1b4c8e-3cc7-4c58-8470-9b92c9d5848b", "isEnabled": true, "isAdmin": false, "value": "Chat.Read.WhereInstalled"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to add and remove members from all chats where the associated Teams application is installed, without a signed-in user.", "consentDisplayName": "Add and remove members from all chats where the associated Teams application is installed.", "id": "e32c2cd9-0124-4e44-88fc-772cd98afbdb", "isEnabled": true, "isAdmin": false, "value": "ChatMember.ReadWrite.WhereInstalled"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the members of all chats where the associated Teams application is installed, without a signed-in user.", "consentDisplayName": "Read the members of all chats where the associated Teams application is installed.", "id": "93e7c9e4-54c5-4a41-b796-f2a5adaacda7", "isEnabled": true, "isAdmin": false, "value": "ChatMember.Read.WhereInstalled"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, update, list, read and delete all workflows, tasks and related lifecycle workflows resources without a signed-in user.", "consentDisplayName": "Read and write all lifecycle workflows resources", "id": "5c505cf4-8424-4b8e-aa14-ee06e3bb23e3", "isEnabled": true, "isAdmin": false, "value": "LifecycleWorkflows.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update all admin report settings, such as whether to display concealed information in reports, without a signed-in user.", "consentDisplayName": "Read and write all admin report settings", "id": "2a60023f-3219-47ad-baa4-40e17cd02a1d", "isEnabled": true, "isAdmin": false, "value": "ReportSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all admin report settings, such as whether to display concealed information in reports, without a signed-in user.", "consentDisplayName": "Read all admin report settings", "id": "ee353f83-55ef-4b78-82da-555bfa2b4b95", "isEnabled": true, "isAdmin": false, "value": "ReportSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage all learning content in the organization's directory, without a signed-in user.", "consentDisplayName": "Manage all learning content", "id": "444d6fcb-b738-41e5-b103-ac4f2a2628a3", "isEnabled": true, "isAdmin": false, "value": "LearningContent.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all learning content in the organization's directory, without a signed-in user.", "consentDisplayName": "Read all learning content", "id": "8740813e-d8aa-4204-860e-2a0f8f84dbc8", "isEnabled": true, "isAdmin": false, "value": "LearningContent.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write subject rights requests without a signed in user.", "consentDisplayName": "Read and write all subject rights requests", "id": "8387eaa4-1a3c-41f5-b261-f888138e6041", "isEnabled": true, "isAdmin": false, "value": "SubjectRightsRequest.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read subject rights requests without a signed-in user.", "consentDisplayName": "Read all subject rights requests", "id": "ee1460f0-368b-4153-870a-4e1ca7e72c42", "isEnabled": true, "isAdmin": false, "value": "SubjectRightsRequest.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the Teams app settings without a signed-in user.", "consentDisplayName": "Read Teams app settings", "id": "475ebe88-f071-4bd7-af2b-642952bd4986", "isEnabled": true, "isAdmin": false, "value": "TeamworkAppSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the Teams app settings without a signed-in user.", "consentDisplayName": "Read and write Teams app settings", "id": "ab5b445e-8f10-45f4-9c79-dd3f8062cc4e", "isEnabled": true, "isAdmin": false, "value": "TeamworkAppSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read attack simulation and training data for an organization without a signed-in user.", "consentDisplayName": "Read attack simulation data of an organization", "id": "93283d0a-6322-4fa8-966b-8c121624760d", "isEnabled": true, "isAdmin": false, "value": "AttackSimulation.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read or write your organization's custom authentication extensions without a signed-in user.", "consentDisplayName": "Read and write all custom authentication extensions", "id": "c2667967-7050-4e7e-b059-4cbbb3811d03", "isEnabled": true, "isAdmin": false, "value": "CustomAuthenticationExtension.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's custom authentication extensions without a signed-in user.", "consentDisplayName": "Read all custom authentication extensions", "id": "88bb2658-5d9e-454f-aacd-a3933e079526", "isEnabled": true, "isAdmin": false, "value": "CustomAuthenticationExtension.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read or write your organization's authentication event listeners without a signed-in user.", "consentDisplayName": "Read and write all authentication event listeners", "id": "0edf5e9e-4ce8-468a-8432-d08631d18c43", "isEnabled": true, "isAdmin": false, "value": "EventListener.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's authentication event listeners without a signed-in user.", "consentDisplayName": "Read all authentication event listeners", "id": "b7f6385c-6ce6-4639-a480-e23c42ed9784", "isEnabled": true, "isAdmin": false, "value": "EventListener.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows custom authentication extensions associated with the app to receive HTTP requests triggered by an authentication event. The request can include information about a user, client and resource service principals, and other information about the authentication.", "consentDisplayName": "Receive custom authentication extension HTTP requests", "id": "214e810f-fda8-4fd7-a475-29461495eb00", "isEnabled": true, "isAdmin": false, "value": "CustomAuthenticationExtension.Receive.Payload"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's directory access review default policy without a signed-in user.", "consentDisplayName": "Read and write your organization's directory access review default policy", "id": "77c863fd-06c0-47ce-a7eb-49773e89d319", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.AccessReview"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's threat submission policies without a signed-in user. Also allows the app to create new threat submission polices without a signed-in user.", "consentDisplayName": "Read and write all of the organization's threat submission policies", "id": "926a6798-b100-4a20-a22f-a4918f13951d", "isEnabled": true, "isAdmin": false, "value": "ThreatSubmissionPolicy.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's threat submissions and to view threat submission policies without a signed-in user.", "consentDisplayName": "Read all of the organization's threat submissions", "id": "86632667-cd15-4845-ad89-48a88e8412e1", "isEnabled": true, "isAdmin": false, "value": "ThreatSubmission.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read and write Bookings appointments and customers, and additionally allows reading businesses, services, and staff without a signed-in user. ", "consentDisplayName": "Read and write all Bookings related resources.", "id": "9769393e-5a9f-4302-9e3d-7e018ecb64a7", "isEnabled": true, "isAdmin": false, "value": "BookingsAppointment.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read Bookings appointments, businesses, customers, services, and staff without a signed-in user.  ", "consentDisplayName": "Read all Bookings related resources.", "id": "6e98f277-b046-4193-a4f2-6bf6a78cd491", "isEnabled": true, "isAdmin": false, "value": "Bookings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's threat submissions and threat submission policies without a signed-in user. Also allows the app to create new threat submissions without a signed-in user.", "consentDisplayName": "Read and write all of the organization's threat submissions", "id": "d72bdbf4-a59b-405c-8b04-5995895819ac", "isEnabled": true, "isAdmin": false, "value": "ThreatSubmission.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the authentication context information in your organization without a signed-in user.", "consentDisplayName": "Read all authentication context information", "id": "381f742f-e1f8-4309-b4ab-e3d91ae4c5c1", "isEnabled": true, "isAdmin": false, "value": "AuthenticationContext.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update the authentication context information in your organization without a signed-in user.", "consentDisplayName": "Read and write all authentication context information", "id": "a88eef72-fed0-4bf7-a2a9-f19df33f8b83", "isEnabled": true, "isAdmin": false, "value": "AuthenticationContext.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update and delete all users’ tasks and task lists in your organization, without a signed-in user", "consentDisplayName": "Read and write all users’ tasks and tasklists", "id": "44e666d1-d276-445b-a5fc-8815eeb81d55", "isEnabled": true, "isAdmin": false, "value": "Tasks.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all users’ tasks and task lists in your organization, without a signed-in user.", "consentDisplayName": "Read all users’ tasks and tasklist", "id": "f10e1f91-74ed-437f-a6fd-d6ae88e26c1f", "isEnabled": true, "isAdmin": false, "value": "Tasks.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the application to create, update and delete any data from Records Management, such as configuration, labels, and policies without the signed in user.", "consentDisplayName": "Read and write Records Management configuration, labels and policies", "id": "eb158f57-df43-4751-8b21-b8932adb3d34", "isEnabled": true, "isAdmin": false, "value": "RecordsManagement.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read any data from Records Management, such as configuration, labels, and policies without the signed in user.", "consentDisplayName": "Read Records Management configuration, labels and policies", "id": "ac3a2b8e-03a3-4da9-9ce0-cbe28bf1accd", "isEnabled": true, "isAdmin": false, "value": "RecordsManagement.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create protected content without a signed-in user. ", "consentDisplayName": "Create protected content", "id": "287bd98c-e865-4e8c-bade-1a85523195b9", "isEnabled": true, "isAdmin": false, "value": "InformationProtectionContent.Write.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all transcripts of all online meetings, without a signed-in user.", "consentDisplayName": "Read all transcripts of online meetings.", "id": "a4a80d8d-d283-4bd8-8504-555ec3870630", "isEnabled": true, "isAdmin": false, "value": "OnlineMeetingTranscript.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to manage license assignments for users and groups, without a signed-in user.", "consentDisplayName": "Manage all license assignments", "id": "5facf0c1-8979-4e95-abcf-ff3d079771c0", "isEnabled": true, "isAdmin": false, "value": "LicenseAssignment.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all recordings of all online meetings, without a signed-in user.", "consentDisplayName": "Read all recordings of online meetings.", "id": "a4a08342-c95d-476b-b943-97e100569c8d", "isEnabled": true, "isAdmin": false, "value": "OnlineMeetingRecording.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to sign digests for data without a signed-in user.", "consentDisplayName": "Sign digests for data", "id": "cbe6c7e4-09aa-4b8d-b3c3-2dbb59af4b54", "isEnabled": true, "isAdmin": false, "value": "InformationProtectionContent.Sign.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read the tenant-level settings of SharePoint and OneDrive, without a signed-in user.", "consentDisplayName": "Read SharePoint and OneDrive tenant settings", "id": "83d4163d-a2d8-4d3b-9695-4ae3ca98f888", "isEnabled": true, "isAdmin": false, "value": "SharePointTenantSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all Azure AD recommendations, without a signed-in user. ", "consentDisplayName": "Read all Azure AD recommendations", "id": "ae73097b-cb2a-4447-b064-5d80f6093921", "isEnabled": true, "isAdmin": false, "value": "DirectoryRecommendations.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update all Azure AD recommendations, without a signed-in user. ", "consentDisplayName": "Read and update all Azure AD recommendations", "id": "0e9eea12-4f01-45f6-9b8d-3ea4c8144158", "isEnabled": true, "isAdmin": false, "value": "DirectoryRecommendations.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and change the tenant-level settings of SharePoint and OneDrive, without a signed-in user.", "consentDisplayName": "Read and change SharePoint and OneDrive tenant settings", "id": "19b94e34-907c-4f43-bde9-38b1909ed408", "isEnabled": true, "isAdmin": false, "value": "SharePointTenantSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage restricted resources based on the other permissions granted to the app, without a signed-in user.", "consentDisplayName": "Manage restricted resources in the directory", "id": "f20584af-9290-4153-9280-ff8bb2c0ea7f", "isEnabled": true, "isAdmin": false, "value": "Directory.Write.Restricted"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant without a signed-in user.  It also permits the application to export external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant without a signed-in user.", "consentDisplayName": "Read all shared cross-tenant user profiles and export their data", "id": "8b919d44-6192-4f3d-8a3b-f86f8069ae3c", "isEnabled": true, "isAdmin": false, "value": "CrossTenantUserProfileSharing.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to list and query any shared user profile information associated with the current tenant without a signed-in user.  It also permits the application to export and remove external user data (e.g. customer content or system-generated logs), for any user associated with the current tenant without a signed-in user.", "consentDisplayName": "Read all shared cross-tenant user profiles and export or delete their data", "id": "306785c5-c09b-4ba0-a4ee-023f3da165cb", "isEnabled": true, "isAdmin": false, "value": "CrossTenantUserProfileSharing.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to obtain basic tenant information about another target tenant within the Azure AD ecosystem without a signed-in user.", "consentDisplayName": "Read cross-tenant basic information", "id": "cac88765-0581-4025-9725-5ebc13f729ee", "isEnabled": true, "isAdmin": false, "value": "CrossTenantInformation.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage (create-update-terminate) Delegated Admin relationships with customers and role assignments to security groups for active Delegated Admin relationships without a signed-in user.", "consentDisplayName": "Manage Delegated Admin relationships with customers", "id": "cc13eba4-8cd8-44c6-b4d4-f93237adce58", "isEnabled": true, "isAdmin": false, "value": "DelegatedAdminRelationship.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read details of delegated admin relationships with customers like access details (that includes roles) and the duration as well as specific role assignments to security groups without a signed-in user.", "consentDisplayName": "Read Delegated Admin relationships with customers", "id": "f6e9e124-4586-492f-adc0-c6f96e4823fd", "isEnabled": true, "isAdmin": false, "value": "DelegatedAdminRelationship.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the Cloud PC role-based access control (RBAC) settings, without a signed-in user.", "consentDisplayName": "Read Cloud PC RBAC settings", "id": "031a549a-bb80-49b6-8032-2068448c6a3c", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.Read.CloudPC"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and manage the Cloud PC role-based access control (RBAC) settings, without a signed-in user. This includes reading and managing Cloud PC role definitions and memberships.", "consentDisplayName": "Read and write all Cloud PC RBAC settings", "id": "274d0592-d1b6-44bd-af1d-26d259bcb43a", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.ReadWrite.CloudPC"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "consentDisplayName": "Export user's data", "id": "405a51b5-8d8d-430b-9842-8be4b0e9f324", "isEnabled": true, "isAdmin": false, "value": "User.Export.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read apps in the app catalogs without a signed-in user.", "consentDisplayName": "Read all app catalogs", "id": "e12dae10-5a57-4817-b79d-dfbec5348930", "isEnabled": true, "isAdmin": false, "value": "AppCatalog.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete apps in the app catalogs without a signed-in user.", "consentDisplayName": "Read and write to all app catalogs", "id": "dc149144-f292-421e-b185-5953f2e98d7f", "isEnabled": true, "isAdmin": false, "value": "AppCatalog.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read online meeting artifacts in your organization, without a signed-in user.", "consentDisplayName": "Read online meeting artifacts", "id": "df01ed3b-eb61-4eca-9965-6b3d789751b2", "isEnabled": true, "isAdmin": false, "value": "OnlineMeetingArtifact.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read search configurations, without a signed-in user.", "consentDisplayName": "Read your organization's search configuration", "id": "ada977a5-b8b1-493b-9a91-66c206d76ecf", "isEnabled": true, "isAdmin": false, "value": "SearchConfiguration.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write search configurations, without a signed-in user.", "consentDisplayName": "Read and write your organization's search configuration", "id": "0e778b85-fefa-466d-9eec-750569d92122", "isEnabled": true, "isAdmin": false, "value": "SearchConfiguration.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all risky service principal information for your organization, without a signed-in user.", "consentDisplayName": "Read all identity risky service principal information", "id": "607c7344-0eed-41e5-823a-9695ebe1b7b0", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskyServicePrincipal.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs for any chat, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage only its own tabs for all chats", "id": "9f62e4a2-a2d6-4350-b28b-d244728c4f86", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteSelfForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs in any team, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage only its own tabs for all teams", "id": "91c32b81-0ef0-453f-a5c7-4ce2e562f449", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteSelfForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall its own tabs for any user, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage only its own tabs for all users", "id": "3c42dec6-49e8-4a0a-b469-36cff0d9da93", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteSelfForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update identity risky service principal for your organization, without a signed-in user.", "consentDisplayName": "Read and write all identity risky service principal information", "id": "cb8d6980-6bcb-4507-afec-ed6de3a2d798", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskyServicePrincipal.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the app to read and write the management data for Teams devices, without a signed-in user.", "consentDisplayName": "Read and write Teams devices", "id": "79c02f5b-bd4f-4713-bc2c-a8a4a66e127b", "isEnabled": true, "isAdmin": false, "value": "TeamworkDevice.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the app to read the management data for Teams devices, without a signed-in user.", "consentDisplayName": "Read Teams devices", "id": "0591bafd-7c1c-4c30-a2a5-2b9aacb1dfe8", "isEnabled": true, "isAdmin": false, "value": "TeamworkDevice.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to run hunting queries, without a signed-in user.", "consentDisplayName": "Run hunting queries", "id": "dd98c7f5-2d42-42d3-a0e4-633161547251", "isEnabled": true, "isAdmin": false, "value": "ThreatHunting.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read eDiscovery objects such as cases, custodians, review sets and other related objects without a signed-in user.", "consentDisplayName": "Read all eDiscovery objects", "id": "50180013-6191-4d1e-a373-e590ff4e66af", "isEnabled": true, "isAdmin": false, "value": "eDiscovery.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write eDiscovery objects such as cases, custodians, review sets and other related objects without a signed-in user.", "consentDisplayName": "Read and write all eDiscovery objects", "id": "b2620db1-3bf7-4c5b-9cb9-576d29eac736", "isEnabled": true, "isAdmin": false, "value": "eDiscovery.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all security alerts, without a signed-in user.", "consentDisplayName": "Read all security alerts", "id": "472e4a4d-bb4a-4026-98d1-0b0d74cb74a5", "isEnabled": true, "isAdmin": false, "value": "SecurityAlert.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write to all security alerts, without a signed-in user.", "consentDisplayName": "Read and write to all security alerts", "id": "ed4fca05-be46-441f-9803-1873825f8fdb", "isEnabled": true, "isAdmin": false, "value": "SecurityAlert.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all security incidents, without a signed-in user.", "consentDisplayName": "Read all security incidents", "id": "45cc0394-e837-488b-a098-1918f48d186c", "isEnabled": true, "isAdmin": false, "value": "SecurityIncident.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write to all security incidents, without a signed-in user.", "consentDisplayName": "Read and write to all security incidents", "id": "34bf0e97-1971-4929-b999-9e2442d941d7", "isEnabled": true, "isAdmin": false, "value": "SecurityIncident.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write custom security attribute assignments for all principals in the tenant without a signed in user.", "consentDisplayName": "Read and write custom security attribute assignments", "id": "de89b5e4-5b8f-48eb-8925-29c2b33bd8bd", "isEnabled": true, "isAdmin": false, "value": "CustomSecAttributeAssignment.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write custom security attribute definitions for the tenant without a signed in user.", "consentDisplayName": "Read and write custom security attribute definitions", "id": "12338004-21f4-4896-bf5e-b75dfaf1016d", "isEnabled": true, "isAdmin": false, "value": "CustomSecAttributeDefinition.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's cross tenant access policies without a signed-in user.", "consentDisplayName": "Read and write your organization's cross tenant access policies", "id": "338163d7-f101-4c92-94ba-ca46fe52447c", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.CrossTenantAccess"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all external items without a signed-in user.", "consentDisplayName": "Read all external items", "id": "7a7cffad-37d2-4f48-afa4-c6ab129adcc2", "isEnabled": true, "isAdmin": false, "value": "ExternalItem.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all external connections without a signed-in user.", "consentDisplayName": "Read and write all external connections", "id": "34c37bc0-2b40-4d5e-85e1-2365cd256d79", "isEnabled": true, "isAdmin": false, "value": "ExternalConnection.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all external connections without a signed-in user.", "consentDisplayName": "Read all external connections", "id": "1914711b-a1cb-4793-b019-c2ce0ed21b8c", "isEnabled": true, "isAdmin": false, "value": "ExternalConnection.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read custom security attribute definitions for the tenant without a signed in user.", "consentDisplayName": "Read custom security attribute definitions", "id": "b185aa14-d8d2-42c1-a685-0f5596613624", "isEnabled": true, "isAdmin": false, "value": "CustomSecAttributeDefinition.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read custom security attribute assignments for all principals in the tenant without a signed in user.", "consentDisplayName": "Read custom security attribute assignments", "id": "3b37c5a4-1226-493d-bec3-5d6c6b866f3f", "isEnabled": true, "isAdmin": false, "value": "CustomSecAttributeAssignment.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage workforce integrations to synchronize data from Microsoft Teams Shifts, without a signed-in user.", "consentDisplayName": "Read and write workforce integrations", "id": "202bf709-e8e6-478e-bcfd-5d63c50b68e3", "isEnabled": true, "isAdmin": false, "value": "WorkforceIntegration.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all presence information and write activity and availability of all users in the directory without a signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, time zone and location.", "consentDisplayName": "Read and write presence information for all users", "id": "83cded22-8297-4ff6-a7fa-e97e9545a259", "isEnabled": true, "isAdmin": false, "value": "Presence.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write tags in Teams without a signed-in user.", "consentDisplayName": "Read and write tags in Teams", "id": "a3371ca5-911d-46d6-901c-42c8c7a937d8", "isEnabled": true, "isAdmin": false, "value": "TeamworkTag.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read tags in Teams without a signed-in user.", "consentDisplayName": "Read tags in Teams", "id": "b74fd6c4-4bde-488e-9695-eeb100e4907f", "isEnabled": true, "isAdmin": false, "value": "TeamworkTag.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all Windows update deployment settings for the organization without a signed-in user.", "consentDisplayName": "Read and write all Windows update deployment settings", "id": "7dd1be58-6e76-4401-bf8d-31d1e8180d5b", "isEnabled": true, "isAdmin": false, "value": "WindowsUpdates.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write external connections without a signed-in user. The app can only read and write external connections that it is authorized to, or it can create new external connections. ", "consentDisplayName": "Read and write external connections", "id": "f431331c-49a6-499f-be1c-62af19c34a9d", "isEnabled": true, "isAdmin": false, "value": "ExternalConnection.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write external items without a signed-in user. The app can only read external items of the connection that it is authorized to.", "consentDisplayName": "Read and write external items", "id": "8116ae0f-55c2-452d-9944-d18420f5b2c8", "isEnabled": true, "isAdmin": false, "value": "ExternalItem.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the properties of Cloud PCs, without a signed-in user.", "consentDisplayName": "Read and write Cloud PCs", "id": "3b4349e1-8cf5-45a3-95b7-69d1751d3e6a", "isEnabled": true, "isAdmin": false, "value": "CloudPC.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the properties of Cloud PCs, without a signed-in user.", "consentDisplayName": "Read Cloud PCs", "id": "a9e09520-8ed4-4cde-838e-4fdea192c227", "isEnabled": true, "isAdmin": false, "value": "CloudPC.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to update service principal endpoints", "consentDisplayName": "Read and update service principal endpoints", "id": "89c8469c-83ad-45f7-8ff2-6e3d4285709e", "isEnabled": true, "isAdmin": false, "value": "ServicePrincipalEndpoint.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read service principal endpoints", "consentDisplayName": "Read service principal endpoints", "id": "5256681e-b7f6-40c0-8447-2d9db68797a0", "isEnabled": true, "isAdmin": false, "value": "ServicePrincipalEndpoint.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read terms of use acceptance statuses, without a signed in user.", "consentDisplayName": "Read all terms of use acceptance statuses", "id": "d8e4ec18-f6c0-4620-8122-c8b1f2bf400e", "isEnabled": true, "isAdmin": false, "value": "AgreementAcceptance.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write terms of use agreements, without a signed in user.", "consentDisplayName": "Read and write all terms of use agreements", "id": "c9090d00-6101-42f0-a729-c41074260d47", "isEnabled": true, "isAdmin": false, "value": "Agreement.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read terms of use agreements, without a signed in user.", "consentDisplayName": "Read all terms of use agreements", "id": "2f3e6f8c-093b-4c57-a58b-ba5ce494a169", "isEnabled": true, "isAdmin": false, "value": "Agreement.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read app consent requests and approvals, and deny or approve those requests without a signed-in user.", "consentDisplayName": "Read and write all consent requests", "id": "9f1b81a7-0223-4428-bfa4-0bcb5535f27d", "isEnabled": true, "isAdmin": false, "value": "ConsentRequest.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's consent requests policy without a signed-in user.", "consentDisplayName": "Read and write your organization's consent request policy", "id": "999f8c63-0a38-4f1b-91fd-ed1947bdd1a9", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.ConsentRequest"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read consent requests and approvals without a signed-in user.", "consentDisplayName": "Read all consent requests", "id": "1260ad83-98fb-4785-abbb-d6cc1806fd41", "isEnabled": true, "isAdmin": false, "value": "ConsentRequest.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the application to access a subset of site collections without a signed in user.  The specific site collections and the permissions granted will be configured in SharePoint Online.", "consentDisplayName": "Access selected site collections", "id": "883ea226-0bf2-4a8f-9f9d-92c9162a727d", "isEnabled": true, "isAdmin": false, "value": "Sites.Selected"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read tenant-wide print settings without a signed-in user.", "consentDisplayName": "Read tenant-wide print settings", "id": "b5991872-94cf-4652-9765-29535087c6d8", "isEnabled": true, "isAdmin": false, "value": "PrintSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create chats without a signed-in user. ", "consentDisplayName": "Create chats", "id": "d9c48af6-9ad9-47ad-82c3-63757137b9af", "isEnabled": true, "isAdmin": false, "value": "Chat.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Add and remove members from all chats, without a signed-in user.", "consentDisplayName": "Add and remove members from all chats", "id": "57257249-34ce-4810-a8a2-a03adf0c5693", "isEnabled": true, "isAdmin": false, "value": "ChatMember.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read the members of all chats, without a signed-in user.", "consentDisplayName": "Read the members of all chats", "id": "a3410be2-8e48-4f32-8454-c29a7465209d", "isEnabled": true, "isAdmin": false, "value": "ChatMember.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create and manage the API connectors used in user authentication flows, without a signed-in user.", "consentDisplayName": "Read and write API connectors for authentication flows", "id": "1dfe531a-24a6-4f1b-80f4-7a0dc5a0a171", "isEnabled": true, "isAdmin": false, "value": "APIConnectors.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the API connectors used in user authentication flows, without a signed-in user.", "consentDisplayName": "Read API connectors for authentication flows", "id": "b86848a7-d5b1-41eb-a9b4-54a4e6306e97", "isEnabled": true, "isAdmin": false, "value": "APIConnectors.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for any user, without a signed-in user.", "consentDisplayName": "Allow the app to manage all tabs for all users", "id": "425b4b59-d5af-45c8-832f-bb0b7402348a", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in any team, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage all tabs for all teams", "id": "6163d4f4-fbf8-43da-a7b4-060fe85ed148", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for any chat, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage all tabs for all chats", "id": "fd9ce730-a250-40dc-bd44-8dc8d20f39ea", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWriteForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all one-to-one and group chats messages in Microsoft Teams, without a signed-in user.", "consentDisplayName": "Read all chat messages", "id": "b9bb2381-47a4-46cd-aafb-00cb12f68504", "isEnabled": true, "isAdmin": false, "value": "ChatMessage.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all PSTN and direct routing call log data without a signed-in user.", "consentDisplayName": "Read PSTN and direct routing call log data", "id": "a2611786-80b3-417e-adaa-707d4261a5f0", "isEnabled": true, "isAdmin": false, "value": "CallRecord-PstnCalls.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read role-based access control (RBAC) settings for all RBAC providers without a signed-in user. This includes reading role definitions and role assignments.", "consentDisplayName": "Read role management data for all RBAC providers", "id": "c7fbd983-d9aa-4fa7-84b8-17382c103bc4", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's conditional access policies, without a signed-in user.", "consentDisplayName": "Read your organization's conditional access policies", "id": "********-e9ba-4e46-b07e-8ca78d182097", "isEnabled": true, "isAdmin": false, "value": "Policy.Read.ConditionalAccess"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create, edit, and delete all the short notes without a signed-in user.", "consentDisplayName": "Read, create, edit, and delete all users' short notes", "id": "842c284c-763d-4a97-838d-79787d129bab", "isEnabled": true, "isAdmin": false, "value": "ShortNotes.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all the short notes without a signed-in user.", "consentDisplayName": "Read all users' short notes", "id": "0c7d31ec-31ca-4f58-b6ec-9950b6b0de69", "isEnabled": true, "isAdmin": false, "value": "ShortNotes.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your tenant's service announcement messages, without a signed-in user. Messages may include information about new or changed features.", "consentDisplayName": "Read service messages", "id": "1b620472-6534-4fe6-9df2-4680e8aa28ec", "isEnabled": true, "isAdmin": false, "value": "ServiceMessage.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your tenant's service health information, without a signed-in user. Health information may include service issues or service health overviews.", "consentDisplayName": "Read service health", "id": "79c261e0-fe76-4144-aad5-bdc68fbe4037", "isEnabled": true, "isAdmin": false, "value": "ServiceHealth.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, edit or write all term store data, without a signed-in user. This includes all sets, groups and terms in the term store.", "consentDisplayName": "Read and write all term store data", "id": "f12eb8d6-28e3-46e6-b2c0-b7e4dc69fc95", "isEnabled": true, "isAdmin": false, "value": "TermStore.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all term store data, without a signed-in user. This includes all sets, groups and terms in the term store.", "consentDisplayName": "Read all term store data", "id": "ea047cc2-df29-4f3e-83a3-205de61501ca", "isEnabled": true, "isAdmin": false, "value": "TermStore.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Add and remove members from all teams, without a signed-in user. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "consentDisplayName": "Add and remove members with non-owner role for all teams", "id": "4437522e-9a86-4a41-a7da-e380edd4a97d", "isEnabled": true, "isAdmin": false, "value": "TeamMember.ReadWriteNonOwnerRole.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create teams without a signed-in user. ", "consentDisplayName": "Create teams", "id": "23fc2474-f741-46ce-8465-674744c5c361", "isEnabled": true, "isAdmin": false, "value": "Team.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself to any user, without a signed-in user.", "consentDisplayName": "Allow the app to manage itself for all users", "id": "908de74d-f8b2-4d6b-a9ed-2a17b3b78179", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteSelfForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in any team, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage itself for all teams", "id": "9f67436c-5415-4e7f-8ac1-3014a7132630", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteSelfForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for any chat, without a signed-in user.", "consentDisplayName": "Allow the Teams app to manage itself for all chats", "id": "73a45059-f39c-4baf-9182-4954ac0e55cf", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteSelfForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps for any user, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage Teams apps for all users", "id": "74ef0291-ca83-4d02-8c7e-d2391e6a444f", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in any team, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage Teams apps for all teams", "id": "5dad17ba-f6cc-4954-a5a2-a0dcc95154f0", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in any chat, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Manage Teams apps for all chats", "id": "9e19bae1-2623-4c4f-ab6e-2664615ff9a0", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadWriteForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the Teams apps that are installed for any user, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read installed Teams apps for all users", "id": "9ce09611-f4f7-4abd-a629-a05450422a97", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadForUser.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the Teams apps that are installed in any team, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read installed Teams apps for all teams", "id": "1f615aea-6bf9-4b05-84bd-46388e138537", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadForTeam.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the Teams apps that are installed in any chat, without a signed-in user. Does not give the ability to read application-specific settings.", "consentDisplayName": "Read installed Teams apps for all chats", "id": "cc7e7635-2586-41d6-adaa-a8d3bcad5ee5", "isEnabled": true, "isAdmin": false, "value": "TeamsAppInstallation.ReadForChat.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create chat and channel messages, without a signed in user. The app specifies which user appears as the sender, and can backdate the message to appear as if it was sent long ago. The messages can be sent to any chat or channel in the organization.", "consentDisplayName": "Create chat and channel messages with anyone's identity and with any timestamp", "id": "dfb0dd15-61de-45b2-be36-d6a69fba3c79", "isEnabled": true, "isAdmin": false, "value": "Teamwork.Migrate.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update print task definitions without a signed-in user. ", "consentDisplayName": "Read, write and update print task definitions", "id": "456b71a7-0ee0-4588-9842-c123fcc8f664", "isEnabled": true, "isAdmin": false, "value": "PrintTaskDefinition.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update the metadata of print jobs without a signed-in user. Does not allow access to print job document content.", "consentDisplayName": "Read and write basic information for print jobs", "id": "57878358-37f4-4d3a-8c20-4816e0d457b1", "isEnabled": true, "isAdmin": false, "value": "PrintJob.ReadWriteBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update the metadata and document content of print jobs without a signed-in user.", "consentDisplayName": "Read and write print jobs", "id": "5114b07b-2898-4de7-a541-53b0004e2e13", "isEnabled": true, "isAdmin": false, "value": "PrintJob.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read the metadata of print jobs without a signed-in user. Does not allow access to print job document content.", "consentDisplayName": "Read basic information for print jobs", "id": "fbf67eee-e074-4ef7-b965-ab5ce1c1f689", "isEnabled": true, "isAdmin": false, "value": "PrintJob.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read the metadata and document content of print jobs without a signed-in user. ", "consentDisplayName": "Read print jobs", "id": "ac6f956c-edea-44e4-bd06-64b1b4b9aec9", "isEnabled": true, "isAdmin": false, "value": "PrintJob.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to perform advanced operations like redirecting a print job to another printer without a signed-in user. Also allows the application to read and update the metadata of print jobs.", "consentDisplayName": "Perform advanced operations on print jobs", "id": "58a52f47-9e36-4b17-9ebe-ce4ef7f3e6c8", "isEnabled": true, "isAdmin": false, "value": "PrintJob.Manage.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and update printers without a signed-in user. Does not allow creating (registering) or deleting (unregistering) printers.", "consentDisplayName": "Read and update printers", "id": "f5b3f73d-6247-44df-a74c-866173fddab0", "isEnabled": true, "isAdmin": false, "value": "Printer.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read printers without a signed-in user. ", "consentDisplayName": "Read printers", "id": "9709bb33-4549-49d4-8ed9-a8f65e45bb0f", "isEnabled": true, "isAdmin": false, "value": "Printer.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage policies related to consent and permission grants for applications, without a signed-in user.", "consentDisplayName": "Manage consent and permission grant policies", "id": "a402ca1c-2696-4531-972d-6e5ee4aa11ea", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.PermissionGrant"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read policies related to consent and permission grants for applications, without a signed-in user.", "consentDisplayName": "Read consent and permission grant policies", "id": "9e640839-a198-48fb-8b9a-013fd6f6cbcd", "isEnabled": true, "isAdmin": false, "value": "Policy.Read.PermissionGrant"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read names and members of all one-to-one and group chats in Microsoft Teams, without a signed-in user.", "consentDisplayName": "Read names and members of all chat threads", "id": "b2e060da-3baf-4687-9611-f4ebc0f0cbde", "isEnabled": true, "isAdmin": false, "value": "Chat.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's authorization policy without a signed in user. For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "consentDisplayName": "Read and write your organization's authorization policy", "id": "fb221be6-99f2-473f-bd32-01c6a0e9ca3b", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.Authorization"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all authentication method policies for the tenant, without a signed-in user. ", "consentDisplayName": "Read and write all authentication method policies ", "id": "29c18626-4985-4dcd-85c0-193eef327366", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.AuthenticationMethod"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all authentication flow policies for the tenant, without a signed-in user.", "consentDisplayName": "Read and write authentication flow policies", "id": "25f85f3c-f66c-4205-8cd5-de92dd7f0cec", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.AuthenticationFlows"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Add and remove members from all channels, without a signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "consentDisplayName": "Add and remove members from all channels", "id": "35930dcf-aceb-4bd1-b99a-8ffed403c974", "isEnabled": true, "isAdmin": false, "value": "ChannelMember.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read the members of all channels, without a signed-in user.", "consentDisplayName": "Read the members of all channels", "id": "3b55498e-47ec-484f-8136-9013221c06a9", "isEnabled": true, "isAdmin": false, "value": "ChannelMember.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Add and remove members from all teams, without a signed-in user. Also allows changing a team member's role, for example from owner to non-owner.", "consentDisplayName": "Add and remove members from all teams", "id": "0121dc95-1b9f-4aed-8bac-58c5ac466691", "isEnabled": true, "isAdmin": false, "value": "TeamMember.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read the members of all teams, without a signed-in user.", "consentDisplayName": "Read the members of all teams", "id": "660b7406-55f1-41ca-a0ed-0b035e182f3e", "isEnabled": true, "isAdmin": false, "value": "TeamMember.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read all team's settings, without a signed-in user.", "consentDisplayName": "Read all teams' settings", "id": "242607bd-1d2c-432c-82eb-bdb27baa23ab", "isEnabled": true, "isAdmin": false, "value": "TeamSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read and change all teams' settings, without a signed-in user.", "consentDisplayName": "Read and change all teams' settings", "id": "bdd80a03-d9bc-451d-b7c4-ce7c63fe3c8f", "isEnabled": true, "isAdmin": false, "value": "TeamSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read all channel names and channel descriptions, without a signed-in user.", "consentDisplayName": "Read the names and descriptions  of all channels", "id": "59a6b24b-4225-4393-8165-ebaec5f55d7a", "isEnabled": true, "isAdmin": false, "value": "Channel.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Get a list of all teams, without a signed-in user.", "consentDisplayName": "Get a list of all teams", "id": "2280dda6-0bfd-44ee-a2f4-cb867cfc4c1e", "isEnabled": true, "isAdmin": false, "value": "Team.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read and write the names, descriptions, and settings of all channels, without a signed-in user.", "consentDisplayName": "Read and write the names, descriptions, and settings of all channels", "id": "243cded2-bd16-4fd6-a953-ff8177894c3d", "isEnabled": true, "isAdmin": false, "value": "ChannelSettings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read all channel names, channel descriptions, and channel settings, without a signed-in user.", "consentDisplayName": "Read the names, descriptions, and settings of all channels", "id": "c97b873f-f59f-49aa-8a0e-52b32d762124", "isEnabled": true, "isAdmin": false, "value": "ChannelSettings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Delete channels in any team, without a signed-in user.", "consentDisplayName": "Delete channels", "id": "6a118a39-1227-45d4-af0c-ea7b40d210bc", "isEnabled": true, "isAdmin": false, "value": "Channel.Delete.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Create channels in any team, without a signed-in user.", "consentDisplayName": "Create channels", "id": "f3a65bd4-b703-46df-8f7e-0174fea562aa", "isEnabled": true, "isAdmin": false, "value": "Channel.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write access packages and related entitlement management resources without a signed-in user.", "consentDisplayName": "Read and write all entitlement management resources", "id": "9acd699f-1e81-4958-b001-93b1d2506e19", "isEnabled": true, "isAdmin": false, "value": "EntitlementManagement.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read access packages and related entitlement management resources without a signed-in user.", "consentDisplayName": "Read all entitlement management resources", "id": "c74fd47d-ed3c-45c3-9a9e-b8676de685d2", "isEnabled": true, "isAdmin": false, "value": "EntitlementManagement.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create or delete document libraries and lists in all site collections without a signed in user.", "consentDisplayName": "Create, edit, and delete items and lists in all site collections", "id": "0c0bf378-bf22-4481-8f81-9e89a9b4960a", "isEnabled": true, "isAdmin": false, "value": "Sites.Manage.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to have full control of all site collections without a signed in user.", "consentDisplayName": "Have full control of all site collections", "id": "a82116e5-55eb-4c41-a434-62fe8a61c773", "isEnabled": true, "isAdmin": false, "value": "Sites.FullControl.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all the OneNote notebooks in your organization, without a signed-in user.", "consentDisplayName": "Read and write all OneNote notebooks", "id": "0c458cef-11f3-48c2-a568-c66751c238c0", "isEnabled": true, "isAdmin": false, "value": "Notes.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage all users' shift schedule preferences without a signed-in user.", "consentDisplayName": "Read and write all user shift preferences", "id": "d1eec298-80f3-49b0-9efb-d90e224798ac", "isEnabled": true, "isAdmin": false, "value": "UserShiftPreferences.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all users' shift schedule preferences without a signed-in user.", "consentDisplayName": "Read all user shift preferences", "id": "de023814-96df-4f53-9376-1e2891ef5a18", "isEnabled": true, "isAdmin": false, "value": "UserShiftPreferences.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update and delete identities that are associated with a user's account, without a signed in user. This controls the identities users can sign-in with.", "consentDisplayName": "Manage all users' identities", "id": "c529cfca-c91b-489c-af2b-d92990b66ce6", "isEnabled": true, "isAdmin": false, "value": "User.ManageIdentities.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's devices' configuration information without a signed-in user.", "consentDisplayName": "Read all devices", "id": "7438b122-aefc-4978-80ed-43db9fcc7715", "isEnabled": true, "isAdmin": false, "value": "Device.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's application configuration policies, without a signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy, tokenIssuancePolicy  and tokenLifetimePolicy.", "consentDisplayName": "Read and write your organization's application configuration policies", "id": "be74164b-cff1-491c-8741-e671cb536e13", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.ApplicationConfiguration"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all domain properties without a signed-in user.", "consentDisplayName": "Read domains", "id": "dbb9058a-0e50-45d7-ae91-66909b5d4664", "isEnabled": true, "isAdmin": false, "value": "Domain.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read and write tabs in any team in Microsoft Teams, without a signed-in user. This does not give access to the content inside the tabs.", "consentDisplayName": "Read and write tabs in Microsoft Teams.", "id": "a96d855f-016b-47d7-b51c-1218a98d791c", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read the names and settings of tabs inside any team in Microsoft Teams, without a signed-in user. This does not give access to the content inside the tabs. ", "consentDisplayName": "Read tabs in Microsoft Teams.", "id": "46890524-499a-4bb2-ad64-1476b4f3e1cf", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create tabs in any team in Microsoft Teams, without a signed-in user. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "consentDisplayName": "Create tabs in Microsoft Teams.", "id": "49981c42-fd7b-4530-be03-e77b21aed25e", "isEnabled": true, "isAdmin": false, "value": "TeamsTab.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": " Allows the app to read authentication methods of all users in your organization, without a signed-in user.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the                      app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "consentDisplayName": " Read all users' authentication methods", "id": "38d9df27-64da-44fd-b7c5-a6fbac20248f", "isEnabled": true, "isAdmin": false, "value": "UserAuthenticationMethod.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the application to read and write authentication methods of all users in your organization, without a signed-in user.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This                      does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods", "consentDisplayName": "Read and write all users' authentication methods ", "id": "50483e42-d915-4231-9639-7fdb7fd190e5", "isEnabled": true, "isAdmin": false, "value": "UserAuthenticationMethod.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's conditional access policies, without a signed-in user.", "consentDisplayName": "Read and write your organization's conditional access policies", "id": "01c0a623-fc9b-48e9-b794-0756f8e8f067", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.ConditionalAccess"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read call records for all calls and online meetings without a signed-in user.", "consentDisplayName": "Read all call records", "id": "45bbb07e-7321-4fd7-a8f6-3ff27e6a81c8", "isEnabled": true, "isAdmin": false, "value": "CallRecords.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage all schedules, schedule groups, shifts and associated entities in the Teams or Shifts application without a signed-in user.", "consentDisplayName": "Read and write all schedule items", "id": "b7760610-0545-4e8a-9ec3-cce9e63db01c", "isEnabled": true, "isAdmin": false, "value": "Schedule.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all schedules, schedule groups, shifts and associated entities in the Teams or Shifts application without a signed-in user.", "consentDisplayName": "Read all schedule items", "id": "7b2ebf90-d836-437f-b90d-7b62722c4456", "isEnabled": true, "isAdmin": false, "value": "Schedule.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read your organization's threat assessment requests, without a signed-in user.", "consentDisplayName": "Read threat assessment requests", "id": "f8f035bb-2cce-47fb-8bf5-7baf3ecbee48", "isEnabled": true, "isAdmin": false, "value": "ThreatAssessment.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create groups without a signed-in user.", "consentDisplayName": "Create groups", "id": "bf7b1a76-6e77-406b-b258-bf5c7720e98f", "isEnabled": true, "isAdmin": false, "value": "Group.Create"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to list groups, read basic properties, read and update the membership of the groups this app has access to without a signed-in user. Group properties and owners cannot be updated and groups cannot be deleted.", "consentDisplayName": "Read and write all group memberships", "id": "dbaae8cf-10b5-4b86-a4a1-f871c94c6695", "isEnabled": true, "isAdmin": false, "value": "GroupMember.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read memberships and basic group properties for all groups without a signed-in user.", "consentDisplayName": "Read all group memberships", "id": "98830695-27a2-44f7-8c18-0c3ebc9698f6", "isEnabled": true, "isAdmin": false, "value": "GroupMember.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all applications and service principals without a signed-in user.", "consentDisplayName": "Read all applications", "id": "9a5d68dd-52b0-4cc2-bd40-abcf44ac3a30", "isEnabled": true, "isAdmin": false, "value": "Application.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to send, read, update and delete user’s notifications, without a signed-in user.", "consentDisplayName": "Deliver and manage all user's notifications", "id": "4e774092-a092-48d1-90bd-baad67c7eb47", "isEnabled": true, "isAdmin": false, "value": "UserNotification.ReadWrite.CreatedByApp"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all the indicators for your organization, without a signed-in user.", "consentDisplayName": "Read all threat indicators", "id": "197ee4e9-b993-4066-898f-d6aecc55125b", "isEnabled": true, "isAdmin": false, "value": "ThreatIndicators.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) in your organization, without a signed-in user.", "consentDisplayName": "Read and write privileged access to Azure resources", "id": "6f9d5abc-2db6-400b-a267-7de22a40fb87", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.ReadWrite.AzureResources"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups in your organization, without a signed-in user.", "consentDisplayName": "Read and write privileged access to Azure AD groups", "id": "2f6817f8-7b12-4f0f-bc18-eeaf60705a9e", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.ReadWrite.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles in your organization, without a signed-in user.", "consentDisplayName": "Read and write privileged access to Azure AD roles", "id": "854d9ab1-6657-4ec8-be45-823027bcd009", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.ReadWrite.AzureAD"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation of user privileges to audit Azure resources in your organization, without a signed-in user.", "consentDisplayName": "Read privileged access to Azure resources", "id": "5df6fe86-1be0-44eb-b916-7bd443a71236", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.Read.AzureResources"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups in your organization, without a signed-in user.", "consentDisplayName": "Read privileged access to Azure AD groups", "id": "01e37dc9-c035-40bd-b438-b2879c4870a6", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.Read.AzureADGroup"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles in your organization, without a signed-in user.", "consentDisplayName": "Read privileged access to Azure AD roles", "id": "4cdc2547-9148-4295-8d11-be0db1391d6b", "isEnabled": true, "isAdmin": false, "value": "PrivilegedAccess.Read.AzureAD"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create new notifications in users' teamwork activity feeds without a signed in user. These notifications may not be discoverable or be held or governed by compliance policies.", "consentDisplayName": "Send a teamwork activity to any user", "id": "a267235f-af13-44dc-8385-c1dc93023186", "isEnabled": true, "isAdmin": false, "value": "TeamsActivity.Send"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all users' teamwork activity feed, without a signed-in user.", "consentDisplayName": "Read all users' teamwork activity feed", "id": "70dec828-f620-4914-aa83-a29117306807", "isEnabled": true, "isAdmin": false, "value": "TeamsActivity.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), without a signed-in user.", "consentDisplayName": "Manage all delegated permission grants", "id": "8e8e4742-1d95-4f68-9d56-6ee75648c72a", "isEnabled": true, "isAdmin": false, "value": "DelegatedPermissionGrant.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, without a signed-in user.", "consentDisplayName": "Manage app permission grants and app role assignments", "id": "06b708a9-e830-4db3-a914-8e69da51d44f", "isEnabled": true, "isAdmin": false, "value": "AppRoleAssignment.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration, without a signed-in user.", "consentDisplayName": "Read and write Microsoft Intune configuration", "id": "5ac13192-7ace-4fcf-b828-1a26f28068ee", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementServiceConfig.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings, without a signed-in user.", "consentDisplayName": "Read and write Microsoft Intune RBAC settings", "id": "e330c4f0-4170-414e-a55a-2f022ec2b57b", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementRBAC.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the properties of devices managed by Microsoft Intune, without a signed-in user. Does not allow high impact operations such as remote wipe and password reset on the device’s owner", "consentDisplayName": "Read and write Microsoft Intune devices", "id": "243333ab-4d21-40cb-a475-36241daa0842", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementManagedDevices.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune, without a signed-in user.", "consentDisplayName": "Perform user-impacting remote actions on Microsoft Intune devices", "id": "5b07b0dd-2377-4e44-a38d-703f09a0dc3c", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementManagedDevices.PrivilegedOperations.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups, without a signed-in user.", "consentDisplayName": "Read and write Microsoft Intune device configuration and policies", "id": "9241abd9-d0e6-425a-bd4f-47ba86e767a4", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementConfiguration.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune, without a signed-in user.", "consentDisplayName": "Read and write Microsoft Intune apps", "id": "78145de6-330d-4800-a6ce-494ff2d33d07", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementApps.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all organizational contacts without a signed-in user.  These contacts are managed by the organization and are different from a user's personal contacts.", "consentDisplayName": "Read organizational contacts", "id": "e1a88a34-94c4-4418-be12-c87b00e26bea", "isEnabled": true, "isAdmin": false, "value": "OrgContact.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to place outbound calls to a single user and transfer calls to users in your organization’s directory, without a signed-in user.", "consentDisplayName": "Initiate outgoing 1 to 1 calls from the app", "id": "284383ee-7f6e-4e40-a2a8-e85dcb029101", "isEnabled": true, "isAdmin": false, "value": "Calls.Initiate.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to place outbound calls to multiple users and add participants to meetings in your organization, without a signed-in user.", "consentDisplayName": "Initiate outgoing group calls from the app", "id": "4c277553-8a09-487b-8023-29ee378d8324", "isEnabled": true, "isAdmin": false, "value": "Calls.InitiateGroupCall.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to join group calls and scheduled meetings in your organization, without a signed-in user.  The app will be joined with the privileges of a directory user to meetings in your organization.", "consentDisplayName": "Join group calls and meetings as an app", "id": "f6b49018-60ab-4f81-83bd-22caeabfed2d", "isEnabled": true, "isAdmin": false, "value": "Calls.JoinGroupCall.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to anonymously join group calls and scheduled meetings in your organization, without a signed-in user.  The app will be joined as a guest to meetings in your organization.", "consentDisplayName": "Join group calls and meetings as a guest", "id": "fd7ccf6b-3d28-418b-9701-cd10f5cd2fd4", "isEnabled": true, "isAdmin": false, "value": "Calls.JoinGroupCallAsGuest.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to get direct access to media streams in a call, without a signed-in user.", "consentDisplayName": "Access media streams in a call as an app", "id": "a7a681dc-756e-4909-b988-f160edc6655f", "isEnabled": true, "isAdmin": false, "value": "Calls.AccessMedia.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read online meeting details in your organization, without a signed-in user.", "consentDisplayName": "Read online meeting details", "id": "c1684f21-1984-47fa-9d61-2dc8c296bb70", "isEnabled": true, "isAdmin": false, "value": "OnlineMeetings.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and create online meetings as an application in your organization.", "consentDisplayName": "Read and create online meetings", "id": "b8bb2037-6e08-44ac-a4ea-4674e010e2a4", "isEnabled": true, "isAdmin": false, "value": "OnlineMeetings.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read or write your organization's user flows, without a signed-in user.", "consentDisplayName": "Read and write all identity user flows", "id": "65319a09-a2be-469d-8782-f6b07debf789", "isEnabled": true, "isAdmin": false, "value": "IdentityUserFlow.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization's user flows, without a signed-in user.", "consentDisplayName": "Read all identity user flows", "id": "1b0c317f-dd31-4305-9932-259a8b6e8099", "isEnabled": true, "isAdmin": false, "value": "IdentityUserFlow.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete events of all calendars without a signed-in user.", "consentDisplayName": "Read and write calendars in all mailboxes", "id": "ef54d2bf-783f-4e0f-bca1-3210c0444d99", "isEnabled": true, "isAdmin": false, "value": "Calendars.ReadWrite"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read events of all calendars without a signed-in user.", "consentDisplayName": "Read calendars in all mailboxes", "id": "798ee544-9d2d-430c-a058-570e29e34338", "isEnabled": true, "isAdmin": false, "value": "Calendars.Read"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all device properties without a signed in user.  Does not allow device creation, device deletion or update of device alternative security identifiers.", "consentDisplayName": "Read and write devices", "id": "1138cb37-bd11-4084-a2b7-9f71582aeddb", "isEnabled": true, "isAdmin": false, "value": "Device.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write data in your organization's directory, such as users, and groups, without a signed-in user.  Does not allow user or group deletion.", "consentDisplayName": "Read and write directory data", "id": "19dbc75e-c2e2-444c-a770-ec69d8559fc7", "isEnabled": true, "isAdmin": false, "value": "Directory.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read data in your organization's directory, such as users, groups and apps, without a signed-in user.", "consentDisplayName": "Read directory data", "id": "7ab1d382-f21e-4acd-a863-ba3e13f7da61", "isEnabled": true, "isAdmin": false, "value": "Directory.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create groups, read all group properties and memberships, update group properties and memberships, and delete groups. Also allows the app to read and write conversations. All of these operations can be performed by the app without a signed-in user.", "consentDisplayName": "Read and write all groups", "id": "62a82d76-70ea-41e2-9197-370581804d09", "isEnabled": true, "isAdmin": false, "value": "Group.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read group properties and memberships, and read conversations for all groups, without a signed-in user.", "consentDisplayName": "Read all groups", "id": "5b567255-7703-4780-807c-7be8301ae99b", "isEnabled": true, "isAdmin": false, "value": "Group.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete all contacts in all mailboxes without a signed-in user.", "consentDisplayName": "Read and write contacts in all mailboxes", "id": "6918b873-d17a-4dc1-b314-35f528134491", "isEnabled": true, "isAdmin": false, "value": "Contacts.ReadWrite"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all contacts in all mailboxes without a signed-in user.", "consentDisplayName": "Read contacts in all mailboxes", "id": "089fe4d0-434a-44c5-8827-41ba8a0b17f5", "isEnabled": true, "isAdmin": false, "value": "Contacts.Read"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to send mail as any user without a signed-in user.", "consentDisplayName": "Send mail as any user", "id": "b633e1c5-b582-4048-a93e-9f11b44c7e96", "isEnabled": true, "isAdmin": false, "value": "Mail.Send"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete mail in all mailboxes without a signed-in user. Does not include permission to send mail.", "consentDisplayName": "Read and write mail in all mailboxes", "id": "e2a3a72e-5f79-4c64-b1b1-878b674786c9", "isEnabled": true, "isAdmin": false, "value": "Mail.ReadWrite"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read mail in all mailboxes without a signed-in user.", "consentDisplayName": "Read mail in all mailboxes", "id": "810c84a8-4a9e-49e6-bf7d-12d183f40d01", "isEnabled": true, "isAdmin": false, "value": "Mail.Read"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read user's mailbox settings without a signed-in user. Does not include permission to send mail.", "consentDisplayName": "Read all user mailbox settings", "id": "40f97065-369a-49f4-947c-6a255697ae91", "isEnabled": true, "isAdmin": false, "value": "MailboxSettings.Read"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write all domain properties without a signed in user.  Also allows the app to add,  verify and remove domains.", "consentDisplayName": "Read and write domains", "id": "7e05723c-0bb0-42da-be95-ae9f08a6e53c", "isEnabled": true, "isAdmin": false, "value": "Domain.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete user's mailbox settings without a signed-in user. Does not include permission to send mail.", "consentDisplayName": "Read and write all user mailbox settings", "id": "6931bccd-447a-43d1-b442-00a195474933", "isEnabled": true, "isAdmin": false, "value": "MailboxSettings.ReadWrite"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update and delete applications and service principals without a signed-in user.  Does not allow management of consent grants.", "consentDisplayName": "Read and write all applications", "id": "1bfefb4e-e0b5-418b-a88f-73c46d2cc8e9", "isEnabled": true, "isAdmin": false, "value": "Application.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to update Microsoft Teams channel messages by patching a set of Data Loss Prevention (DLP) policy violation properties to handle the output of DLP processing.", "consentDisplayName": "Flag channel messages for violating policy", "id": "4d02b0cc-d90b-441f-8d82-4fb55c34d6bb", "isEnabled": true, "isAdmin": false, "value": "ChannelMessage.UpdatePolicyViolation.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all channel messages in Microsoft Teams", "consentDisplayName": "Read all channel messages", "id": "7b2449af-6ccd-4f4d-9f78-e550c193f0d1", "isEnabled": true, "isAdmin": false, "value": "ChannelMessage.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all 1-to-1 or group chat messages in Microsoft Teams.", "consentDisplayName": "Read all chat messages", "id": "6b7d71aa-70aa-4810-a8d9-5d9fb2830017", "isEnabled": true, "isAdmin": false, "value": "Chat.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to update Microsoft Teams 1-to-1 or group chat messages by patching a set of Data Loss Prevention (DLP) policy violation properties to handle the output of DLP processing.", "consentDisplayName": "Flag chat messages for violating policy", "id": "7e847308-e030-4183-9899-5235d7270f58", "isEnabled": true, "isAdmin": false, "value": "Chat.UpdatePolicyViolation.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read any user's scored list of relevant people, without a signed-in user. The list can include local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "consentDisplayName": "Read all users' relevant people lists", "id": "b528084d-ad10-4598-8b93-929746b4d7d6", "isEnabled": true, "isAdmin": false, "value": "People.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read all service usage reports without a signed-in user.  Services that provide usage reports include Office 365 and Azure Active Directory.", "consentDisplayName": "Read all usage reports", "id": "230c1aed-a721-4c5d-9cb4-a90514e508ef", "isEnabled": true, "isAdmin": false, "value": "Reports.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read access reviews, reviewers, decisions and settings in the organization, without a signed-in user.", "consentDisplayName": "Read all access reviews", "id": "d07a8cc0-3d51-4b77-b3b0-32704d1f69fa", "isEnabled": true, "isAdmin": false, "value": "AccessReview.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings in the organization, without a signed-in user.", "consentDisplayName": "Manage all access reviews", "id": "ef5f7d5c-338f-44b0-86c3-351f46c8bb5f", "isEnabled": true, "isAdmin": false, "value": "AccessReview.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read programs and program controls in the organization, without a signed-in user.", "consentDisplayName": "Read all programs", "id": "eedb7fdd-7539-4345-a38b-4839e4a84cbd", "isEnabled": true, "isAdmin": false, "value": "ProgramControl.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, delete and perform actions on programs and program controls in the organization, without a signed-in user.", "consentDisplayName": "Manage all programs", "id": "60a901ed-09f7-4aa5-a16e-7dd3d6f9de36", "isEnabled": true, "isAdmin": false, "value": "ProgramControl.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create other applications, and fully manage those applications (read, update, update application secrets and delete), without a signed-in user.  It cannot update any apps that it is not an owner of.", "consentDisplayName": "Manage apps that this app creates or owns", "id": "18a4783c-866b-4cc7-a460-3d5e5662c884", "isEnabled": true, "isAdmin": false, "value": "Application.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and query your audit log activities, without a signed-in user.", "consentDisplayName": "Read all audit log data", "id": "b0afded3-3588-46d8-8b3d-9842eff778da", "isEnabled": true, "isAdmin": false, "value": "AuditLog.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read user profiles without a signed in user.", "consentDisplayName": "Read all users' full profiles", "id": "df021288-bdef-4463-88db-98f22de89214", "isEnabled": true, "isAdmin": false, "value": "User.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update user profiles without a signed in user.", "consentDisplayName": "Read and write all users' full profiles", "id": "741f803b-c850-494e-b5df-cde7c675a1ca", "isEnabled": true, "isAdmin": false, "value": "User.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the identity risky user information for your organization without a signed in user.", "consentDisplayName": "Read all identity risky user information", "id": "dc5007c0-2d7d-4c42-879c-2dab87571379", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskyUser.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Manage the state and settings of all Microsoft education apps.", "consentDisplayName": "Manage education app settings", "id": "9bc431c3-b8bc-4a8d-a219-40f10f92eff6", "isEnabled": true, "isAdmin": false, "value": "EduAdministration.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Read the state and settings of all Microsoft education apps.", "consentDisplayName": "Read Education app settings", "id": "7c9db06a-ec2d-4e7b-a592-5a1e30992566", "isEnabled": true, "isAdmin": false, "value": "EduAdministration.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update and delete all class assignments with grades for all users without a signed-in user.", "consentDisplayName": "Create, read, update and delete all class assignments with grades", "id": "0d22204b-6cad-4dd0-8362-3e3f2ae699d9", "isEnabled": true, "isAdmin": false, "value": "EduAssignments.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all class assignments with grades for all users without a signed-in user.", "consentDisplayName": "Read all class assignments with grades", "id": "4c37e1b6-35a1-43bf-926a-6f30f2cdf585", "isEnabled": true, "isAdmin": false, "value": "EduAssignments.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update and delete all class assignments without grades for all users without a signed-in user.", "consentDisplayName": "Create, read, update and delete all class assignments without grades", "id": "f431cc63-a2de-48c4-8054-a34bc093af84", "isEnabled": true, "isAdmin": false, "value": "EduAssignments.ReadWriteBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all class assignments without grades for all users without a signed-in user.", "consentDisplayName": "Read all class assignments without grades", "id": "6e0a958b-b7fc-4348-b7c4-a6ab9fd3dd0e", "isEnabled": true, "isAdmin": false, "value": "EduAssignments.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the structure of schools and classes in the organization's roster and education-specific information about all users to be read and written.", "consentDisplayName": "Read and write the organization's roster", "id": "d1808e82-ce13-47af-ae0d-f9b254e6d58a", "isEnabled": true, "isAdmin": false, "value": "EduRoster.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the structure of schools and classes in the organization's roster and education-specific information about all users to be read.", "consentDisplayName": "Read the organization's roster", "id": "e0ac9e1b-cb65-4fc5-87c5-1a8bc181f648", "isEnabled": true, "isAdmin": false, "value": "EduRoster.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read a limited subset of properties from both the structure of schools and classes in the organization's roster and education-specific information about all users. Includes name, status, role, email address and photo.", "consentDisplayName": "Read a limited subset of the organization's roster", "id": "0d412a8c-a06c-439f-b3ec-8abcf54d2f96", "isEnabled": true, "isAdmin": false, "value": "EduRoster.ReadBasic.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the identity risk event information for your organization without a signed in user.", "consentDisplayName": "Read all identity risk event information", "id": "6e472fd1-ad78-48da-a0f0-97ab2c6b769e", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskEvent.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all files in all site collections without a signed in user.", "consentDisplayName": "Read files in all site collections", "id": "01d4889c-1287-42c6-ac1f-5d1e02578ef6", "isEnabled": true, "isAdmin": false, "value": "Files.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update identity risky user information for your organization without a signed-in user.  Update operations include dismissing risky users.", "consentDisplayName": "Read and write all risky user information", "id": "656f6061-f9fe-4807-9708-6a2e0934df76", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskyUser.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and update identity risk detection information for your organization without a signed-in user. Update operations include confirming risk event detections. ", "consentDisplayName": "Read and write all risk detection information", "id": "db06fb33-1953-4b7b-a2ac-f1e2c854f7ae", "isEnabled": true, "isAdmin": false, "value": "IdentityRiskEvent.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read and write all chat messages in Microsoft Teams, without a signed-in user.", "consentDisplayName": "Read and write all chat messages", "id": "294ce7c9-31ba-490a-ad7d-97a7d075e4ed", "isEnabled": true, "isAdmin": false, "value": "Chat.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization’s security events without a signed-in user.", "consentDisplayName": "Read your organization’s security events", "id": "bf394140-e372-4bf9-a898-299cfc7564e5", "isEnabled": true, "isAdmin": false, "value": "SecurityEvents.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization’s security events without a signed-in user. Also allows the app to update editable properties in security events.", "consentDisplayName": "Read and update your organization’s security events", "id": "d903a879-88e0-4c09-b0c9-82f6a1333f84", "isEnabled": true, "isAdmin": false, "value": "SecurityEvents.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read documents and list items in all site collections without a signed in user.", "consentDisplayName": "Read items in all site collections ", "id": "332a536c-c7ef-4017-ab91-336970924f0d", "isEnabled": true, "isAdmin": false, "value": "Sites.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read security actions, without a signed-in user.", "consentDisplayName": "Read your organization's security actions", "id": "5e0edab9-c148-49d0-b423-ac253e121825", "isEnabled": true, "isAdmin": false, "value": "SecurityActions.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read or update security actions, without a signed-in user.", "consentDisplayName": "Read and update your organization's security actions", "id": "f2bf083f-0179-402a-bedb-b2784de8a49b", "isEnabled": true, "isAdmin": false, "value": "SecurityActions.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), without a signed-in user.  It cannot update any threat indicators it does not own.", "consentDisplayName": "Manage threat indicators this app creates or owns", "id": "21792b6c-c986-4ffc-85de-df9da54b52fa", "isEnabled": true, "isAdmin": false, "value": "ThreatIndicators.ReadWrite.OwnedBy"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, create, update and delete all files in all site collections without a signed in user. ", "consentDisplayName": "Read and write files in all site collections", "id": "75359482-378d-4052-8f01-80520e7db3cd", "isEnabled": true, "isAdmin": false, "value": "Files.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to invite guest users to the organization, without a signed-in user.", "consentDisplayName": "Invite guest users to the organization", "id": "09850681-111b-4a89-9bed-3f2cae46d706", "isEnabled": true, "isAdmin": false, "value": "User.Invite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all the OneNote notebooks in your organization, without a signed-in user.", "consentDisplayName": "Read all OneNote notebooks", "id": "3aeca27b-ee3a-4c2b-8ded-80376e2134a4", "isEnabled": true, "isAdmin": false, "value": "Notes.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows an app to read published sensitivity labels and label policy settings for the entire organization or a specific user, without a signed in user.", "consentDisplayName": "Read all published labels and label policies for an organization.", "id": "19da66cb-0fb0-4390-b071-ebc76a349482", "isEnabled": true, "isAdmin": false, "value": "InformationProtectionPolicy.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read administrative units and administrative unit membership without a signed-in user.", "consentDisplayName": "Read all administrative units", "id": "134fd756-38ce-4afd-ba33-e9623dbe66c2", "isEnabled": true, "isAdmin": false, "value": "AdministrativeUnit.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership without a signed-in user.", "consentDisplayName": "Read and write all administrative units", "id": "5eb59dd3-1da2-4329-8733-9dabdc435916", "isEnabled": true, "isAdmin": false, "value": "AdministrativeUnit.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read your organization’s identity (authentication) providers’ properties without a signed in user.", "consentDisplayName": "Read identity providers", "id": "e321f0bb-e7f7-481e-bb28-e3b0b32d4bd0", "isEnabled": true, "isAdmin": false, "value": "IdentityProvider.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization’s identity (authentication) providers’ properties without a signed in user.", "consentDisplayName": "Read and write identity providers", "id": "90db2b9a-d928-4d33-a4dd-8442ae3d41e4", "isEnabled": true, "isAdmin": false, "value": "IdentityProvider.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read all your organization's policies without a signed in user.", "consentDisplayName": "Read your organization's policies", "id": "246dd0d5-5bd0-4def-940b-0421030a5b68", "isEnabled": true, "isAdmin": false, "value": "Policy.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write your organization's trust framework policies without a signed in user.", "consentDisplayName": "Read and write your organization's trust framework policies", "id": "79a677f7-b79d-40d0-a36a-3e6f8688dd7a", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.TrustFramework"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read trust framework key set properties without a signed-in user.", "consentDisplayName": "Read trust framework key sets", "id": "fff194f1-7dce-4428-8301-1badb5518201", "isEnabled": true, "isAdmin": false, "value": "TrustFrameworkKeySet.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write trust framework key set properties without a signed-in user.", "consentDisplayName": "Read and write trust framework key sets", "id": "4a771c9a-1cf2-4609-b88e-3d3e02d539cd", "isEnabled": true, "isAdmin": false, "value": "TrustFrameworkKeySet.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, view, update and delete on-premises published resources, on-premises agents and agent groups, as part of a hybrid identity configuration, without a signed in user.", "consentDisplayName": "Manage on-premises published resources", "id": "0b57845e-aa49-4e6f-8109-ce654fffa618", "isEnabled": true, "isAdmin": false, "value": "OnPremisesPublishingProfiles.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration, without a signed-in user.", "consentDisplayName": "Read Microsoft Intune configuration", "id": "06a5fe6d-c49d-46a7-b082-56b1b14103c7", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementServiceConfig.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings, without a signed-in user.", "consentDisplayName": "Read Microsoft Intune RBAC settings", "id": "58ca0d9a-1575-47e1-a3cb-007ef2e4583b", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementRBAC.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the properties of devices managed by Microsoft Intune, without a signed-in user.", "consentDisplayName": "Read Microsoft Intune devices", "id": "2f51be20-0bb4-4fed-bf7b-db946066c75e", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementManagedDevices.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune, without a signed-in user.", "consentDisplayName": "Read Microsoft Intune apps", "id": "7a6ee1e7-141e-4cec-ae74-d9db155731ff", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementApps.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups, without a signed-in user.", "consentDisplayName": "Read Microsoft Intune device configuration and policies", "id": "dc377aa6-52d8-4e23-b271-2a7ae04cedf3", "isEnabled": true, "isAdmin": false, "value": "DeviceManagementConfiguration.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings in the organization for group and app memberships, without a signed-in user.", "consentDisplayName": "Manage access reviews for group and app memberships", "id": "18228521-a591-40f1-b215-5fad4488c117", "isEnabled": true, "isAdmin": false, "value": "AccessReview.ReadWrite.Membership"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allow the app to read or write items in all external datasets that the app is authorized to access", "consentDisplayName": "Read and write items in external datasets", "id": "38c3d6ee-69ee-422f-b954-e17819665354", "isEnabled": true, "isAdmin": false, "value": "ExternalItem.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the memberships of hidden groups and administrative units without a signed-in user.", "consentDisplayName": "Read all hidden memberships", "id": "658aa5d8-239f-45c4-aa12-864f4fc7e490", "isEnabled": true, "isAdmin": false, "value": "Member<PERSON><PERSON><PERSON>"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read company places (conference rooms and room lists) for calendar events and other applications, without a signed-in user.", "consentDisplayName": "Read all company places", "id": "913b9306-0ce1-42b8-9137-6a7df690a760", "isEnabled": true, "isAdmin": false, "value": "Place.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the organization and related resources, without a signed-in user. Related resources include things like subscribed skus and tenant branding information.", "consentDisplayName": "Read organization information", "id": "498476ce-e0fe-48b0-b801-37ba7e2685c6", "isEnabled": true, "isAdmin": false, "value": "Organization.Read.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write the organization and related resources, without a signed-in user. Related resources include things like subscribed skus and tenant branding information.", "consentDisplayName": "Read and write organization information", "id": "292d869f-3427-49a8-9dab-8c70152b74e9", "isEnabled": true, "isAdmin": false, "value": "Organization.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, without a signed-in user.  This includes reading directory role templates, directory roles and memberships.", "consentDisplayName": "Read all directory RBAC settings", "id": "483bed4a-2ad3-4361-a73b-c83ccdbdc53c", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.Read.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, without a signed-in user. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "consentDisplayName": "Read and write all directory RBAC settings", "id": "9e3f62cf-ca93-4989-b6ce-bf83c28f9fe8", "isEnabled": true, "isAdmin": false, "value": "RoleManagement.ReadWrite.Directory"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to create, read, update, and delete documents and list items in all site collections without a signed in user.", "consentDisplayName": "Read and write items in all site collections", "id": "9492366f-7969-46a4-8d15-ed1a20078fff", "isEnabled": true, "isAdmin": false, "value": "Sites.ReadWrite.All"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read and write feature rollout policies without a signed-in user. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "consentDisplayName": "Read and write feature rollout policies", "id": "2044e4f1-e56c-435b-925c-44cd8f6ba89a", "isEnabled": true, "isAdmin": false, "value": "Policy.ReadWrite.FeatureRollout"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read basic mail properties in all mailboxes without a signed-in user. Includes all properties except body, previewBody, attachments and any extended properties.", "consentDisplayName": "Read basic mail in all mailboxes", "id": "6be147d2-ea4f-4b5a-a3fa-3eab6f3c140a", "isEnabled": true, "isAdmin": false, "value": "Mail.ReadBasic"}, {"allowedMemberTypes": ["Application"], "consentDescription": "Allows the app to read basic mail properties in all mailboxes without a signed-in user. Includes all properties except body, previewBody, attachments and any extended properties.", "consentDisplayName": "Read basic mail in all mailboxes", "id": "693c5e45-0940-467d-9b8a-1022fb9d42ef", "isEnabled": true, "isAdmin": false, "value": "Mail.ReadBasic.All"}]}