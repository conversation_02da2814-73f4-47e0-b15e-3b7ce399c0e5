using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Lib;

internal interface IInventoryIdentificationCmd
{
  Task<OpResult<InventoryIdentificationCmdResult>> RunAsync(CancellationToken token);
}

internal record InventoryIdentificationCmdResult(Dictionary<int, int> IdentifiedProviderAgentIdsToComputerIds);

internal class InventoryIdentificationCmd : IInventoryIdentificationCmd
{
  private readonly ILogger<InventoryIdentificationCmd> _logger;
  private readonly IProviderActions _providerActions;
  private readonly IProviderStore _providerStore;
  private readonly IInventoryStore _inventoryStore;

  public InventoryIdentificationCmd(
    ILogger<InventoryIdentificationCmd> logger,
    IProviderActions providerActions,
    IProviderStore providerStore,
    IInventoryStore inventoryStore)
  {
    _logger = logger;
    _providerActions = providerActions;
    _providerStore = providerStore;
    _inventoryStore = inventoryStore;
  }

  public async Task<OpResult<InventoryIdentificationCmdResult>> RunAsync(CancellationToken token)
  {
    _logger.LogDebug("Starting {cmd}", nameof(InventoryIdentificationCmd));
    try
    {
      // fetch all providers that support inventory identification
      var providerTypeIds = _providerActions
        .GetAllProviderTypes()
        .Where(a => a.ProviderCapabilities.Contains(nameof(ISupportsInventoryIdentification)))
        .Select(a => a.ProviderTypeId)
        .ToList();

      if (!providerTypeIds.Any())
      {
        _logger.LogDebug("No providers support inventory identification.");
        return OpResult.Ok(new InventoryIdentificationCmdResult([]));
      }

      // fetch all non-disabled and healthy provider links for these provider types
      var providerLinks = _providerStore.GetProviderLinks().Using(q => q
        .Where(a => providerTypeIds.Contains(a.ProviderTypeId))
        .Where(a => !a.Disabled)
        .Where(a => a.HealthStatus == HealthStatus.Healthy)
        .ToList());

      _logger.LogDebug("Fetched {count} provider links for inventory identification.", providerLinks.Count);

      if (!providerLinks.Any())
      {
        return OpResult.Ok(new InventoryIdentificationCmdResult([]));
      }

      _logger.LogDebug("Links fetched: {links}", string.Join(", ", providerLinks.Select(a => $"#{a.Id} - {a.Name}")));


      var successfulAgentMatches = new Dictionary<int, int>();

      foreach (var link in providerLinks)
      {
        try
        {
          var matches = await UpdateProviderAgentComputerIdMatchesForLink(link, token);
          foreach (var match in matches)
          {
            successfulAgentMatches.TryAdd(match.Key, match.Value);
          }
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "Error running {cmd} for provider link {link}", nameof(InventoryIdentificationCmd), link.Id);
        }
      }

      return OpResult.Ok(new InventoryIdentificationCmdResult(successfulAgentMatches));
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error running {cmd}", nameof(InventoryIdentificationCmd));
      return OpResult.Fail<InventoryIdentificationCmdResult>(ex.Message);
    }
    finally
    {
      _logger.LogDebug("Finished {cmd}", nameof(InventoryIdentificationCmd));
    }
  }

  private async Task<Dictionary<int, int>> UpdateProviderAgentComputerIdMatchesForLink(IProviderLinkDetails link, CancellationToken token)
  {
    var loggerState = new Dictionary<string, object?>() {
      { "provider_link_id", link.Id },
      { "provider_link_name", link.Name },
    };

    using var _ = _logger.BeginScope(loggerState);

    var successfulAgentMatches = new Dictionary<int, int>();

    // fetch all provider agents needing inventory identification from the database
    using var agentsDisposable = _providerStore.GetAgentsForProviderLink(link.Id);
    var agents = agentsDisposable.Value
      .Where(a => a.ComputerId == null)
      .Select(a => new { a.Id, a.ProviderLinkId, a.ExternalClientId, a.ExternalAgentId, a.DeviceDetails.DeviceName })
      .ToList();

    _logger.LogDebug("Fetched {count} agents needing inventory identification.", agents.Count);

    if (agents.Count == 0) return successfulAgentMatches;

    var inventoryKey = InventoryKeys.GetInventoryIdentificationAgentKey(link.Id);

    var inventories = _inventoryStore.GetLatestInventoryForComputers(inventoryKey).Using(q => q.ToList());

    _logger.LogDebug("Fetched {count} inventories for inventory identification.", inventories.Count);

    // for each computer, grab the value at every provider links inventory identification key
    var computerInventoryValues = new Dictionary<string, int>();

    foreach (var inventory in inventories)
    {
      // do nothing if the inventory doesn't have a value at the output key
      HydrationException.ThrowIfNull(inventory.LatestSuccessResult, nameof(inventory.LatestSuccessResult));
      if (!inventory.LatestSuccessResult.RootElement.TryGetProperty(InventoryKeys.OutputStreamKey, out var el))
      {
        continue;
      }

      // cache the value for this computer
      var value = el.ToString();
      computerInventoryValues[value] = inventory.ComputerId;
    }

    // for each provider agent, see if a computer has a matching value
    foreach (var agent in agents)
    {
      if (!computerInventoryValues.TryGetValue(agent.ExternalAgentId, out var computerId))
      {
        continue;
      }

      // found computer id
      _logger.LogDebug(
        "Inventory identification match found for agent {agentName} and computer {computerId}.",
        agent.DeviceName, computerId);

      // if another agent from the same provider link has already matched to this computer, then skip this agent (foreign key would prevent the update)
      if (successfulAgentMatches.ContainsValue(computerId))
      {
        var existingMatch = successfulAgentMatches.First(a => a.Value == computerId);
        _logger.LogDebug("Computer {computerId} already matched to another agent {existingAgent}.", computerId,
          existingMatch.Key);
        continue;
      }

      successfulAgentMatches.TryAdd(agent.Id, computerId);
    }

    _logger.LogDebug("Found {count} total matches for inventory identification.", successfulAgentMatches.Count);

    if (!successfulAgentMatches.Any())
    {
      return successfulAgentMatches;
    }

    List<int> agentIdsToUnsetComputerId = [];

    // fetch existing provider agents that have already matched to these computers
    var matchedComputerIds = successfulAgentMatches.Values.ToHashSet();
    var existingAgentsForComputers = agentsDisposable.Value
      .Select(a => new { a.Id, a.ExternalAgentId, a.ComputerId })
      .Where(a => a.ComputerId != null && matchedComputerIds.Contains(a.ComputerId.Value))
      .ToList();

    _logger.LogDebug("Found {count} existing agents for computers that are assigned a newly matched computer.", existingAgentsForComputers.Count);

    foreach (var existingAgent in existingAgentsForComputers)
    {
      var matchingNewAgentId = successfulAgentMatches.FirstOrDefault(a => a.Value == existingAgent.ComputerId).Key;
      if (matchingNewAgentId is 0) continue;
      var newAgent = agents.Find(a => a.Id == matchingNewAgentId);
      if (newAgent is null) continue;

      if (newAgent.ExternalAgentId == existingAgent.ExternalAgentId)
      {
        _logger.LogDebug("Agent {agentId} already exists for computer #{computerId}. The incoming agent is a duplicate.", newAgent.ExternalAgentId, existingAgent.ComputerId);
        successfulAgentMatches.Remove(newAgent.Id);
      }
      else
      {
        // delete the existing agent so that we can update the computer id on the successful matches
        _logger.LogDebug("Unsetting existing agent #{agentId} for computer #{computerId} since newer agent matches the computer.", existingAgent.Id, existingAgent.ComputerId);
        agentIdsToUnsetComputerId.Add(existingAgent.Id);
      }
    }

    // unset old existing agent computer ids so the new ones can be matched correctly
    if (agentIdsToUnsetComputerId.Any())
    {
      await _providerStore.UnsetProviderAgentComputerIds(agentIdsToUnsetComputerId, token);
    }

    // if we matched a computer to an agent, then update the provider agent's computer id
    await _providerStore.UpdateProviderAgentsWithComputerIds(successfulAgentMatches, token);

    return successfulAgentMatches;
  }
}
