using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using CliWrap;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using System.Collections.Generic;
using System.Collections;
using System.Threading.Tasks.Dataflow;
using System.Text;
using System.IO;
using System.Reflection;

namespace Immybot.Backend.Application.Lib.MetaScripts;

internal class EditorServicesProcess : IAsyncDisposable
{
  public string PipeName => _pipeName;
  public string DebugPipeName => _debugPipeName;
  public TimeSpan ProcessUptime => DateTime.UtcNow - _proccesStartTime;

  private readonly CommandTask<CommandResult> _psesProcess;
  private readonly CancellationTokenSource _gracefulShutdown;
  private readonly CancellationTokenSource _forcefulShutdown;
  private readonly CancellationTokenRegistration _forcefulShutdownLink;

  private readonly string _pipeName;
  private readonly string _debugPipeName;
  private readonly BroadcastBlock<string> _processOutputBlock;

  private readonly DateTime _proccesStartTime;

  public IDisposable LinkToProcessOutput(ITargetBlock<string> block)
  {
    return _processOutputBlock.LinkTo(block);
  }

  public static async Task<EditorServicesProcess> LaunchAsync(int? scriptId, DatabaseType? scriptType, ScriptCategory scriptCategory, ScriptExecutionContext scriptExecutionContext, PowershellEditorServicesOptions launchOptions, CancellationToken token)
  {
    if (Environment.ProcessPath is null)
    {
      throw new InvalidOperationException("The current process path is null! Unable to launch PSES out of process! How did this happen?");
    }

    var envVars = new Dictionary<string, string?>();
    // Use shorter pipe name to avoid Unix domain socket path length limit (104 chars)
    // CoreFxPipe_ prefix + temp path + pipe name must be <= 104 chars
    var psesPipeName = $"PSES_L_{Guid.NewGuid().ToString("N")[..8]}";
    var debugPipeName = $"PSES_D_{Guid.NewGuid().ToString("N")[..8]}";


    // The fact that Environment.GetEnvironmentVariables() returns a Hashtable is stupid. I hate it.
    foreach (DictionaryEntry variable in Environment.GetEnvironmentVariables())
      envVars[variable.Key.ToString() ?? ""] = variable.Value?.ToString();

    var sessionInfoFilePath = Path.GetTempFileName();

    // set the DOTNET_ENVIRONMENT to the same value as ASPNETCORE_ENVIRONMENT. The ASPNETCORE_ENVIRONMENT is only observed when the the builder is WebApplicationBuilder.
    // The DOTNET_ENVIRONMENT is observed by HostApplicationBuilder (which is the host type PSES standalone runs in)
    envVars["DOTNET_ENVIRONMENT"] = envVars["ASPNETCORE_ENVIRONMENT"];
    // add our own environment variables to the dictionary and overwrite any existing ones if they exist
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.LaunchAsEditorServicesServer))] = "True";
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.LanguagePipeName))] = psesPipeName;
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.DebugPipeName))] = debugPipeName;
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.ScriptId))] = scriptId.ToString();
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.ScriptType))] = scriptType.ToString();
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.ScriptCategory))] = scriptCategory.ToString();
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.ScriptExecutionContext))] = scriptExecutionContext.ToString();
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.ParentProcessPid))] = Environment.ProcessId.ToString();
    envVars[ConfigEnvVarName(nameof(PowershellEditorServicesOptions.SessionInfoFilePath))] = sessionInfoFilePath;

    var gracefulShutdown = new CancellationTokenSource();
    var forcefulShutdown = new CancellationTokenSource();

    // graceful shutdown is linked to forceful shutdown, so if graceful is requested, forceful will be requested as well after 5 seconds
    var forcefulShutdownLink = gracefulShutdown.Token.Register(() => forcefulShutdown.CancelAfter(TimeSpan.FromSeconds(5)));

    try
    {
      var broadcastBlock = new BroadcastBlock<string>(x => x, new DataflowBlockOptions { EnsureOrdered = true });

      var outputBuffer = new StringBuilder();

      var tempBufferOpts = new DataflowLinkOptions { PropagateCompletion = true };
      using var tempBufferLink = broadcastBlock.LinkTo(new ActionBlock<string>(line => outputBuffer.AppendLine(line)), tempBufferOpts);

      var processPipeTarget = PipeTarget.ToDelegate(line => broadcastBlock.Post(line));

      // when launching via dotnet tool, we have to target the DLL.
      // when running under Linux, the process path will already be the DLL,
      // but when running under Windows, the process path will be the EXE.
      var processPath = Assembly.GetEntryAssembly()?.Location ?? Path.ChangeExtension(Environment.ProcessPath, "dll");

      var psesProcessLaunchCmd = Cli.Wrap("dotnet")
      .WithArguments(processPath)
      .WithEnvironmentVariables(envVars)
      .WithValidation(CommandResultValidation.None)
      .WithStandardOutputPipe(processPipeTarget)
      .WithStandardErrorPipe(processPipeTarget);

      var psesProcessLaunch = psesProcessLaunchCmd
      .ExecuteAsync(forcefulCancellationToken: forcefulShutdown.Token, gracefulCancellationToken: gracefulShutdown.Token);

      // this link is temporary while we launch the process, and will be disposed of after the process is launched successfully, or faulted.
      await using var gracefulLink = token.Register(gracefulShutdown.Cancel);


      // We will wait up to specified timeout for the sub-process to start up and become available
      var startTime = DateTime.UtcNow;
      var timeout = TimeSpan.FromSeconds(launchOptions.SubProcessInitTimeoutSeconds);
      while (DateTime.UtcNow - startTime <= timeout)
      {
        await Task.Delay(TimeSpan.FromMilliseconds(100), token);

        if (token.IsCancellationRequested)
          break;

        // check to see if we have named pipe available yet by checking the session info file size
        var fileInfo = new FileInfo(sessionInfoFilePath);
        if (fileInfo.Length == 0)
          continue;

        // PSES should be up and running now. Just hand off the objects to ctor and return the instance
        return new(psesProcessLaunch, gracefulShutdown, forcefulShutdown, forcefulShutdownLink, psesPipeName, debugPipeName, broadcastBlock);
      }
      // If we've reached this point, the process has not started up in time, so we'll cancel the process and throw an exception
      await gracefulShutdown.CancelAsync();
      // This will throw/propagate the OperationCanceledException once the process has been killed,
      // or, if the process exited before we cancelled, it will contain the exit code and output
      var res = await psesProcessLaunch;

      broadcastBlock.Complete();

      var processFailureInfoBuilder = new StringBuilder();

      processFailureInfoBuilder.AppendLine("--PSES Sub-Process Info--");
      processFailureInfoBuilder.AppendLine($"Run time: {(int)res.RunTime.TotalSeconds}s");
      processFailureInfoBuilder.AppendLine($"Exit code: {res.ExitCode}");

      if (launchOptions.SubProcessLaunchFailureDetailedLogging)
      {
        processFailureInfoBuilder.AppendLine($"Target path: {psesProcessLaunchCmd.TargetFilePath}");
        processFailureInfoBuilder.AppendLine($"Arguments: {psesProcessLaunchCmd.Arguments}");
        processFailureInfoBuilder.AppendLine($"Working Directory: {psesProcessLaunchCmd.WorkingDirPath}");
        processFailureInfoBuilder.AppendLine($"StdOut/Err:");
        processFailureInfoBuilder.AppendJoin(Environment.NewLine, outputBuffer);
      }
      else
      {
        processFailureInfoBuilder.AppendLine($"(Detailed info omitted ; Enable `SubProcessLaunchFailureDetailedLogging` for more info)");
      }

      if (res.IsSuccess)
        throw new InvalidOperationException($"The PSES sub-process somehow exited with code 0 instead of waiting for connection! This should never happen.\r\n{processFailureInfoBuilder}");

      throw new InvalidOperationException($"The PSES sub-process unexpectedly exited.\r\n{processFailureInfoBuilder}");
    }
    // If an error is encountered while initializing the process, ensure we dispose of any resources we've created since we haven't handed off the lifetime to the caller yet
    catch
    {
      await forcefulShutdownLink.DisposeAsync();
      gracefulShutdown.Dispose();
      forcefulShutdown.Dispose();

      throw;
    }
  }

  private static string ConfigEnvVarName(string name)
  {
    return $"{PowershellEditorServicesOptions.SectionKey}__{name}";
  }

  public async ValueTask DisposeAsync()
  {
    try
    {
      // only attempt to cancel the process if it's still running
      if (_psesProcess.Task.IsCompleted)
        return;

      // Handle process cleanup
      await _gracefulShutdown.CancelAsync();
      // Kinda weird, but we need to await the process task to ensure it's fully exited before we dispose of the resources
      await _psesProcess;
      // Mark the output block as complete to ensure any consumers of the output block are aware that the process has exited
      _processOutputBlock.Complete();
    }
    catch (OperationCanceledException)
    {
      // This is expected, so we'll just swallow it
    }
    finally
    {
      await _forcefulShutdownLink.DisposeAsync();
      _gracefulShutdown.Dispose();
      _forcefulShutdown.Dispose();
    }
  }

  private EditorServicesProcess(CommandTask<CommandResult> psesProcess,
    CancellationTokenSource gracefulShutdown,
    CancellationTokenSource forcefulShutdown,
    CancellationTokenRegistration forcefulShutdownLink,
    string pipeName,
    string debugPipeName,
    BroadcastBlock<string> processOutputBlock)
  {
    _psesProcess = psesProcess;
    _gracefulShutdown = gracefulShutdown;
    _forcefulShutdown = forcefulShutdown;
    _forcefulShutdownLink = forcefulShutdownLink;
    _pipeName = pipeName;
    _debugPipeName = debugPipeName;
    _processOutputBlock = processOutputBlock;
    _proccesStartTime = DateTime.UtcNow;
  }
}
