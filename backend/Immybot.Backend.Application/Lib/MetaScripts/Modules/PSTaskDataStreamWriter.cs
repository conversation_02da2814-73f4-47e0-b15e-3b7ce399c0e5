using System;
using System.Management.Automation;
using System.Management.Automation.Remoting.Internal;
using System.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Class that handles writing task data stream objects to a cmdlet.
/// </summary>
public sealed class PSTaskDataStreamWriter : IDisposable
{
  #region Members

  private readonly PSCmdlet? _cmdlet;
  private readonly PSDataCollection<PSStreamObject> _dataStream;
  private readonly int _cmdletThreadId;
  private WaitHandle? _waitHandle;

  #endregion

  #region Properties

  /// <summary>
  /// Gets wait-able handle that signals when new data has been added to
  /// the data stream collection.
  /// </summary>
  /// <returns>Data added wait handle.</returns>
  internal WaitHandle? DataAddedWaitHandle
  {
    get
    {
      if (_waitHandle == null)
      {
        _waitHandle = _dataStream.GetWaitHandle();
      }
      return _waitHandle;
    }
  }

  internal PSCmdlet? Cmdlet => _cmdlet;

  #endregion

  #region Constructor

  private PSTaskDataStreamWriter()
  {
    _dataStream = [];
  }

  /// <summary>
  /// Initializes a new instance of the <see cref="PSTaskDataStreamWriter"/> class.
  /// </summary>
  /// <param name="psCmdlet">Parent cmdlet.</param>
  public PSTaskDataStreamWriter(PSCmdlet psCmdlet)
  {
    _cmdlet = psCmdlet;
    _cmdletThreadId = Environment.CurrentManagedThreadId;
    _dataStream = [];
  }

  #endregion

  #region Public Methods

  /// <summary>
  /// Add data stream object to the writer.
  /// </summary>
  /// <param name="streamObject">Data stream object to write.</param>
  public void Add(PSStreamObject streamObject)
  {
    if (!_dataStream.IsOpen)
    {
      return;
    }
    _dataStream.Add(streamObject);
  }

  /// <summary>
  /// Write all objects in data stream collection to the cmdlet data stream.
  /// </summary>
  public void WriteImmediate()
  {
    CheckCmdletThread();
    foreach (var item in _dataStream.ReadAll())
    {
      item.WriteStreamObject(cmdlet: _cmdlet, overrideInquire: true);
    }
  }

  /// <summary>
  /// Waits for data stream objects to be added to the collection, and writes them
  /// to the cmdlet data stream.
  /// This method returns only after the writer has been closed.
  /// </summary>
  public void WaitAndWrite()
  {
    CheckCmdletThread();

    while (true)
    {
      DataAddedWaitHandle?.WaitOne();
      WriteImmediate();

      if (!_dataStream.IsOpen)
      {
        WriteImmediate();
        break;
      }
    }
  }

  /// <summary>
  /// Waits for a single data stream object to be added to the collection, and writes it
  /// to the cmdlet data stream. Accepts an optional timeout.
  /// This method returns after a single object has been written to the stream.
  /// </summary>
  public void WaitAndWriteSingle(TimeSpan? timeout = null)
  {
    CheckCmdletThread();
    if (timeout is null)
    {
      DataAddedWaitHandle?.WaitOne();
    }
    else
    {
      DataAddedWaitHandle?.WaitOne(timeout.Value);
    }
    WriteImmediate();
  }

  /// <summary>
  /// Closes the stream writer.
  /// </summary>
  public void Close()
  {
    _dataStream.Complete();
  }

  #endregion

  #region Private Methods

  private void CheckCmdletThread()
  {
    if (Environment.CurrentManagedThreadId != _cmdletThreadId)
    {
      throw new PSInvalidOperationException("This method cannot be run on the current thread. It can only be called on the cmdlet thread.");
    }
  }

  #endregion

  #region IDisposable

  /// <summary>
  /// Dispose the stream writer.
  /// </summary>
  public void Dispose()
  {
    _dataStream.Dispose();
  }

  #endregion
}
