using System;
using System.Management.Automation;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("Refresh", "ComputerSystemInfo")]
internal class RefreshComputerSystemInfoCommand : RunContextPSCmdlet
{
  [Parameter(ValueFromPipeline = true, ValueFromPipelineByPropertyName = true)]
  public PSComputer? Computer { get; set; }

  private IInventoryDeviceCmd? _inventoryDeviceCmd;
  private IDomainEventBroker? _eventBroker;
  private IDomainEventReceiver? _eventReceiver;

  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    _inventoryDeviceCmd = ServiceScope.ServiceProvider.GetRequiredService<IInventoryDeviceCmd>();
    _eventBroker = ServiceScope.ServiceProvider.GetRequiredService<IDomainEventBroker>();
    _eventReceiver = ServiceScope.ServiceProvider.GetRequiredService<IDomainEventReceiver>();
  }

  protected override void ProcessRecord()
  {
    CancellationToken.ThrowIfCancellationRequested();

    Computer? resolvedComputer;

    if (Computer is not null)
    {
      resolvedComputer = LocalDbContext.GetComputerById(Computer.Id, asNoTracking: true);
      // ensure computer's tenantId matches the run context's tenantId
      if (resolvedComputer is null || resolvedComputer.TenantId != RunContext.TenantId)
      {
        WriteError(
          new ErrorRecord(
            new Exception("Not found"), "1", ErrorCategory.InvalidData, Computer));
        return;
      }
    }
    else if (RunContext.IsComputerTarget)
    {
      resolvedComputer = RunContext.Args.Computer;
    }
    else
    {
      // no computer provided
      WriteObject(null);
      return;
    }

    if (resolvedComputer is null)
    {
      WriteObject(null);
      return;
    }

    using (var handler = new CmdletXThreadHandler<RefreshComputerSystemInfoCommand>(cmdlet: this))
    {
      _ = Task.Run(async () =>
      {
        try
        {
          // create a signal to wait for the inventory event
          var signal = new AsyncAutoResetEvent();

          // subscribe to inventory event for this computer
          using var updateSuccessHandle = _eventReceiver?.Subscribe<ComputerInventoryKeyUpdatedEvent>((ev) =>
          {
            if (ev.ComputerId != resolvedComputer.Id ||
                ev.InventoryKey != InventoryKeys.WindowsSystemInfo)
            {
              return Task.CompletedTask;
            }

            signal.Set();
            handler.WriteProgress(new ProgressRecord(
              1,
              "Refresh-ComputerSystemInfo",
              "Refresh was successful"));

            return Task.CompletedTask;
          });

          using var updateFailedHandle = _eventReceiver?.Subscribe<ComputerInventoryKeyUpdateFailedEvent>((ev) =>
          {
            if (ev.ComputerId != resolvedComputer.Id ||
                ev.InventoryKey != InventoryKeys.WindowsSystemInfo)
            {
              return Task.CompletedTask;
            }

            handler.WriteProgress(new ProgressRecord(
              1,
              "Refresh-ComputerSystemInfo",
              "Refresh was not successful"));

            return Task.CompletedTask;
          });

          RunContext.AddLog("Running inventory to refresh system info.");
          if (_inventoryDeviceCmd is null) throw new InvalidOperationException("InventoryDeviceCmd is null");
          var result = await _inventoryDeviceCmd.Run(
            new InventoryDeviceCmdPayload(
              resolvedComputer.DeviceId,
              null,
              InventoryKeys.SystemRequiredInventoryKeys,
              false
            ),
            CancellationToken.None,
            runContext: RunContext);

          if (!string.IsNullOrEmpty(result.FailureReason))
          {
            handler.WriteError(
              new ErrorRecord(
                new Exception($"Command failed with reason: {result.FailureReason}"),
                "1",
                ErrorCategory.InvalidResult,
                null));
            return;
          }

          handler.WriteProgress(new ProgressRecord(1,
            "Refresh-ComputerSystemInfo",
            "Waiting for refresh to complete"));

          // wait until we receive the event or a timeout or a cancellation request
          var signalTask = signal.WaitAsync();
          await signalTask.WaitAsync(TimeSpan.FromSeconds(20), CancellationToken);

          handler.WriteProgress(new ProgressRecord(1,
            "Refresh-ComputerSystemInfo",
            "Finished refresh"));

        }
        // most notably will handle TimeoutException, but other potential exceptions can be caught here as well.
        catch (Exception ex)
        {
          handler.WriteError(
            new ErrorRecord(
              ex,
              "1",
              ErrorCategory.InvalidResult,
              null));
        }
        finally
        {
          handler.Complete();
        }
      });

      handler.BlockAndProcessRequests();
    }

    Computer? updatedComputer;
    if (resolvedComputer == RunContext.Args.Computer || (
      RunContext.IsComputerTarget && resolvedComputer.Id == RunContext.Args.Computer?.Id))
    {
      // update the computer object in the run context
      RunContext.RefreshComputer();
      updatedComputer = RunContext.Args.Computer;
    }
    else
    {
      HydrationException.ThrowIfNull(Computer, nameof(Computer));
      updatedComputer = LocalDbContext.GetComputerById(Computer.Id, asNoTracking: true);
    }

    if (updatedComputer is not null && updatedComputer.ComputerName != resolvedComputer.ComputerName)
    {
      RunContext.AddLog($"Finished running inventory to refresh system info - computer name has changed from {resolvedComputer.ComputerName} to {updatedComputer.ComputerName}");
      // emit computer name updated event
      _eventBroker?.ComputerNameUpdated(updatedComputer.Id, updatedComputer.ComputerName ?? string.Empty);
    }
    else
    {
      RunContext.AddLog("Finished running inventory to refresh system info - computer name did not change");
    }
  }
}
