using System;
using System.Collections;
using System.Linq;
using System.Management.Automation;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives.Attributes;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet(VerbsCommon.New, "TextParameter")]
internal class NewTextParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(string);
  }
}

[Cmdlet(VerbsCommon.New, "NumberParameter")]
internal class NewNumberParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(int);
  }
}

[Cmdlet(VerbsCommon.New, "DateTimeParameter")]
internal class NewDateTimeParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(DateTime);
  }
}

[Cmdlet(VerbsCommon.New, "CheckboxParameter")]
internal class NewCheckboxParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(SwitchParameter);
  }
}

[Cmdlet(VerbsCommon.New, "BooleanParameter")]
internal class NewBooleanParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(bool);
  }
}

[Cmdlet(VerbsCommon.New, "KeyValueParameter")]
internal class NewKeyValueParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(Hashtable);
  }
}

[Cmdlet(VerbsCommon.New, "UriParameter")]
internal class NewUriParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    _runtimeDefinedParameter.ParameterType = typeof(Uri);
  }
}

[Cmdlet(VerbsCommon.New, "RadioParameter")]
internal class NewRadioParameterCommand : NewDropdownParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    var dropdownAttribute = _runtimeDefinedParameter.Attributes.OfType<DropdownAttribute>().Last();
    dropdownAttribute.ShowAsRadioButtons = true;
  }
}

[Cmdlet(VerbsCommon.New, "MediaParameter")]
internal class NewMediaParameterCommand : NewParameterCommand
{
  [Parameter(Position = 1)]
  public int? DefaultMediaId { get; set; }

  [Parameter(Position = 2)]
  public DatabaseType? DefaultMediaType { get; set; }

  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    var mediaAttribute = new MediaAttribute
    {
      DefaultMediaId = DefaultMediaId,
      DefaultMediaType = DefaultMediaType
    };

    _runtimeDefinedParameter.Attributes.Add(mediaAttribute);
  }
}

[Cmdlet(VerbsCommon.New, "PasswordParameter")]
internal class NewPasswordParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    var attr = new PasswordAttribute();

    _runtimeDefinedParameter.Attributes.Add(attr);
  }
}

/// <summary>
/// This cmdlet is used to create a parameter that will perform the oauth code authorization flow.
/// </summary>
[Cmdlet(VerbsCommon.New, "OauthConsentParameter")]
internal class NewOauthConsentParameterCommand : NewParameterCommand
{
  [Parameter(HelpMessage = "Deprecated - do not use")]
  [ValidateSet("code", "token")]
  public string ResponseType { get; set; } = "code";

  [Parameter(HelpMessage = "Scope to request access to")]
  public string[] Scope { get; set; } = Array.Empty<string>();

  [Parameter(HelpMessage = "Base Uri; default: https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize")]
  public Uri AuthorizationEndpoint { get; set; } =
    new("https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize");

  [Parameter(HelpMessage = "Base Uri; default: https://login.microsoftonline.com/organizations/oauth2/v2.0/token")]
  public Uri TokenEndpoint { get; set; } = new("https://login.microsoftonline.com/organizations/oauth2/v2.0/token");

  [Parameter(HelpMessage = "Resource to request access to")]
  public string? Resource { get; set; }

  [Parameter(HelpMessage = "Client ID")]
  public string? ClientId { get; set; }

  [Parameter(HelpMessage = "Client Secret")]
  public string? ClientSecret { get; set; }

  [Parameter(HelpMessage =
      "Extra query parameters to supply to the authorization and token endpoints. Do not supply 'redirect_uri', 'response_type', 'response_mode', 'resource', 'scope', 'client_id' or 'client_secret' here - they will be appended automatically based on the parameters to this cmdlet.")]
  public Hashtable ExtraQueryParameters { get; set; } = [];

  protected override void ProcessRecord()
  {
    base.ProcessRecord();

    // convert the hashtable to a dictionary
    var dictionary = ExtraQueryParameters
      .Cast<DictionaryEntry>()
      .Where(kvp => kvp.Value != null)
      .ToDictionary(kvp => (string)kvp.Key, kvp => kvp.Value!);

    if (dictionary.ContainsKey("redirect_uri"))
    {
      throw new ArgumentException("Do not supply 'redirect_uri' in ExtraQueryParameters");
    }

    if (dictionary.ContainsKey("scope"))
    {
      throw new ArgumentException("Do not supply 'scope' in ExtraQueryParameters - it should be specified by the Scope parameter to this cmdlet");
    }

    if (dictionary.ContainsKey("response_type"))
    {
      throw new ArgumentException(
        "Do not supply 'response_type' in ExtraQueryParameters - it should be specified by the ResponseType parameter to this cmdlet");
    }

    if (dictionary.ContainsKey("client_id"))
    {
      throw new ArgumentException(
        "Do not supply 'client_id' in ExtraQueryParameters - it will automatically be set to the provided ClientId, if provided, or ImmyBot's client id");
    }

    if (dictionary.ContainsKey("client_secret"))
    {
      throw new ArgumentException(
        "Do not supply 'client_secret' in ExtraQueryParameters - it will automatically be set to the provided ClientSecret, if provided, or ImmyBot's client secret");
    }

    if (dictionary.ContainsKey("response_mode"))
    {
      throw new ArgumentException(
        "Do not supply 'response_mode' in ExtraQueryParameters");
    }

    // if we have a client id and secret, store the client secret in key vault
    if (!string.IsNullOrEmpty(ClientId) && !string.IsNullOrEmpty(ClientSecret))
    {
      var azAdOpts = ServiceScope.ServiceProvider.GetRequiredService<IOptions<AzureActiveDirectoryAuthOptions>>();
      if (ClientId != azAdOpts.Value.ClientId)
      {
        // TODO: We will eventually want some way to delete the client secret from key vault
        // TODO: We will eventually want verification to prevent users from overwriting other users' client secrets
        var clientSecretRepo = ServiceScope.ServiceProvider.GetRequiredService<IOauthClientSecretRepository>();
        TaskHelper.RunAsJoinableTask(async () => await clientSecretRepo
          .UpsertClientSecretForClientId(AuthorizationEndpoint, ClientId, ClientSecret, CancellationToken));
      }
    }

    var attr = new OauthConsentAttribute()
    {
      AuthorizationEndpoint = AuthorizationEndpoint,
      TokenEndpoint = TokenEndpoint,
      ResponseType = ResponseType,
      Resource = Resource,
      Scope = string.Join(' ', Scope),
      ClientId = ClientId,
      ExtraQueryParameters = dictionary
    };

    _runtimeDefinedParameter.Attributes.Add(attr);
  }
}

[Cmdlet(VerbsCommon.Get, "OAuthAccessToken")]
internal class GetOauthAccessTokenCommand : ServiceScopePSCmdlet
{
  [Parameter(Mandatory = true, Position = 0, ValueFromPipeline = true)]
  public string AccessTokenId { get; set; } = string.Empty;

  [Parameter] public SwitchParameter Refresh { get; set; }

  protected override void ProcessRecord()
  {
    var token = TaskHelper.RunAsJoinableTask(async () =>
    {
      var store = ServiceScope.ServiceProvider.GetRequiredService<IOauthAccessTokenStore>();
      var t = await store.GetOauthAccessTokenByAccessTokenId(AccessTokenId, CancellationToken);
      return await store.HydrateOauthAccessToken(t, CancellationToken, forceRefresh: Refresh);
    });

    if (Refresh)
    {
      var refreshedAt = DateTimeOffset.UtcNow;
      WriteVerbose($"Token was forcefully refreshed at {refreshedAt}");
      WriteVerbose($"New Access Token: {token.AccessToken}");
    }

    WriteVerbose($"Token Expires at {token.ExpiresAtUtc}");

    WriteObject(new OauthConsentParameterValue(
      token.AccessTokenId,
      token.AccessToken,
      token.TokenType,
      token.IdToken,
      token.ExpiresAtUtc));
  }
}

[Cmdlet(VerbsCommon.New, "PersonDropdownParameter")]
internal class NewPersonParameterCommand : NewParameterCommand
{
  protected override void ProcessRecord()
  {
    base.ProcessRecord();
    _runtimeDefinedParameter.Attributes.Add(new PersonAttribute());
  }
}

[Cmdlet(VerbsCommon.New, "DropdownParameter")]
internal class NewDropdownParameterCommand : NewParameterCommand
{
  [Parameter(Position = 1)]
  public ScriptBlock? ScriptBlock { get; set; }

  [Parameter(Position = 1)]
  public new object? ValidValues { get; set; }

  [Parameter(Position = 2)]
  public string? IdPropertyName { get; set; }

  [Parameter(Position = 3)]
  public string? LabelPropertyName { get; set; }

  [Parameter()]
  public bool ShowAsRadioButtons { get; set; }

  [Parameter()]
  public SwitchParameter Ordered { get; set; }

  [Parameter()]
  public TimeSpan? Timeout { get; set; }

  [Parameter()]
  public SwitchParameter MultiSelect { get; set; }

  protected override void ProcessRecord()
  {
    base.ProcessRecord();
    DropdownAttribute attr;

    if (ScriptBlock is not null)
    {
      attr = new DropdownAttribute(ScriptBlock, IdPropertyName, LabelPropertyName, timeout: Timeout);
    }
    else if (ValidValues is not null)
    {
      if (ValidValues is Hashtable ht)
      {
        attr = new DropdownAttribute(ht, IdPropertyName);
      }
      else if (ValidValues is object[] objs)
      {
        attr = new DropdownAttribute(objs, IdPropertyName, LabelPropertyName);
      }
      else if (ValidValues is object obj && !obj.GetType().IsArray)
      {
        // convert single items into an array
        var objArr = new object[] { obj };
        attr = new DropdownAttribute(objArr, IdPropertyName, LabelPropertyName);
      }
      else
      {
        throw new ValidationException($"{Name}: The type of ValidValues must either be Object[] or Hashtable. {ValidValues.GetType().FullName} was provided.");
      }
    }
    else
    {
      throw new ValidationException($"{Name}: ScriptBlock or ValidValues must be specified.");
    }

    attr.ShowAsRadioButtons = ShowAsRadioButtons;
    attr.Ordered = Ordered;
    attr.MultiSelect = MultiSelect;
    _runtimeDefinedParameter.Attributes.Add(attr);
  }
}

[Cmdlet(VerbsCommon.New, "ParameterCollection")]
internal class NewParameterCollectionCommand : ServiceScopePSCmdlet
{
  [Parameter(Position = 0)]
  public RuntimeDefinedParameter[]? Parameters { get; set; }

  protected override void ProcessRecord()
  {
    var parameterCollection = new RuntimeDefinedParameterDictionary();
    if (Parameters is not null)
    {
      foreach (var parameter in Parameters)
      {
        if (parameter is not null)
        {
          parameterCollection.Add(parameter.Name, parameter);
        }
      }
    }

    WriteObject(parameterCollection);
  }
}

[Cmdlet(VerbsCommon.New, "Parameter")]
internal class NewParameterCommand : ServiceScopePSCmdlet
{
  internal readonly RuntimeDefinedParameter _runtimeDefinedParameter = new();

  [Parameter(Mandatory = true, Position = 0)]
  public string Name { get; set; } = "";

  [Parameter()]
  public SwitchParameter AllowNull { get; set; }

  [Parameter()]
  [AllowNull]
  public object? DefaultValue { get; set; }

  [Parameter()]
  public string? HelpMessage { get; set; }

  [Parameter()]
  public SwitchParameter Hidden { get; set; }

  [Parameter()]
  public SwitchParameter Mandatory { get; set; }

  [Parameter()]
  public int? Position { get; set; }

  [Parameter()]
  public Type Type { get; set; } = typeof(object);

  [Parameter()]
  public string? ValidatePattern { get; set; }

  [Parameter()]
  public string? ValidatePatternErrorMessage { get; set; }

  [Parameter()]
  public string[]? ValidValues { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromPipeline { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromPipelineByPropertyName { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromRemainingArguments { get; set; }

  [Parameter(HelpMessage = "Incremental Binding logic is skipped and a RuntimeDefinedParameter is emitted to the Output stream")]
  public SwitchParameter SkipBind { get; set; }
  [Parameter(HelpMessage = "Writes the RuntimeDefinedParameter to the Output stream. Incremental Binding logic is still performed unless SkipBind is also specified. Note: This may cause parameter collisions")]
  public SwitchParameter PassThru { get; set; }

  [Parameter()]
  public ScriptBlock? ValidateScript { get; set; }

  [Parameter()]
  public new string? ParameterSetName { get; set; }

  protected override void ProcessRecord()
  {
    _runtimeDefinedParameter.Name = Name;
    _runtimeDefinedParameter.ParameterType = Type;
    _runtimeDefinedParameter.Value = DefaultValue;

    var parameterAttribute = new ParameterAttribute
    {
      Mandatory = Mandatory,
      DontShow = Hidden,
      ValueFromPipelineByPropertyName = ValueFromPipelineByPropertyName,
      ValueFromRemainingArguments = ValueFromRemainingArguments,
    };

    if (Position is int pos)
      parameterAttribute.Position = pos;

    if (!string.IsNullOrEmpty(HelpMessage))
      parameterAttribute.HelpMessage = HelpMessage;

    if (!string.IsNullOrEmpty(ParameterSetName))
      parameterAttribute.ParameterSetName = ParameterSetName;

    if (ValueFromPipeline)
      parameterAttribute.ValueFromPipeline = ValueFromPipeline;

    _runtimeDefinedParameter.Attributes.Add(parameterAttribute);
    _runtimeDefinedParameter.Attributes.Add(new ArgumentTypeConverterAttribute(typeof(object)));

    if (!string.IsNullOrEmpty(ValidatePattern))
    {
      var attr = new ValidatePatternAttribute(ValidatePattern);
      if (!string.IsNullOrEmpty(ValidatePatternErrorMessage))
        attr.ErrorMessage = ValidatePatternErrorMessage;
      _runtimeDefinedParameter.Attributes.Add(attr);
    }

    if (ValidateScript is not null)
    {
      var attr = new ValidateScriptAttribute(ValidateScript);
      _runtimeDefinedParameter.Attributes.Add(attr);
    }

    if (ValidValues != null)
      _runtimeDefinedParameter.Attributes.Add(new ValidateSetAttribute(ValidValues));

    if (AllowNull)
      _runtimeDefinedParameter.Attributes.Add(new AllowNullAttribute());

    if (DefaultValue is not null)
      _runtimeDefinedParameter.Attributes.Add(new DefaultValueAttribute(DefaultValue));
  }

  protected override void EndProcessing()
  {
    bool emitParameter = PassThru || SkipBind;
    if (!SkipBind)
    {
      if (this.SessionState.PSVariable.Get("ParameterBinderController")?.Value is
          IncrementalParameterBinder parameterBinderOverride)
      {
        parameterBinderOverride.BindRuntimeDefinedParameter(_runtimeDefinedParameter);
      }
      else
      {
        WriteWarning($"Unable to retrieve IncrementalParameterBinder. Emitting parameter {Name}");
        emitParameter = true;
      }
    }
    if (emitParameter)
    {
      WriteObject(_runtimeDefinedParameter);
    }
  }
}


[Cmdlet(VerbsCommon.New, "HelpText")]
internal class NewHelpTextCommand : NewParameterCommand
{
  [Parameter()]
  public SwitchParameter Raw { get; set; }
  [Parameter(Position = 1, Mandatory = true)]
  public new required string HelpMessage { get; set; }
  protected override void ProcessRecord()
  {
    _runtimeDefinedParameter.Name = Name;
    _runtimeDefinedParameter.ParameterType = typeof(string);
    _runtimeDefinedParameter.Attributes.Add(new ParameterAttribute
    {
      HelpMessage = HelpMessage,
      DontShow = Hidden
    });
    _runtimeDefinedParameter.Attributes.Add(new HelpTextAttribute(Raw));
  }
}

[Cmdlet(VerbsCommon.Set, "Parameter")]
internal class SetParameterCommand : ServiceScopePSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  [Alias("RuntimeDefinedParameter")]
  public RuntimeDefinedParameter Parameter { get; set; } = null!;

  [Parameter()]
  [AllowNull]
  public object? DefaultValue { get; set; }

  [Parameter()]
  public string? HelpMessage { get; set; }

  [Parameter()]
  public SwitchParameter Hidden { get; set; }

  [Parameter()]
  public SwitchParameter Mandatory { get; set; }

  [Parameter()]
  public int? Position { get; set; }

  [Parameter()]
  public Type? Type { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromPipeline { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromPipelineByPropertyName { get; set; }

  [Parameter()]
  public SwitchParameter ValueFromRemainingArguments { get; set; }

  protected override void ProcessRecord()
  {
    var parameterAttribute = Parameter.Attributes.OfType<ParameterAttribute>().FirstOrDefault();

    if (parameterAttribute is null)
    {
      parameterAttribute = new ParameterAttribute();
      Parameter.Attributes.Add(parameterAttribute);
    }

    parameterAttribute.Mandatory = Mandatory;
    parameterAttribute.DontShow = Hidden;
    parameterAttribute.ValueFromRemainingArguments = ValueFromRemainingArguments;
    parameterAttribute.ValueFromPipelineByPropertyName = ValueFromPipelineByPropertyName;
    parameterAttribute.ValueFromPipeline = ValueFromPipeline;
    if (!string.IsNullOrEmpty(HelpMessage)) parameterAttribute.HelpMessage = HelpMessage;
    if (!string.IsNullOrEmpty(ParameterSetName)) parameterAttribute.ParameterSetName = ParameterSetName;
    if (Position is not null) parameterAttribute.Position = Position.Value;

    if (DefaultValue is not null) Parameter.Value = DefaultValue;
    if (Type is not null) Parameter.ParameterType = Type;

    WriteObject(Parameter);
  }
}
