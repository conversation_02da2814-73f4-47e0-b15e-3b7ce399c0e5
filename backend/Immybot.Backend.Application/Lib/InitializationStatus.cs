using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Microsoft.VisualStudio.Threading;
using Nito.AsyncEx;

namespace Immybot.Backend.Application.Lib;

internal class InitializationStatus : IInitializationStatus
{
  public bool IsInitialized { get => _readyTcs.Task.IsCompletedSuccessfully; }

  private readonly TaskCompletionSource _readyTcs = new();
  private readonly SemaphoreSlim _initializationLock = new(1, 1);

  [SuppressMessage("Usage", "VSTHRD003:Avoid awaiting foreign Tasks")]
  public Task WaitForInitializationAsync(CancellationToken cancellationToken) =>
    _readyTcs.Task.WithCancellation(cancellationToken);

  public Task<IDisposable> AcquireInitializationLockAsync(CancellationToken cancellationToken) =>
    _initializationLock.LockAsync(cancellationToken);

  public void SetInitialized() =>
    _readyTcs.TrySetResult();
}
