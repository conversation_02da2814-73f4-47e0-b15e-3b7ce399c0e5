using System;
using Immybot.Backend.Domain.Infrastructure;

namespace Immybot.Backend.Application.Lib.Exceptions;
internal class PreflightScriptFailedException : DeviceException
{
  public PreflightScriptFailedException(int? computerId) : base(computerId)
  {
  }

  public PreflightScriptFailedException(int? computerId, string? message) : base(computerId, message)
  {
  }

  public PreflightScriptFailedException(int? computerId, string? message, Exception? innerException) : base(computerId, message, innerException)
  {
  }
}
