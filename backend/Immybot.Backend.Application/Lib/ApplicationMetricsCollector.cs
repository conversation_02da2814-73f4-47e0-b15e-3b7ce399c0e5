using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using App.Metrics;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.MetricsRegistries;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;

namespace Immybot.Backend.Application.Lib;

internal class ApplicationMetricsCollector : IApplicationMetricsCollector
{
  public static bool HasCollectedMetricsAtLeastOnce { get; private set; }
  private readonly IMetrics _metrics;
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IDashboardActions _dashboardActions;
  private readonly IFeatureManager _featureManager;

  public ApplicationMetricsCollector(
    IMetrics metrics,
    Func<ImmybotDbContext> ctxFactory,
    IDashboardActions dashboardActions,
    IFeatureManager featureManager)
  {
    _metrics = metrics;
    _ctxFactory = ctxFactory;
    _dashboardActions = dashboardActions;
    _featureManager = featureManager;
  }

  public Task DoCollection(CancellationToken stoppingToken)
  {
    using var ctx = _ctxFactory();

    var sessionData = _dashboardActions.GetSessionData();

    _metrics.Measure.Gauge.SetValue(
          DatabaseMetricsRegistry.Gauges.TotalMaintenanceSessions,
          sessionData.Total);

    foreach (var s in sessionData.Counts)
    {
      stoppingToken.ThrowIfCancellationRequested();

      _metrics.Measure.Gauge.SetValue(
        DatabaseMetricsRegistry.Gauges.TotalMaintenanceSessions,
        new MetricTags("Status", ((SessionStatus)s.Key).ToString()),
        s.Value);
    }

    stoppingToken.ThrowIfCancellationRequested();

    _metrics.Measure.Gauge.SetValue(
      DatabaseMetricsRegistry.Gauges.TotalProviderAgents,
      ctx.GetProviderAgents().Count());

    stoppingToken.ThrowIfCancellationRequested();

    _metrics.Measure.Gauge.SetValue(
      DatabaseMetricsRegistry.Gauges.TotalComputers,
      ctx.GetAllComputers().Count());

    stoppingToken.ThrowIfCancellationRequested();

    _metrics.Measure.Gauge.SetValue(
      DatabaseMetricsRegistry.Gauges.TotalComputersWithoutAgents,
      ctx.GetAllComputers().Count(c => !c.Agents.Any()));

    stoppingToken.ThrowIfCancellationRequested();
    foreach (var feature in _featureManager.GetFeaturesEnabledFromSubscription())
    {
      if (feature.IsUsageBased && feature.FeatureUsage is { ItemCount: { } itemCount })
        _metrics.Measure.Gauge.SetValue(
          FeatureMetricsRegistry.GetGaugeOptionsForFeature(feature.FeatureId),
          itemCount);
    }

    HasCollectedMetricsAtLeastOnce = true;

    return Task.CompletedTask;
  }
}
