using System;
using System.Collections.Concurrent;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib;

public class WebHookService : IWebHookService
{
  private readonly IOptionsMonitor<AppSettingsOptions> _appSettingsOptions;
  private readonly ConcurrentDictionary<Guid, WebHook> _webHooks = new();

  public WebHookService(IOptionsMonitor<AppSettingsOptions> appSettingsOptions)
  {
    _appSettingsOptions = appSettingsOptions;
  }

  public WebHook? GetWebHook(Guid webHookGuid)
  {
    return _webHooks.TryGetValue(webHookGuid, out var webHook) ? webHook : null;
  }

  public WebHook CreateWebHook(TimeSpan duration)
  {
    var appSettings = _appSettingsOptions.CurrentValue;
    var webHook = new WebHook(duration, appSettings.RootUrl);
    _webHooks.TryAdd(webHook.Id, webHook);
    return webHook;
  }
}
