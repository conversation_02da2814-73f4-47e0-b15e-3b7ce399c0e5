using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using App.Metrics;
using App.Metrics.Counter;
using App.Metrics.Gauge;
using App.Metrics.Meter;
using Immybot.Backend.Providers.Interfaces;

namespace Immybot.Backend.Application.Lib.Providers;

/// <inheritdoc />
internal class ProviderMetrics : IProviderMetrics
{
  private readonly IMetricsRoot _metrics;
  private readonly ConcurrentDictionary<int, IMetricsRoot> _providerMetrics = new();

  public ProviderMetrics(IMetricsRoot metrics)
  {
    _metrics = metrics;
  }

  public IMeter CreateMeter(int providerLinkId, MeterOptions options)
  {
    var metrics = GetProviderMetrics(providerLinkId);
    return metrics.Provider.Meter.Instance(options);
  }

  public IGauge CreateGauge(int providerLinkId, GaugeOptions options)
  {
    var metrics = GetProviderMetrics(providerLinkId);
    return metrics.Provider.Gauge.Instance(options);
  }

  public void IncrementMeasureCounter(CounterOptions options)
  {
    _metrics.Measure.Counter.Increment(options);
  }

  public ReadOnlyDictionary<int, IMetricsRoot> GetMetrics()
  {
    return _providerMetrics.AsReadOnly();
  }

  public void TryRemoveMetrics(int providerLinkId)
  {
    if (_providerMetrics.TryRemove(providerLinkId, out var metrics))
    {
      metrics.Manage.Disable();
    }
  }

  private IMetricsRoot GetProviderMetrics(int providerLinkId)
  {
    if (_providerMetrics.TryGetValue(providerLinkId, out var metricsRoot))
      return metricsRoot;

    var builder = new MetricsBuilder();
    var metrics = builder.Configuration
      .Configure(opts =>
      {
        opts.Enabled = true;
        opts.ReportingEnabled = true;
      })
      .OutputMetrics.AsJson()
      .Build();

    _providerMetrics.TryAdd(providerLinkId, metrics);
    return metrics;
  }
}
