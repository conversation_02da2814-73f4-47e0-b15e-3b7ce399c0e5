using System;
using System.Runtime.Serialization;
using Immybot.Backend.Domain.Oauth;

namespace Immybot.Backend.Application.Oauth;
[Serializable]
public class OauthException : Exception
{
  public Oauth2AccessTokenErrorResponse? ErrorResponse { get; }
  public OauthException()
  {
  }

  public OauthException(Oauth2AccessTokenErrorResponse errorResponse) : this("OAuth returned an error response")
  {
    ErrorResponse = errorResponse;
  }

  public OauthException(string? message) : base(message)
  {
  }

  public OauthException(string? message, Exception? innerException) : base(message, innerException)
  {
  }

#pragma warning disable SYSLIB0051
  protected OauthException(SerializationInfo info, StreamingContext context) : base(info, context)
#pragma warning restore SYSLIB0051
  {
    ErrorResponse = (Oauth2AccessTokenErrorResponse?)info.GetValue(nameof(ErrorResponse), typeof(Oauth2AccessTokenErrorResponse));
  }

#pragma warning disable CS0672 // Member overrides obsolete member
  public override void GetObjectData(SerializationInfo info, StreamingContext context)
#pragma warning restore CS0672 // Member overrides obsolete member
  {
#pragma warning disable SYSLIB0051
    base.GetObjectData(info, context);
#pragma warning restore SYSLIB0051

    info.AddValue(nameof(ErrorResponse), ErrorResponse, typeof(Oauth2AccessTokenErrorResponse));
  }
}
