<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04">
  <Obj RefId="0">
    <TN RefId="0">
      <T>Microsoft.PowerShell.Commands.PSRepositoryItemInfo</T>
      <T>System.Management.Automation.PSCustomObject</T>
      <T>System.Object</T>
    </TN>
    <MS>
      <S N="Name">Microsoft.Graph.Groups</S>
      <Version N="Version">1.23.0</Version>
      <S N="Type">Module</S>
      <S N="Description">Microsoft Graph PowerShell Cmdlets</S>
      <S N="Author">Microsoft Corporation</S>
      <S N="CompanyName">msgraph-sdk-powershell</S>
      <S N="Copyright">Microsoft Corporation. All rights reserved.</S>
      <DT N="PublishedDate">2023-03-04T04:00:28-06:00</DT>
      <Obj N="InstalledDate" RefId="1">
        <DT>2023-03-06T17:24:36.0613829-06:00</DT>
        <MS>
          <Obj N="DisplayHint" RefId="2">
            <TN RefId="1">
              <T>Microsoft.PowerShell.Commands.DisplayHintType</T>
              <T>System.Enum</T>
              <T>System.ValueType</T>
              <T>System.Object</T>
            </TN>
            <ToString>DateTime</ToString>
            <I32>2</I32>
          </Obj>
        </MS>
      </Obj>
      <Nil N="UpdatedDate" />
      <URI N="LicenseUri">https://aka.ms/devservicesagreement</URI>
      <URI N="ProjectUri">https://github.com/microsoftgraph/msgraph-sdk-powershell</URI>
      <URI N="IconUri">https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/master/documentation/images/graph_color256.png</URI>
      <Obj N="Tags" RefId="3">
        <TN RefId="2">
          <T>System.Object[]</T>
          <T>System.Array</T>
          <T>System.Object</T>
        </TN>
        <LST>
          <S>Microsoft</S>
          <S>Office365</S>
          <S>Graph</S>
          <S>PowerShell</S>
          <S>PSModule</S>
          <S>PSEdition_Core</S>
          <S>PSEdition_Desktop</S>
        </LST>
      </Obj>
      <Obj N="Includes" RefId="4">
        <TN RefId="3">
          <T>System.Collections.Hashtable</T>
          <T>System.Object</T>
        </TN>
        <DCT>
          <En>
            <S N="Key">DscResource</S>
            <Obj N="Value" RefId="5">
              <TNRef RefId="2" />
              <LST />
            </Obj>
          </En>
          <En>
            <S N="Key">Command</S>
            <Obj N="Value" RefId="6">
              <TNRef RefId="2" />
              <LST>
                <S>Add-MgGroupDriveListContentTypeCopy</S>
                <S>Add-MgGroupDriveListContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupFavorite</S>
                <S>Add-MgGroupSite</S>
                <S>Add-MgGroupSiteContentTypeCopy</S>
                <S>Add-MgGroupSiteContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupSiteListContentTypeCopy</S>
                <S>Add-MgGroupSiteListContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupToLifecyclePolicy</S>
                <S>Confirm-MgGroupGrantedPermissionForApp</S>
                <S>Confirm-MgGroupMemberGroup</S>
                <S>Confirm-MgGroupMemberObject</S>
                <S>Confirm-MgGroupPermissionGrantMemberGroup</S>
                <S>Confirm-MgGroupPermissionGrantMemberObject</S>
                <S>Confirm-MgGroupSiteInformationProtectionSignature</S>
                <S>Copy-MgGroupDriveItem</S>
                <S>Copy-MgGroupDriveListContentTypeToDefaultContentLocation</S>
                <S>Copy-MgGroupDriveRoot</S>
                <S>Copy-MgGroupOnenoteNotebook</S>
                <S>Copy-MgGroupOnenotePageToSection</S>
                <S>Copy-MgGroupOnenoteSectionToNotebook</S>
                <S>Copy-MgGroupOnenoteSectionToSectionGroup</S>
                <S>Copy-MgGroupSiteContentTypeToDefaultContentLocation</S>
                <S>Copy-MgGroupSiteListContentTypeToDefaultContentLocation</S>
                <S>Get-MgGroup</S>
                <S>Get-MgGroupAcceptedSender</S>
                <S>Get-MgGroupAcceptedSenderByRef</S>
                <S>Get-MgGroupById</S>
                <S>Get-MgGroupCalendarEventDelta</S>
                <S>Get-MgGroupCalendarSchedule</S>
                <S>Get-MgGroupConversation</S>
                <S>Get-MgGroupConversationThread</S>
                <S>Get-MgGroupConversationThreadPost</S>
                <S>Get-MgGroupConversationThreadPostAttachment</S>
                <S>Get-MgGroupConversationThreadPostExtension</S>
                <S>Get-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>Get-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Get-MgGroupConversationThreadPostInReplyToMention</S>
                <S>Get-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostMention</S>
                <S>Get-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Get-MgGroupCreatedOnBehalfOf</S>
                <S>Get-MgGroupDelta</S>
                <S>Get-MgGroupDriveItemActivityByInterval</S>
                <S>Get-MgGroupDriveItemDelta</S>
                <S>Get-MgGroupDriveItemListItemActivityByInterval</S>
                <S>Get-MgGroupDriveListContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupDriveListItemActivityByInterval</S>
                <S>Get-MgGroupDriveListItemDelta</S>
                <S>Get-MgGroupDriveRootActivityByInterval</S>
                <S>Get-MgGroupDriveRootDelta</S>
                <S>Get-MgGroupDriveRootListItemActivityByInterval</S>
                <S>Get-MgGroupEndpoint</S>
                <S>Get-MgGroupEventDelta</S>
                <S>Get-MgGroupExtension</S>
                <S>Get-MgGroupLifecyclePolicy</S>
                <S>Get-MgGroupLifecyclePolicyByGroup</S>
                <S>Get-MgGroupMember</S>
                <S>Get-MgGroupMemberByRef</S>
                <S>Get-MgGroupMemberGroup</S>
                <S>Get-MgGroupMemberObject</S>
                <S>Get-MgGroupMemberOf</S>
                <S>Get-MgGroupMemberWithLicenseError</S>
                <S>Get-MgGroupOnenoteNotebookFromWebUrl</S>
                <S>Get-MgGroupOnenoteRecentNotebook</S>
                <S>Get-MgGroupOwner</S>
                <S>Get-MgGroupOwnerByRef</S>
                <S>Get-MgGroupPermissionGrant</S>
                <S>Get-MgGroupPermissionGrantAvailableExtensionProperty</S>
                <S>Get-MgGroupPermissionGrantById</S>
                <S>Get-MgGroupPermissionGrantMemberGroup</S>
                <S>Get-MgGroupPermissionGrantMemberObject</S>
                <S>Get-MgGroupPermissionGrantUserOwnedObject</S>
                <S>Get-MgGroupPhoto</S>
                <S>Get-MgGroupPhotoContent</S>
                <S>Get-MgGroupRejectedSender</S>
                <S>Get-MgGroupRejectedSenderByRef</S>
                <S>Get-MgGroupSetting</S>
                <S>Get-MgGroupSiteActivityByInterval</S>
                <S>Get-MgGroupSiteApplicableContentTypeForList</S>
                <S>Get-MgGroupSiteByPath</S>
                <S>Get-MgGroupSiteContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupSiteDelta</S>
                <S>Get-MgGroupSiteListContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupSiteListItemActivityByInterval</S>
                <S>Get-MgGroupSiteListItemDelta</S>
                <S>Get-MgGroupSitePageCanvaLayoutHorizontalSectionColumnWebpartPositionOfWebPart</S>
                <S>Get-MgGroupSitePageCanvaLayoutVerticalSectionWebpartPositionOfWebPart</S>
                <S>Get-MgGroupSitePageWebPartByPosition</S>
                <S>Get-MgGroupSitePageWebPartPositionOfWebPart</S>
                <S>Get-MgGroupThread</S>
                <S>Get-MgGroupThreadPost</S>
                <S>Get-MgGroupThreadPostAttachment</S>
                <S>Get-MgGroupThreadPostExtension</S>
                <S>Get-MgGroupThreadPostInReplyToAttachment</S>
                <S>Get-MgGroupThreadPostInReplyToExtension</S>
                <S>Get-MgGroupThreadPostInReplyToMention</S>
                <S>Get-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostMention</S>
                <S>Get-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Get-MgGroupTransitiveMember</S>
                <S>Get-MgGroupTransitiveMemberOf</S>
                <S>Get-MgGroupUserOwnedObject</S>
                <S>Get-MgUserJoinedGroup</S>
                <S>Grant-MgGroupDriveItemPermission</S>
                <S>Grant-MgGroupDriveRootPermission</S>
                <S>Grant-MgGroupSitePermission</S>
                <S>Invoke-MgAcceptGroupCalendarEvent</S>
                <S>Invoke-MgAcceptGroupCalendarEventTentatively</S>
                <S>Invoke-MgAcceptGroupEvent</S>
                <S>Invoke-MgAcceptGroupEventTentatively</S>
                <S>Invoke-MgBufferGroupSiteInformationProtectionDecrypt</S>
                <S>Invoke-MgBufferGroupSiteInformationProtectionEncrypt</S>
                <S>Invoke-MgCalendarGroupCalendar</S>
                <S>Invoke-MgCheckinGroupDriveItem</S>
                <S>Invoke-MgCheckinGroupDriveRoot</S>
                <S>Invoke-MgCheckoutGroupDriveItem</S>
                <S>Invoke-MgCheckoutGroupDriveRoot</S>
                <S>Invoke-MgDeclineGroupCalendarEvent</S>
                <S>Invoke-MgDeclineGroupEvent</S>
                <S>Invoke-MgDismissGroupCalendarEventReminder</S>
                <S>Invoke-MgDismissGroupEventReminder</S>
                <S>Invoke-MgExtractGroupDriveItemSensitivityLabel</S>
                <S>Invoke-MgExtractGroupDriveRootSensitivityLabel</S>
                <S>Invoke-MgExtractGroupSiteInformationProtectionPolicyLabel</S>
                <S>Invoke-MgFollowGroupDriveItem</S>
                <S>Invoke-MgFollowGroupDriveRoot</S>
                <S>Invoke-MgForwardGroupCalendarEvent</S>
                <S>Invoke-MgForwardGroupConversationThreadPost</S>
                <S>Invoke-MgForwardGroupConversationThreadPostInReplyTo</S>
                <S>Invoke-MgForwardGroupEvent</S>
                <S>Invoke-MgForwardGroupThreadPost</S>
                <S>Invoke-MgForwardGroupThreadPostInReplyTo</S>
                <S>Invoke-MgGraphGroup</S>
                <S>Invoke-MgGraphGroupDrive</S>
                <S>Invoke-MgInviteGroupDriveItem</S>
                <S>Invoke-MgInviteGroupDriveRoot</S>
                <S>Invoke-MgPreviewGroupDriveItem</S>
                <S>Invoke-MgPreviewGroupDriveRoot</S>
                <S>Invoke-MgPreviewGroupOnenotePage</S>
                <S>Invoke-MgReauthorizeGroupDriveItemSubscription</S>
                <S>Invoke-MgReauthorizeGroupDriveListSubscription</S>
                <S>Invoke-MgReauthorizeGroupDriveRootSubscription</S>
                <S>Invoke-MgReauthorizeGroupSiteListSubscription</S>
                <S>Invoke-MgRecentGroupDrive</S>
                <S>Invoke-MgRenewGroup</S>
                <S>Invoke-MgRenewGroupLifecyclePolicy</S>
                <S>Invoke-MgReplyGroupConversationThread</S>
                <S>Invoke-MgReplyGroupConversationThreadPost</S>
                <S>Invoke-MgReplyGroupConversationThreadPostInReplyTo</S>
                <S>Invoke-MgReplyGroupThread</S>
                <S>Invoke-MgReplyGroupThreadPost</S>
                <S>Invoke-MgReplyGroupThreadPostInReplyTo</S>
                <S>Invoke-MgSignGroupSiteInformationProtectionDigest</S>
                <S>Invoke-MgSnoozeGroupCalendarEventReminder</S>
                <S>Invoke-MgSnoozeGroupEventReminder</S>
                <S>Invoke-MgSubscribeGroupByMail</S>
                <S>Invoke-MgUnfollowGroupDriveItem</S>
                <S>Invoke-MgUnfollowGroupDriveRoot</S>
                <S>Join-MgGroupDriveListContentTypeWithHubSite</S>
                <S>Join-MgGroupSiteContentTypeWithHubSite</S>
                <S>Join-MgGroupSiteListContentTypeWithHubSite</S>
                <S>New-MgGroup</S>
                <S>New-MgGroupAcceptedSenderByRef</S>
                <S>New-MgGroupConversation</S>
                <S>New-MgGroupConversationThread</S>
                <S>New-MgGroupConversationThreadPostAttachment</S>
                <S>New-MgGroupConversationThreadPostAttachmentUploadSession</S>
                <S>New-MgGroupConversationThreadPostExtension</S>
                <S>New-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>New-MgGroupConversationThreadPostInReplyToAttachmentUploadSession</S>
                <S>New-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>New-MgGroupConversationThreadPostInReplyToMention</S>
                <S>New-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostMention</S>
                <S>New-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>New-MgGroupDriveItemLink</S>
                <S>New-MgGroupDriveItemListItemLink</S>
                <S>New-MgGroupDriveItemUploadSession</S>
                <S>New-MgGroupDriveListItemLink</S>
                <S>New-MgGroupDriveRootLink</S>
                <S>New-MgGroupDriveRootListItemLink</S>
                <S>New-MgGroupDriveRootUploadSession</S>
                <S>New-MgGroupEndpoint</S>
                <S>New-MgGroupExtension</S>
                <S>New-MgGroupLifecyclePolicy</S>
                <S>New-MgGroupMember</S>
                <S>New-MgGroupMemberByRef</S>
                <S>New-MgGroupOwner</S>
                <S>New-MgGroupOwnerByRef</S>
                <S>New-MgGroupPermissionGrant</S>
                <S>New-MgGroupRejectedSenderByRef</S>
                <S>New-MgGroupSetting</S>
                <S>New-MgGroupSiteListItemLink</S>
                <S>New-MgGroupThread</S>
                <S>New-MgGroupThreadPostAttachment</S>
                <S>New-MgGroupThreadPostAttachmentUploadSession</S>
                <S>New-MgGroupThreadPostExtension</S>
                <S>New-MgGroupThreadPostInReplyToAttachment</S>
                <S>New-MgGroupThreadPostInReplyToAttachmentUploadSession</S>
                <S>New-MgGroupThreadPostInReplyToExtension</S>
                <S>New-MgGroupThreadPostInReplyToMention</S>
                <S>New-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>New-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>New-MgGroupThreadPostMention</S>
                <S>New-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>New-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Publish-MgGroupDriveListContentType</S>
                <S>Publish-MgGroupSiteContentType</S>
                <S>Publish-MgGroupSiteListContentType</S>
                <S>Publish-MgGroupSitePage</S>
                <S>Remove-MgGroup</S>
                <S>Remove-MgGroupAcceptedSenderByRef</S>
                <S>Remove-MgGroupConversation</S>
                <S>Remove-MgGroupConversationThread</S>
                <S>Remove-MgGroupConversationThreadPostAttachment</S>
                <S>Remove-MgGroupConversationThreadPostExtension</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToMention</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostMention</S>
                <S>Remove-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Remove-MgGroupEndpoint</S>
                <S>Remove-MgGroupExtension</S>
                <S>Remove-MgGroupFavorite</S>
                <S>Remove-MgGroupFromLifecyclePolicy</S>
                <S>Remove-MgGroupLifecyclePolicy</S>
                <S>Remove-MgGroupMemberByRef</S>
                <S>Remove-MgGroupOwnerByRef</S>
                <S>Remove-MgGroupPermissionGrant</S>
                <S>Remove-MgGroupRejectedSenderByRef</S>
                <S>Remove-MgGroupSetting</S>
                <S>Remove-MgGroupSite</S>
                <S>Remove-MgGroupThread</S>
                <S>Remove-MgGroupThreadPostAttachment</S>
                <S>Remove-MgGroupThreadPostExtension</S>
                <S>Remove-MgGroupThreadPostInReplyToAttachment</S>
                <S>Remove-MgGroupThreadPostInReplyToExtension</S>
                <S>Remove-MgGroupThreadPostInReplyToMention</S>
                <S>Remove-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostMention</S>
                <S>Remove-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Reset-MgGroupUnseenCount</S>
                <S>Restore-MgGroupDriveItem</S>
                <S>Restore-MgGroupDriveItemListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveItemListItemVersion</S>
                <S>Restore-MgGroupDriveItemVersion</S>
                <S>Restore-MgGroupDriveListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveListItemVersion</S>
                <S>Restore-MgGroupDriveRoot</S>
                <S>Restore-MgGroupDriveRootListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveRootListItemVersion</S>
                <S>Restore-MgGroupDriveRootVersion</S>
                <S>Restore-MgGroupSiteListItemDocumentSetVersion</S>
                <S>Restore-MgGroupSiteListItemVersion</S>
                <S>Revoke-MgGroupDriveItemPermissionGrant</S>
                <S>Revoke-MgGroupDriveRootPermissionGrant</S>
                <S>Revoke-MgGroupSitePermissionGrant</S>
                <S>Search-MgGroupDrive</S>
                <S>Search-MgGroupDriveItem</S>
                <S>Search-MgGroupDriveRoot</S>
                <S>Set-MgGroupDriveItemSensitivityLabel</S>
                <S>Set-MgGroupDriveRootSensitivityLabel</S>
                <S>Set-MgGroupLicense</S>
                <S>Set-MgGroupPhotoContent</S>
                <S>Stop-MgGroupCalendarEvent</S>
                <S>Stop-MgGroupEvent</S>
                <S>Test-MgGroupDriveItemPermission</S>
                <S>Test-MgGroupDriveListContentTypePublished</S>
                <S>Test-MgGroupDriveRootPermission</S>
                <S>Test-MgGroupDynamicMembership</S>
                <S>Test-MgGroupDynamicMembershipRule</S>
                <S>Test-MgGroupPermissionGrantProperty</S>
                <S>Test-MgGroupProperty</S>
                <S>Test-MgGroupSiteContentTypePublished</S>
                <S>Test-MgGroupSiteInformationProtectionDataLossPreventionPolicy</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelApplication</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelClassificationResult</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelRemoval</S>
                <S>Test-MgGroupSiteInformationProtectionSensitivityLabel</S>
                <S>Test-MgGroupSiteInformationProtectionSensitivityLabelSublabel</S>
                <S>Test-MgGroupSiteListContentTypePublished</S>
                <S>Unpublish-MgGroupDriveListContentType</S>
                <S>Unpublish-MgGroupSiteContentType</S>
                <S>Unpublish-MgGroupSiteListContentType</S>
                <S>Update-MgGroup</S>
                <S>Update-MgGroupConversationThread</S>
                <S>Update-MgGroupConversationThreadPost</S>
                <S>Update-MgGroupConversationThreadPostExtension</S>
                <S>Update-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Update-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Update-MgGroupEndpoint</S>
                <S>Update-MgGroupExtension</S>
                <S>Update-MgGroupLifecyclePolicy</S>
                <S>Update-MgGroupOnenotePageContent</S>
                <S>Update-MgGroupPermissionGrant</S>
                <S>Update-MgGroupPhoto</S>
                <S>Update-MgGroupSetting</S>
                <S>Update-MgGroupThread</S>
                <S>Update-MgGroupThreadPost</S>
                <S>Update-MgGroupThreadPostExtension</S>
                <S>Update-MgGroupThreadPostInReplyToExtension</S>
                <S>Update-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostSingleValueExtendedProperty</S>
              </LST>
            </Obj>
          </En>
          <En>
            <S N="Key">RoleCapability</S>
            <Ref N="Value" RefId="5" />
          </En>
          <En>
            <S N="Key">Workflow</S>
            <Ref N="Value" RefId="5" />
          </En>
          <En>
            <S N="Key">Function</S>
            <Obj N="Value" RefId="7">
              <TNRef RefId="2" />
              <LST>
                <S>Add-MgGroupDriveListContentTypeCopy</S>
                <S>Add-MgGroupDriveListContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupFavorite</S>
                <S>Add-MgGroupSite</S>
                <S>Add-MgGroupSiteContentTypeCopy</S>
                <S>Add-MgGroupSiteContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupSiteListContentTypeCopy</S>
                <S>Add-MgGroupSiteListContentTypeCopyFromContentTypeHub</S>
                <S>Add-MgGroupToLifecyclePolicy</S>
                <S>Confirm-MgGroupGrantedPermissionForApp</S>
                <S>Confirm-MgGroupMemberGroup</S>
                <S>Confirm-MgGroupMemberObject</S>
                <S>Confirm-MgGroupPermissionGrantMemberGroup</S>
                <S>Confirm-MgGroupPermissionGrantMemberObject</S>
                <S>Confirm-MgGroupSiteInformationProtectionSignature</S>
                <S>Copy-MgGroupDriveItem</S>
                <S>Copy-MgGroupDriveListContentTypeToDefaultContentLocation</S>
                <S>Copy-MgGroupDriveRoot</S>
                <S>Copy-MgGroupOnenoteNotebook</S>
                <S>Copy-MgGroupOnenotePageToSection</S>
                <S>Copy-MgGroupOnenoteSectionToNotebook</S>
                <S>Copy-MgGroupOnenoteSectionToSectionGroup</S>
                <S>Copy-MgGroupSiteContentTypeToDefaultContentLocation</S>
                <S>Copy-MgGroupSiteListContentTypeToDefaultContentLocation</S>
                <S>Get-MgGroup</S>
                <S>Get-MgGroupAcceptedSender</S>
                <S>Get-MgGroupAcceptedSenderByRef</S>
                <S>Get-MgGroupById</S>
                <S>Get-MgGroupCalendarEventDelta</S>
                <S>Get-MgGroupCalendarSchedule</S>
                <S>Get-MgGroupConversation</S>
                <S>Get-MgGroupConversationThread</S>
                <S>Get-MgGroupConversationThreadPost</S>
                <S>Get-MgGroupConversationThreadPostAttachment</S>
                <S>Get-MgGroupConversationThreadPostExtension</S>
                <S>Get-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>Get-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Get-MgGroupConversationThreadPostInReplyToMention</S>
                <S>Get-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostMention</S>
                <S>Get-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Get-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Get-MgGroupCreatedOnBehalfOf</S>
                <S>Get-MgGroupDelta</S>
                <S>Get-MgGroupDriveItemActivityByInterval</S>
                <S>Get-MgGroupDriveItemDelta</S>
                <S>Get-MgGroupDriveItemListItemActivityByInterval</S>
                <S>Get-MgGroupDriveListContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupDriveListItemActivityByInterval</S>
                <S>Get-MgGroupDriveListItemDelta</S>
                <S>Get-MgGroupDriveRootActivityByInterval</S>
                <S>Get-MgGroupDriveRootDelta</S>
                <S>Get-MgGroupDriveRootListItemActivityByInterval</S>
                <S>Get-MgGroupEndpoint</S>
                <S>Get-MgGroupEventDelta</S>
                <S>Get-MgGroupExtension</S>
                <S>Get-MgGroupLifecyclePolicy</S>
                <S>Get-MgGroupLifecyclePolicyByGroup</S>
                <S>Get-MgGroupMember</S>
                <S>Get-MgGroupMemberByRef</S>
                <S>Get-MgGroupMemberGroup</S>
                <S>Get-MgGroupMemberObject</S>
                <S>Get-MgGroupMemberOf</S>
                <S>Get-MgGroupMemberWithLicenseError</S>
                <S>Get-MgGroupOnenoteNotebookFromWebUrl</S>
                <S>Get-MgGroupOnenoteRecentNotebook</S>
                <S>Get-MgGroupOwner</S>
                <S>Get-MgGroupOwnerByRef</S>
                <S>Get-MgGroupPermissionGrant</S>
                <S>Get-MgGroupPermissionGrantAvailableExtensionProperty</S>
                <S>Get-MgGroupPermissionGrantById</S>
                <S>Get-MgGroupPermissionGrantMemberGroup</S>
                <S>Get-MgGroupPermissionGrantMemberObject</S>
                <S>Get-MgGroupPermissionGrantUserOwnedObject</S>
                <S>Get-MgGroupPhoto</S>
                <S>Get-MgGroupPhotoContent</S>
                <S>Get-MgGroupRejectedSender</S>
                <S>Get-MgGroupRejectedSenderByRef</S>
                <S>Get-MgGroupSetting</S>
                <S>Get-MgGroupSiteActivityByInterval</S>
                <S>Get-MgGroupSiteApplicableContentTypeForList</S>
                <S>Get-MgGroupSiteByPath</S>
                <S>Get-MgGroupSiteContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupSiteDelta</S>
                <S>Get-MgGroupSiteListContentTypeCompatibleHubContentType</S>
                <S>Get-MgGroupSiteListItemActivityByInterval</S>
                <S>Get-MgGroupSiteListItemDelta</S>
                <S>Get-MgGroupSitePageCanvaLayoutHorizontalSectionColumnWebpartPositionOfWebPart</S>
                <S>Get-MgGroupSitePageCanvaLayoutVerticalSectionWebpartPositionOfWebPart</S>
                <S>Get-MgGroupSitePageWebPartByPosition</S>
                <S>Get-MgGroupSitePageWebPartPositionOfWebPart</S>
                <S>Get-MgGroupThread</S>
                <S>Get-MgGroupThreadPost</S>
                <S>Get-MgGroupThreadPostAttachment</S>
                <S>Get-MgGroupThreadPostExtension</S>
                <S>Get-MgGroupThreadPostInReplyToAttachment</S>
                <S>Get-MgGroupThreadPostInReplyToExtension</S>
                <S>Get-MgGroupThreadPostInReplyToMention</S>
                <S>Get-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostMention</S>
                <S>Get-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Get-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Get-MgGroupTransitiveMember</S>
                <S>Get-MgGroupTransitiveMemberOf</S>
                <S>Get-MgGroupUserOwnedObject</S>
                <S>Get-MgUserJoinedGroup</S>
                <S>Grant-MgGroupDriveItemPermission</S>
                <S>Grant-MgGroupDriveRootPermission</S>
                <S>Grant-MgGroupSitePermission</S>
                <S>Invoke-MgAcceptGroupCalendarEvent</S>
                <S>Invoke-MgAcceptGroupCalendarEventTentatively</S>
                <S>Invoke-MgAcceptGroupEvent</S>
                <S>Invoke-MgAcceptGroupEventTentatively</S>
                <S>Invoke-MgBufferGroupSiteInformationProtectionDecrypt</S>
                <S>Invoke-MgBufferGroupSiteInformationProtectionEncrypt</S>
                <S>Invoke-MgCalendarGroupCalendar</S>
                <S>Invoke-MgCheckinGroupDriveItem</S>
                <S>Invoke-MgCheckinGroupDriveRoot</S>
                <S>Invoke-MgCheckoutGroupDriveItem</S>
                <S>Invoke-MgCheckoutGroupDriveRoot</S>
                <S>Invoke-MgDeclineGroupCalendarEvent</S>
                <S>Invoke-MgDeclineGroupEvent</S>
                <S>Invoke-MgDismissGroupCalendarEventReminder</S>
                <S>Invoke-MgDismissGroupEventReminder</S>
                <S>Invoke-MgExtractGroupDriveItemSensitivityLabel</S>
                <S>Invoke-MgExtractGroupDriveRootSensitivityLabel</S>
                <S>Invoke-MgExtractGroupSiteInformationProtectionPolicyLabel</S>
                <S>Invoke-MgFollowGroupDriveItem</S>
                <S>Invoke-MgFollowGroupDriveRoot</S>
                <S>Invoke-MgForwardGroupCalendarEvent</S>
                <S>Invoke-MgForwardGroupConversationThreadPost</S>
                <S>Invoke-MgForwardGroupConversationThreadPostInReplyTo</S>
                <S>Invoke-MgForwardGroupEvent</S>
                <S>Invoke-MgForwardGroupThreadPost</S>
                <S>Invoke-MgForwardGroupThreadPostInReplyTo</S>
                <S>Invoke-MgGraphGroup</S>
                <S>Invoke-MgGraphGroupDrive</S>
                <S>Invoke-MgInviteGroupDriveItem</S>
                <S>Invoke-MgInviteGroupDriveRoot</S>
                <S>Invoke-MgPreviewGroupDriveItem</S>
                <S>Invoke-MgPreviewGroupDriveRoot</S>
                <S>Invoke-MgPreviewGroupOnenotePage</S>
                <S>Invoke-MgReauthorizeGroupDriveItemSubscription</S>
                <S>Invoke-MgReauthorizeGroupDriveListSubscription</S>
                <S>Invoke-MgReauthorizeGroupDriveRootSubscription</S>
                <S>Invoke-MgReauthorizeGroupSiteListSubscription</S>
                <S>Invoke-MgRecentGroupDrive</S>
                <S>Invoke-MgRenewGroup</S>
                <S>Invoke-MgRenewGroupLifecyclePolicy</S>
                <S>Invoke-MgReplyGroupConversationThread</S>
                <S>Invoke-MgReplyGroupConversationThreadPost</S>
                <S>Invoke-MgReplyGroupConversationThreadPostInReplyTo</S>
                <S>Invoke-MgReplyGroupThread</S>
                <S>Invoke-MgReplyGroupThreadPost</S>
                <S>Invoke-MgReplyGroupThreadPostInReplyTo</S>
                <S>Invoke-MgSignGroupSiteInformationProtectionDigest</S>
                <S>Invoke-MgSnoozeGroupCalendarEventReminder</S>
                <S>Invoke-MgSnoozeGroupEventReminder</S>
                <S>Invoke-MgSubscribeGroupByMail</S>
                <S>Invoke-MgUnfollowGroupDriveItem</S>
                <S>Invoke-MgUnfollowGroupDriveRoot</S>
                <S>Join-MgGroupDriveListContentTypeWithHubSite</S>
                <S>Join-MgGroupSiteContentTypeWithHubSite</S>
                <S>Join-MgGroupSiteListContentTypeWithHubSite</S>
                <S>New-MgGroup</S>
                <S>New-MgGroupAcceptedSenderByRef</S>
                <S>New-MgGroupConversation</S>
                <S>New-MgGroupConversationThread</S>
                <S>New-MgGroupConversationThreadPostAttachment</S>
                <S>New-MgGroupConversationThreadPostAttachmentUploadSession</S>
                <S>New-MgGroupConversationThreadPostExtension</S>
                <S>New-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>New-MgGroupConversationThreadPostInReplyToAttachmentUploadSession</S>
                <S>New-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>New-MgGroupConversationThreadPostInReplyToMention</S>
                <S>New-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostMention</S>
                <S>New-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>New-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>New-MgGroupDriveItemLink</S>
                <S>New-MgGroupDriveItemListItemLink</S>
                <S>New-MgGroupDriveItemUploadSession</S>
                <S>New-MgGroupDriveListItemLink</S>
                <S>New-MgGroupDriveRootLink</S>
                <S>New-MgGroupDriveRootListItemLink</S>
                <S>New-MgGroupDriveRootUploadSession</S>
                <S>New-MgGroupEndpoint</S>
                <S>New-MgGroupExtension</S>
                <S>New-MgGroupLifecyclePolicy</S>
                <S>New-MgGroupMember</S>
                <S>New-MgGroupMemberByRef</S>
                <S>New-MgGroupOwner</S>
                <S>New-MgGroupOwnerByRef</S>
                <S>New-MgGroupPermissionGrant</S>
                <S>New-MgGroupRejectedSenderByRef</S>
                <S>New-MgGroupSetting</S>
                <S>New-MgGroupSiteListItemLink</S>
                <S>New-MgGroupThread</S>
                <S>New-MgGroupThreadPostAttachment</S>
                <S>New-MgGroupThreadPostAttachmentUploadSession</S>
                <S>New-MgGroupThreadPostExtension</S>
                <S>New-MgGroupThreadPostInReplyToAttachment</S>
                <S>New-MgGroupThreadPostInReplyToAttachmentUploadSession</S>
                <S>New-MgGroupThreadPostInReplyToExtension</S>
                <S>New-MgGroupThreadPostInReplyToMention</S>
                <S>New-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>New-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>New-MgGroupThreadPostMention</S>
                <S>New-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>New-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Publish-MgGroupDriveListContentType</S>
                <S>Publish-MgGroupSiteContentType</S>
                <S>Publish-MgGroupSiteListContentType</S>
                <S>Publish-MgGroupSitePage</S>
                <S>Remove-MgGroup</S>
                <S>Remove-MgGroupAcceptedSenderByRef</S>
                <S>Remove-MgGroupConversation</S>
                <S>Remove-MgGroupConversationThread</S>
                <S>Remove-MgGroupConversationThreadPostAttachment</S>
                <S>Remove-MgGroupConversationThreadPostExtension</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToAttachment</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToMention</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostMention</S>
                <S>Remove-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Remove-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Remove-MgGroupEndpoint</S>
                <S>Remove-MgGroupExtension</S>
                <S>Remove-MgGroupFavorite</S>
                <S>Remove-MgGroupFromLifecyclePolicy</S>
                <S>Remove-MgGroupLifecyclePolicy</S>
                <S>Remove-MgGroupMemberByRef</S>
                <S>Remove-MgGroupOwnerByRef</S>
                <S>Remove-MgGroupPermissionGrant</S>
                <S>Remove-MgGroupRejectedSenderByRef</S>
                <S>Remove-MgGroupSetting</S>
                <S>Remove-MgGroupSite</S>
                <S>Remove-MgGroupThread</S>
                <S>Remove-MgGroupThreadPostAttachment</S>
                <S>Remove-MgGroupThreadPostExtension</S>
                <S>Remove-MgGroupThreadPostInReplyToAttachment</S>
                <S>Remove-MgGroupThreadPostInReplyToExtension</S>
                <S>Remove-MgGroupThreadPostInReplyToMention</S>
                <S>Remove-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostMention</S>
                <S>Remove-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Remove-MgGroupThreadPostSingleValueExtendedProperty</S>
                <S>Reset-MgGroupUnseenCount</S>
                <S>Restore-MgGroupDriveItem</S>
                <S>Restore-MgGroupDriveItemListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveItemListItemVersion</S>
                <S>Restore-MgGroupDriveItemVersion</S>
                <S>Restore-MgGroupDriveListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveListItemVersion</S>
                <S>Restore-MgGroupDriveRoot</S>
                <S>Restore-MgGroupDriveRootListItemDocumentSetVersion</S>
                <S>Restore-MgGroupDriveRootListItemVersion</S>
                <S>Restore-MgGroupDriveRootVersion</S>
                <S>Restore-MgGroupSiteListItemDocumentSetVersion</S>
                <S>Restore-MgGroupSiteListItemVersion</S>
                <S>Revoke-MgGroupDriveItemPermissionGrant</S>
                <S>Revoke-MgGroupDriveRootPermissionGrant</S>
                <S>Revoke-MgGroupSitePermissionGrant</S>
                <S>Search-MgGroupDrive</S>
                <S>Search-MgGroupDriveItem</S>
                <S>Search-MgGroupDriveRoot</S>
                <S>Set-MgGroupDriveItemSensitivityLabel</S>
                <S>Set-MgGroupDriveRootSensitivityLabel</S>
                <S>Set-MgGroupLicense</S>
                <S>Set-MgGroupPhotoContent</S>
                <S>Stop-MgGroupCalendarEvent</S>
                <S>Stop-MgGroupEvent</S>
                <S>Test-MgGroupDriveItemPermission</S>
                <S>Test-MgGroupDriveListContentTypePublished</S>
                <S>Test-MgGroupDriveRootPermission</S>
                <S>Test-MgGroupDynamicMembership</S>
                <S>Test-MgGroupDynamicMembershipRule</S>
                <S>Test-MgGroupPermissionGrantProperty</S>
                <S>Test-MgGroupProperty</S>
                <S>Test-MgGroupSiteContentTypePublished</S>
                <S>Test-MgGroupSiteInformationProtectionDataLossPreventionPolicy</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelApplication</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelClassificationResult</S>
                <S>Test-MgGroupSiteInformationProtectionPolicyLabelRemoval</S>
                <S>Test-MgGroupSiteInformationProtectionSensitivityLabel</S>
                <S>Test-MgGroupSiteInformationProtectionSensitivityLabelSublabel</S>
                <S>Test-MgGroupSiteListContentTypePublished</S>
                <S>Unpublish-MgGroupDriveListContentType</S>
                <S>Unpublish-MgGroupSiteContentType</S>
                <S>Unpublish-MgGroupSiteListContentType</S>
                <S>Update-MgGroup</S>
                <S>Update-MgGroupConversationThread</S>
                <S>Update-MgGroupConversationThreadPost</S>
                <S>Update-MgGroupConversationThreadPostExtension</S>
                <S>Update-MgGroupConversationThreadPostInReplyToExtension</S>
                <S>Update-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostMultiValueExtendedProperty</S>
                <S>Update-MgGroupConversationThreadPostSingleValueExtendedProperty</S>
                <S>Update-MgGroupEndpoint</S>
                <S>Update-MgGroupExtension</S>
                <S>Update-MgGroupLifecyclePolicy</S>
                <S>Update-MgGroupOnenotePageContent</S>
                <S>Update-MgGroupPermissionGrant</S>
                <S>Update-MgGroupPhoto</S>
                <S>Update-MgGroupSetting</S>
                <S>Update-MgGroupThread</S>
                <S>Update-MgGroupThreadPost</S>
                <S>Update-MgGroupThreadPostExtension</S>
                <S>Update-MgGroupThreadPostInReplyToExtension</S>
                <S>Update-MgGroupThreadPostInReplyToMultiValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostInReplyToSingleValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostMultiValueExtendedProperty</S>
                <S>Update-MgGroupThreadPostSingleValueExtendedProperty</S>
              </LST>
            </Obj>
          </En>
          <En>
            <S N="Key">Cmdlet</S>
            <Ref N="Value" RefId="5" />
          </En>
        </DCT>
      </Obj>
      <Nil N="PowerShellGetFormatVersion" />
      <S N="ReleaseNotes">See https://aka.ms/GraphPowerShell-Release.</S>
      <Obj N="Dependencies" RefId="8">
        <TNRef RefId="2" />
        <LST>
          <Obj RefId="9">
            <TN RefId="4">
              <T>System.Collections.Specialized.OrderedDictionary</T>
              <T>System.Object</T>
            </TN>
            <DCT>
              <En>
                <S N="Key">Name</S>
                <S N="Value">Microsoft.Graph.Authentication</S>
              </En>
              <En>
                <S N="Key">RequiredVersion</S>
                <S N="Value">1.23.0</S>
              </En>
              <En>
                <S N="Key">CanonicalId</S>
                <S N="Value">nuget:Microsoft.Graph.Authentication/[1.23.0]</S>
              </En>
            </DCT>
          </Obj>
        </LST>
      </Obj>
      <S N="RepositorySourceLocation">https://www.powershellgallery.com/api/v2</S>
      <S N="Repository">PSGallery</S>
      <S N="PackageManagementProvider">NuGet</S>
      <Obj N="AdditionalMetadata" RefId="10">
        <TN RefId="5">
          <T>System.Management.Automation.PSCustomObject</T>
          <T>System.Object</T>
        </TN>
        <MS>
          <S N="copyright">Microsoft Corporation. All rights reserved.</S>
          <S N="description">Microsoft Graph PowerShell Cmdlets</S>
          <S N="requireLicenseAcceptance">True</S>
          <S N="releaseNotes">See https://aka.ms/GraphPowerShell-Release.</S>
          <S N="isLatestVersion">True</S>
          <S N="isAbsoluteLatestVersion">False</S>
          <S N="versionDownloadCount">9842</S>
          <S N="downloadCount">1497426</S>
          <S N="packageSize">7462763</S>
          <S N="published">3/4/2023 4:00:28 AM -06:00</S>
          <S N="created">3/4/2023 4:00:28 AM -06:00</S>
          <S N="lastUpdated">3/6/2023 11:19:47 PM -06:00</S>
          <S N="tags">Microsoft Office365 Graph PowerShell PSModule PSIncludes_Cmdlet PSEdition_Core PSEdition_Desktop PSFunction_Add-MgGroupDriveListContentTypeCopy PSCommand_Add-MgGroupDriveListContentTypeCopy PSFunction_Add-MgGroupDriveListContentTypeCopyFromContentTypeHub PSCommand_Add-MgGroupDriveListContentTypeCopyFromContentTypeHub PSFunction_Add-MgGroupFavorite PSCommand_Add-MgGroupFavorite PSFunction_Add-MgGroupSite PSCommand_Add-MgGroupSite PSFunction_Add-MgGroupSiteContentTypeCopy PSCommand_Add-MgGroupSiteContentTypeCopy PSFunction_Add-MgGroupSiteContentTypeCopyFromContentTypeHub PSCommand_Add-MgGroupSiteContentTypeCopyFromContentTypeHub PSFunction_Add-MgGroupSiteListContentTypeCopy PSCommand_Add-MgGroupSiteListContentTypeCopy PSFunction_Add-MgGroupSiteListContentTypeCopyFromContentTypeHub PSCommand_Add-MgGroupSiteListContentTypeCopyFromContentTypeHub PSFunction_Add-MgGroupToLifecyclePolicy PSCommand_Add-MgGroupToLifecyclePolicy PSFunction_Confirm-MgGroupGrantedPermissionForApp PSCommand_Confirm-MgGroupGrantedPermissionForApp PSFunction_Confirm-MgGroupMemberGroup PSCommand_Confirm-MgGroupMemberGroup PSFunction_Confirm-MgGroupMemberObject PSCommand_Confirm-MgGroupMemberObject PSFunction_Confirm-MgGroupPermissionGrantMemberGroup PSCommand_Confirm-MgGroupPermissionGrantMemberGroup PSFunction_Confirm-MgGroupPermissionGrantMemberObject PSCommand_Confirm-MgGroupPermissionGrantMemberObject PSFunction_Confirm-MgGroupSiteInformationProtectionSignature PSCommand_Confirm-MgGroupSiteInformationProtectionSignature PSFunction_Copy-MgGroupDriveItem PSCommand_Copy-MgGroupDriveItem PSFunction_Copy-MgGroupDriveListContentTypeToDefaultContentLocation PSCommand_Copy-MgGroupDriveListContentTypeToDefaultContentLocation PSFunction_Copy-MgGroupDriveRoot PSCommand_Copy-MgGroupDriveRoot PSFunction_Copy-MgGroupOnenoteNotebook PSCommand_Copy-MgGroupOnenoteNotebook PSFunction_Copy-MgGroupOnenotePageToSection PSCommand_Copy-MgGroupOnenotePageToSection PSFunction_Copy-MgGroupOnenoteSectionToNotebook PSCommand_Copy-MgGroupOnenoteSectionToNotebook PSFunction_Copy-MgGroupOnenoteSectionToSectionGroup PSCommand_Copy-MgGroupOnenoteSectionToSectionGroup PSFunction_Copy-MgGroupSiteContentTypeToDefaultContentLocation PSCommand_Copy-MgGroupSiteContentTypeToDefaultContentLocation PSFunction_Copy-MgGroupSiteListContentTypeToDefaultContentLocation PSCommand_Copy-MgGroupSiteListContentTypeToDefaultContentLocation PSFunction_Get-MgGroup PSCommand_Get-MgGroup PSFunction_Get-MgGroupAcceptedSender PSCommand_Get-MgGroupAcceptedSender PSFunction_Get-MgGroupAcceptedSenderByRef PSCommand_Get-MgGroupAcceptedSenderByRef PSFunction_Get-MgGroupById PSCommand_Get-MgGroupById PSFunction_Get-MgGroupCalendarEventDelta PSCommand_Get-MgGroupCalendarEventDelta PSFunction_Get-MgGroupCalendarSchedule PSCommand_Get-MgGroupCalendarSchedule PSFunction_Get-MgGroupConversation PSCommand_Get-MgGroupConversation PSFunction_Get-MgGroupConversationThread PSCommand_Get-MgGroupConversationThread PSFunction_Get-MgGroupConversationThreadPost PSCommand_Get-MgGroupConversationThreadPost PSFunction_Get-MgGroupConversationThreadPostAttachment PSCommand_Get-MgGroupConversationThreadPostAttachment PSFunction_Get-MgGroupConversationThreadPostExtension PSCommand_Get-MgGroupConversationThreadPostExtension PSFunction_Get-MgGroupConversationThreadPostInReplyToAttachment PSCommand_Get-MgGroupConversationThreadPostInReplyToAttachment PSFunction_Get-MgGroupConversationThreadPostInReplyToExtension PSCommand_Get-MgGroupConversationThreadPostInReplyToExtension PSFunction_Get-MgGroupConversationThreadPostInReplyToMention PSCommand_Get-MgGroupConversationThreadPostInReplyToMention PSFunction_Get-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSCommand_Get-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSFunction_Get-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSCommand_Get-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSFunction_Get-MgGroupConversationThreadPostMention PSCommand_Get-MgGroupConversationThreadPostMention PSFunction_Get-MgGroupConversationThreadPostMultiValueExtendedProperty PSCommand_Get-MgGroupConversationThreadPostMultiValueExtendedProperty PSFunction_Get-MgGroupConversationThreadPostSingleValueExtendedProperty PSCommand_Get-MgGroupConversationThreadPostSingleValueExtendedProperty PSFunction_Get-MgGroupCreatedOnBehalfOf PSCommand_Get-MgGroupCreatedOnBehalfOf PSFunction_Get-MgGroupDelta PSCommand_Get-MgGroupDelta PSFunction_Get-MgGroupDriveItemActivityByInterval PSCommand_Get-MgGroupDriveItemActivityByInterval PSFunction_Get-MgGroupDriveItemDelta PSCommand_Get-MgGroupDriveItemDelta PSFunction_Get-MgGroupDriveItemListItemActivityByInterval PSCommand_Get-MgGroupDriveItemListItemActivityByInterval PSFunction_Get-MgGroupDriveListContentTypeCompatibleHubContentType PSCommand_Get-MgGroupDriveListContentTypeCompatibleHubContentType PSFunction_Get-MgGroupDriveListItemActivityByInterval PSCommand_Get-MgGroupDriveListItemActivityByInterval PSFunction_Get-MgGroupDriveListItemDelta PSCommand_Get-MgGroupDriveListItemDelta PSFunction_Get-MgGroupDriveRootActivityByInterval PSCommand_Get-MgGroupDriveRootActivityByInterval PSFunction_Get-MgGroupDriveRootDelta PSCommand_Get-MgGroupDriveRootDelta PSFunction_Get-MgGroupDriveRootListItemActivityByInterval PSCommand_Get-MgGroupDriveRootListItemActivityByInterval PSFunction_Get-MgGroupEndpoint PSCommand_Get-MgGroupEndpoint PSFunction_Get-MgGroupEventDelta PSCommand_Get-MgGroupEventDelta PSFunction_Get-MgGroupExtension PSCommand_Get-MgGroupExtension PSFunction_Get-MgGroupLifecyclePolicy PSCommand_Get-MgGroupLifecyclePolicy PSFunction_Get-MgGroupLifecyclePolicyByGroup PSCommand_Get-MgGroupLifecyclePolicyByGroup PSFunction_Get-MgGroupMember PSCommand_Get-MgGroupMember PSFunction_Get-MgGroupMemberByRef PSCommand_Get-MgGroupMemberByRef PSFunction_Get-MgGroupMemberGroup PSCommand_Get-MgGroupMemberGroup PSFunction_Get-MgGroupMemberObject PSCommand_Get-MgGroupMemberObject PSFunction_Get-MgGroupMemberOf PSCommand_Get-MgGroupMemberOf PSFunction_Get-MgGroupMemberWithLicenseError PSCommand_Get-MgGroupMemberWithLicenseError PSFunction_Get-MgGroupOnenoteNotebookFromWebUrl PSCommand_Get-MgGroupOnenoteNotebookFromWebUrl PSFunction_Get-MgGroupOnenoteRecentNotebook PSCommand_Get-MgGroupOnenoteRecentNotebook PSFunction_Get-MgGroupOwner PSCommand_Get-MgGroupOwner PSFunction_Get-MgGroupOwnerByRef PSCommand_Get-MgGroupOwnerByRef PSFunction_Get-MgGroupPermissionGrant PSCommand_Get-MgGroupPermissionGrant PSFunction_Get-MgGroupPermissionGrantAvailableExtensionProperty PSCommand_Get-MgGroupPermissionGrantAvailableExtensionProperty PSFunction_Get-MgGroupPermissionGrantById PSCommand_Get-MgGroupPermissionGrantById PSFunction_Get-MgGroupPermissionGrantMemberGroup PSCommand_Get-MgGroupPermissionGrantMemberGroup PSFunction_Get-MgGroupPermissionGrantMemberObject PSCommand_Get-MgGroupPermissionGrantMemberObject PSFunction_Get-MgGroupPermissionGrantUserOwnedObject PSCommand_Get-MgGroupPermissionGrantUserOwnedObject PSFunction_Get-MgGroupPhoto PSCommand_Get-MgGroupPhoto PSFunction_Get-MgGroupPhotoContent PSCommand_Get-MgGroupPhotoContent PSFunction_Get-MgGroupRejectedSender PSCommand_Get-MgGroupRejectedSender PSFunction_Get-MgGroupRejectedSenderByRef PSCommand_Get-MgGroupRejectedSenderByRef PSFunction_Get-MgGroupSetting PSCommand_Get-MgGroupSetting PSFunction_Get-MgGroupSiteActivityByInterval PSCommand_Get-MgGroupSiteActivityByInterval PSFunction_Get-MgGroupSiteApplicableContentTypeForList PSCommand_Get-MgGroupSiteApplicableContentTypeForList PSFunction_Get-MgGroupSiteByPath PSCommand_Get-MgGroupSiteByPath PSFunction_Get-MgGroupSiteContentTypeCompatibleHubContentType PSCommand_Get-MgGroupSiteContentTypeCompatibleHubContentType PSFunction_Get-MgGroupSiteDelta PSCommand_Get-MgGroupSiteDelta PSFunction_Get-MgGroupSiteListContentTypeCompatibleHubContentType PSCommand_Get-MgGroupSiteListContentTypeCompatibleHubContentType PSFunction_Get-MgGroupSiteListItemActivityByInterval PSCommand_Get-MgGroupSiteListItemActivityByInterval PSFunction_Get-MgGroupSiteListItemDelta PSCommand_Get-MgGroupSiteListItemDelta PSFunction_Get-MgGroupSitePageCanvaLayoutHorizontalSectionColumnWebpartPositionOfWebPart PSCommand_Get-MgGroupSitePageCanvaLayoutHorizontalSectionColumnWebpartPositionOfWebPart PSFunction_Get-MgGroupSitePageCanvaLayoutVerticalSectionWebpartPositionOfWebPart PSCommand_Get-MgGroupSitePageCanvaLayoutVerticalSectionWebpartPositionOfWebPart PSFunction_Get-MgGroupSitePageWebPartByPosition PSCommand_Get-MgGroupSitePageWebPartByPosition PSFunction_Get-MgGroupSitePageWebPartPositionOfWebPart PSCommand_Get-MgGroupSitePageWebPartPositionOfWebPart PSFunction_Get-MgGroupThread PSCommand_Get-MgGroupThread PSFunction_Get-MgGroupThreadPost PSCommand_Get-MgGroupThreadPost PSFunction_Get-MgGroupThreadPostAttachment PSCommand_Get-MgGroupThreadPostAttachment PSFunction_Get-MgGroupThreadPostExtension PSCommand_Get-MgGroupThreadPostExtension PSFunction_Get-MgGroupThreadPostInReplyToAttachment PSCommand_Get-MgGroupThreadPostInReplyToAttachment PSFunction_Get-MgGroupThreadPostInReplyToExtension PSCommand_Get-MgGroupThreadPostInReplyToExtension PSFunction_Get-MgGroupThreadPostInReplyToMention PSCommand_Get-MgGroupThreadPostInReplyToMention PSFunction_Get-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSCommand_Get-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSFunction_Get-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSCommand_Get-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSFunction_Get-MgGroupThreadPostMention PSCommand_Get-MgGroupThreadPostMention PSFunction_Get-MgGroupThreadPostMultiValueExtendedProperty PSCommand_Get-MgGroupThreadPostMultiValueExtendedProperty PSFunction_Get-MgGroupThreadPostSingleValueExtendedProperty PSCommand_Get-MgGroupThreadPostSingleValueExtendedProperty PSFunction_Get-MgGroupTransitiveMember PSCommand_Get-MgGroupTransitiveMember PSFunction_Get-MgGroupTransitiveMemberOf PSCommand_Get-MgGroupTransitiveMemberOf PSFunction_Get-MgGroupUserOwnedObject PSCommand_Get-MgGroupUserOwnedObject PSFunction_Get-MgUserJoinedGroup PSCommand_Get-MgUserJoinedGroup PSFunction_Grant-MgGroupDriveItemPermission PSCommand_Grant-MgGroupDriveItemPermission PSFunction_Grant-MgGroupDriveRootPermission PSCommand_Grant-MgGroupDriveRootPermission PSFunction_Grant-MgGroupSitePermission PSCommand_Grant-MgGroupSitePermission PSFunction_Invoke-MgAcceptGroupCalendarEvent PSCommand_Invoke-MgAcceptGroupCalendarEvent PSFunction_Invoke-MgAcceptGroupCalendarEventTentatively PSCommand_Invoke-MgAcceptGroupCalendarEventTentatively PSFunction_Invoke-MgAcceptGroupEvent PSCommand_Invoke-MgAcceptGroupEvent PSFunction_Invoke-MgAcceptGroupEventTentatively PSCommand_Invoke-MgAcceptGroupEventTentatively PSFunction_Invoke-MgBufferGroupSiteInformationProtectionDecrypt PSCommand_Invoke-MgBufferGroupSiteInformationProtectionDecrypt PSFunction_Invoke-MgBufferGroupSiteInformationProtectionEncrypt PSCommand_Invoke-MgBufferGroupSiteInformationProtectionEncrypt PSFunction_Invoke-MgCalendarGroupCalendar PSCommand_Invoke-MgCalendarGroupCalendar PSFunction_Invoke-MgCheckinGroupDriveItem PSCommand_Invoke-MgCheckinGroupDriveItem PSFunction_Invoke-MgCheckinGroupDriveRoot PSCommand_Invoke-MgCheckinGroupDriveRoot PSFunction_Invoke-MgCheckoutGroupDriveItem PSCommand_Invoke-MgCheckoutGroupDriveItem PSFunction_Invoke-MgCheckoutGroupDriveRoot PSCommand_Invoke-MgCheckoutGroupDriveRoot PSFunction_Invoke-MgDeclineGroupCalendarEvent PSCommand_Invoke-MgDeclineGroupCalendarEvent PSFunction_Invoke-MgDeclineGroupEvent PSCommand_Invoke-MgDeclineGroupEvent PSFunction_Invoke-MgDismissGroupCalendarEventReminder PSCommand_Invoke-MgDismissGroupCalendarEventReminder PSFunction_Invoke-MgDismissGroupEventReminder PSCommand_Invoke-MgDismissGroupEventReminder PSFunction_Invoke-MgExtractGroupDriveItemSensitivityLabel PSCommand_Invoke-MgExtractGroupDriveItemSensitivityLabel PSFunction_Invoke-MgExtractGroupDriveRootSensitivityLabel PSCommand_Invoke-MgExtractGroupDriveRootSensitivityLabel PSFunction_Invoke-MgExtractGroupSiteInformationProtectionPolicyLabel PSCommand_Invoke-MgExtractGroupSiteInformationProtectionPolicyLabel PSFunction_Invoke-MgFollowGroupDriveItem PSCommand_Invoke-MgFollowGroupDriveItem PSFunction_Invoke-MgFollowGroupDriveRoot PSCommand_Invoke-MgFollowGroupDriveRoot PSFunction_Invoke-MgForwardGroupCalendarEvent PSCommand_Invoke-MgForwardGroupCalendarEvent PSFunction_Invoke-MgForwardGroupConversationThreadPost PSCommand_Invoke-MgForwardGroupConversationThreadPost PSFunction_Invoke-MgForwardGroupConversationThreadPostInReplyTo PSCommand_Invoke-MgForwardGroupConversationThreadPostInReplyTo PSFunction_Invoke-MgForwardGroupEvent PSCommand_Invoke-MgForwardGroupEvent PSFunction_Invoke-MgForwardGroupThreadPost PSCommand_Invoke-MgForwardGroupThreadPost PSFunction_Invoke-MgForwardGroupThreadPostInReplyTo PSCommand_Invoke-MgForwardGroupThreadPostInReplyTo PSFunction_Invoke-MgGraphGroup PSCommand_Invoke-MgGraphGroup PSFunction_Invoke-MgGraphGroupDrive PSCommand_Invoke-MgGraphGroupDrive PSFunction_Invoke-MgInviteGroupDriveItem PSCommand_Invoke-MgInviteGroupDriveItem PSFunction_Invoke-MgInviteGroupDriveRoot PSCommand_Invoke-MgInviteGroupDriveRoot PSFunction_Invoke-MgPreviewGroupDriveItem PSCommand_Invoke-MgPreviewGroupDriveItem PSFunction_Invoke-MgPreviewGroupDriveRoot PSCommand_Invoke-MgPreviewGroupDriveRoot PSFunction_Invoke-MgPreviewGroupOnenotePage PSCommand_Invoke-MgPreviewGroupOnenotePage PSFunction_Invoke-MgReauthorizeGroupDriveItemSubscription PSCommand_Invoke-MgReauthorizeGroupDriveItemSubscription PSFunction_Invoke-MgReauthorizeGroupDriveListSubscription PSCommand_Invoke-MgReauthorizeGroupDriveListSubscription PSFunction_Invoke-MgReauthorizeGroupDriveRootSubscription PSCommand_Invoke-MgReauthorizeGroupDriveRootSubscription PSFunction_Invoke-MgReauthorizeGroupSiteListSubscription PSCommand_Invoke-MgReauthorizeGroupSiteListSubscription PSFunction_Invoke-MgRecentGroupDrive PSCommand_Invoke-MgRecentGroupDrive PSFunction_Invoke-MgRenewGroup PSCommand_Invoke-MgRenewGroup PSFunction_Invoke-MgRenewGroupLifecyclePolicy PSCommand_Invoke-MgRenewGroupLifecyclePolicy PSFunction_Invoke-MgReplyGroupConversationThread PSCommand_Invoke-MgReplyGroupConversationThread PSFunction_Invoke-MgReplyGroupConversationThreadPost PSCommand_Invoke-MgReplyGroupConversationThreadPost PSFunction_Invoke-MgReplyGroupConversationThreadPostInReplyTo PSCommand_Invoke-MgReplyGroupConversationThreadPostInReplyTo PSFunction_Invoke-MgReplyGroupThread PSCommand_Invoke-MgReplyGroupThread PSFunction_Invoke-MgReplyGroupThreadPost PSCommand_Invoke-MgReplyGroupThreadPost PSFunction_Invoke-MgReplyGroupThreadPostInReplyTo PSCommand_Invoke-MgReplyGroupThreadPostInReplyTo PSFunction_Invoke-MgSignGroupSiteInformationProtectionDigest PSCommand_Invoke-MgSignGroupSiteInformationProtectionDigest PSFunction_Invoke-MgSnoozeGroupCalendarEventReminder PSCommand_Invoke-MgSnoozeGroupCalendarEventReminder PSFunction_Invoke-MgSnoozeGroupEventReminder PSCommand_Invoke-MgSnoozeGroupEventReminder PSFunction_Invoke-MgSubscribeGroupByMail PSCommand_Invoke-MgSubscribeGroupByMail PSFunction_Invoke-MgUnfollowGroupDriveItem PSCommand_Invoke-MgUnfollowGroupDriveItem PSFunction_Invoke-MgUnfollowGroupDriveRoot PSCommand_Invoke-MgUnfollowGroupDriveRoot PSFunction_Join-MgGroupDriveListContentTypeWithHubSite PSCommand_Join-MgGroupDriveListContentTypeWithHubSite PSFunction_Join-MgGroupSiteContentTypeWithHubSite PSCommand_Join-MgGroupSiteContentTypeWithHubSite PSFunction_Join-MgGroupSiteListContentTypeWithHubSite PSCommand_Join-MgGroupSiteListContentTypeWithHubSite PSFunction_New-MgGroup PSCommand_New-MgGroup PSFunction_New-MgGroupAcceptedSenderByRef PSCommand_New-MgGroupAcceptedSenderByRef PSFunction_New-MgGroupConversation PSCommand_New-MgGroupConversation PSFunction_New-MgGroupConversationThread PSCommand_New-MgGroupConversationThread PSFunction_New-MgGroupConversationThreadPostAttachment PSCommand_New-MgGroupConversationThreadPostAttachment PSFunction_New-MgGroupConversationThreadPostAttachmentUploadSession PSCommand_New-MgGroupConversationThreadPostAttachmentUploadSession PSFunction_New-MgGroupConversationThreadPostExtension PSCommand_New-MgGroupConversationThreadPostExtension PSFunction_New-MgGroupConversationThreadPostInReplyToAttachment PSCommand_New-MgGroupConversationThreadPostInReplyToAttachment PSFunction_New-MgGroupConversationThreadPostInReplyToAttachmentUploadSession PSCommand_New-MgGroupConversationThreadPostInReplyToAttachmentUploadSession PSFunction_New-MgGroupConversationThreadPostInReplyToExtension PSCommand_New-MgGroupConversationThreadPostInReplyToExtension PSFunction_New-MgGroupConversationThreadPostInReplyToMention PSCommand_New-MgGroupConversationThreadPostInReplyToMention PSFunction_New-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSCommand_New-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSFunction_New-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSCommand_New-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSFunction_New-MgGroupConversationThreadPostMention PSCommand_New-MgGroupConversationThreadPostMention PSFunction_New-MgGroupConversationThreadPostMultiValueExtendedProperty PSCommand_New-MgGroupConversationThreadPostMultiValueExtendedProperty PSFunction_New-MgGroupConversationThreadPostSingleValueExtendedProperty PSCommand_New-MgGroupConversationThreadPostSingleValueExtendedProperty PSFunction_New-MgGroupDriveItemLink PSCommand_New-MgGroupDriveItemLink PSFunction_New-MgGroupDriveItemListItemLink PSCommand_New-MgGroupDriveItemListItemLink PSFunction_New-MgGroupDriveItemUploadSession PSCommand_New-MgGroupDriveItemUploadSession PSFunction_New-MgGroupDriveListItemLink PSCommand_New-MgGroupDriveListItemLink PSFunction_New-MgGroupDriveRootLink PSCommand_New-MgGroupDriveRootLink PSFunction_New-MgGroupDriveRootListItemLink PSCommand_New-MgGroupDriveRootListItemLink PSFunction_New-MgGroupDriveRootUploadSession PSCommand_New-MgGroupDriveRootUploadSession PSFunction_New-MgGroupEndpoint PSCommand_New-MgGroupEndpoint PSFunction_New-MgGroupExtension PSCommand_New-MgGroupExtension PSFunction_New-MgGroupLifecyclePolicy PSCommand_New-MgGroupLifecyclePolicy PSFunction_New-MgGroupMember PSCommand_New-MgGroupMember PSFunction_New-MgGroupMemberByRef PSCommand_New-MgGroupMemberByRef PSFunction_New-MgGroupOwner PSCommand_New-MgGroupOwner PSFunction_New-MgGroupOwnerByRef PSCommand_New-MgGroupOwnerByRef PSFunction_New-MgGroupPermissionGrant PSCommand_New-MgGroupPermissionGrant PSFunction_New-MgGroupRejectedSenderByRef PSCommand_New-MgGroupRejectedSenderByRef PSFunction_New-MgGroupSetting PSCommand_New-MgGroupSetting PSFunction_New-MgGroupSiteListItemLink PSCommand_New-MgGroupSiteListItemLink PSFunction_New-MgGroupThread PSCommand_New-MgGroupThread PSFunction_New-MgGroupThreadPostAttachment PSCommand_New-MgGroupThreadPostAttachment PSFunction_New-MgGroupThreadPostAttachmentUploadSession PSCommand_New-MgGroupThreadPostAttachmentUploadSession PSFunction_New-MgGroupThreadPostExtension PSCommand_New-MgGroupThreadPostExtension PSFunction_New-MgGroupThreadPostInReplyToAttachment PSCommand_New-MgGroupThreadPostInReplyToAttachment PSFunction_New-MgGroupThreadPostInReplyToAttachmentUploadSession PSCommand_New-MgGroupThreadPostInReplyToAttachmentUploadSession PSFunction_New-MgGroupThreadPostInReplyToExtension PSCommand_New-MgGroupThreadPostInReplyToExtension PSFunction_New-MgGroupThreadPostInReplyToMention PSCommand_New-MgGroupThreadPostInReplyToMention PSFunction_New-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSCommand_New-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSFunction_New-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSCommand_New-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSFunction_New-MgGroupThreadPostMention PSCommand_New-MgGroupThreadPostMention PSFunction_New-MgGroupThreadPostMultiValueExtendedProperty PSCommand_New-MgGroupThreadPostMultiValueExtendedProperty PSFunction_New-MgGroupThreadPostSingleValueExtendedProperty PSCommand_New-MgGroupThreadPostSingleValueExtendedProperty PSFunction_Publish-MgGroupDriveListContentType PSCommand_Publish-MgGroupDriveListContentType PSFunction_Publish-MgGroupSiteContentType PSCommand_Publish-MgGroupSiteContentType PSFunction_Publish-MgGroupSiteListContentType PSCommand_Publish-MgGroupSiteListContentType PSFunction_Publish-MgGroupSitePage PSCommand_Publish-MgGroupSitePage PSFunction_Remove-MgGroup PSCommand_Remove-MgGroup PSFunction_Remove-MgGroupAcceptedSenderByRef PSCommand_Remove-MgGroupAcceptedSenderByRef PSFunction_Remove-MgGroupConversation PSCommand_Remove-MgGroupConversation PSFunction_Remove-MgGroupConversationThread PSCommand_Remove-MgGroupConversationThread PSFunction_Remove-MgGroupConversationThreadPostAttachment PSCommand_Remove-MgGroupConversationThreadPostAttachment PSFunction_Remove-MgGroupConversationThreadPostExtension PSCommand_Remove-MgGroupConversationThreadPostExtension PSFunction_Remove-MgGroupConversationThreadPostInReplyToAttachment PSCommand_Remove-MgGroupConversationThreadPostInReplyToAttachment PSFunction_Remove-MgGroupConversationThreadPostInReplyToExtension PSCommand_Remove-MgGroupConversationThreadPostInReplyToExtension PSFunction_Remove-MgGroupConversationThreadPostInReplyToMention PSCommand_Remove-MgGroupConversationThreadPostInReplyToMention PSFunction_Remove-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSCommand_Remove-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSFunction_Remove-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSCommand_Remove-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSFunction_Remove-MgGroupConversationThreadPostMention PSCommand_Remove-MgGroupConversationThreadPostMention PSFunction_Remove-MgGroupConversationThreadPostMultiValueExtendedProperty PSCommand_Remove-MgGroupConversationThreadPostMultiValueExtendedProperty PSFunction_Remove-MgGroupConversationThreadPostSingleValueExtendedProperty PSCommand_Remove-MgGroupConversationThreadPostSingleValueExtendedProperty PSFunction_Remove-MgGroupEndpoint PSCommand_Remove-MgGroupEndpoint PSFunction_Remove-MgGroupExtension PSCommand_Remove-MgGroupExtension PSFunction_Remove-MgGroupFavorite PSCommand_Remove-MgGroupFavorite PSFunction_Remove-MgGroupFromLifecyclePolicy PSCommand_Remove-MgGroupFromLifecyclePolicy PSFunction_Remove-MgGroupLifecyclePolicy PSCommand_Remove-MgGroupLifecyclePolicy PSFunction_Remove-MgGroupMemberByRef PSCommand_Remove-MgGroupMemberByRef PSFunction_Remove-MgGroupOwnerByRef PSCommand_Remove-MgGroupOwnerByRef PSFunction_Remove-MgGroupPermissionGrant PSCommand_Remove-MgGroupPermissionGrant PSFunction_Remove-MgGroupRejectedSenderByRef PSCommand_Remove-MgGroupRejectedSenderByRef PSFunction_Remove-MgGroupSetting PSCommand_Remove-MgGroupSetting PSFunction_Remove-MgGroupSite PSCommand_Remove-MgGroupSite PSFunction_Remove-MgGroupThread PSCommand_Remove-MgGroupThread PSFunction_Remove-MgGroupThreadPostAttachment PSCommand_Remove-MgGroupThreadPostAttachment PSFunction_Remove-MgGroupThreadPostExtension PSCommand_Remove-MgGroupThreadPostExtension PSFunction_Remove-MgGroupThreadPostInReplyToAttachment PSCommand_Remove-MgGroupThreadPostInReplyToAttachment PSFunction_Remove-MgGroupThreadPostInReplyToExtension PSCommand_Remove-MgGroupThreadPostInReplyToExtension PSFunction_Remove-MgGroupThreadPostInReplyToMention PSCommand_Remove-MgGroupThreadPostInReplyToMention PSFunction_Remove-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSCommand_Remove-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSFunction_Remove-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSCommand_Remove-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSFunction_Remove-MgGroupThreadPostMention PSCommand_Remove-MgGroupThreadPostMention PSFunction_Remove-MgGroupThreadPostMultiValueExtendedProperty PSCommand_Remove-MgGroupThreadPostMultiValueExtendedProperty PSFunction_Remove-MgGroupThreadPostSingleValueExtendedProperty PSCommand_Remove-MgGroupThreadPostSingleValueExtendedProperty PSFunction_Reset-MgGroupUnseenCount PSCommand_Reset-MgGroupUnseenCount PSFunction_Restore-MgGroupDriveItem PSCommand_Restore-MgGroupDriveItem PSFunction_Restore-MgGroupDriveItemListItemDocumentSetVersion PSCommand_Restore-MgGroupDriveItemListItemDocumentSetVersion PSFunction_Restore-MgGroupDriveItemListItemVersion PSCommand_Restore-MgGroupDriveItemListItemVersion PSFunction_Restore-MgGroupDriveItemVersion PSCommand_Restore-MgGroupDriveItemVersion PSFunction_Restore-MgGroupDriveListItemDocumentSetVersion PSCommand_Restore-MgGroupDriveListItemDocumentSetVersion PSFunction_Restore-MgGroupDriveListItemVersion PSCommand_Restore-MgGroupDriveListItemVersion PSFunction_Restore-MgGroupDriveRoot PSCommand_Restore-MgGroupDriveRoot PSFunction_Restore-MgGroupDriveRootListItemDocumentSetVersion PSCommand_Restore-MgGroupDriveRootListItemDocumentSetVersion PSFunction_Restore-MgGroupDriveRootListItemVersion PSCommand_Restore-MgGroupDriveRootListItemVersion PSFunction_Restore-MgGroupDriveRootVersion PSCommand_Restore-MgGroupDriveRootVersion PSFunction_Restore-MgGroupSiteListItemDocumentSetVersion PSCommand_Restore-MgGroupSiteListItemDocumentSetVersion PSFunction_Restore-MgGroupSiteListItemVersion PSCommand_Restore-MgGroupSiteListItemVersion PSFunction_Revoke-MgGroupDriveItemPermissionGrant PSCommand_Revoke-MgGroupDriveItemPermissionGrant PSFunction_Revoke-MgGroupDriveRootPermissionGrant PSCommand_Revoke-MgGroupDriveRootPermissionGrant PSFunction_Revoke-MgGroupSitePermissionGrant PSCommand_Revoke-MgGroupSitePermissionGrant PSFunction_Search-MgGroupDrive PSCommand_Search-MgGroupDrive PSFunction_Search-MgGroupDriveItem PSCommand_Search-MgGroupDriveItem PSFunction_Search-MgGroupDriveRoot PSCommand_Search-MgGroupDriveRoot PSFunction_Set-MgGroupDriveItemSensitivityLabel PSCommand_Set-MgGroupDriveItemSensitivityLabel PSFunction_Set-MgGroupDriveRootSensitivityLabel PSCommand_Set-MgGroupDriveRootSensitivityLabel PSFunction_Set-MgGroupLicense PSCommand_Set-MgGroupLicense PSFunction_Set-MgGroupPhotoContent PSCommand_Set-MgGroupPhotoContent PSFunction_Stop-MgGroupCalendarEvent PSCommand_Stop-MgGroupCalendarEvent PSFunction_Stop-MgGroupEvent PSCommand_Stop-MgGroupEvent PSFunction_Test-MgGroupDriveItemPermission PSCommand_Test-MgGroupDriveItemPermission PSFunction_Test-MgGroupDriveListContentTypePublished PSCommand_Test-MgGroupDriveListContentTypePublished PSFunction_Test-MgGroupDriveRootPermission PSCommand_Test-MgGroupDriveRootPermission PSFunction_Test-MgGroupDynamicMembership PSCommand_Test-MgGroupDynamicMembership PSFunction_Test-MgGroupDynamicMembershipRule PSCommand_Test-MgGroupDynamicMembershipRule PSFunction_Test-MgGroupPermissionGrantProperty PSCommand_Test-MgGroupPermissionGrantProperty PSFunction_Test-MgGroupProperty PSCommand_Test-MgGroupProperty PSFunction_Test-MgGroupSiteContentTypePublished PSCommand_Test-MgGroupSiteContentTypePublished PSFunction_Test-MgGroupSiteInformationProtectionDataLossPreventionPolicy PSCommand_Test-MgGroupSiteInformationProtectionDataLossPreventionPolicy PSFunction_Test-MgGroupSiteInformationProtectionPolicyLabelApplication PSCommand_Test-MgGroupSiteInformationProtectionPolicyLabelApplication PSFunction_Test-MgGroupSiteInformationProtectionPolicyLabelClassificationResult PSCommand_Test-MgGroupSiteInformationProtectionPolicyLabelClassificationResult PSFunction_Test-MgGroupSiteInformationProtectionPolicyLabelRemoval PSCommand_Test-MgGroupSiteInformationProtectionPolicyLabelRemoval PSFunction_Test-MgGroupSiteInformationProtectionSensitivityLabel PSCommand_Test-MgGroupSiteInformationProtectionSensitivityLabel PSFunction_Test-MgGroupSiteInformationProtectionSensitivityLabelSublabel PSCommand_Test-MgGroupSiteInformationProtectionSensitivityLabelSublabel PSFunction_Test-MgGroupSiteListContentTypePublished PSCommand_Test-MgGroupSiteListContentTypePublished PSFunction_Unpublish-MgGroupDriveListContentType PSCommand_Unpublish-MgGroupDriveListContentType PSFunction_Unpublish-MgGroupSiteContentType PSCommand_Unpublish-MgGroupSiteContentType PSFunction_Unpublish-MgGroupSiteListContentType PSCommand_Unpublish-MgGroupSiteListContentType PSFunction_Update-MgGroup PSCommand_Update-MgGroup PSFunction_Update-MgGroupConversationThread PSCommand_Update-MgGroupConversationThread PSFunction_Update-MgGroupConversationThreadPost PSCommand_Update-MgGroupConversationThreadPost PSFunction_Update-MgGroupConversationThreadPostExtension PSCommand_Update-MgGroupConversationThreadPostExtension PSFunction_Update-MgGroupConversationThreadPostInReplyToExtension PSCommand_Update-MgGroupConversationThreadPostInReplyToExtension PSFunction_Update-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSCommand_Update-MgGroupConversationThreadPostInReplyToMultiValueExtendedProperty PSFunction_Update-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSCommand_Update-MgGroupConversationThreadPostInReplyToSingleValueExtendedProperty PSFunction_Update-MgGroupConversationThreadPostMultiValueExtendedProperty PSCommand_Update-MgGroupConversationThreadPostMultiValueExtendedProperty PSFunction_Update-MgGroupConversationThreadPostSingleValueExtendedProperty PSCommand_Update-MgGroupConversationThreadPostSingleValueExtendedProperty PSFunction_Update-MgGroupEndpoint PSCommand_Update-MgGroupEndpoint PSFunction_Update-MgGroupExtension PSCommand_Update-MgGroupExtension PSFunction_Update-MgGroupLifecyclePolicy PSCommand_Update-MgGroupLifecyclePolicy PSFunction_Update-MgGroupOnenotePageContent PSCommand_Update-MgGroupOnenotePageContent PSFunction_Update-MgGroupPermissionGrant PSCommand_Update-MgGroupPermissionGrant PSFunction_Update-MgGroupPhoto PSCommand_Update-MgGroupPhoto PSFunction_Update-MgGroupSetting PSCommand_Update-MgGroupSetting PSFunction_Update-MgGroupThread PSCommand_Update-MgGroupThread PSFunction_Update-MgGroupThreadPost PSCommand_Update-MgGroupThreadPost PSFunction_Update-MgGroupThreadPostExtension PSCommand_Update-MgGroupThreadPostExtension PSFunction_Update-MgGroupThreadPostInReplyToExtension PSCommand_Update-MgGroupThreadPostInReplyToExtension PSFunction_Update-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSCommand_Update-MgGroupThreadPostInReplyToMultiValueExtendedProperty PSFunction_Update-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSCommand_Update-MgGroupThreadPostInReplyToSingleValueExtendedProperty PSFunction_Update-MgGroupThreadPostMultiValueExtendedProperty PSCommand_Update-MgGroupThreadPostMultiValueExtendedProperty PSFunction_Update-MgGroupThreadPostSingleValueExtendedProperty PSCommand_Update-MgGroupThreadPostSingleValueExtendedProperty PSIncludes_Function</S>
          <S N="developmentDependency">False</S>
          <S N="updated">2023-03-06T23:19:47Z</S>
          <S N="NormalizedVersion">1.23.0</S>
          <S N="Authors">Microsoft Corporation</S>
          <S N="IsPrerelease">false</S>
          <S N="ItemType">Module</S>
          <S N="FileList">Microsoft.Graph.Groups.nuspec|Microsoft.Graph.Groups.format.ps1xml|Microsoft.Graph.Groups.psd1|Microsoft.Graph.Groups.psm1|bin\Microsoft.Graph.Groups.private.dll|bin\Microsoft.Graph.Groups.private.deps.json|internal\Microsoft.Graph.Groups.internal.psm1|custom\Microsoft.Graph.Groups.custom.psm1|exports\v1.0-beta\ProxyCmdletDefinitions.ps1|exports\v1.0\ProxyCmdletDefinitions.ps1|.signature.p7s</S>
          <S N="GUID">50bc9e18-e281-4208-8913-c9e1bef6083d</S>
          <S N="PowerShellVersion">5.1</S>
          <S N="DotNetFrameworkVersion">4.7.2</S>
          <S N="CompanyName">Microsoft Corporation</S>
        </MS>
      </Obj>
      <S N="InstalledLocation">C:\Users\<USER>\OneDrive - Immense Networks\Documents\PowerShell\Modules\Microsoft.Graph.Groups\1.23.0</S>
    </MS>
  </Obj>
</Objs>
