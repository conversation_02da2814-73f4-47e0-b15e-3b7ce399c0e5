<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADApplicationProxyApplication</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADApplicationProxyApplication</command:noun>
      <maml:description>
        <maml:para>The New-AzureADApplicationProxyApplication cmdlet creates a new application configured for Application Proxy in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADApplicationProxyApplication cmdlet creates a new application configured for Application Proxy in Azure Active Directory. To ensure this application is usable, please also make sure you assign users and configure SSO if needed. Note that without specifying a ConnectorGroupId, this application by default will use the �Default� connector group in your tenant.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADApplicationProxyApplication</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ApplicationServerTimeout</maml:name>
          <maml:Description>
            <maml:para>Set this value to Long only if your application is slow to authenticate and connect.</maml:para>
          </maml:Description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Default</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">Long</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ApplicationServerTimeoutEnum</command:parameterValue>
          <dev:type>
            <maml:name>ApplicationServerTimeoutEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ConnectorGroupId</maml:name>
          <maml:Description>
            <maml:para>Provide the Id of the Connector group you would like assigned to this application. You can find this value by using the Get-AzureADApplicationProxyConnectorGroup command. Connectors process the remote access to your application, and connector groups help you organize connectors and apps by region, network, or purpose. If you don't have any connector groups created yet, your app is assigned to Default.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>DisplayName</maml:name>
          <maml:Description>
            <maml:para>The displayname of the new Application</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ExternalAuthenticationType</maml:name>
          <maml:Description>
            <maml:para>How Application Proxy verifies users before giving them access to your application.  AadPreAuthentication: Application Proxy redirects users to sign in with Azure AD, which authenticates their permissions for the directory and application. We recommend keeping this option as the default, so that you can take advantage of Azure AD security features like conditional access and Multi-Factor Authentication. Passthru: Users don't have to authenticate against Azure Active Directory to access the application. You can still set up authentication requirements on the backend.</maml:para>
          </maml:Description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">AadPreAuthentication</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">Passthru</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ExternalAuthenticationTypeEnum</command:parameterValue>
          <dev:type>
            <maml:name>ExternalAuthenticationTypeEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ExternalUrl</maml:name>
          <maml:Description>
            <maml:para>The address your users will go to in order to access the app from outside your network.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>InternalUrl</maml:name>
          <maml:Description>
            <maml:para>The URL that you use to access the application from inside your private network. You can provide a specific path on the backend server to publish, while the rest of the server is unpublished. In this way, you can publish different sites on the same server as different apps, and give each one its own name and access rules. If you publish a path, make sure that it includes all the necessary images, scripts, and style sheets for your application.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>IsTranslateHostHeaderEnabled</maml:name>
          <maml:Description>
            <maml:para>If set to true, translates urls in headers. Keep this value true unless your application required the original host header in the authentication request.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>IsTranslateLinksInBodyEnabled</maml:name>
          <maml:Description>
            <maml:para>If set to true, translates urls in body. Keep this value as No unless you have hardcoded HTML links to other on-premises applications, and don't use custom domains.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ApplicationServerTimeout</maml:name>
        <maml:Description>
          <maml:para>Set this value to Long only if your application is slow to authenticate and connect.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ApplicationServerTimeoutEnum</command:parameterValue>
        <dev:type>
          <maml:name>ApplicationServerTimeoutEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ConnectorGroupId</maml:name>
        <maml:Description>
          <maml:para>Provide the Id of the Connector group you would like assigned to this application. You can find this value by using the Get-AzureADApplicationProxyConnectorGroup command. Connectors process the remote access to your application, and connector groups help you organize connectors and apps by region, network, or purpose. If you don't have any connector groups created yet, your app is assigned to Default.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>DisplayName</maml:name>
        <maml:Description>
          <maml:para>The displayname of the new Application</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ExternalAuthenticationType</maml:name>
        <maml:Description>
          <maml:para>How Application Proxy verifies users before giving them access to your application.  AadPreAuthentication: Application Proxy redirects users to sign in with Azure AD, which authenticates their permissions for the directory and application. We recommend keeping this option as the default, so that you can take advantage of Azure AD security features like conditional access and Multi-Factor Authentication. Passthru: Users don't have to authenticate against Azure Active Directory to access the application. You can still set up authentication requirements on the backend.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ExternalAuthenticationTypeEnum</command:parameterValue>
        <dev:type>
          <maml:name>ExternalAuthenticationTypeEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ExternalUrl</maml:name>
        <maml:Description>
          <maml:para>The address your users will go to in order to access the app from outside your network.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>InternalUrl</maml:name>
        <maml:Description>
          <maml:para>The URL that you use to access the application from inside your private network. You can provide a specific path on the backend server to publish, while the rest of the server is unpublished. In this way, you can publish different sites on the same server as different apps, and give each one its own name and access rules. If you publish a path, make sure that it includes all the necessary images, scripts, and style sheets for your application.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>IsTranslateHostHeaderEnabled</maml:name>
        <maml:Description>
          <maml:para>If set to true, translates urls in headers. Keep this value true unless your application required the original host header in the authentication request.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>IsTranslateLinksInBodyEnabled</maml:name>
        <maml:Description>
          <maml:para>If set to true, translates urls in body. Keep this value as No unless you have hardcoded HTML links to other on-premises applications, and don't use custom domains.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[Microsoft.Open.MSGraph.Model.ApplicationProxyApplicationObject+ExternalAuthenticationTypeEnum, Microsoft.Open.MS.GraphV10.Client, Version=*******, Culture=neutral, PublicKeyToken=null]] System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]] System.Nullable`1[[Microsoft.Open.MSGraph.Model.ApplicationProxyApplicationObject+ApplicationServerTimeoutEnum, Microsoft.Open.MS.GraphV10.Client, Version=*******, Culture=neutral, PublicKeyToken=null]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADApplicationProxyApplication -DisplayName "Finance Tracker" -ExternalUrl "https://finance-awcycles.msappproxy.net/" -InternalUrl "http://finance/" 


ExternalAuthenticationType               : AadPreAuthentication
ApplicationServerTimeout                 : Default
ExternalUrl                              : https://finance-awcycles.msappproxy.net/
InternalUrl                              : http://finance/
IsTranslateHostHeaderEnabled             : True
IsTranslateLinksInBodyEnabled            : False
IsOnPremPublishingEnabled                : True
VerifiedCustomDomainCertificatesMetadata : 
VerifiedCustomDomainKeyCredential        : 
VerifiedCustomDomainPasswordCredential   : 
SingleSignOnSettings                     :</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Creating a new application with only the basic required settings, and the default domain for applications.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADApplicationProxyApplication -DisplayName "HR Resources" -ExternalUrl "https://hr.adventure-works.com/" -InternalUrl "http://hr.adventure-works.com/" -ApplicationServerTimeout Long 


ExternalAuthenticationType               : AadPreAuthentication
ApplicationServerTimeout                 : Long
ExternalUrl                              : https://hr.adventure-works.com/
InternalUrl                              : http://hr.adventure-works.com/
IsTranslateHostHeaderEnabled             : True
IsTranslateLinksInBodyEnabled            : False
IsOnPremPublishingEnabled                : True
VerifiedCustomDomainCertificatesMetadata : class OnPremisesPublishingVerifiedCustomDomainCertificatesMetadataObject {
                                             Thumbprint: [XXXXX]
                                             SubjectName: [XXXXX]
                                             Issuer: 
                                             IssueDate: 11/9/2017 5:54:29
                                             ExpiryDate: 11/9/2019 5:54:29
                                           }
                                           
VerifiedCustomDomainKeyCredential        : 
VerifiedCustomDomainPasswordCredential   : 
SingleSignOnSettings                     :</dev:code>
        <dev:remarks>
          <maml:para>Example 2: Creating a new application that uses a custom domain and sets several optional flags.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADApplicationProxyApplication</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADApplicationProxyApplication</command:noun>
      <maml:description>
        <maml:para>Deletes an Application Proxy application.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADApplicationProxyApplication cmdlet removes Application Proxy configurations from a specific application in Azure Active Directory, and can delete the application completely if specified.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADApplicationProxyApplication</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>The unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>RemoveADApplication</maml:name>
          <maml:Description>
            <maml:para>This allows you to delete application completely. When this is false (default), Application Proxy properties are removed from the application but the application still exists. If this is true, the application is completely removed from Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>The unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>RemoveADApplication</maml:name>
        <maml:Description>
          <maml:para>This allows you to delete application completely. When this is false (default), Application Proxy properties are removed from the application but the application still exists. If this is true, the application is completely removed from Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADApplicationProxyApplication -ObjectId 257098d1-f8dd-4efb-88a2-1c92d3654f10</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Remove a Proxy Application</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADApplicationProxyApplication -ObjectId 0d7b0f02-3f63-414d-8d20-4b8bd0291e42 -RemoveADApplication $true</dev:code>
        <dev:remarks>
          <maml:para>Example 2: Remove a Proxy Application, and remove it from Azure AD completely</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyApplication</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyApplication</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyApplication allows you to modify and set configurations for an application in Azure Active Directory configured to use ApplicationProxy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyApplication allows you to modify and set additional settings for an application in Azure Active Directory configured to use ApplicationProxy.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyApplication</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies a unique application ID of an application in Azure Active Directory. This can be found using the Get-AzureADApplication command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ExternalUrl</maml:name>
          <maml:Description>
            <maml:para>The address your users will go to in order to access the app from outside your network.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>InternalUrl</maml:name>
          <maml:Description>
            <maml:para>The URL that you use to access the application from inside your private network. You can provide a specific path on the backend server to publish, while the rest of the server is unpublished. In this way, you can publish different sites on the same server as different apps, and give each one its own name and access rules. If you publish a path, make sure that it includes all the necessary images, scripts, and style sheets for your application.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ExternalAuthenticationType</maml:name>
          <maml:Description>
            <maml:para>How Application Proxy verifies users before giving them access to your application.  AadPreAuth: Application Proxy redirects users to sign in with Azure AD, which authenticates their permissions for the directory and application. We recommend keeping this option as the default, so that you can take advantage of Azure AD security features like conditional access and Multi-Factor Authentication. Passthru: Users don't have to authenticate against Azure Active Directory to access the application. You can still set up authentication requirements on the backend.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ExternalAuthenticationTypeEnum</command:parameterValue>
          <dev:type>
            <maml:name>ExternalAuthenticationTypeEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>IsTranslateHostHeaderEnabled</maml:name>
          <maml:Description>
            <maml:para>If set to true, translates urls in headers. Keep this value true unless your application required the original host header in the authentication request.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>IsTranslateLinksInBodyEnabled</maml:name>
          <maml:Description>
            <maml:para>If set to true, translates urls in body. Keep this value as No unless you have hardcoded HTML links to other on-premises applications, and don't use custom domains.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ApplicationServerTimeout</maml:name>
          <maml:Description>
            <maml:para>Specifies the backend server timeout type. Set this value to Long only if your application is slow to authenticate and connect.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ApplicationServerTimeoutEnum</command:parameterValue>
          <dev:type>
            <maml:name>ApplicationServerTimeoutEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ConnectorGroupId</maml:name>
          <maml:Description>
            <maml:para>Provide the Id of the Connector group you would like assigned to this application. You can find this value by using the Get-AzureADApplicationProxyConnectorGroup command. Connectors process the remote access to your application, and connector groups help you organize connectors and apps by region, network, or purpose. If you don't have any connector groups created yet, your app is assigned to Default.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies a unique application ID of an application in Azure Active Directory. This can be found using the Get-AzureADApplication command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ExternalUrl</maml:name>
        <maml:Description>
          <maml:para>The address your users will go to in order to access the app from outside your network.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>InternalUrl</maml:name>
        <maml:Description>
          <maml:para>The URL that you use to access the application from inside your private network. You can provide a specific path on the backend server to publish, while the rest of the server is unpublished. In this way, you can publish different sites on the same server as different apps, and give each one its own name and access rules. If you publish a path, make sure that it includes all the necessary images, scripts, and style sheets for your application.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ExternalAuthenticationType</maml:name>
        <maml:Description>
          <maml:para>How Application Proxy verifies users before giving them access to your application.  AadPreAuth: Application Proxy redirects users to sign in with Azure AD, which authenticates their permissions for the directory and application. We recommend keeping this option as the default, so that you can take advantage of Azure AD security features like conditional access and Multi-Factor Authentication. Passthru: Users don't have to authenticate against Azure Active Directory to access the application. You can still set up authentication requirements on the backend.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ExternalAuthenticationTypeEnum</command:parameterValue>
        <dev:type>
          <maml:name>ExternalAuthenticationTypeEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>IsTranslateHostHeaderEnabled</maml:name>
        <maml:Description>
          <maml:para>If set to true, translates urls in headers. Keep this value true unless your application required the original host header in the authentication request.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>IsTranslateLinksInBodyEnabled</maml:name>
        <maml:Description>
          <maml:para>If set to true, translates urls in body. Keep this value as No unless you have hardcoded HTML links to other on-premises applications, and don't use custom domains.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ApplicationServerTimeout</maml:name>
        <maml:Description>
          <maml:para>Specifies the backend server timeout type. Set this value to Long only if your application is slow to authenticate and connect.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ApplicationServerTimeoutEnum</command:parameterValue>
        <dev:type>
          <maml:name>ApplicationServerTimeoutEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ConnectorGroupId</maml:name>
        <maml:Description>
          <maml:para>Provide the Id of the Connector group you would like assigned to this application. You can find this value by using the Get-AzureADApplicationProxyConnectorGroup command. Connectors process the remote access to your application, and connector groups help you organize connectors and apps by region, network, or purpose. If you don't have any connector groups created yet, your app is assigned to Default.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyApplication -ObjectId 257098d1-f8dd-4efb-88a2-1c92d3654f10 -IsTranslateLinksInBodyEnabled $true


ExternalAuthenticationType               : AadPreAuthentication
ApplicationServerTimeout                 : Default
ExternalUrl                              : https://finance-awcycles.msappproxy.net/
InternalUrl                              : http://finance/
IsTranslateHostHeaderEnabled             : True
IsTranslateLinksInBodyEnabled            : True
IsOnPremPublishingEnabled                : True
VerifiedCustomDomainCertificatesMetadata : 
VerifiedCustomDomainKeyCredential        : 
VerifiedCustomDomainPasswordCredential   : 
SingleSignOnSettings                     :</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Add the link translation feature to an application</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyApplicationConnectorGroup</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyApplicationConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyApplicationConnectorGroup cmdlet assigns the given connector group to a specified application.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyApplicationConnectorGroup cmdlet sets the connector group assigned for the specified application. The application must be configured for Application Proxy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyApplicationConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ConnectorGroupId</maml:name>
          <maml:Description>
            <maml:para>The Id of the Connector group that should be assigned to the application. You can find this by using the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>The unique application Id for the application the Connector group will be assigned to. This can be found using the Get-AzureADApplication command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ConnectorGroupId</maml:name>
        <maml:Description>
          <maml:para>The Id of the Connector group that should be assigned to the application. You can find this by using the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>The unique application Id for the application the Connector group will be assigned to. This can be found using the Get-AzureADApplication command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyApplicationConnectorGroup -ObjectId 59462d3c-a1bc-40a0-9bed-be799357ebce -ConnectorGroupId a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Set a new Connector Group for a specific application</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyApplicationCustomDomainCertificate</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyApplicationCustomDomainCertificate</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyApplicationCustomDomainCertificate cmdlet assigns a certificate to an application configured for Application Proxy in Azure Active Directory (AD). This will upload the certificate and allow the application to use Custom Domains.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyApplicationCustomDomainCertificate cmdlet assigns a certificate to an application configured for Application Proxy in Azure Active Directory (AD). This will upload the certificate and allow the application to use Custom Domains. If you have one certificate that includes many of your applications, you only need to upload it with one application and it will also be assigned to the other relevant applications.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyApplicationCustomDomainCertificate</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>The unique application Id for the application the certificate should be uploaded to. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Password</maml:name>
          <maml:Description>
            <maml:para>A secure string containing the password for the pfx certificate</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
          <dev:type>
            <maml:name>SecureString</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>PfxFilePath</maml:name>
          <maml:Description>
            <maml:para>The file path for the pfx certificate for the custom domain</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>The unique application Id for the application the certificate should be uploaded to. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Password</maml:name>
        <maml:Description>
          <maml:para>A secure string containing the password for the pfx certificate</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
        <dev:type>
          <maml:name>SecureString</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>PfxFilePath</maml:name>
        <maml:Description>
          <maml:para>The file path for the pfx certificate for the custom domain</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Security.SecureString</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; $securePassword = Read-Host -AsSecureString
PS C:\&gt; Set-AzureADApplicationProxyApplicationCustomDomainCertificate -ObjectId 4eba5342-8d17-4eac-a1f6-62a0de26311e -PfxFilePath "C:\Temp\Certificates\cert.pfx" -Password $securePassword</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Assign a certificate to an application configured for Application Proxy</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyApplicationSingleSignOn</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyApplicationSingleSignOn</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyApplicationSingleSignOn cmdlet allows you to set and modify single sign-on (SSO) settings for an application configured for Application Proxy in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyApplicationSingleSignOn cmdlet allows you to set and modify single sign-on (SSO) settings for an application configured for Application Proxy in Azure Active Directory. This is limited to setting No SSO, Kerberos Constrained Delegation (for applications using Integrated Windows Authentication), and Header-based SSO.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyApplicationSingleSignOn</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>KerberosDelegatedLoginIdentity</maml:name>
          <maml:Description>
            <maml:para>The identity that the Connector can use on behalf of your users to authenticate.</maml:para>
          </maml:Description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">UserPrincipalName</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">OnPremisesUserPrincipalName</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">UserPrincipalUsername</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">OnPremisesUserPrincipalUsername</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">OnPremisesSAMAccountName</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">KerberosSignOnMappingAttributeTypeEnum</command:parameterValue>
          <dev:type>
            <maml:name>KerberosSignOnMappingAttributeTypeEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>KerberosInternalApplicationServicePrincipalName</maml:name>
          <maml:Description>
            <maml:para>The internal application SPN of the application server. This SPN needs to be in the list of services to which the Connector can present delegated credentials.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>The unique application Id of the application that needs different SSO settings. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>SingleSignOnMode</maml:name>
          <maml:Description>
            <maml:para>Choose the type of SSO you would like the application to use. Please note that only three SSO settings are supported in powershell, for more options, please use the Azure Portal.</maml:para>
          </maml:Description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">None</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">OnPremisesKerberos</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">HeaderBased</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">SingleSignOnModeEnum</command:parameterValue>
          <dev:type>
            <maml:name>SingleSignOnModeEnum</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>KerberosDelegatedLoginIdentity</maml:name>
        <maml:Description>
          <maml:para>The identity that the Connector can use on behalf of your users to authenticate.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">KerberosSignOnMappingAttributeTypeEnum</command:parameterValue>
        <dev:type>
          <maml:name>KerberosSignOnMappingAttributeTypeEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>KerberosInternalApplicationServicePrincipalName</maml:name>
        <maml:Description>
          <maml:para>The internal application SPN of the application server. This SPN needs to be in the list of services to which the Connector can present delegated credentials.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>The unique application Id of the application that needs different SSO settings. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>SingleSignOnMode</maml:name>
        <maml:Description>
          <maml:para>Choose the type of SSO you would like the application to use. Please note that only three SSO settings are supported in powershell, for more options, please use the Azure Portal.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SingleSignOnModeEnum</command:parameterValue>
        <dev:type>
          <maml:name>SingleSignOnModeEnum</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[Microsoft.Open.MSGraph.Model.OnPremisesPublishingSingleSignOnObject+SingleSignOnModeEnum, Microsoft.Open.MS.GraphV10.Client, Version=*******, Culture=neutral, PublicKeyToken=null]] System.Nullable`1[[Microsoft.Open.MSGraph.Model.OnPremisesPublishingKerberosSignOnSettingsObject+KerberosSignOnMappingAttributeTypeEnum, Microsoft.Open.MS.GraphV10.Client, Version=*******, Culture=neutral, PublicKeyToken=null]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyApplicationSingleSignOn -ObjectId 4eba5342-8d17-4eac-a1f6-62a0de26311e -SingleSignOnMode OnPremisesKerberos -KerberosInternalApplicationServicePrincipalName "http/www.adventure-works.com" -KerberosDelegatedLoginIdentity OnPremisesUserPrincipalName</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Assign an application to use Kerberos Constrained Delegation, and specify required parameters.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyApplicationSingleSignOn -ObjectId 4eba5342-8d17-4eac-a1f6-62a0de26311e -SingleSignOnMode None</dev:code>
        <dev:remarks>
          <maml:para>Example 2: Remove SSO from an application</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyConnector</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyConnector</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyConnector cmdlet allows reassignment of the connector to another connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyConnector cmdlet allows reassignment of the connector to another connector group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyConnector</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the Connector being moved. You can find this value using the Get-AzureADApplicationProxyConnector command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ConnectorGroupId</maml:name>
          <maml:Description>
            <maml:para>The unique idenfier of the target application proxy connector group in Azure Active Directory. You can find this value using the Get-AzureAdApplicationProxyConnectorGroup command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the Connector being moved. You can find this value using the Get-AzureADApplicationProxyConnector command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ConnectorGroupId</maml:name>
        <maml:Description>
          <maml:para>The unique idenfier of the target application proxy connector group in Azure Active Directory. You can find this value using the Get-AzureAdApplicationProxyConnectorGroup command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyConnector -Id 834c5dd6-f2e8-47ae-973a-9fc769289b3d -ConnectorGroupId a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Move a Connector to a different Connector Group</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
</helpItems>