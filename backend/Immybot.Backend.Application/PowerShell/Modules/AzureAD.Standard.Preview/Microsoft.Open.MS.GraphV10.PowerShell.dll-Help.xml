<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Add-AzureADApplicationPolicy</command:name>
      <command:verb>Add</command:verb>
      <command:noun>AzureADApplicationPolicy</command:noun>
      <maml:description>
        <maml:para>The Add-AzureADApplicationPolicy cmdlet is not available at this time .</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Add-AzureADApplicationPolicy cmdlet adds an Azure Active Directory application policy.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Add-AzureADApplicationPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are: * Continue</maml:para>
            <maml:para>* Ignore</maml:para>
            <maml:para>* Inquire</maml:para>
            <maml:para>* SilentlyContinue</maml:para>
            <maml:para>* Stop</maml:para>
            <maml:para>* Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies a variable in which to store an information event message.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>RefObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the policy.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The ID of the application for which you need to set the policy</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are: * Continue</maml:para>
          <maml:para>* Ignore</maml:para>
          <maml:para>* Inquire</maml:para>
          <maml:para>* SilentlyContinue</maml:para>
          <maml:para>* Stop</maml:para>
          <maml:para>* Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies a variable in which to store an information event message.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>RefObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the policy.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The ID of the application for which you need to set the policy</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------- Example 1: Add an application policy -------------</maml:title>
        <dev:code>PS C:\&gt;Add-AzureADApplicationPolicy -ObjectId &lt;object id of application&gt; -RefObjectId &lt;object id of policy&gt;</dev:code>
        <dev:remarks>
          <maml:para>This command adds an application policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Add-AzureADMSLifecyclePolicyGroup</command:name>
      <command:verb>Add</command:verb>
      <command:noun>AzureADMSLifecyclePolicyGroup</command:noun>
      <maml:description>
        <maml:para>Adds a group to a lifecycle policy</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Add-AzureADMSLifecyclePolicyGroup cmdlet adds a group to a lifecycle policy in Azure Active Directory</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Add-AzureADMSLifecyclePolicyGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the lifecycle policy object in Azure Active Directory.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the lifecycle policy object in Azure Active Directory.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt;Add-AzureADMSLifecyclePolicyGroup -Id "b4c908b0-3595-4add-91b4-c5400b31b57b" -groupId "cffd97bd-6b91-4c4e-b553-6918a320211c"</dev:code>
        <dev:remarks>
          <maml:para>This command adds a group to the lifecycle policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Add-AzureADServicePrincipalPolicy</command:name>
      <command:verb>Add</command:verb>
      <command:noun>AzureADServicePrincipalPolicy</command:noun>
      <maml:description>
        <maml:para>Adds a service principal policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Add-AzureADServicePrincipalPolicy cmdlet adds a service principal policy.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Add-AzureADServicePrincipalPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies a variable in which to store an information event message.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>RefObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the object Id of the policy.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The ID of the Service Principal for which you need to set the policy</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies a variable in which to store an information event message.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>RefObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the object Id of the policy.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The ID of the Service Principal for which you need to set the policy</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------- Example 1: Add a service principal policy ----------</maml:title>
        <dev:code>PS C:\&gt;Add-AzureADServicePrincipalPolicy -Id &lt;object id of service principal&gt; -RefObjectId &lt;object id of policy&gt;</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationPolicy</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationPolicy</command:noun>
      <maml:description>
        <maml:para>Gets an application policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationPolicy cmdlet gets an Azure Active Directory application policy.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event.</maml:para>
            <maml:para>The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies a variable in which to store an information event message.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The ID of the application for which you need to retrieve the policy</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event.</maml:para>
          <maml:para>The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies a variable in which to store an information event message.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The ID of the application for which you need to retrieve the policy</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------- Example 1: Get an application policy -------------</maml:title>
        <dev:code>PS C:\&gt;Get-AzureADApplicationPolicy -Id "&lt;object id of application&gt;"</dev:code>
        <dev:remarks>
          <maml:para>This command gets the specified application policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Add-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyApplication</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyApplication</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyApplication cmdlet retrieves an application configured for Application Proxy in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyApplication cmdlet retrieves an application configured for Application Proxy in Azure Active Directory.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyApplication</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>This is the unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>This is the unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyApplication -ObjectId 8d6c6684-6f8c-42e2-8914-32ed2adf9ccf


ExternalAuthenticationType               : AadPreAuthentication
ApplicationServerTimeout                 : Default
ExternalUrl                              : https://travel.cycles.adventure-works.com/
InternalUrl                              : https://awcyclesapps.adventure-works.com:3000/
IsTranslateHostHeaderEnabled             : False
IsTranslateLinksInBodyEnabled            : False
IsOnPremPublishingEnabled                : True
VerifiedCustomDomainCertificatesMetadata : class OnPremisesPublishingVerifiedCustomDomainCertificatesMetadataObject {
                                             Thumbprint:  [XXXXX]
                                             SubjectName: [XXXXX]
                                             Issuer: 
                                             IssueDate: 11/9/2017 5:54:29
                                             ExpiryDate: 11/9/2019 5:54:29
                                           }
                                           
VerifiedCustomDomainKeyCredential        : 
VerifiedCustomDomainPasswordCredential   : 
SingleSignOnSettings                     :</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyApplicationConnectorGroup</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyApplicationConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyApplicationConnectorGroup cmdlet retrieves the connector group assigned for a specific application.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyApplicationConnectorGroup cmdlet retrieves the connector group assigned for the specified application. The application must be configured for Application Proxy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyApplicationConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>ObjectId is the Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>ObjectId is the Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyApplicationConnectorGroup -ObjectId 8d6c6684-6f8c-42e2-8914-32ed2adf9ccf

Id                                   Name                ConnectorGroupType IsDefault
--                                   ----                ------------------ ---------
a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84 Application Servers applicationProxy       False</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyConnector</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyConnector</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyApplicationConnector cmdlet a list of all connectors, or if specified, details of a specific connector.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyApplicationConnector cmdlet retrieves the details for a given connector. If no connectorId is specified, it retrieves all the connectors assigned to the tenant.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnector</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Filter</maml:name>
          <maml:Description>
            <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnector</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the specific connector. You can find this by running the command without this parameter to get the desired Id, or by going into the portal and viewing connector details.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnector</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>SearchString</maml:name>
          <maml:Description>
            <maml:para>Specifies a search string.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Filter</maml:name>
        <maml:Description>
          <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the specific connector. You can find this by running the command without this parameter to get the desired Id, or by going into the portal and viewing connector details.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>SearchString</maml:name>
        <maml:Description>
          <maml:para>Specifies a search string.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]] System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyConnector

Id                                   MachineName                      ExternalIp     Status
--                                   -----------                      ----------     ------
4c8b06e7-9751-41d5-8e5e-48e9b9bc2c66 AWCyclesApps.adventure-works.com ************** active
834c5dd6-f2e8-47ae-973a-9fc769289b3d AWCyclesAD.adventure-works.com   ************** active</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Retrieve all connectors</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyConnector -Id 4c8b06e7-9751-41d5-8e5e-48e9b9bc2c66

Id                                   MachineName                      ExternalIp     Status
--                                   -----------                      ----------     ------
4c8b06e7-9751-41d5-8e5e-48e9b9bc2c66 AWCyclesApps.adventure-works.com ************** active</dev:code>
        <dev:remarks>
          <maml:para>Example 2: Retrieve information for a specific connector</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyConnectorGroup</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyConnectorGroup cmdlet retrieves a list of all connector groups, or if specified, details of a specific connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyConnectorGroup cmdlet retrieves a list of all connector groups, or if specified, details of the specified connector group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Filter</maml:name>
          <maml:Description>
            <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the specific connector group. You can find this by running the command without this parameter to get the desired Id, or by going into the portal and viewing connector group details.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>SearchString</maml:name>
          <maml:Description>
            <maml:para>Specifies the search string.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Filter</maml:name>
        <maml:Description>
          <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the specific connector group. You can find this by running the command without this parameter to get the desired Id, or by going into the portal and viewing connector group details.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>SearchString</maml:name>
        <maml:Description>
          <maml:para>Specifies the search string.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]] System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyConnectorGroup

Id                                   Name                ConnectorGroupType IsDefault
--                                   ----                ------------------ ---------
1a0bc41a-8663-4da3-934c-214640663a33 Default             applicationProxy        True
68348ab6-4cc5-4c8c-a0f0-7a43db2f4ff6 Guest Applications  applicationProxy       False
a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84 Application Servers applicationProxy       False</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Retrieve all connector groups</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\Users\<USER>\Desktop\Microsoft.Open.AzureAD.Module.Preview&gt; Get-AzureADApplicationProxyConnectorGroup -Id a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84

Id                                   Name                ConnectorGroupType IsDefault
--                                   ----                ------------------ ---------
a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84 Application Servers applicationProxy       False</dev:code>
        <dev:remarks>
          <maml:para>Example 2: Retrieve a specific connector groups</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyConnectorGroupMembers</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyConnectorGroupMembers</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyConnectorGroupMembers gets all the Application Proxy connectors associated with the given connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyConnectorGroupMembers gets all the Application Proxy connectors associated with the given connector group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnectorGroupMembers</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Filter</maml:name>
          <maml:Description>
            <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the Connector group. This can be found by running the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all users. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Filter</maml:name>
        <maml:Description>
          <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned. Details on querying with oData can be found here: http://www.odata.org/documentation/odata-version-3-0/odata-version-3-0-core-protocol/#queryingcollections</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the Connector group. This can be found by running the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]] System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyConnectorGroupMembers -Id ba07e273-6b9e-4567-afe4-efddac32509d

Id                                   MachineName     ExternalIp   Status
--                                   -----------     ----------   ------
969eddd2-ad11-47ca-92ba-4442b9901edf vm-test-010 ************ active
ea4a4b91-aace-4e8b-b81a-b2f6429a477e test-vm-conn1 *********** active</dev:code>
        <dev:remarks>
          <maml:para>The output of this command, showing all the connectors in the group.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADApplicationProxyConnectorMemberOf</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADApplicationProxyConnectorMemberOf</command:noun>
      <maml:description>
        <maml:para>The Get-AzureADApplicationProxyConnectorMemberOf command gets the ConnectorGroup that the specified Connector is a member of.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADApplicationProxyConnectorMemberOf command gets the ConnectorGroup that the specified Connector is a member of. If no group has been assigned to the connector, by default it will be in 'Default'.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADApplicationProxyConnectorMemberOf</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the connector. You can find this by running Get-AzureADApplicationProxyConnector.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the connector. You can find this by running Get-AzureADApplicationProxyConnector.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADApplicationProxyConnectorMemberOf -Id 4c8b06e7-9751-41d5-8e5e-48e9b9bc2c66

Id                                   Name                ConnectorGroupType IsDefault
--                                   ----                ------------------ ---------
a39b9095-8dc8-4d3a-86c3-e7b5c3f0fb84 Application Servers applicationProxy       False</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADDirectorySetting</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADDirectorySetting</command:noun>
      <maml:description>
        <maml:para>Gets a directory setting.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADDirectorySetting cmdlet gets a directory setting from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADDirectorySetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all directory settings. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a directory in Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADDirectorySetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all directory settings. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all directory settings. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a directory in Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>New-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADDirectorySettingTemplate</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADDirectorySettingTemplate</command:noun>
      <maml:description>
        <maml:para>Gets a directory setting template.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADDirectorySettingTemplate cmdlet gets a directory setting template from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADDirectorySettingTemplate</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The ID of the settings template you want to retrieve</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The ID of the settings template you want to retrieve</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSDeletedDirectoryObject</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSDeletedDirectoryObject</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to retrieve a soft deleted directory object from the directory</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to retrieve a soft deleted directory object from the directory. Note that soft delete for groups is currently only implemented for Unified Groups (a.k.a. Office 365 Groups).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSDeletedDirectoryObject</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the directory object to retrieve</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the directory object to retrieve</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>Get-AzureADMSDeletedDirectoryObject -Id 85b5ff1e-0402-400c-9e3c-0f9e965325d1</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to retrieve the deleted directory object with id = 85b5ff1e-0402-400c-9e3c-0f9e965325d1 from the directory</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSDeletedGroup</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSDeletedGroup</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to retrieve the soft deleted groups in a directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to retrieve the soft deleted groups in a directory. When a group is deleted it is initially soft deleted and can be recovered during the first 30 days after deletion. After 30 days the group is permanently deleted and can no longer be recovered. Note that soft delete is currently only implemented for Unified Groups (a.k.a. Office 365 Groups).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSDeletedGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all group members. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Filter</maml:name>
          <maml:Description>
            <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSDeletedGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all group members. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the deleted group to be retrieved</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSDeletedGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all group members. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>SearchString</maml:name>
          <maml:Description>
            <maml:para>Specifies a search string.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all group members. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Filter</maml:name>
        <maml:Description>
          <maml:para>Specifies an oData v3.0 filter statement. This parameter controls which objects are returned.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the deleted group to be retrieved</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>SearchString</maml:name>
        <maml:Description>
          <maml:para>Specifies a search string.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]] System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>Get-AzureAdMSDeletedGroup</dev:code>
        <dev:remarks>
          <maml:para>This cmdlet will retrieve all recoverable deleted groups in the directory.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSGroup</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSGroup</command:noun>
      <maml:description>
        <maml:para>Gets information about groups in Azure AD.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADMSGroup cmdlet gets information about groups in Azure Active Directory (Azure AD). To get a group, specify the Id parameter.  Specify the SearchString or Filter parameter to find particular groups.  If you specify no parameters, this cmdlet gets all groups.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all groups. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Filter</maml:name>
          <maml:Description>
            <maml:para>Specifies an oData v3.0 filter string to match a set of groups.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records that this cmldet gets. The default value is 100.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all groups. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the group that this cmdlet gets.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all groups. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>SearchString</maml:name>
          <maml:Description>
            <maml:para>Specifies a search string.  This cmdlet gets groups that have DisplayName or Description attributes that match the search string.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all groups. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Filter</maml:name>
        <maml:Description>
          <maml:para>Specifies an oData v3.0 filter string to match a set of groups.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the group that this cmdlet gets.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>SearchString</maml:name>
        <maml:Description>
          <maml:para>Specifies a search string.  This cmdlet gets groups that have DisplayName or Description attributes that match the search string.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records that this cmldet gets. The default value is 100.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para>This cmdlet is currently in Public Preview. While a cmdlet is in Public Preview, we may make changes to the cmdlet which could have unexpected effects. We recommend that you do not use this cmdlet in a production environment.</maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------ Example 1: Get all groups ------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSGroup

Id                            : d539a25e-2db2-482a-9dcb-2a0b27fe4f27 
Description                   : 
OnPremisesSyncEnabled         : 
DisplayName                   : 
ADSyncOperators 
OnPremisesLastSyncDateTime    : 
Mail                          : 
MailEnabled                   : False
MailNickname                  : ADSyncOperators 
OnPremisesSecurityIdentifier  : S-1-5-21-2695029449-1154706203-1063139792-1243 
ProxyAddresses                : {} 
SecurityEnabled               : True 
GroupTypes                    : {} 
MembershipRule                : 
MembershipRuleProcessingState :


Id                            : d98ddc78-6e8d-4f0d-8a3f-b923c6ebc14b 
Description                   : 
OnPremisesSyncEnabled         : 
DisplayName                   : Project Icarus 
OnPremisesLastSyncDateTime    : 
Mail                          : 
MailEnabled                   : False 
MailNickname                  : 60f3d02c-0c6e-41da-bb64-128c73b4d9e6 
OnPremisesSecurityIdentifier  : 
ProxyAddresses                : {} 
SecurityEnabled               : True 
GroupTypes                    : {DynamicMembership} 
MembershipRule                : (user.jobtitle -eq "Sales manager") -or ((user.department -eq "Marketing") -and (user.country -eq "Greece")) 
MembershipRuleProcessingState : On</dev:code>
        <dev:remarks>
          <maml:para>This command gets all groups in Azure AD.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------- Example 2: Get a specific group by using an ID --------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSGroup -Id "d98ddc78-6e8d-4f0d-8a3f-b923c6ebc14b"

Id                            : d98ddc78-6e8d-4f0d-8a3f-b923c6ebc14b 
Description                   : 
OnPremisesSyncEnabled         : 
DisplayName                   : Project Icarus 
OnPremisesLastSyncDateTime    : 
Mail                          : 
MailEnabled                   : False 
MailNickname                  : 60f3d02c-0c6e-41da-bb64-128c73b4d9e6 
OnPremisesSecurityIdentifier  : 
ProxyAddresses                : {} 
SecurityEnabled               : True 
GroupTypes                    : {DynamicMembership} 
MembershipRule                : (user.jobtitle -eq "Sales manager") -or ((user.department -eq "Marketing") -and (user.country -eq "Greece")) 
MembershipRuleProcessingState : On</dev:code>
        <dev:remarks>
          <maml:para>This command gets information for the group that has the specified ID.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>New-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>#AzureAD: Certificate based authentication for iOS and Android now in preview!</maml:linkText>
        <maml:uri>https://blogs.technet.microsoft.com/enterprisemobility/2016/07/18/azuread-certificate-based-authentication-for-ios-and-android-now-in-preview/</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSGroupLifecyclePolicy</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSGroupLifecyclePolicy</command:noun>
      <maml:description>
        <maml:para>Retrieves the properties and relationships of a groupLifecyclePolicies object in Azure Active Directory. If you specify no parameters, this cmdlet gets all groupLifecyclePolicies.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADMSGroupLifecyclePolicy command retrieves the properties and relationships of a groupLifecyclePolicies object in Azure Active Directory. If you specify no parameters, this cmdlet gets all groupLifecyclePolicies.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSGroupLifecyclePolicy</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a groupLifecyclePolicies object in Azure Active Directory</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a groupLifecyclePolicies object in Azure Active Directory</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSGroupLifecyclePolicy</dev:code>
        <dev:remarks>
          <maml:para>This command retrieves the group expiration settings configured for the tenant</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSIdentityProvider</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSIdentityProvider</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to retrieve the configured identity providers in the directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to retrieve the identity providers that have been configured in the directory. These identity providers can be used to allow users to sign up for or sign into applications secured by Azure AD B2C.</maml:para>
      <maml:para>Configuring an identity provider in your Azure AD tenant also enables future B2B guest scenarios. For example, an organization has resources in Office 365 that needs to be shared with a Gmail user. The Gmail user will use their Google account credentials to authenticate and access the documents.</maml:para>
      <maml:para>The current set of identity providers can be Microsoft, Google, Facebook, Amazon, or LinkedIn.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSIdentityProvider</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The unique identifier for an identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The unique identifier for an identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSIdentityProvider</dev:code>
        <dev:remarks>
          <maml:para>This example retrieves the list of all configured identity providers and their properties.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSIdentityProvider -Id LinkedIn-OAUTH</dev:code>
        <dev:remarks>
          <maml:para>This example retrieves the properties for the identity provider specified.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADMSLifecyclePolicyGroup</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADMSLifecyclePolicyGroup</command:noun>
      <maml:description>
        <maml:para>Retrieves the lifecycle policy object to which a group belongs.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADMSLifecyclePolicyGroup retrieves the lifecycle policy object to which a group belongs.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADMSLifecyclePolicyGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a group in Azure Active Directory</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a group in Azure Active Directory</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Get-AzureADMSLifecyclePolicyGroup -Id cffd97bd-6b91-4c4e-b553-6918a320211c</dev:code>
        <dev:remarks>
          <maml:para>This command retrieves the lifecycle policy object to which a group belongs.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADObjectSetting</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADObjectSetting</command:noun>
      <maml:description>
        <maml:para>Gets an object setting.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADObjectSetting cmdlet gets an object setting from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADObjectSetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all objects settings. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a settings object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the target object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetType</maml:name>
          <maml:Description>
            <maml:para>Specifies the target type.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Get-AzureADObjectSetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:Description>
            <maml:para>If true, return all objects settings. If false, return the number of objects specified by the Top parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the target object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetType</maml:name>
          <maml:Description>
            <maml:para>Specifies the target type.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Top</maml:name>
          <maml:Description>
            <maml:para>Specifies the maximum number of records to return.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:Description>
          <maml:para>If true, return all objects settings. If false, return the number of objects specified by the Top parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a settings object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the target object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetType</maml:name>
        <maml:Description>
          <maml:para>Specifies the target type.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Top</maml:name>
        <maml:Description>
          <maml:para>Specifies the maximum number of records to return.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>New-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADPolicy</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADPolicy</command:noun>
      <maml:description>
        <maml:para>Gets a policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADPolicy cmdlet gets a policy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the policy you want to retrieve</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the policy you want to retrieve</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>New-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADPolicyAppliedObject</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADPolicyAppliedObject</command:noun>
      <maml:description>
        <maml:para></maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADPolicyAppliedObject cmdlet gets a policy-applied object from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADPolicyAppliedObject</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the policy for which you want to find the objects</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the policy for which you want to find the objects</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-AzureADServicePrincipalPolicy</command:name>
      <command:verb>Get</command:verb>
      <command:noun>AzureADServicePrincipalPolicy</command:noun>
      <maml:description>
        <maml:para></maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Get-AzureADServicePrincipalPolicy cmdlet gets the policy of a service principal in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-AzureADServicePrincipalPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The ID of the Service Principal for which you want to retrieve the policy</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The ID of the Service Principal for which you want to retrieve the policy</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------- Example 1: Get a policy -------------------</maml:title>
        <dev:code>PS C:\&gt;Get-AzureADServicePrincipalPolicy -Id "&lt;object id of service principal&gt;"</dev:code>
        <dev:remarks>
          <maml:para>This command get the policy for the specified service principal.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Add-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADApplicationProxyConnectorGroup</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADApplicationProxyConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The New-AzureADApplicationProxyConnectorGroup cmdlet creates a new Application Proxy Connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADApplicationProxyConnectorGroup cmdlet creates a new Application Proxy connector group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:Description>
            <maml:para>The name of the new Connector Group.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Name</command:parameterValue>
          <dev:type>
            <maml:name>Name</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:Description>
          <maml:para>The name of the new Connector Group.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Name</command:parameterValue>
        <dev:type>
          <maml:name>Name</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>Microsoft.Open.MSGraph.Model.Name</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADApplicationProxyConnectorGroup -Name "Backup Application Servers"

Id                                   Name                       ConnectorGroupType IsDefault
--                                   ----                       ------------------ ---------
d533d7b1-fd92-49e8-a200-3e7dcf7c2ab5 Backup Application Servers applicationProxy       False</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Create a new Connector Group with the name "Backup Application Servers"</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADDirectorySetting</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADDirectorySetting</command:noun>
      <maml:description>
        <maml:para>Creates a directory settings object.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADDirectorySetting cmdlet creates a directory settings object in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADDirectorySetting</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>DirectorySetting</maml:name>
          <maml:Description>
            <maml:para>Specifies directory settings.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
          <dev:type>
            <maml:name>DirectorySetting</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>DirectorySetting</maml:name>
        <maml:Description>
          <maml:para>Specifies directory settings.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
        <dev:type>
          <maml:name>DirectorySetting</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADMSGroup</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADMSGroup</command:noun>
      <maml:description>
        <maml:para>Creates an Azure AD group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADMSGroup cmdlet creates an Azure Active Directory (Azure AD) group.</maml:para>
      <maml:para>For information about creating dynamic groups, see Using attributes to create advanced rules (https://azure.microsoft.com/en-us/documentation/articles/active-directory-accessmanagement-groups-with-advanced-rules/).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADMSGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Description</maml:name>
          <maml:Description>
            <maml:para>Specifies a description for the group.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisplayName</maml:name>
          <maml:Description>
            <maml:para>Specifies a display name for the group.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MailEnabled</maml:name>
          <maml:Description>
            <maml:para>Specifies whether this group is mail enabled.</maml:para>
            <maml:para>Currently, you cannot create mail enabled groups in Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MailNickname</maml:name>
          <maml:Description>
            <maml:para>Specifies a mail nickname for the group. If MailEnabled is $False you must still specify a mail nickname.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SecurityEnabled</maml:name>
          <maml:Description>
            <maml:para>Specifies whether the group is security enabled. For security groups, this value must be $True.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupTypes</maml:name>
          <maml:Description>
            <maml:para>Specifies that the group is a dynamic group.  To create a dynamic group, specify a value of DynamicMembership.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MembershipRule</maml:name>
          <maml:Description>
            <maml:para>Specifies the membership rule for a dynamic group.</maml:para>
            <maml:para>For more information about the rules that you can use for dynamic groups, see Using attributes to create advanced rules (https://azure.microsoft.com/en-us/documentation/articles/active-directory-accessmanagement-groups-with-advanced-rules/).</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MembershipRuleProcessingState</maml:name>
          <maml:Description>
            <maml:para>Specifies the rule processing state. The acceptable values for this parameter are:</maml:para>
            <maml:para>* "On". Process the group rule.</maml:para>
            <maml:para>* "Paused". Stop processing the group rule.</maml:para>
            <maml:para></maml:para>
            <maml:para>Changing the value of the processing state does not change the members list of the group.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Visibility</maml:name>
          <maml:Description>
            <maml:para>This parameter determines the visibility of the group's content and members list. This parameter can take one of the following values:</maml:para>
            <maml:para>* "Public" - Anyone can view the contents of the group</maml:para>
            <maml:para>* "Private" - Only members can view the content of the group</maml:para>
            <maml:para>* "HiddenMembership" - Only members can view the content of the group and only members, owners, Global/Company Administrator, User Administrator and Helpdesk Administrators can view the members list of the group.</maml:para>
            <maml:para></maml:para>
            <maml:para>If no value is provided, the default value will be "Public".</maml:para>
            <maml:para>Notes:</maml:para>
            <maml:para>* This parameter is only valid for groups that have the groupType set to "Unified".</maml:para>
            <maml:para>* If a group has this attribute set to "HiddenMembership" it cannot be changed later.</maml:para>
            <maml:para>* Anyone can join a group that has this attribute set to "Public". If the attribute is set to Private or HiddenMembership, only owner(s) can add new members to the group and requests to join the group need approval of the owner(s).</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Description</maml:name>
        <maml:Description>
          <maml:para>Specifies a description for the group.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisplayName</maml:name>
        <maml:Description>
          <maml:para>Specifies a display name for the group.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MailEnabled</maml:name>
        <maml:Description>
          <maml:para>Specifies whether this group is mail enabled.</maml:para>
          <maml:para>Currently, you cannot create mail enabled groups in Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MailNickname</maml:name>
        <maml:Description>
          <maml:para>Specifies a mail nickname for the group. If MailEnabled is $False you must still specify a mail nickname.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SecurityEnabled</maml:name>
        <maml:Description>
          <maml:para>Specifies whether the group is security enabled. For security groups, this value must be $True.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupTypes</maml:name>
        <maml:Description>
          <maml:para>Specifies that the group is a dynamic group.  To create a dynamic group, specify a value of DynamicMembership.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MembershipRule</maml:name>
        <maml:Description>
          <maml:para>Specifies the membership rule for a dynamic group.</maml:para>
          <maml:para>For more information about the rules that you can use for dynamic groups, see Using attributes to create advanced rules (https://azure.microsoft.com/en-us/documentation/articles/active-directory-accessmanagement-groups-with-advanced-rules/).</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MembershipRuleProcessingState</maml:name>
        <maml:Description>
          <maml:para>Specifies the rule processing state. The acceptable values for this parameter are:</maml:para>
          <maml:para>* "On". Process the group rule.</maml:para>
          <maml:para>* "Paused". Stop processing the group rule.</maml:para>
          <maml:para></maml:para>
          <maml:para>Changing the value of the processing state does not change the members list of the group.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Visibility</maml:name>
        <maml:Description>
          <maml:para>This parameter determines the visibility of the group's content and members list. This parameter can take one of the following values:</maml:para>
          <maml:para>* "Public" - Anyone can view the contents of the group</maml:para>
          <maml:para>* "Private" - Only members can view the content of the group</maml:para>
          <maml:para>* "HiddenMembership" - Only members can view the content of the group and only members, owners, Global/Company Administrator, User Administrator and Helpdesk Administrators can view the members list of the group.</maml:para>
          <maml:para></maml:para>
          <maml:para>If no value is provided, the default value will be "Public".</maml:para>
          <maml:para>Notes:</maml:para>
          <maml:para>* This parameter is only valid for groups that have the groupType set to "Unified".</maml:para>
          <maml:para>* If a group has this attribute set to "HiddenMembership" it cannot be changed later.</maml:para>
          <maml:para>* Anyone can join a group that has this attribute set to "Public". If the attribute is set to Private or HiddenMembership, only owner(s) can add new members to the group and requests to join the group need approval of the owner(s).</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para>This cmdlet is currently in Public Preview. While a cmdlet is in Public Preview, we may make changes to the cmdlet which could have unexpected effects. We recommend that you do not use this cmdlet in a production environment.</maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------- Example 1: Create a dynamic group --------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADMSGroup -DisplayName "Dynamic Group 01" -Description "Dynamic group created from PS" -MailEnabled $False -MailNickName "group" -SecurityEnabled $True -GroupTypes "DynamicMembership" -MembershipRule "(user.department -contains ""Marketing"")" -MembershipRuleProcessingState "On"

Id                            : 9126185e-25df-4522-a380-7ab697a7241c
Description                   : Dynamic group created from PS
OnPremisesSyncEnabled         : 
DisplayName                   : Dynamic Group 01
OnPremisesLastSyncDateTime    : 
Mail                          : 
MailEnabled                   : False 
MailNickname                  : group 
OnPremisesSecurityIdentifier  : 
ProxyAddresses                : {} 
SecurityEnabled               : True 
GroupTypes                    : {} 
MembershipRule                : (user.department -eq "Marketing") MembershipRuleProcessingState : Paused</dev:code>
        <dev:remarks>
          <maml:para>This command creates a new dynamic group with the following rule:</maml:para>
          <maml:para>`user.department -contains "Marketing"`</maml:para>
          <maml:para>The double quotation marks are replaced with single quotation marks.</maml:para>
          <maml:para>The processing state is On.  This means that all users in the directory that qualify the rule are added as members to the group. Any users that do not qualify are removed from the group.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Using attributes to create advanced rules</maml:linkText>
        <maml:uri>https://azure.microsoft.com/en-us/documentation/articles/active-directory-accessmanagement-groups-with-advanced-rules/</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADMSGroupLifecyclePolicy</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADMSGroupLifecyclePolicy</command:noun>
      <maml:description>
        <maml:para>Creates a new groupLifecyclePolicy</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Creates a new groupLifecyclePolicy in Azure Active Directory</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADMSGroupLifecyclePolicy</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AlternateNotificationEmails</maml:name>
          <maml:Description>
            <maml:para>Notification emails for groups that have no owners will be sent to these email addresses. List of email addresses separated by a ";".</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupLifetimeInDays</maml:name>
          <maml:Description>
            <maml:para>The number of days a group can exist before it needs to be renewed</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ManagedGroupTypes</maml:name>
          <maml:Description>
            <maml:para>This parameter allows the admin to select which office 365 groups the policy will apply to. "None" will create the policy in a disabled state. "All" will apply the policy to every Office 365 group in the tenant. "Selected" will allow the admin to choose specific Office 365 groups that the policy will apply to.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AlternateNotificationEmails</maml:name>
        <maml:Description>
          <maml:para>Notification emails for groups that have no owners will be sent to these email addresses. List of email addresses separated by a ";".</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupLifetimeInDays</maml:name>
        <maml:Description>
          <maml:para>The number of days a group can exist before it needs to be renewed</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ManagedGroupTypes</maml:name>
        <maml:Description>
          <maml:para>This parameter allows the admin to select which office 365 groups the policy will apply to. "None" will create the policy in a disabled state. "All" will apply the policy to every Office 365 group in the tenant. "Selected" will allow the admin to choose specific Office 365 groups that the policy will apply to.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADMSGroupLifecyclePolicy -GroupLifetimeInDays 99 -ManagedGroupTypes "Selected" -AlternateNotificationEmails "<EMAIL>"</dev:code>
        <dev:remarks>
          <maml:para>This will create a a new groupLifecyclePolicy setting the group lifetime to 99 days for a selected set of Office 365 groups and send renewal notification emails to groups that have no owners to "<EMAIL>"</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADMSIdentityProvider</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADMSIdentityProvider</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to configure a new identity provider in the directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to configure an identity provider in the directory. Adding an identity provider will allow users to sign up for or sign into applications secured by Azure AD B2C using the identity provider.</maml:para>
      <maml:para>Configuring an identity provider in your Azure AD tenant also enables future B2B guest scenarios. For example, an organization has resources in Office 365 that needs to be shared with a Gmail user. The Gmail user will use their Google account credentials to authenticate and access the documents.</maml:para>
      <maml:para>The current set of identity providers can be Microsoft, Google, Facebook, Amazon, or LinkedIn.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADMSIdentityProvider</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientId</maml:name>
          <maml:Description>
            <maml:para>The client ID for the application. This is the client ID obtained when registering the application with the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientSecret</maml:name>
          <maml:Description>
            <maml:para>The client secret for the application. This is the client secret obtained when registering the application with the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:Description>
            <maml:para>The display name of the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Type</maml:name>
          <maml:Description>
            <maml:para>The identity provider type. It must be one of the following values: Microsoft, Google, Facebook, Amazon, or LinkedIn.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ClientId</maml:name>
        <maml:Description>
          <maml:para>The client ID for the application. This is the client ID obtained when registering the application with the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ClientSecret</maml:name>
        <maml:Description>
          <maml:para>The client secret for the application. This is the client secret obtained when registering the application with the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:Description>
          <maml:para>The display name of the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Type</maml:name>
        <maml:Description>
          <maml:para>The identity provider type. It must be one of the following values: Microsoft, Google, Facebook, Amazon, or LinkedIn.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; New-AzureADMSIdentityProvider -Type LinkedIn -Name LinkedInName -ClientId LinkedInAppClientId -ClientSecret LinkedInAppClientSecret</dev:code>
        <dev:remarks>
          <maml:para>This example adds a LinkedIn identity provider.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADMSInvitation</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADMSInvitation</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to invite a new external user to your directory</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to invite a new external user to your directory.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADMSInvitation</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>InvitedUserDisplayName</maml:name>
          <maml:Description>
            <maml:para>The display name of the user as it will appear in your directory</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>InvitedUserEmailAddress</maml:name>
          <maml:Description>
            <maml:para>The Email address to which the invitation is sent</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>InvitedUserMessageInfo</maml:name>
          <maml:Description>
            <maml:para>Addition information to specify how the invitation message is sent</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">InvitedUserMessageInfo</command:parameterValue>
          <dev:type>
            <maml:name>InvitedUserMessageInfo</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>InvitedUserType</maml:name>
          <maml:Description>
            <maml:para>The userType of the user being invited. By default, this is Guest. You can invite as Member if you're are company administrator.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>InviteRedirectUrl</maml:name>
          <maml:Description>
            <maml:para>The URL to which the invited user is forwarded after accepting the invitation</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SendInvitationMessage</maml:name>
          <maml:Description>
            <maml:para>A Boolean parameter that indicates whether or not an invitation message will be sent to the invited user.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>InvitedUserDisplayName</maml:name>
        <maml:Description>
          <maml:para>The display name of the user as it will appear in your directory</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>InvitedUserEmailAddress</maml:name>
        <maml:Description>
          <maml:para>The Email address to which the invitation is sent</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>InvitedUserMessageInfo</maml:name>
        <maml:Description>
          <maml:para>Addition information to specify how the invitation message is sent</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">InvitedUserMessageInfo</command:parameterValue>
        <dev:type>
          <maml:name>InvitedUserMessageInfo</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>InvitedUserType</maml:name>
        <maml:Description>
          <maml:para>The userType of the user being invited. By default, this is Guest. You can invite as Member if you're are company administrator.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>InviteRedirectUrl</maml:name>
        <maml:Description>
          <maml:para>The URL to which the invited user is forwarded after accepting the invitation</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SendInvitationMessage</maml:name>
        <maml:Description>
          <maml:para>A Boolean parameter that indicates whether or not an invitation message will be sent to the invited user.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>--------- Invite a new external user to your directory ---------</maml:title>
        <dev:code>New-AzureADMSInvitation -InvitedUserEmailAddress <EMAIL> -SendInvitationMessage $True -InviteRedirectUrl "http://myapps.onmicrosoft.com"</dev:code>
        <dev:remarks>
          <maml:para>Using the cmdlet in this example, an email is sent to the user who's email address is in the -InvitedUserEmailAddress parameter. When the user accepts the invitation, they are forwarded to the url as specified in the -InviteRedirectUrl parameter</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADObjectSetting</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADObjectSetting</command:noun>
      <maml:description>
        <maml:para>Creates a settings object.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADObjectSetting cmdlet creates a settings object in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADObjectSetting</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>DirectorySetting</maml:name>
          <maml:Description>
            <maml:para>Specifies the new settings.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
          <dev:type>
            <maml:name>DirectorySetting</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event.</maml:para>
            <maml:para>The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of directory object to which to assign settings.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetType</maml:name>
          <maml:Description>
            <maml:para>Specifies the type of the directory object to which to assign settings.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>DirectorySetting</maml:name>
        <maml:Description>
          <maml:para>Specifies the new settings.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
        <dev:type>
          <maml:name>DirectorySetting</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event.</maml:para>
          <maml:para>The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of directory object to which to assign settings.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetType</maml:name>
        <maml:Description>
          <maml:para>Specifies the type of the directory object to which to assign settings.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>New-AzureADPolicy</command:name>
      <command:verb>New</command:verb>
      <command:noun>AzureADPolicy</command:noun>
      <maml:description>
        <maml:para>Creates a policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The New-AzureADPolicy cmdlet creates a policy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>New-AzureADPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AlternativeIdentifier</maml:name>
          <maml:Description>
            <maml:para>Specifies an alternative ID.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Definition</maml:name>
          <maml:Description>
            <maml:para>Specifies an array of JSON that contains all the rules of the policy, for example: -Definition @("{"TokenLifetimePolicy":{"Version":1,"MaxInactiveTime":"20:00:00"}}")</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisplayName</maml:name>
          <maml:Description>
            <maml:para>String of the policy name</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IsOrganizationDefault</maml:name>
          <maml:Description>
            <maml:para>True if this policy is the organisational default</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>KeyCredentials</maml:name>
          <maml:Description>
            <maml:para>@{Text=}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Type</maml:name>
          <maml:Description>
            <maml:para>Specifies the type of policy. For token lifetimes, specify "TokenLifetimePolicy".</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AlternativeIdentifier</maml:name>
        <maml:Description>
          <maml:para>Specifies an alternative ID.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Definition</maml:name>
        <maml:Description>
          <maml:para>Specifies an array of JSON that contains all the rules of the policy, for example: -Definition @("{"TokenLifetimePolicy":{"Version":1,"MaxInactiveTime":"20:00:00"}}")</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisplayName</maml:name>
        <maml:Description>
          <maml:para>String of the policy name</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>IsOrganizationDefault</maml:name>
        <maml:Description>
          <maml:para>True if this policy is the organisational default</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>KeyCredentials</maml:name>
        <maml:Description>
          <maml:para>@{Text=}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Type</maml:name>
        <maml:Description>
          <maml:para>Specifies the type of policy. For token lifetimes, specify "TokenLifetimePolicy".</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------ Example 1: Create a policy ------------------</maml:title>
        <dev:code>PS C:\&gt;New-AzureADPolicy -Definition &lt;Array of Rules&gt; -DisplayName &lt;Name of Policy&gt; -IsTenantDefault</dev:code>
        <dev:remarks>
          <maml:para>This command creates a new policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADApplicationPolicy</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADApplicationPolicy</command:noun>
      <maml:description>
        <maml:para>Removes an application policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADApplicationPolicy cmdlet removes an application policy from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADApplicationPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>PolicyId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the policy.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>{{Fill Id Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>PolicyId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the policy.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>{{Fill Id Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>----------- Example 1: Remove an application policy -----------</maml:title>
        <dev:code>PS C:\&gt;Remove-AzureADApplicationPolicy -ObjectId &lt;object id of application&gt; -PolicyId &lt;object id of policy&gt;</dev:code>
        <dev:remarks>
          <maml:para>This command removes the specified application policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Add-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADApplicationPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADApplicationProxyApplicationConnectorGroup</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADApplicationProxyApplicationConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Remove-AzureADApplicationProxyApplicationConnectorGroup cmdlet sets the connector group assigned for the specified application to 'Default' and removes the current assignment.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>If your application is already in the 'Default' group, you will see an error because the application cannot be removed from the 'Default' group unless it is being added to another group. The application must be configured for Application Proxy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADApplicationProxyApplicationConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>ObjectId</maml:name>
          <maml:Description>
            <maml:para>The unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>ObjectId</maml:name>
        <maml:Description>
          <maml:para>The unique application Id of the application. This can be found using the Get-AzureADApplication command. You can also find this in the Azure Portal by navigating to AAD, Enterprise Applications, All Applications, Select your application, go to the properties tab, and use the ObjectId on that page.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADApplicationProxyApplicationConnectorGroup -ObjectId 59462d3c-a1bc-40a0-9bed-be799357ebce</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Remove the Connector Group associated with an application, setting the group to 'Default'</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADApplicationProxyConnectorGroup</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADApplicationProxyConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Remove-AzureADApplicationProxyApplicationConnectorGroup cmdlet deletes an Application Proxy Connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADApplicationProxyConnectorGroup cmdlet deletes an Application Proxy Connector Group. It can only be used on an empty connector group, with no connectors assigned.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the Connector group to delete. You can find this value by running the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the Connector group to delete. You can find this value by running the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADApplicationProxyApplicationConnectorGroup -ObjectId 59462d3c-a1bc-40a0-9bed-be799357ebce</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Remove a specific Connector Group</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADDirectorySetting</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADDirectorySetting</command:noun>
      <maml:description>
        <maml:para>Deletes a directory setting in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADDirectorySetting cmdlet removes a directory setting from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADDirectorySetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the object ID of a settings object in Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the object ID of a settings object in Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADMSDeletedDirectoryObject</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADMSDeletedDirectoryObject</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to permanently delete a previously deleted directory object</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to permanently delete a previously deleted directory object. When a directory object is permanently deleted it can no longer be restored.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADMSDeletedDirectoryObject</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the directory object that is permanently deleted</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the directory object that is permanently deleted</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>Remove-AzureADMSDeletedDirectoryObject -Id aa644285-eb75-4389-885e-7233f096984c</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to permanently delete a previously deleted directory object with Id = aa644285-eb75-4389-885e-7233f096984c</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADMSGroup</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADMSGroup</command:noun>
      <maml:description>
        <maml:para>Removes an Azure AD group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADMSGroup cmdlet removes an Azure Active Directory (Azure AD) group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADMSGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the group that this cmdlet removes.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the group that this cmdlet removes.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para>This cmdlet is currently in Public Preview. While a cmdlet is in Public Preview, we may make changes to the cmdlet which could have unexpected effects. We recommend that you do not use this cmdlet in a production environment.</maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------ Example 1: Remove a group ------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADMSGroup -Id "ce0a2213-bd57-4e2f-b9fa-408582e2e260"</dev:code>
        <dev:remarks>
          <maml:para>This cmdlet removes the group that has the specified ID.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADMSGroup</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Using attributes to create advanced rules</maml:linkText>
        <maml:uri>https://azure.microsoft.com/en-us/documentation/articles/active-directory-accessmanagement-groups-with-advanced-rules/</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADMSGroupLifecyclePolicy</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADMSGroupLifecyclePolicy</command:noun>
      <maml:description>
        <maml:para>Deletes a groupLifecyclePolicies object</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADMSGroupLifecyclePolicy command deletes a groupLifecyclePolicies object in Azure Active Directory.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADMSGroupLifecyclePolicy</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the groupLifecyclePolicies object that this cmdlet removes.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the groupLifecyclePolicies object that this cmdlet removes.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADMSGroupLifecyclePolicy -Id "13bed58e-6144-41e5-abbd-47c95964e671"</dev:code>
        <dev:remarks>
          <maml:para>This cmdlet deletes the groupLifecyclePolicies object that has the specified ID.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADMSIdentityProvider</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADMSIdentityProvider</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to delete an identity provider in the directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to delete an identity provider that has been configured in the directory. The identity provider will be permanently deleted.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADMSIdentityProvider</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The unique identifier for an identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The unique identifier for an identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADMSIdentityProvider -Id LinkedIn-OAUTH</dev:code>
        <dev:remarks>
          <maml:para>This example removes the specified identity provider.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADMSLifecyclePolicyGroup</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADMSLifecyclePolicyGroup</command:noun>
      <maml:description>
        <maml:para>Removes a group from a lifecycle policy</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADMSLifecyclePolicyGroup cmdlet removes a group from a lifecycle policy in Azure Active Directory</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADMSLifecyclePolicyGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of the lifecycle policy object in Azure Active Directory.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of the lifecycle policy object in Azure Active Directory.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Remove-AzureADMSLifecyclePolicyGroup -Id b4c908b0-3595-4add-91b4-c5400b31b57b -groupId cffd97bd-6b91-4c4e-b553-6918a320211c</dev:code>
        <dev:remarks>
          <maml:para>This command removes a group from a lifecycle policy in Azure Active Directory</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADObjectSetting</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADObjectSetting</command:noun>
      <maml:description>
        <maml:para>Deletes settings in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADObjectSetting cmdlet removes object settings in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADObjectSetting</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specfies the ID of a settings object in Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the object ID of the target.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetType</maml:name>
          <maml:Description>
            <maml:para>Specifies the target type.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specfies the ID of a settings object in Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the object ID of the target.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetType</maml:name>
        <maml:Description>
          <maml:para>Specifies the target type.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADPolicy</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADPolicy</command:noun>
      <maml:description>
        <maml:para>Removes a policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Remove-AzureADPolicy cmdlet removes a policy from Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the policy you want to remove</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the policy you want to remove</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------ Example 1: Remove a policy ------------------</maml:title>
        <dev:code>PS C:\&gt;Remove-AzureADPolicy -Id *&lt;ID&gt;*.</dev:code>
        <dev:remarks>
          <maml:para>This command removes the specified policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Set-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-AzureADServicePrincipalPolicy</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>AzureADServicePrincipalPolicy</command:noun>
      <maml:description>
        <maml:para></maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para></maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-AzureADServicePrincipalPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>PolicyId</maml:name>
          <maml:Description>
            <maml:para>Specifies the object ID of a policy.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>{{Fill Id Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>PolicyId</maml:name>
        <maml:Description>
          <maml:para>Specifies the object ID of a policy.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>{{Fill Id Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>--------- Example 1: Remove a service principal policy ---------</maml:title>
        <dev:code>PS C:\&gt;Remove-AzureADApplicationPolicy -ObjectId &lt;object id of application&gt; -PolicyId &lt;object id of policy&gt;</dev:code>
        <dev:remarks>
          <maml:para>This command removes a service principal policy.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Add-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADServicePrincipalPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Reset-AzureADMSLifeCycleGroup</command:name>
      <command:verb>Reset</command:verb>
      <command:noun>AzureADMSLifeCycleGroup</command:noun>
      <maml:description>
        <maml:para>Renews a group by updating the RenewedDateTime property on a group to the current DateTime.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Reset-AzureADMSLifeCycleGroup renews a group by updating the RenewedDateTime property on a group to the current DateTime. When a group is renewed, the group expiration is extended by the number of days defined in the policy.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Reset-AzureADMSLifeCycleGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupId</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupId</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a group in Azure Active Directory.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Reset-AzureADMSLifeCycleGroup -groupId cffd97bd-6b91-4c4e-b553-6918a320211c</dev:code>
        <dev:remarks>
          <maml:para>The Reset-AzureADMSLifeCycleGroup renews a specified group by updating the RenewedDateTime property on a group to the current DateTime.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Restore-AzureADMSDeletedDirectoryObject</command:name>
      <command:verb>Restore</command:verb>
      <command:noun>AzureADMSDeletedDirectoryObject</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to restore a previously deleted object.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet is used to restore a previously deleted object. Currently, only restoring Group and Application objects is supported.  When a group or an application is deleted it is initially soft deleted and can be recovered during the first 30 days after deletion. After 30 days the deleted object is permanently deleted and can no longer be recovered. Note that only Unified Groups (a.k.a. Office 365 Groups) can be restored. Security groups cannot be restored.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Restore-AzureADMSDeletedDirectoryObject</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id of the directory object to restore</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id of the directory object to restore</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>Restore-AzureADMSDeletedDirectoryObject -Id aa644285-eb75-4389-885e-7233f096984c</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to restore a deleted object with Id aa644285-eb75-4389-885e-7233f096984c</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADApplicationProxyConnectorGroup</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADApplicationProxyConnectorGroup</command:noun>
      <maml:description>
        <maml:para>The Set-AzureADApplicationProxyConnectorGroup cmdlet allows you to change the name of a given Application Proxy connector group.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADApplicationProxyConnectorGroup cmdlet allows you to change the name of a given Application Proxy connector group.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADApplicationProxyConnectorGroup</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The unique identifier of the Connector group that will be renamed. You can find the Id using the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:Description>
            <maml:para>The new name for the Connector group.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Name</command:parameterValue>
          <dev:type>
            <maml:name>Name</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The unique identifier of the Connector group that will be renamed. You can find the Id using the Get-AzureADApplicationProxyConnectorGroup command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:Description>
          <maml:para>The new name for the Connector group.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Name</command:parameterValue>
        <dev:type>
          <maml:name>Name</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>Microsoft.Open.MSGraph.Model.Name</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADApplicationProxyConnectorGroup -Id d533d7b1-fd92-49e8-a200-3e7dcf7c2ab5 -Name "Offsite Application Servers"</dev:code>
        <dev:remarks>
          <maml:para>Example 1: Rename a Connector Group to "Offsite Application Servers"</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADDirectorySetting</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADDirectorySetting</command:noun>
      <maml:description>
        <maml:para>Updates a directory setting in Azure Active Directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADDirectorySetting cmdlet updates a directory setting in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADDirectorySetting</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>DirectorySetting</maml:name>
          <maml:Description>
            <maml:para>Specifies the directory settings.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
          <dev:type>
            <maml:name>DirectorySetting</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a settings object in Azure AD.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>DirectorySetting</maml:name>
        <maml:Description>
          <maml:para>Specifies the directory settings.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
        <dev:type>
          <maml:name>DirectorySetting</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a settings object in Azure AD.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADDirectorySetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADMSGroup</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADMSGroup</command:noun>
      <maml:description>
        <maml:para>{{Fill in the Synopsis}}</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>{{Fill in the Description}}</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADMSGroup</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Description</maml:name>
          <maml:Description>
            <maml:para>{{Fill Description Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisplayName</maml:name>
          <maml:Description>
            <maml:para>{{Fill DisplayName Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupTypes</maml:name>
          <maml:Description>
            <maml:para>{{Fill GroupTypes Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>{{Fill Id Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MailEnabled</maml:name>
          <maml:Description>
            <maml:para>{{Fill MailEnabled Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MailNickname</maml:name>
          <maml:Description>
            <maml:para>{{Fill MailNickname Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MembershipRule</maml:name>
          <maml:Description>
            <maml:para>{{Fill MembershipRule Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>MembershipRuleProcessingState</maml:name>
          <maml:Description>
            <maml:para>{{Fill MembershipRuleProcessingState Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SecurityEnabled</maml:name>
          <maml:Description>
            <maml:para>{{Fill SecurityEnabled Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Visibility</maml:name>
          <maml:Description>
            <maml:para>{{Fill Visibility Description}}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Description</maml:name>
        <maml:Description>
          <maml:para>{{Fill Description Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisplayName</maml:name>
        <maml:Description>
          <maml:para>{{Fill DisplayName Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupTypes</maml:name>
        <maml:Description>
          <maml:para>{{Fill GroupTypes Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>{{Fill Id Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MailEnabled</maml:name>
        <maml:Description>
          <maml:para>{{Fill MailEnabled Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MailNickname</maml:name>
        <maml:Description>
          <maml:para>{{Fill MailNickname Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MembershipRule</maml:name>
        <maml:Description>
          <maml:para>{{Fill MembershipRule Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>MembershipRuleProcessingState</maml:name>
        <maml:Description>
          <maml:para>{{Fill MembershipRuleProcessingState Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SecurityEnabled</maml:name>
        <maml:Description>
          <maml:para>{{Fill SecurityEnabled Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Visibility</maml:name>
        <maml:Description>
          <maml:para>{{Fill Visibility Description}}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADMSGroupLifecyclePolicy</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADMSGroupLifecyclePolicy</command:noun>
      <maml:description>
        <maml:para>Updates a specific group Lifecycle Policy in Azure Active Directory</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADMSGroupLifecyclePolicy command updates a specific group Lifecycle Policy in Azure Active Directory</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADMSGroupLifecyclePolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AlternateNotificationEmails</maml:name>
          <maml:Description>
            <maml:para>Notification emails for groups that have no owners will be sent to these email addresses. List of email addresses separated by a ";".</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>GroupLifetimeInDays</maml:name>
          <maml:Description>
            <maml:para>The number of days a group can exist before it needs to be renewed</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a groupLifecyclePolicies object in Azure Active Directory</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ManagedGroupTypes</maml:name>
          <maml:Description>
            <maml:para>Allows the admin to select which office 365 groups the policy will apply to. "None" will create the policy in a disabled state. "All" will apply the policy to every Office 365 group in the tenant. "Selected" will allow the admin to choose specific Office 365 groups that the policy will apply to.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AlternateNotificationEmails</maml:name>
        <maml:Description>
          <maml:para>Notification emails for groups that have no owners will be sent to these email addresses. List of email addresses separated by a ";".</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>GroupLifetimeInDays</maml:name>
        <maml:Description>
          <maml:para>The number of days a group can exist before it needs to be renewed</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a groupLifecyclePolicies object in Azure Active Directory</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ManagedGroupTypes</maml:name>
        <maml:Description>
          <maml:para>Allows the admin to select which office 365 groups the policy will apply to. "None" will create the policy in a disabled state. "All" will apply the policy to every Office 365 group in the tenant. "Selected" will allow the admin to choose specific Office 365 groups that the policy will apply to.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADMSGroupLifecyclePolicy -Id "b4c908b0-3595-4add-91b4-c5400b31b57b" -GroupLifetimeInDays 200 -AlternateNotificationEmails "<EMAIL>"</dev:code>
        <dev:remarks>
          <maml:para>This command updates the specified groupLifecyclePolicy in Azure Active Directory</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADMSIdentityProvider</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADMSIdentityProvider</command:noun>
      <maml:description>
        <maml:para>This cmdlet is used to update the properties of an existing identity provider configured in the directory.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet can be used to update the properties of an existing identity provider. The type of the identity provider cannot be modified.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADMSIdentityProvider</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientId</maml:name>
          <maml:Description>
            <maml:para>The client ID for the application. This is the client ID obtained when registering the application with the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientSecret</maml:name>
          <maml:Description>
            <maml:para>The client secret for the application. This is the client secret obtained when registering the application with the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The unique identifier for an identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:Description>
            <maml:para>The display name of the identity provider.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ClientId</maml:name>
        <maml:Description>
          <maml:para>The client ID for the application. This is the client ID obtained when registering the application with the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ClientSecret</maml:name>
        <maml:Description>
          <maml:para>The client secret for the application. This is the client secret obtained when registering the application with the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The unique identifier for an identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:Description>
          <maml:para>The display name of the identity provider.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; Set-AzureADMSIdentityProvider -Id LinkedIn-OAUTH -ClientId NewClientId -ClientSecret NewClientSecret</dev:code>
        <dev:remarks>
          <maml:para>This example updates the client ID and client secret for the specified identity provider.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADObjectSetting</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADObjectSetting</command:noun>
      <maml:description>
        <maml:para>Updates object settings.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADObjectSetting cmdlet updates the settings for an object in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADObjectSetting</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>DirectorySetting</maml:name>
          <maml:Description>
            <maml:para>Specifies a DirectorySetting object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
          <dev:type>
            <maml:name>DirectorySetting</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
          <maml:name>InformationAction</maml:name>
          <maml:Description>
            <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
            <maml:para>- Continue</maml:para>
            <maml:para>- Ignore</maml:para>
            <maml:para>- Inquire</maml:para>
            <maml:para>- SilentlyContinue</maml:para>
            <maml:para>- Stop</maml:para>
            <maml:para>- Suspend</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
          <maml:name>InformationVariable</maml:name>
          <maml:Description>
            <maml:para>Specifies an information variable.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>Specifies the ID of a settings object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetObjectId</maml:name>
          <maml:Description>
            <maml:para>Specifies the object ID of directory object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>TargetType</maml:name>
          <maml:Description>
            <maml:para>Specifies the target type of a directory object.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>DirectorySetting</maml:name>
        <maml:Description>
          <maml:para>Specifies a DirectorySetting object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">DirectorySetting</command:parameterValue>
        <dev:type>
          <maml:name>DirectorySetting</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="infa">
        <maml:name>InformationAction</maml:name>
        <maml:Description>
          <maml:para>Specifies how this cmdlet responds to an information event. The acceptable values for this parameter are:</maml:para>
          <maml:para>- Continue</maml:para>
          <maml:para>- Ignore</maml:para>
          <maml:para>- Inquire</maml:para>
          <maml:para>- SilentlyContinue</maml:para>
          <maml:para>- Stop</maml:para>
          <maml:para>- Suspend</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="iv">
        <maml:name>InformationVariable</maml:name>
        <maml:Description>
          <maml:para>Specifies an information variable.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>Specifies the ID of a settings object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetObjectId</maml:name>
        <maml:Description>
          <maml:para>Specifies the object ID of directory object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>TargetType</maml:name>
        <maml:Description>
          <maml:para>Specifies the target type of a directory object.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>PS C:\&gt; {{ Add example code here }}</dev:code>
        <dev:remarks>
          <maml:para>{{ Add example description here }}</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADObjectSetting</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-AzureADPolicy</command:name>
      <command:verb>Set</command:verb>
      <command:noun>AzureADPolicy</command:noun>
      <maml:description>
        <maml:para>Updates a policy.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The Set-AzureADPolicy cmdlet sets a policy in Azure Active Directory (AD).</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-AzureADPolicy</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AlternativeIdentifier</maml:name>
          <maml:Description>
            <maml:para>Specifies an alternative ID for the policy.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Definition</maml:name>
          <maml:Description>
            <maml:para>Specifies the array of stringfied JSON that contains all the rules of the policy. For example -Definition @("{"TokenLifetimePolicy":{"Version":1,"MaxInactiveTime":"20:00:00"}}") .</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisplayName</maml:name>
          <maml:Description>
            <maml:para>Specifies the display name.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IsOrganizationDefault</maml:name>
          <maml:Description>
            <maml:para>True if this policy is the organisational default</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>KeyCredentials</maml:name>
          <maml:Description>
            <maml:para>Specifies the key credentials.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</command:parameterValue>
          <dev:type>
            <maml:name>System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Type</maml:name>
          <maml:Description>
            <maml:para>Specifies the type of policy. For token lifetimes, use "TokenLifetimePolicy".</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
          <maml:name>Id</maml:name>
          <maml:Description>
            <maml:para>The Id pf the policy for which you want to set values.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AlternativeIdentifier</maml:name>
        <maml:Description>
          <maml:para>Specifies an alternative ID for the policy.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Definition</maml:name>
        <maml:Description>
          <maml:para>Specifies the array of stringfied JSON that contains all the rules of the policy. For example -Definition @("{"TokenLifetimePolicy":{"Version":1,"MaxInactiveTime":"20:00:00"}}") .</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[System.String]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[System.String]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisplayName</maml:name>
        <maml:Description>
          <maml:para>Specifies the display name.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>IsOrganizationDefault</maml:name>
        <maml:Description>
          <maml:para>True if this policy is the organisational default</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>KeyCredentials</maml:name>
        <maml:Description>
          <maml:para>Specifies the key credentials.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</command:parameterValue>
        <dev:type>
          <maml:name>System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.KeyCredential]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Type</maml:name>
        <maml:Description>
          <maml:para>Specifies the type of policy. For token lifetimes, use "TokenLifetimePolicy".</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="named" aliases="none">
        <maml:name>Id</maml:name>
        <maml:Description>
          <maml:para>The Id pf the policy for which you want to set values.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------------ Example 1: Update a policy ------------------</maml:title>
        <dev:code>PS C:\&gt;Set-AzureADPolicy -ObjectId &lt;object id of policy&gt; -DisplayName &lt;string&gt;</dev:code>
        <dev:remarks>
          <maml:para>This command updates the specified policy in Azure AD.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Get-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>New-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Remove-AzureADPolicy</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
</helpItems>