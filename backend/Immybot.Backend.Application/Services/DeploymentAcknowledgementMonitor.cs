using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Helpers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Services;

public class DeploymentAcknowledgementMonitor : BackgroundService
{
  private readonly ILogger<DeploymentAcknowledgementMonitor> _logger;
  private readonly IServiceScopeFactory _serviceScopeFactory;

  public DeploymentAcknowledgementMonitor(
    ILogger<DeploymentAcknowledgementMonitor> logger,
    IServiceScopeFactory serviceScopeFactory)
  {
    _logger = logger;
    _serviceScopeFactory = serviceScopeFactory;
  }

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    await Task.Yield();

    try
    {
      _logger.LogInformation("Starting {service}", nameof(DeploymentAcknowledgementMonitor));

      // once now
     CheckDeployments(stoppingToken);

      // then every 5 minutes
      using var timer = new PeriodicTimer(TimeSpan.FromMinutes(5));

      while (await timer.WaitForNextTickAsync(stoppingToken))
      {
        CheckDeployments(stoppingToken);
      }
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      _logger.LogError(ex, "Failed to start {service}", nameof(DeploymentAcknowledgementMonitor));
    }
    finally
    {
      _logger.LogInformation("Stopping {service}", nameof(DeploymentAcknowledgementMonitor));
    }
  }

  private void CheckDeployments(CancellationToken stoppingToken)
  {
    try
    {
      using var scope = _serviceScopeFactory.CreateScope();
      var actions = scope.ServiceProvider.GetRequiredService<IDeploymentAcknowledgementMonitorActions>();
      var ids = actions.CheckForUnacknowledgedGlobalDeployments(stoppingToken);
      actions.EmitUnacknowledgedDeploymentsEvent(ids.Count);
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      _logger.LogError(ex, "Failed to check for unacknowledged global deployments");
    }
  }
}
