using System.Net.WebSockets;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json.Serialization;
using OmniSharp.Extensions.LanguageServer.Protocol.Models;
using StreamJsonRpc;
using System.IO.Pipes;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using OmniSharp.Extensions.LanguageServer.Protocol;
using Immybot.Backend.Application.Lib.MetaScripts;
using System.Threading.Tasks.Dataflow;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Interfaces;
using System.Text;
using System.Collections.Concurrent;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Shared.Telemetry;

namespace Immybot.Backend.Application.Services;


/// <summary>
/// The purpose of this service is to pair the connection between language client and the language server(s) running as separate processes.
/// </summary>
internal class OutOfProcessLanguageService : IHostedService, ILanguageService
{
  private readonly ILogger<OutOfProcessLanguageService> _logger;
  private readonly LanguageServiceConnectionHandler _languageServiceConnectionHandler;
  private readonly IOptions<PowershellEditorServicesOptions> _editorServicesOptions;
  private readonly IUserService _userService;
  private readonly ICachedSingleton<ApplicationPreferences> _cachedAppPrefs;
  private readonly KeyedLocker _keyedLocker;
  private readonly ConcurrentDictionary<Guid, (User ProcessOwner, EditorServicesProcess Process)> _editorServicesProcesses = new();
  private readonly ConcurrentDictionary<Guid, (LanguageServerInterceptor Interceptor, Func<string, Task> RemoteTermination)> _editorServiceInterceptors = new();

  private readonly TimeSpan _idleTimeout;
  private readonly TimeSpan _launchSubprocessTimeout;

  private const string _idleTimeoutMessage = "Still around? This Editor Services session has been detected as idle. Session will be ended in 60s if no further activity is detected to conserve resources.";
  private const string _idleTimeoutResetMessage = "Activity detected. Idle timeout reset.";
  private const string _idleTimeoutReachedMessage = "Editor idle timeout reached. Editor Services has been terminated to conserve resources.";
  private const string _disconnectLeastActiveMessage = "This Editor Session has been terminated due to its inactivity to make room for an incoming editor request.";

  public OutOfProcessLanguageService(
    ILogger<OutOfProcessLanguageService> logger,
    LanguageServiceConnectionHandler languageServiceConnectionHandler,
    IOptions<PowershellEditorServicesOptions> editorServicesOptions,
    IUserService userService,
    ICachedSingleton<ApplicationPreferences> cachedAppPrefs,
    KeyedLocker keyedLocker)
  {
    _logger = logger;
    _languageServiceConnectionHandler = languageServiceConnectionHandler;
    _editorServicesOptions = editorServicesOptions;
    _userService = userService;
    _cachedAppPrefs = cachedAppPrefs;
    _keyedLocker = keyedLocker;

    _idleTimeout = TimeSpan.FromSeconds(editorServicesOptions.Value.TerminalInactivityTimeoutSeconds);
    _launchSubprocessTimeout = TimeSpan.FromSeconds(editorServicesOptions.Value.SubProcessInitTimeoutSeconds);
  }
  public async Task StartEditorServices(Guid terminalId, int? scriptId, DatabaseType? scriptType, ScriptCategory scriptCategory, ScriptExecutionContext scriptExecutionContext, CancellationToken cancellationToken)
  {
    using var activity = Telemetry.StartActivity(ActivityType.Script, tags: new()
    {
      { "TerminalId", terminalId },
      { "ScriptId", scriptId },
      { "ScriptType", scriptType },
      { "ScriptCategory", scriptCategory },
      { "ScriptExecutionContext", scriptExecutionContext }
    });

    var lockOpts = CreateKeyedLockOptions
      .WithMethodInvocationCallerName()
      .WithDefaultEventFilter()
      .WithoutWatchdogConfig()
      .WithInputTokenCancellationStrategy(KeyedLockOptions.TokenStrategy.PropagateInputTokenCancellation);

    using var lockHandle = await _keyedLocker.LockAsync("LaunchPowershellEditorServiceProcess", eventHandler: null, lockOpts, cancellationToken, lockWaitTimeout: _launchSubprocessTimeout * 2);

    if (_editorServicesProcesses.ContainsKey(terminalId))
    {
      _logger.LogWarning("Request for Editor services for {terminalId} is already running. This is unusual.", terminalId);
      var ex = new StartEditorServicesException("Unable to start Editor services process for this terminal, as one is already running. Consider refreshing the page if this persists.");
      throw ex;
    }

    var currentUser = _userService.GetCurrentUser();

    var subProcessLimit = _editorServicesOptions.Value.SubProcessLimit;
    if (subProcessLimit != -1 && _editorServicesProcesses.Count >= subProcessLimit)
    {
      if (_cachedAppPrefs.Value.DisconnectLeastActiveEditorServiceWhenLimitReached)
      {
        var leastActiveTerminal = _editorServiceInterceptors.OrderByDescending(kvp => kvp.Value.Interceptor.TimeSinceLastActivity).First();
        _logger.LogInformation("Disconnecting least active terminal {TerminalId} to make room for new terminal", leastActiveTerminal.Key);

        await leastActiveTerminal.Value.Interceptor.ShowBrowserMessage(MessageType.Warning, _disconnectLeastActiveMessage);
        await leastActiveTerminal.Value.RemoteTermination(_disconnectLeastActiveMessage);

        await LaunchProcess(currentUser, terminalId, scriptId, scriptType, scriptCategory, scriptExecutionContext, lockHandle.CancellationToken);
        return;
      }

      var sb = new StringBuilder();

      sb.AppendLine($"The maximum resource limit of {subProcessLimit} running editor services has been reached, please try again later.");
      sb.AppendLine("If you have inactive Script Editors open, consider closing them and reloading the page to regain intellisense.");

      // We only want to show the list of running terminals to admins since it contains peoples name and email
      if (currentUser.IsAdmin)
      {
        sb.AppendLine("The following terminals are currently running:");

        foreach (var (terminal, (user, process)) in _editorServicesProcesses)
        {
          var interceptorFound = _editorServiceInterceptors.TryGetValue(terminal, out var connection);
          var lastActivity = (interceptorFound) ? connection.Interceptor.TimeSinceLastActivity : TimeSpan.FromSeconds(-1);

          sb.AppendLine($"  {user.DisplayName}({user.Email ?? "Email Unavailable"}) => {terminal} [LastActivity: {(int)lastActivity.TotalSeconds}s ago, Uptime: {process.ProcessUptime.ToString("d'd 'hh'h 'mm'm 'ss's'")}]");
        }
        sb.AppendLine("To have the least active terminal session be disconnected automatically to service a new request, you can enable it in the preferences under the 'Script Editor' section.");
      }
      var ex = new StartEditorServicesException(sb.ToString());
      _logger.LogError(ex, "Unable to start Editor Service for requested {TerminalId} as the sub-process limit has been reached", terminalId);
      throw ex;
    }

    await LaunchProcess(currentUser, terminalId, scriptId, scriptType, scriptCategory, scriptExecutionContext, lockHandle.CancellationToken);
  }

  private async Task LaunchProcess(User user, Guid terminalId, int? scriptId, DatabaseType? scriptType, ScriptCategory scriptCategory, ScriptExecutionContext scriptExecutionContext, CancellationToken token)
  {
    var psesProcess = await EditorServicesProcess.LaunchAsync(scriptId, scriptType, scriptCategory, scriptExecutionContext, _editorServicesOptions.Value, token);
    _editorServicesProcesses[terminalId] = (user, psesProcess);
  }

  public async Task ConnectLanguageServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken)
  {
    if (!_editorServicesProcesses.TryGetValue(terminalId, out var proc))
    {
      var msg = $"Editor services for terminal {terminalId} not found. Cannot connect language services.";
      var ex = new InvalidOperationException(msg);
      _logger.LogError(ex, "Unable to find Editor Service for requested {TerminalId}", terminalId);
      throw ex;
    }
    var editorProcess = proc.Process;
    try
    {
      await using (editorProcess)
      {
        var interceptorHook = CreateInterceptorHook(editorProcess, webSocket, terminalId);

        await _languageServiceConnectionHandler.ConnectEditorServicesPipeToWebsocketAsync(webSocket, editorProcess.PipeName, interceptorHook, cancellationToken);
      }
    }
    finally
    {
      _editorServicesProcesses.TryRemove(terminalId, out _);
    }
  }

  public Task ConnectDebugServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken)
  {
    // The out-of-process language service implementation currently only supports a single pipe
    // for the language service. Debug adapter support would require significant architectural changes
    // to support separate debug service pipes in the EditorServicesProcess class.
    throw new NotImplementedException(
      "Debug adapter connection is not currently supported in out-of-process mode. " +
      "Please use in-process mode for debugging functionality.");
  }

  private Func<LanguageServerInterceptor, CancellationToken, Task> CreateInterceptorHook(EditorServicesProcess editorServicesProcess, WebSocket webSocket, Guid terminalId)
  {
    return async (interceptor, token) =>
    {
      try
      {
        var remoteTerminationFunc = new Func<string, Task>(async (reason) =>
        {
          await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, reason, CancellationToken.None);
        });

        _editorServiceInterceptors[terminalId] = (interceptor, remoteTerminationFunc);

        using var psesLogContext = _logger.BeginScope("PSES Sub-Process for {TerminalId}", terminalId);

        using var processOutputConsumerLink =
          editorServicesProcess.LinkToProcessOutput(
            new ActionBlock<string>(async output =>
            {
              if (token.IsCancellationRequested)
              {
                return;
              }
              _logger.LogInformation("PSES Log: {Log}", output);
              if (_editorServicesOptions.Value.EnableSubProcessLoggingToBrowser)
              {
                await interceptor.LogBrowserTerminal(MessageType.Log, output);
              }
            }
          ));

        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(5));

        DateTime? warnedAt = null;

        while (await timer.WaitForNextTickAsync(token))
        {
          // Do nothing if we are not configured to have an idle timeout
          if (_idleTimeout <= TimeSpan.Zero) continue;

          // Warn the user of pending timeout if they have met/exceeded the idle timeout threshold
          if (interceptor.TimeSinceLastActivity >= _idleTimeout && warnedAt is null)
          {
            warnedAt = DateTime.UtcNow;

            await interceptor.LogBrowserTerminal(MessageType.Info, _idleTimeoutMessage);
            await interceptor.ShowBrowserMessage(MessageType.Warning, _idleTimeoutMessage);

            continue;
          }
          // Reset the warning if the user has done something in the editor
          if (warnedAt is not null && interceptor.LastActivity > warnedAt)
          {
            warnedAt = null;

            await interceptor.LogBrowserTerminal(MessageType.Info, _idleTimeoutResetMessage);
            continue;
          }
          // Close session if the user has been warned, and they still haven't done anything after 1 minute.
          if (warnedAt is { } timeWarned && DateTime.UtcNow - timeWarned >= TimeSpan.FromMinutes(1))
          {
            await interceptor.ShowBrowserMessage(MessageType.Info, _idleTimeoutReachedMessage);
            await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Idle timeout reached", CancellationToken.None);
          }
        }
      }
      finally
      {
        _editorServiceInterceptors.TryRemove(terminalId, out _);
      }
    };
  }

  public Task StartAsync(CancellationToken cancellationToken)
  {
    return Task.CompletedTask;
  }

  public async Task StopAsync(CancellationToken cancellationToken)
  {
    var processKillTasks = _editorServicesProcesses.Values.Select(p => p.Process.DisposeAsync().AsTask());

    await Task.WhenAll(processKillTasks);
  }
}

public class LanguageServiceConnectionHandler
{
  private readonly ILogger<LanguageServiceConnectionHandler> _logger;
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly Func<SoftwareDbContext> _globalCtxFactory;

  public LanguageServiceConnectionHandler(ILogger<LanguageServiceConnectionHandler> logger, Func<ImmybotDbContext> ctxFactory, Func<SoftwareDbContext> globalCtxFactory)
  {
    _logger = logger;
    _ctxFactory = ctxFactory;
    _globalCtxFactory = globalCtxFactory;
  }

  public async Task ConnectEditorServicesPipeToWebsocketAsync(WebSocket webSocket, string psesPipeName, Func<LanguageServerInterceptor, CancellationToken, Task>? interceptorHook = null, CancellationToken cancellationToken = default)
  {
    var formatter = new JsonMessageFormatter();
    // responses MUST BE formatted camelCase
    formatter.JsonSerializer.ContractResolver = new CamelCasePropertyNamesContractResolver();
    await using var namedPipeClientStream = await ConnectNamedPipe(psesPipeName, cancellationToken);
    using var editorServicesRpc = new JsonRpc(namedPipeClientStream);
    await using var browserWebSocketRequestHandler = new WebSocketMessageHandler(webSocket, formatter);
    using var browserJsonRpc = new JsonRpc(browserWebSocketRequestHandler);
    var interceptor = new LanguageServerInterceptor(editorServicesRpc, browserJsonRpc, _ctxFactory, _globalCtxFactory, psesPipeName);
    try
    {
      browserJsonRpc.CancelLocallyInvokedMethodsWhenConnectionIsClosed = true;

      // This allows the browser (MonacoLanguageClient) to invoke methods in PowerShell Editor Services
      browserJsonRpc.AddRemoteRpcTarget(editorServicesRpc);

      // This allows the PowerShell Editor Services to invoke methods in the browser
      editorServicesRpc.AddRemoteRpcTarget(browserJsonRpc);

      // Intercepting initialization to adjust parameters we don't want the frontend to control like working directory
      browserJsonRpc.AddLocalRpcTarget(interceptor);

      // local rpc method to capture when the monaco requests a definition for a particular script.
      // we intercept it here in case monaco cannot find it, and we look it up in our database
      editorServicesRpc.StartListening();
      browserJsonRpc.StartListening();
      try
      {
        await interceptor.LogBrowserTerminal(MessageType.Info, "You are connected to Editor Services. Intellisense ready.");
        using var interceptorCts = new CancellationTokenSource();
        var interceptorHookTask = interceptorHook?.Invoke(interceptor, interceptorCts.Token) ?? Task.CompletedTask;
        try
        {
          // Waits here until the session is over
          await Task.WhenAny(browserJsonRpc.Completion, editorServicesRpc.Completion);
        }
        finally
        {
          // Inform the interceptor task to exit if it is still running
          await interceptorCts.CancelAsync();
          try
          {
            await interceptorHookTask;
          }
          catch (Exception)
          {
            // Swallow exceptions from the hook task if it throws any, we just want to ensure its fully exited before the token source is disposed.
          }
        }
      }
      catch (Exception ex)
      {
        try
        {
          await interceptor.LogBrowserTerminal(MessageType.Error, ex.ToString());
        }
        catch (ConnectionLostException ex1)
        {
          _logger.LogError(new AggregateException(ex, ex1),
            "Connection dropped while attempting to write an exception to Monaco language client");
        }
      }
    }
    finally
    {
      if (interceptor.IsInitialized && !editorServicesRpc.Completion.IsCompleted)
      {
        // Browser Closed unexpectedly, but we still need to shut down editor services
        await editorServicesRpc.InvokeAsync(GeneralNames.Shutdown, CancellationToken.None);
        await editorServicesRpc.NotifyAsync(GeneralNames.Exit);
      }
    }
  }

  internal static async Task<NamedPipeClientStream> ConnectNamedPipe(string pipeName,
  CancellationToken cancellationToken
  )
  {
    pipeName = Path.GetFileName(pipeName);
    var powerShellEditorServicesNamedPipeStream = new NamedPipeClientStream(".",
      pipeName,
      PipeDirection.InOut,
      PipeOptions.Asynchronous);
    await powerShellEditorServicesNamedPipeStream.ConnectAsync(cancellationToken);

    return powerShellEditorServicesNamedPipeStream;
  }
}
