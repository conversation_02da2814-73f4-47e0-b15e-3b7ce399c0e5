using System.Collections;
using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Net.WebSockets;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Windows.PowerShell.ScriptAnalyzer;
using MonoMod.RuntimeDetour;
using JsonNamingPolicy = System.Text.Json.JsonNamingPolicy;
using JsonSerializer = System.Text.Json.JsonSerializer;
using JsonSerializerOptions = System.Text.Json.JsonSerializerOptions;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Domain.Helpers;
using Microsoft.PowerShell;

namespace Immybot.Backend.Application.Services;

/// <summary>
/// The purpose of this service is to bridge websockets to the named pipes
/// exposed by PowerShell Editor Services
///
/// This includes intercepting JSON-RPC requests to make them compatible or more contextually relevant to ImmyBot
/// and fixing encoding issues between the typescript debug adapter client and PowerShell Editor Services Debugger
/// </summary>
internal class LanguageService(
  IInitialSessionStateFactory _initialSessionStateFactory,
  IImmyCancellationManager _immyCancellationManager,
  LanguageServiceConnectionHandler _languageServiceConnectionHandler,
  ILogger<LanguageService> _logger) : ILanguageService, ILanguageServiceSpawner
{
  private static readonly JsonSerializerOptions _serializerOpts =
    new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };

  private readonly ConcurrentDictionary<Guid, string> languageServerPipenames = new();
  private readonly ConcurrentDictionary<Guid, string> debugServerPipenames = new();

  private sealed record LanguageServerPipeNames(string LanguageServicePipeName, string DebugServicePipeName = "");

  [UnsafeAccessor(UnsafeAccessorKind.Field, Name = "_commandInfoCache")]
  public extern static ref ConcurrentDictionary<CommandInfoCache.CommandLookupKey, Lazy<CommandInfo>> _commandInfoCache(CommandInfoCache @this);

  [UnsafeAccessor(UnsafeAccessorKind.Field, Name = "_runspacePool")]
  public extern static ref RunspacePool _runspacePool(CommandInfoCache @this);

  public void CreateNewPipeNames(Guid terminalId)
  {
    // Use shorter pipe names to avoid Unix domain socket path length limit (104 chars)
    languageServerPipenames.AddOrUpdate(terminalId, $"PSES_L_{Guid.NewGuid().ToString("N")[..8]}", (_, _) => $"PSES_L_{Guid.NewGuid().ToString("N")[..8]}");
    debugServerPipenames.AddOrUpdate(terminalId, $"PSES_D_{Guid.NewGuid().ToString("N")[..8]}", (_, _) => $"PSES_D_{Guid.NewGuid().ToString("N")[..8]}");
  }
  public string GetLanguageServicePipeName(Guid terminalId) => languageServerPipenames.GetOrAdd(terminalId, $"PSES_L_{terminalId.ToString("N")[..8]}");
  public string GetDebugServicePipeName(Guid terminalId) => debugServerPipenames.GetOrAdd(terminalId, $"PSES_D_{terminalId.ToString("N")[..8]}");

  public async Task StartEditorServices(
    Guid terminalId,
    int? scriptId,
    DatabaseType? scriptType,
    ScriptCategory scriptCategory,
    ScriptExecutionContext scriptExecutionContext,
    CancellationToken cancellationToken)
  {
    CreateNewPipeNames(terminalId);


    await StartEditorServices(
      languageServerPipeName: GetLanguageServicePipeName(terminalId),
      sessionInfoFilePath: Path.GetTempFileName(),
      terminalId,
      scriptId,
      scriptType,
      scriptCategory,
      scriptExecutionContext,
      cancellationToken);
  }

  public async Task<Guid?> StartEditorServices(
    string languageServerPipeName,
    string sessionInfoFilePath,
    Guid terminalId,
    int? scriptId,
    DatabaseType? scriptType,
    ScriptCategory scriptCategory,
    ScriptExecutionContext scriptExecutionContext,
    CancellationToken cancellationToken)
  {
    // This script cancellationToken is used to facilitate cancellation of the script from the
    // inside-out. A variable will be supplied to the script providing the cancellationId, which,
    // it can then use to request itself to be terminated. This was added primarily for the
    // Invoke-AtomicCommand and Invoke-CachedCommand, which need the ability to forcefully
    // terminate themselves in the situations of cancellation or watchdog deadlock detection.
    var selfCancellationId = Guid.NewGuid();
    var scriptSelfCancellationToken = _immyCancellationManager
      .GetOrCreateScriptCancellationToken(selfCancellationId);

    try
    {
      // Different Cmdlets/Functions/Variables get included based on
      //  script properties like ScriptCategory and ScriptExecutionContext
      // The scripts in the editor don't always exist in the database, and even if they do
      //  we want Intellisense to honor the options selected in the UI rather than the database.
      // To that end, we directly use the UI-selected properties here
      var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(
        scriptExecutionContext,
        scriptCategory,
        scriptType ?? DatabaseType.Local,
        dynamicProviderStoreId: null,
        canAccessMspResources: false,
        canAccessParentTenant: false);

      var iss = await _initialSessionStateFactory.CreateInitialSessionStateAsync(runspaceAttributes, cancellationToken);

      // remove default CancellationToken.None and add ours
      iss.Variables.Remove(SessionStateEntries.CancellationTokenVariableEntry.Name, null);
      iss.Variables.Add(SessionStateEntries.CancellationTokenVariableEntry
        .ToSessionStateVariableEntry(cancellationToken));

      iss.Variables.Add(SessionStateEntries.ScriptSelfCancellationIdVariableEntry
        .ToSessionStateVariableEntry(selfCancellationId));

      var host = new DefaultHost(CultureInfo.CurrentCulture, CultureInfo.CurrentUICulture);
      var hooks = new List<Hook>();

      // This is a workaround for the ARM architecture, which doesn't work with the hook library.
      // Metascripts will not work on ARM until this is resolved.
      if (RuntimeInformation.ProcessArchitecture == Architecture.X64)
      {
        // Hook InitialSessionState.CreateDefault2 so PSScriptAnalyzer.PssaCmdletAnalysisEngine returns our custom InitialSessionState
        // This is necessary because PSScriptAnalyzer.PssaCmdletAnalysisEngine creates its own RunspacePool using CreateDefault2 which is missing
        //  the Join-Path cmdlet as well as our custom functions and modules
        hooks.Add(new(
          source: () => InitialSessionState.CreateDefault2(),
          target: (Func<InitialSessionState> orig) => iss));

        // Hook PSScriptAnalyzer.CommandInfoCache constructor so we can create our own RunspacePool that uses the correct InitialSessionState
        hooks.Add(new(
          source: typeof(CommandInfoCache).GetConstructor([])!,
          target: (Action<CommandInfoCache> orig, CommandInfoCache commandInfoCache) =>
          {
            _commandInfoCache(commandInfoCache) = new();
            var pool = RunspaceFactory.CreateRunspacePool(1, 10, iss, host);
            pool.Open();
            _runspacePool(commandInfoCache) = pool;
          }));
      }

      var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(iss);
      var powershell = PowerShell.Create(runspace);

      powershell.Runspace.SessionStateProxy.SetVariable("VerbosePreference", ActionPreference.SilentlyContinue);
      powershell.Runspace.SessionStateProxy.SetVariable("DebugPreference", ActionPreference.SilentlyContinue);

      var scriptInvocation = powershell.Runspace.SessionStateProxy.InvokeCommand
        .GetCommand(MetascriptInvokerDefaults.StartEditorServicesScriptPath, CommandTypes.ExternalScript);

      if (!Directory.Exists(MetascriptInvokerDefaults.PSESBundledModulesPath))
      {
        throw new DirectoryNotFoundException($"{MetascriptInvokerDefaults.PSESBundledModulesPath} does not exist");
      }

      var debugServerPipeName = GetDebugServicePipeName(terminalId);

      powershell.AddCommand(scriptInvocation)
        .AddParameter("HostName", "monaco")
        .AddParameter("HostProfileId", "0")
        .AddParameter("HostVersion", "1.0.0")
        .AddParameter("LogPath", Path.Combine(Path.GetTempPath(), $"{languageServerPipeName}.log"))
        .AddParameter("LogLevel", "Normal") // Use "Diagnostic" for most verbose output
        .AddParameter("FeatureFlags", Array.Empty<string>())
        .AddParameter("BundledModulesPath", MetascriptInvokerDefaults.PSESBundledModulesPath)
        .AddParameter("SessionDetailsPath", sessionInfoFilePath)
        .AddParameter("LanguageServicePipeName", languageServerPipeName)
        .AddParameter("DebugServicePipeName", debugServerPipeName);
        // Removed LanguageServiceOnly to enable both language and debug services

      foreach (var command in powershell.Commands.Commands)
      {
        command.MergeMyResults(PipelineResultTypes.Error, PipelineResultTypes.Output);
      }

      var cancellationRegistration = scriptSelfCancellationToken.Register(() =>
      {
        _logger.LogInformation("Cancelling Start-EditorServices script");
        try
        {
          powershell.Stop();
        }
        catch (NullReferenceException)
        {
          // throws this if already stopped
        }
      });


      [SuppressMessage("ReSharper", "AccessToDisposedClosure")]
      void Handler(object? self, PSInvocationStateChangedEventArgs e)
      {
        if (e.InvocationStateInfo.State is
            PSInvocationState.Completed
            or PSInvocationState.Failed
            or PSInvocationState.Stopped)
        {
          powershell.InvocationStateChanged -= Handler;
          _logger.LogInformation("InvocationStateChanged: {State}", e.InvocationStateInfo.State);
          _immyCancellationManager.RemoveScriptCancellationToken(selfCancellationId);
          powershell.Dispose();
          runspace.Dispose();
          foreach (var hook in hooks)
            hook.Dispose();

          // otherwise can deadlock with powershell.Stop() in the registration
          Task.Run(() => cancellationRegistration.Dispose(), CancellationToken.None).Forget();
        }
      }
      powershell.InvocationStateChanged += Handler;

      var result = powershell.InvokeAsync();


      var pipeNames = await GetPipesFromFileAsync(sessionInfoFilePath, powershell, cancellationToken);
      if (pipeNames == null)
      {
        try
        {
          await result;
        }
        catch (CmdletInvocationException ex)
        {
          var errorMessage = ex.ErrorRecord.FullyQualifiedErrorId;
          _logger.LogError(ex, "Error starting editor services: {ErrorMessage}", errorMessage);
          return null;
        }
        catch (RuntimeException ex)
        {
          _logger.LogError(ex, "Error starting editor services");
        }

        if (powershell.HadErrors && powershell.Runspace.SessionStateProxy.GetVariable("error") is ArrayList errorArray)
        {
          foreach (var error in errorArray)
          {
            if (error?.ToString() is { } errorString)
              _logger.LogError("Error starting editor services: {PowershellErrorMessage}", errorString);
          }
        }

        throw new MetascriptException("Start-EditorServices pipes result is null - check the logs");
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Encountered an error inside {nameof(StartEditorServices)}");
      throw;
    }

    return selfCancellationId;
  }

  public static string GetScriptPath(Script script)
  {
    return $"inmemory://{script.ScriptType}/{script.ScriptCategory}s/{script.Name}" +
           (script.ScriptCategory == ScriptCategory.Module ? ".psm1" : ".ps1");
  }

  public async Task ConnectLanguageServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken)
  {
    var pipeName = GetLanguageServicePipeName(terminalId);
    await ConnectLanguageServices(webSocket, pipeName, cancellationToken);
  }

  public async Task ConnectLanguageServices(WebSocket webSocket, string pipeName, CancellationToken cancellationToken)
  {
    await _languageServiceConnectionHandler.ConnectEditorServicesPipeToWebsocketAsync(webSocket,
      pipeName,
      cancellationToken: cancellationToken);
  }

  public async Task ConnectDebugServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken)
  {
    var pipeName = GetDebugServicePipeName(terminalId);
    await ConnectDebugServices(webSocket, pipeName, cancellationToken);
  }

  public async Task ConnectDebugServices(WebSocket webSocket, string pipeName, CancellationToken cancellationToken)
  {
    await _languageServiceConnectionHandler.ConnectEditorServicesPipeToWebsocketAsync(webSocket,
      pipeName,
      cancellationToken: cancellationToken);
  }


  private async Task<LanguageServerPipeNames?> GetPipesFromFileAsync(
    string path,
    PowerShell powershell,
    CancellationToken stopProcessing)
  {
    // powershell (running the Start-EditorServices process) will write
    // the language server pipe names to a json file at path. We want to read
    // that file as soon as it's created so we can interact with the lang server

    var jsonString = "";

    // Poll the path for file contents by trying to read the file in a loop. If the read
    // fails, wait 100 ms and try again. Stop the loop if the powershell process
    // stops or if we get some contents
    do
    {
      stopProcessing.ThrowIfCancellationRequested();
      try
      {
        jsonString = await File.ReadAllTextAsync(path, stopProcessing);
      }
      catch (Exception)
      {
        // Normal, we are waiting for the file to be written.
      }

      await Task.Delay(100, stopProcessing);
    } while (string.IsNullOrEmpty(jsonString) && powershell.HadErrors == false);

    if (powershell.HadErrors)
    {
      foreach (var error in powershell.Streams.Error.ReadAll())
      {
        _logger.LogWarning("Start-EditorServices command error: {ErrorRecord}", error.ToString());
      }

      return null;
    }

    // jsonString has structure like:
    // {"status":"started","languageServiceTransport":"NamedPipe","languageServicePipeName":"/tmp/CoreFxPipe_PSES_1kvvshds.gww","debugServiceTransport":"NamedPipe","debugServicePipeName":"/tmp/CoreFxPipe_PSES_2abc3def.gww"}
    var sessionData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonString, _serializerOpts);
    if (sessionData == null || !sessionData.TryGetValue("languageServicePipeName", out var langPipeObj))
    {
      _logger.LogWarning(
        "Start-EditorServices command produced a pipe file with no languageServicePipeName; pipesObj: {PipesObj}",
        jsonString);
      return null;
    }

    var languageServicePipeName = langPipeObj?.ToString();
    var debugServicePipeName = sessionData.TryGetValue("debugServicePipeName", out var debugPipeObj)
      ? debugPipeObj?.ToString() ?? ""
      : "";

    if (string.IsNullOrEmpty(languageServicePipeName))
    {
      _logger.LogWarning("Language service pipe name is null or empty");
      return null;
    }

    // If pipe names have backslash (i.e. windows drive char) strip that off first
    if (languageServicePipeName.LastIndexOf('\\') is int i and > -1)
      languageServicePipeName = languageServicePipeName[(i + 1)..];

    if (!string.IsNullOrEmpty(debugServicePipeName) && debugServicePipeName.LastIndexOf('\\') is int j and > -1)
      debugServicePipeName = debugServicePipeName[(j + 1)..];

    return new LanguageServerPipeNames(languageServicePipeName, debugServicePipeName);
  }
}
