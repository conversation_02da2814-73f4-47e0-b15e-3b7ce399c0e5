using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Notifications;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Notifications.Converters;

public class AzureMultiCustomerPreconsentFailedNotificationConverter
  : IApplicationEventToNotificationConverter<AzureMultiCustomerPreconsentFailedEvent>
{
  private static readonly JsonSerializerOptions _jsonOpts = new()
  {
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
  };
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly INotificationsStore _notificationsStore;

  public AzureMultiCustomerPreconsentFailedNotificationConverter(
    Func<ImmybotDbContext> ctxFactory,
    INotificationsStore notificationsStore)
  {
    _ctxFactory = ctxFactory;
    _notificationsStore = notificationsStore;
  }

  public async Task<ICollection<Notification>?> Convert(
    AzureMultiCustomerPreconsentFailedEvent[] applicationEvents)
  {
    var applicationEvent = applicationEvents.FirstOrDefault();
    if (applicationEvent is null) return null;

    var partnerAzTenantPrincipalId = applicationEvent.PartnerTenantPrincipalId;

    // add notification of type AzureMultiCustomerPreconsentFailed for this failure

    await using var ctx = _ctxFactory();

    var tenant = await ctx.Tenants
      .AsNoTracking()
      .Where(t =>
        t.AzureTenantLink != null && t.AzureTenantLink.AzTenantId == partnerAzTenantPrincipalId &&
        !t.AzureTenantLink.ShouldLimitDomains)
      .Select(t => new { t.Name })
      .FirstOrDefaultAsync();

    var input = applicationEvent.Error.IsT0
      ? new AzureMultiCustomerPreconsentFailedNotificationInput(partnerAzTenantPrincipalId, applicationEvent.Error.AsT0?.Id, null)
      : new AzureMultiCustomerPreconsentFailedNotificationInput(partnerAzTenantPrincipalId, null, applicationEvent.Error.AsT1);

    if (tenant == null) return null;

    var notification = (await _notificationsStore
      .GetUnresolvedNotificationsOfType(
        NotificationType.AzureMultiCustomerPreconsentFailed,
        [partnerAzTenantPrincipalId.ToString()]))
      .FirstOrDefault();

    if (notification is null)
    {
      notification = new Notification
      {
        Id = Guid.NewGuid(),
        Type = NotificationType.AzureMultiCustomerPreconsentFailed,
        Title = "Azure Customer Pre-Consent Failed",
        ObjectId = partnerAzTenantPrincipalId.ToString(),
        Description = $"Pre-consent failed for one or more customers of {tenant.Name}",
        InputData = JsonSerializer.SerializeToElement(input, _jsonOpts),
        Severity = NotificationSeverity.Error,
        CreatedDate = applicationEvent.DateUTC,
        UpdatedDate = applicationEvent.DateUTC
      };
    }
    else
    {
      notification.UpdatedDate = applicationEvent.DateUTC;
      notification.InputData = JsonSerializer.SerializeToElement(input, _jsonOpts);
    }
    return new[] { notification };
  }
}
