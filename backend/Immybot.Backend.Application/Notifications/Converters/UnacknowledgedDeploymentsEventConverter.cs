using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Notifications;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Notifications.Converters;

public class UnacknowledgedDeploymentEventsConverter : IApplicationEventToNotificationConverter<UnacknowledgedDeploymentsEvent>
{
  private readonly Func<ImmybotDbContext> _ctxFactory;

  private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
  {
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
  };

  public UnacknowledgedDeploymentEventsConverter(Func<ImmybotDbContext> ctxFactory)
  {
    _ctxFactory = ctxFactory;
  }

  public Task<ICollection<Notification>?> Convert(
    UnacknowledgedDeploymentsEvent[] applicationEvents)
  {
    var applicationEvent = applicationEvents.FirstOrDefault();
    if (applicationEvent is null) return Task.FromResult<ICollection<Notification>?>(null);

    // fetch the latest unresolved notification for this event
    using var ctx = _ctxFactory();
    var notification = ctx.Notifications
      .AsNoTracking()
      .OrderByDescending(a => a.CreatedDate)
      .FirstOrDefault(a => !a.Resolved && a.Type == NotificationType.UnacknowledgedDeployments && a.Acknowledgement == NotificationAcknowledgement.Unacknowledged);

    // nothing to do if the count is zero and we don't have any notifications to resolve
    if (notification is null && applicationEvent.Count is 0) return Task.FromResult<ICollection<Notification>?>(null);

    if (notification is not null)
    {
      var existingCount = notification.InputData
        ?.GetProperty("count")
        .GetInt32();

      // if the counts are the same, do nothing
      if (applicationEvent.Count == existingCount) return Task.FromResult<ICollection<Notification>?>(null);

      // if the new count is zero, then resolve and return
      if (applicationEvent.Count is 0)
      {
        notification.UpdatedDate = DateTime.UtcNow;
        notification.Resolved = true;
        notification.Description += " (Resolved)";
        return Task.FromResult<ICollection<Notification>?>(new[] { notification });
      }

      // the count was updated, update the notification
      notification.InputData = JsonSerializer.SerializeToElement(
        new UnacknowledgedDeploymentNotificationInput(applicationEvent.Count),
        _jsonSerializerOptions);
      notification.UpdatedDate = DateTime.UtcNow;
      return Task.FromResult<ICollection<Notification>?>(new[] { notification });
    }

    // create a notification from an access requested event
    notification = new Notification
    {
      Id = Guid.NewGuid(),
      Type = NotificationType.UnacknowledgedDeployments,
      Title = "Deployment Approval Required",
      Description = $"There are {applicationEvent.Count} global deployments that require approval.",
      InputData = JsonSerializer.SerializeToElement(new UnacknowledgedDeploymentNotificationInput(applicationEvent.Count), _jsonSerializerOptions),
      Severity = NotificationSeverity.Info,
      CreatedDate = DateTime.UtcNow,
      UpdatedDate = DateTime.UtcNow
    };

    return Task.FromResult<ICollection<Notification>?>(new[] { notification });
  }
}
