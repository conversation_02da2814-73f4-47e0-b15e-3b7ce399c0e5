using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface;

public interface ILocalToGlobalMigrator
{
  /// <summary>
  /// Moves a local software to global software and updates all references
  /// </summary>
  /// <param name="softwareId"></param>
  Task<int> MigrateLocalSoftwareToGlobalSoftware(int softwareId, User migratedByUser);

  /// <summary>
  /// Moves a local maintenance task to global maintenance task and updates all references
  /// </summary>
  /// <param name="softwareId"></param>
  int MigrateLocalTaskToGlobalTask(int taskId, User migratedByUser);

  /// <summary>
  /// Moves a local script to a global script and updates all references
  /// </summary>
  /// <param name="scriptId"></param>
  int MigrateLocalScriptToGlobalScript(int scriptId, User migratedByUser);

  /// <summary>
  /// Returns all objects that would be moved as part of this migration
  /// </summary>
  /// <param name="softwareId"></param>
  /// <returns></returns>
  MigrationPreviewResponse MigrateLocalSoftwareToGlobalSoftwareWhatIf(int softwareId);

  /// <summary>
  /// Returns all objects that would be moved as part of this migration
  /// </summary>
  /// <param name="softwareId"></param>
  /// <returns></returns>
  MigrationPreviewResponse MigrateLocalTaskToGlobalTaskWhatIf(int taskId);

  /// <summary>
  /// Returns all objects that would be moved as part of this migration
  /// </summary>
  /// <param name="softwareId"></param>
  /// <returns></returns>
  MigrationPreviewResponse MigrateLocalScriptToGlobalScriptWhatIf(int scriptId);

}
