using Immybot.Backend.Application.Interface.Commands.Payloads.MaintenanceTasks;
using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface;

public interface IEntityValidator
{
  ValidationResult ValidateScript(IScriptDetailsBase script, Script? existing = null, bool ignoreSyntaxErrors = false);

  ValidationResult ValidateMaintenanceTask(IMaintenanceTaskBaseWithParameters task, MaintenanceTask? existing = null);
}
