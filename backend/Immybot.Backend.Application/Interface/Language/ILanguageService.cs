using System;
using System.Net.WebSockets;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.MetaScripts;

public interface ILanguageService
{
  Task StartEditorServices(Guid terminalId,
    int? scriptId,
    DatabaseType? scriptType,
    ScriptCategory scriptCategory,
    ScriptExecutionContext scriptExecutionContext,
    CancellationToken cancellationToken);
  Task ConnectLanguageServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken);
  Task ConnectDebugServices(WebSocket webSocket, Guid terminalId, CancellationToken cancellationToken);
}
