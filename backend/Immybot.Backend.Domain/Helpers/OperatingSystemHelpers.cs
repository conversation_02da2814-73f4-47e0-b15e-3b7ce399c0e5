using System;
using System.Linq;

namespace Immybot.Backend.Domain.Helpers;

public static class OperatingSystemHelpers
{
  private static readonly string[] _valuesToExclude =
  [
    "mac os",
    "macos",
    "linux"
  ];
  public static bool IsWindowsOperatingSystem(string? osName)
  {
    if (osName is null) return false;
    if (Array.Exists(_valuesToExclude, v => osName.Contains(v, StringComparison.OrdinalIgnoreCase))) return false;
    return osName.Contains("win", StringComparison.OrdinalIgnoreCase) || osName.Contains("microsoft", StringComparison.OrdinalIgnoreCase);
  }
}
