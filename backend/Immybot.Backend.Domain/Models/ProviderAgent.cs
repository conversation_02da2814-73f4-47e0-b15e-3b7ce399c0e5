using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text.Json;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Providers;
using SemanticVersion = NuGet.Versioning.SemanticVersion;

namespace Immybot.Backend.Domain.Models;

/// <summary>
/// Represents an agent of a specific provider running on a device
/// </summary>
public class ProviderAgent : IProviderAgentDetails, ISoftDelete
{
  public ProviderAgent()
  {
    IdentificationFailures = new HashSet<AgentIdentificationFailure>();
    IdentificationLogs = new HashSet<AgentIdentificationLog>();
  }

  public int Id { get; set; }
  public int ProviderLinkId { get; set; }
  public required string ExternalClientId { get; set; }
  public required string ExternalAgentId { get; set; }
  public int? ComputerId { get; set; }

  /// <summary>
  /// This field should only be used when the integration supports online status
  /// </summary>
  public bool IsOnline { get; set; }

  [Obsolete(
    "This field is deprecated and will be removed in a future version. The integration now determines whether it supports online status.")]
  public bool SupportsOnlineStatus { get; set; } = true;
  public bool SupportsRunningScripts { get; set; }
  public SemanticVersion? AgentVersion { get; set; }
  public DateTime DateAddedUTC { get; set; }
  public DateTime LastUpdatedUTC { get; set; }
  public JsonElement? InternalData { get; set; }
  public DeviceDetails DeviceDetails { get; set; } = new();
  public AgentOnboardingOptions OnboardingOptions { get; set; } = new();
  public bool RequireManualIdentification { get; set; }

  public ProviderLink? ProviderLink { get; set; }
  public Computer? Computer { get; set; }
  public ProviderClient? ProviderClient { get; set; }
  public ICollection<AgentIdentificationFailure> IdentificationFailures { get; }
  public ICollection<AgentIdentificationLog> IdentificationLogs { get; }
  public bool IsMemberOfInitialDeviceSync { get; set; }

  private IdentificationDetails? _identificationDetails;
  public IdentificationDetails GetIdentificationDetails()
  {
    if (_identificationDetails is not null) return _identificationDetails;

    _identificationDetails = new();

    return _identificationDetails;
  }

  public DateTime? DeletedAt { get; set; }
  public string? DeletedReason { get; set; }
}

[DataContract]
public class DeviceDetails
{
  // These fields provided by the provider are used to help match agents to
  // immy computers on the frontend. Once a computer is matched, immy will
  // re-fetch these values along with other data
  [DataMember] public string? OperatingSystemName { get; set; }
  [DataMember]
  public string? Manufacturer { get; set; }

  [DataMember] public string? DeviceName { get; set; }
  [DataMember] public string? SerialNumber { get; set; }

  // if these fields are present but the ComputerId is not, then this agent has already been
  // identified and does not need to have a script run against it to determine these values
  // reasons for it still not having been linked to a computer are:
  // - requires manual decision,
  // - failed to dedupe,
  // - failed to generate a new device id if the "Keep both" option is selected, etc
  // - the computer is offline
  // - an error occurs running the identification script
  [DataMember]
  public Guid? DeviceId { get; set; }
  [DataMember]
  public DateTime? OSInstallDateUTC { get; set; }

  [DataMember] public string? MachineId { get; set; }
  [DataMember] public bool IsSandbox { get; set; }
  [DataMember]
  public string? Domain { get; set; }
  [DataMember]
  public string? AzureTenantId { get; set; }
  public TrustedManufacturer? GetTrustedManufacturer() => ManufacturerHelpers.GetTrustedManufacturer(Manufacturer);

  [DataMember] public int[]? ChassisTypes { get; set; }
}


public class IdentificationDetails
{
  public int? LinkedToTenantId { get; set; }
  public bool? ExistingComputerHasOtherOnlineAgents { get; set; }
}
