using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Domain.Oauth;

namespace Immybot.Backend.Domain.Models;
public record AzureMessage(string Message, AzureErrorLogItem? Error = null, bool IsErrorNonFatal = false);
public record AzureCustomerPreconsentResult(
  string CustomerPrincipalId,
  IList<AzureMessage> Messages);
public record ODataErrorDetails(string Code, DateTimeOffset? Date, string RequestId, string ClientRequestId, string OdataType);
public record MsalErrorDetails(string Error, string ErrorDescription, int[] ErrorCodes, string Timestamp, string TraceId, string CorrelationId);

public record MissingAccessTokenDetails(string? TenantId, string RequiredScopes);

public record ApiResponseErrorContent(string? HttpMethod, string? Uri, int StatusCode, string Content);
public record AzureTenantTokenCredentialDetails(
  string TenantPrincipalId,
  string? PartnerPrincipalId,
  AccessTokenSource? GotAccessTokenFrom,
  string? ResolvedClientId,
  bool OnlyUsePartnerCenterRefresh,
  string? TenantPreferredAzureClientId,
  AzurePermissionLevel2? TenantAzurePermissionLevel);

public record AzureError(string Message,
  ODataErrorDetails? ODataError = null,
  MsalErrorDetails? MsalError = null,
  MissingAccessTokenDetails? MissingAccessToken = null,
  ApiResponseErrorContent? ApiResponseContent = null,
  ApiResponseErrorContent? PartnerCenterApiResponseDetails = null,
  Oauth2AccessTokenErrorResponse? OauthAccessTokenErrorResponse = null,
  AzureTenantTokenCredentialDetails? CredentialDetails = null)
{
  public string FormattedErrorMessage => this switch
  {
    { ODataError: { } err } => FormatODataErrorMessage(err),
    { MsalError: { } m } => FormatMsalErrorMessage(m),
    { ApiResponseContent: { } a } => FormatApiResponseMessage(a),
    { PartnerCenterApiResponseDetails: { } p } => FormatApiResponseMessage(p),
    { OauthAccessTokenErrorResponse: { } o } => FormatOauthAccessTokenErrorResponse(o),
    _ => Message,
  };

  private string FormatOauthAccessTokenErrorResponse(Oauth2AccessTokenErrorResponse err)
  {
    var parts = new[]
    {
      err.Error != null ? $"Code: {err.Error}" : null,
      $"Message: {Message}",
      err.ErrorDescription != null ? $"Error Description: {err.ErrorDescription}" : null,
      err.TraceId != null && !Message.Contains("Trace ID")
        ? $"Trace ID: {err.TraceId}"
        : null,
      err.CorrelationId != null && !Message.Contains("Correlation ID")
        ? $"Correlation ID: {err.CorrelationId}"
        : null,
      err.Timestamp != null && !Message.Contains("Timestamp: ")
        ? $"Timestamp: {err.Timestamp}"
        : null,
      err.ErrorCodes != null && err.ErrorCodes.Length > 0
        ? $"Error Codes: {string.Join(", ", err.ErrorCodes)}"
        : null,
    }.Where(s => !string.IsNullOrEmpty(s));
    return string.Join('\n', parts);
  }

  private string FormatODataErrorMessage(ODataErrorDetails err)
  {
    var parts = new[]
    {
      err.Code != null ? $"Code: {err.Code}" : null,
      $"Message: {Message}",
      err.Date != null ? $"Date: {err.Date}" : null,
      err.RequestId != null ? $"Request ID: {err.RequestId}" : null,
      err.ClientRequestId != null ? $"Client Request ID: {err.ClientRequestId}" : null,
    }.Where(s => !string.IsNullOrEmpty(s));
    return string.Join('\n', parts);
  }

  private string FormatMsalErrorMessage(MsalErrorDetails err)
  {
    var parts = new[] {
      err.Error != null ? $"Code: {err.Error}" : null,
      $"Message: {Message}",
      err.TraceId != null && !Message.Contains("Trace ID")
        ? $"Trace ID: {err.TraceId}"
        : null,
      err.CorrelationId != null && !Message.Contains("Correlation ID")
        ? $"Correlation ID: {err.CorrelationId}"
        : null,
      err.Timestamp != null && !Message.Contains("Timestamp: ")
        ? $"Timestamp: {err.Timestamp}"
        : null,
      err.ErrorCodes != null && err.ErrorCodes.Length > 0
        ? $"Error Codes: {string.Join(", ", err.ErrorCodes)}"
        : null,
    }.Where(s => !string.IsNullOrEmpty(s));
    return string.Join('\n', parts);
  }

  private string FormatApiResponseMessage(ApiResponseErrorContent err)
  {
    var parts = new[]
    {
      $"Message: {Message}",
      $"Request: {err.HttpMethod} {err.Uri}",
      $"Response: {err.StatusCode}",
      $"Response Content: {err.Content}",
    }.Where(s => !string.IsNullOrEmpty(s));
    return string.Join('\n', parts);
  }
}
