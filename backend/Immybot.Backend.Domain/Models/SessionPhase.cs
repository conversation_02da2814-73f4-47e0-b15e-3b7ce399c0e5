using System;
using System.Collections.Generic;

namespace Immybot.Backend.Domain.Models;

public class SessionPhase
{
  public SessionPhase()
  {
    Logs = new HashSet<SessionLog>();
  }
  public int Id { get; set; }
  public string PhaseName { get; set; } = null!;
  public ActionProgressPhaseName? ActionProgressPhaseName { get; set; }
  public int MaintenanceSessionId { get; set; }
  public int MaintenanceSessionStageId { get; set; }
  public int? MaintenanceActionId { get; set; }

  public SessionPhaseStatus Status { get; set; }

  // for progress logs: indicates the progress of this log as an integer in % complete
  public decimal? ProgressPercentComplete { get; set; }

  // for progress logs: indicates the current status of this progress log
  public string? ProgressStatus { get; set; }

  // for progress logs: indicates whether progress has been completed
  public bool ProgressCompleted { get; set; } = false;

  public MaintenanceSession? MaintenanceSession { get; set; }
  public MaintenanceSessionStage? MaintenanceSessionStage { get; set; }
  public MaintenanceAction? MaintenanceAction { get; set; }
  public DateTime? DateStartedUtc { get; set; }
  public DateTime? DateCompletedUtc { get; set; }
  public virtual ICollection<SessionLog> Logs { get; }
}

public enum ActionProgressPhaseName
{
  // Agent Update Stage Phases
  DetectInstalledAgentVersion = 0,
  UpdateAgentVersion = 1,
  VerifyAgentVersionUpdated = 2,

  // Resolution Stage Phases


  // Detection Stage Phases
  DetectInstalledVersion = 200,
  DetermineDesiredVersion = 201,
  TestSoftware = 202,
  CheckForSoftwareLicense = 203,
  AddConfigTask = 204,
  TestTask = 205,
  MonitorTask = 206, // Can also be in execution
  AuditTask = 207, // Can also be in execution

  // Execution Stage Phases
  FetchDesiredVersion = 301,
  EnforceTask = 302,
  ApplyWindowsPatch = 305,
  DownloadConfigTaskFiles = 306,
  FetchDependentVersion = 307,
  InstallDependentVersion = 308,
  InstallSoftware = 309,
  RunUpgradeSoftwareScript = 310,
  UninstallSoftware = 311,
  RepairSoftware = 313,
  DownloadInstaller = 314,
  PostUninstallSoftware = 315,
  PostInstallSoftware = 316,
  TestTaskBeforeExecution = 317,
  TestSoftwareAfterExecution = 318,
  VerifyTask = 319,
  VerifySoftwareIsInDesiredState = 320,
}
