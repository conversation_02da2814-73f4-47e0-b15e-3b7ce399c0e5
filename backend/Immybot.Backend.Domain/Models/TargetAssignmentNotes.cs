using Immybot.Backend.Domain.Interfaces;

namespace Immybot.Backend.Domain.Models;

public class TargetAssignmentNotes : AuditableDateEntity, IAuditableLoggableEntity
{
  public int Id { get; set; }
  public required int TargetAssignmentId { get; set; }
  public required string UpdatedByName { get; set; }
  public required string Notes { get; set; }
  public TargetAssignment? TargetAssignment { get; set; }
}
