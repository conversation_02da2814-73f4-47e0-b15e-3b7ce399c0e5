using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;
[DebuggerDisplay("{Name} ({Identifier})")]
public abstract class Software : AuditableUserEntity, ISharedSoftwareRelationships, ISharedSoftwareScriptTypes, ISharedSoftwareProperties, IAuditableLoggableEntity
{
  public abstract string Identifier { get; }
  public abstract SoftwareType SoftwareType { get; }

  [AuditObjectName]
  public string Name { get; set; } = null!;
  public SoftwareLicenseRequirement LicenseRequirement { get; set; }
  public int InstallOrder { get; set; }
  public bool Hidden { get; set; }
  public string? SoftwareTableName { get; set; }
  public DetectionMethod DetectionMethod { get; set; }
  /// <summary>
  /// is expected to be set when DetectionMethod is SoftwareTable
  /// </summary>
  public SoftwareTableNameSearchMode? SoftwareTableNameSearchMode { get; set; }

  /// <summary>
  /// Indicates whether this is a provider agent software.
  /// The value should be a known integration type id so we can link it to a specific integration.
  /// </summary>
  public Guid? AgentIntegrationTypeId { get; set; }

  public Script? DetectionScript { get; set; }
  public int? DetectionScriptId { get; set; }
  public DatabaseType? DetectionScriptType { get; set; }

  public Script? DownloadInstallerScript { get; set; }
  public int? DownloadInstallerScriptId { get; set; }
  public DatabaseType? DownloadInstallerScriptType { get; set; }

#pragma warning disable S1133
  [Obsolete("no longer used")]
#pragma warning restore S1133
  public Script? AutoUpdateScript { get; set; }

#pragma warning disable S1133
  [Obsolete("no longer used")]
#pragma warning restore S1133
  public int? AutoUpdateScriptId { get; set; }

#pragma warning disable S1133
  [Obsolete("no longer used")]
#pragma warning restore S1133
  public DatabaseType? AutoUpdateScriptType { get; set; }

  public string? UpgradeCode { get; set; }

  public MaintenanceTask? MaintenanceTask { get; set; }
  public int? MaintenanceTaskId { get; set; }
  public virtual DatabaseType? MaintenanceTaskType { get; set; }

  public string? Notes { get; set; }

  public bool RebootNeeded { get; set; }

  private RepairActionType? _repairType;

  public RepairActionType RepairType
  {
    get => _repairType ?? RepairActionType.UninstallInstall;
    set => _repairType = value;
  }

  public Script? RepairScript { get; set; }
  public int? RepairScriptId { get; set; }
  public DatabaseType? RepairScriptType { get; set; }

  public int? SoftwareIconMediaId { get; set; }

  public bool Recommended { get; set; }

  /// <summary>
  /// Identifier that denotes a choco package as the provider for software versions
  /// </summary>
  public string? ChocoProviderSoftwareId { get; set; }

  /// <summary>
  /// Identifier that desnotes a ninite package as the provider for software versions
  /// </summary>
  public string? NiniteProviderSoftwareId { get; set; }

  public abstract ICollection<SoftwarePrerequisite> SoftwarePrerequisites { get; }

  public abstract ICollection<SoftwareVersion> GetSoftwareVersions();

  // getter to return maintenance type used by maintenance logic
  public MaintenanceType MaintenanceType
  {
    get
    {
      switch (SoftwareType)
      {
        case SoftwareType.GlobalSoftware:
          return MaintenanceType.GlobalSoftware;
        case SoftwareType.LocalSoftware:
          return MaintenanceType.LocalSoftware;
        case SoftwareType.Ninite:
          return MaintenanceType.NiniteSoftware;
        case SoftwareType.Chocolatey:
          return MaintenanceType.ChocolateySoftware;
        case SoftwareType.WindowsUpdate:
          return MaintenanceType.WindowsUpdate;
        default:
          throw new NotSupportedException();
      }
    }
  }

#pragma warning disable S1133
  [Obsolete("No longer used")]
#pragma warning restore S1133
  public bool UseSoftwareTableDetection { get; set; }

  public int? InstallScriptId { get; set; }
  public DatabaseType? InstallScriptType { get; set; }
  public Script? InstallScript { get; set; }
  public int? TestScriptId { get; set; }
  public DatabaseType? TestScriptType { get; set; }
  public Script? TestScript { get; set; }
  public int? UpgradeScriptId { get; set; }
  public DatabaseType? UpgradeScriptType { get; set; }
  public Script? UpgradeScript { get; set; }
  public int? UninstallScriptId { get; set; }
  public DatabaseType? UninstallScriptType { get; set; }
  public Script? UninstallScript { get; set; }
  public int? PostInstallScriptId { get; set; }
  public DatabaseType? PostInstallScriptType { get; set; }
  public Script? PostInstallScript { get; set; }
  public int? PostUninstallScriptId { get; set; }
  public DatabaseType? PostUninstallScriptType { get; set; }
  public Script? PostUninstallScript { get; set; }
  public bool TestRequired { get; set; }
  public string? TestFailedError { get; set; }
  public UpdateActionType UpgradeStrategy { get; set; }
  public LicenseType LicenseType { get; set; }
  public string? LicenseDescription { get; set; }

  // dynamic versions
  public bool UseDynamicVersions { get; set; }
  public int? DynamicVersionsScriptId { get; set; }
  public DatabaseType? DynamicVersionsScriptType { get; set; }
  public Script? DynamicVersionsScript { get; set; }

  // This is used as part of the local path to store assets for this software on a computer.
  // The reason we use this is to reduce the chance of collisions of files with the same name. eg, setup.exe
  public Guid RelativeCacheSourcePath { get; set; }

  public MaintenanceSpecifier MaintenanceSpecifier => new()
  {
    MaintenanceIdentifier = this.Identifier,
    MaintenanceType = this.MaintenanceType,
    MaintenanceName = this.Name
  };
}
