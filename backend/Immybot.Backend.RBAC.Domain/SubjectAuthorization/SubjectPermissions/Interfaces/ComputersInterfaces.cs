namespace Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;

public interface IComputersSubject : ISubjectMetadata, IResourceBased;

public interface IComputersSubjectPermission : IPermissionMetadata, IResourceBased;
public interface IComputersAccessTerminalPermission : IComputersSubjectPermission;
public interface IComputersExportPermission : IComputersSubjectPermission;
public interface IComputersManagePermission : IComputersSubjectPermission;
public interface IComputersManagePrimaryPersonPermission : IComputersSubjectPermission;
public interface IComputersManageRegistryPermission : IComputersSubjectPermission;
public interface IComputersOnboardingPermission : IComputersSubjectPermission;
public interface IComputersRemoteAccessPermission : IComputersSubjectPermission;
public interface IComputersUserAffinityPermission : IComputersSubjectPermission;
public interface IComputersViewAgentStatusReportPermission : IComputersSubjectPermission;
public interface IComputersViewInventoryReportPermission : IComputersSubjectPermission;
public interface IComputersViewPermission : IComputersSubjectPermission;
public interface IComputersViewRegistryPermission : IComputersSubjectPermission;

public interface IComputersChangeTenantPermission : IComputersSubjectPermission;

public interface IComputerIdentifyAgentsPermission : IComputersSubjectPermission;
