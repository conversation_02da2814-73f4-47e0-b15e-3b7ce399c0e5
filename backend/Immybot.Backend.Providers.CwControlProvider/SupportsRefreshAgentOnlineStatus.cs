using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.CwControlProvider;
using Immybot.Backend.Providers.Interfaces;

namespace CwControlProvider;

public partial class CwControlProvider : ISupportsRefreshAgentOnlineStatus
{
  public async Task<bool> RefreshAgentOnlineStatus(IProviderAgentDetails device, CancellationToken token)
  {
    var updatedDevice = await GetAgent(device.ExternalAgentId, token);
    if (updatedDevice == null) return false;
    if (updatedDevice.IsOnline && !device.IsOnline)
    {
      using var handler = QueryHandlerMaker.Invoke(token);
      await _eventHandler.AgentsConnectedAsync(ProviderLinkId, new List<(string, string)> { (device.ExternalClientId, device.ExternalAgentId) }, handler.LockKey, token);
    }
    else if (!updatedDevice.IsOnline && device.IsOnline)
    {
      using var handler = QueryHandlerMaker.Invoke(token);
      await _eventHandler.AgentsDisconnectedAsync(ProviderLinkId, new List<(string, string)> { (device.ExternalClientId, device.ExternalAgentId) }, handler.LockKey, token);
    }
    return updatedDevice.IsOnline;
  }

  public async Task<CwControlAgent?> GetAgent(string sessionId, CancellationToken token)
  {
    var route = ApiRoutes.GetSessionInfo
      .Replace("{sessionId}", sessionId)
      .Replace("{clientIdPropIndex}", FormData.TrueCustomPropertyClientNameIndex.ToString());
    return await ApiClient.GetAsync<CwControlAgent>(route, token);
  }

}
