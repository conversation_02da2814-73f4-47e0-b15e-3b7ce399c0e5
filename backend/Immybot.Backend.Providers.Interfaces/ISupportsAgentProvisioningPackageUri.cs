using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Providers;

namespace Immybot.Backend.Providers.Interfaces;

[IntegrationCapability("Agent OOBE Installation",
  "Installing the agent from PPKG")]
public interface ISupportsAgentProvisioningPackageUri
{
  Task<Uri> GetProvisioningPackageUri(
    GetProvisioningPackageUriParameters requestOptions,
    CancellationToken token);
}

[IntegrationCapability("Agent OOBE Installation With Auto Onboarding",
  "Installing the agent from PPKG and specifying onboarding options with the installer")]
public interface ISupportsAgentProvisioningPackageUriWithOnboardingOptions
{
  Task<Uri> GetProvisioningPackageUri(
    GetProvisioningPackageUriParametersWithOnboardingOptions requestOptions,
    CancellationToken token);
}
