using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.UnitTests.Lib;

namespace Immybot.Backend.Web.Common.UnitTests.Controllers.ComputersController;

public class UpdateComputerTests : UnitTestBase
{
  [Fact]
  public async Task UpdateComputer_ShouldUseAuthorizationFlow()
  {
    // act
    await SwallowException(async () =>
    {
      await ComputersController.UpdateComputer(
        GetService<ImmybotDbContext>(),
        GetService<IEphemeralAgentSessionStore>(),
        GetService<IProviderActions>(),
        GetService<IDomainEventBroker>(),
        GetService<IMaintenanceSessionActions>(),
        GetService<IChangeTenantForComputersCmd>(),
        1,
        new UpdateComputerRequestBody());
    });

    // assert
    AssertAuthorizationFlowCalled<Computer, IComputersManagePermission>(new DefaultKeyParameters(1));
  }
}
