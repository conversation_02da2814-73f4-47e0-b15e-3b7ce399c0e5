using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.UnitTests.Lib;

namespace Immybot.Backend.Web.Common.UnitTests.Controllers.TenantsController;

public class GetProviderLinksTests : UnitTestBase
{
  [Fact]
  public async Task GetProviderLinks_ShouldCallAuthorizationWorkflow()
  {
    // arrange
    SetCurrentUser();
    var tenant = GetOrCreateMspTenant();

    // act
    await TenantsController.GetProviderLinks(
      GetService<ImmybotDbContext>(),
      id: tenant.Id);

    // assert
    AssertAuthorizationFlowCalled<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(tenant.Id));
  }
}
