using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands.Payloads.TargetAssignments;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.Shared.Lib;
using Immybot.Backend.Web.Common.Contracts.V1.Requests.Scripts;
using Moq;
using Polly;

namespace Immybot.Backend.UnitTests;

public class ChangeRequestStoreTests : BaseUnitTests
{
  private readonly User _defaultMspUser;
  private readonly Func<ImmybotDbContext> _ctxFactory;
  public ChangeRequestStoreTests(ITestOutputHelper helper) : base(helper, showLogsInTestOutput: false)
  {
    var defaultTenant = GetOrCreateMspTenant();
    _defaultMspUser = GetOrCreateDefaultUser(defaultTenant.Id);
    _ctxFactory = GetSqliteDbContextFactory();
  }

  private ChangeRequestStore GetChangeRequestStore(List<TargetAssignment>? targetAssignments = null)
  {
    var targetPopulator = new Mock<ITargetPopulator>();
    var softwareActions = new Mock<ISoftwareActions>();

    if (targetAssignments is not null)
    {
      var ret = targetAssignments.Select(a => new PopulatedTargetAssignment(a)).ToList();
      targetPopulator.Setup(a =>
          a.Populate(It.IsAny<IQueryable<PopulatedTargetAssignment>>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(ret);

      softwareActions.Setup(a => a.GetSoftware(
          It.IsAny<SoftwareType>(),
          It.IsAny<string>(),
          It.IsAny<CancellationToken>(),
          It.IsAny<Context?>(),
          It.IsAny<IAsyncPolicy?>()))
        .ReturnsAsync(new LocalSoftware());
    }
    var taskActions = new Mock<IMaintenanceTaskActions>();
    var eventEmitter = new Mock<IDomainEventEmitter>();
    return new ChangeRequestStore(_ctxFactory, targetPopulator.Object, taskActions.Object, softwareActions.Object, eventEmitter.Object);
  }

  [Fact]
  public async Task CreateChangeRequestForNewEntity_ShouldCreateChangeRequest()
  {
    // arrange
    var store = GetChangeRequestStore();
    var payload = new CreateLocalTargetAssignmentPayload()
    {
      MaintenanceIdentifier = "test", MaintenanceType = MaintenanceType.LocalSoftware
    };


    // act
    var res = await store.CreateChangeRequestForNewEntity(_defaultMspUser, ChangeRequestObjectType.TargetAssignment, payload, CancellationToken.None);

    // assert
    Assert.NotNull(res);
    Assert.Equal(1, res.Id);

    Assert.Equal(payload.MaintenanceIdentifier, res.NewValuesJson.TryGetProperty(nameof(payload.MaintenanceIdentifier), out var id ) ? id.GetString() : null);
    Assert.Equal(Convert.ToInt32(payload.MaintenanceType).ToString(), res.NewValuesJson.TryGetProperty(nameof(payload.MaintenanceType), out var type ) ? type.GetInt32().ToString() : null);
  }

  [Fact]
  public async Task CreateChangeRequestForExistingEntity_ShouldCreateDeploymentChangeRequest()
  {
    // arrange
    var store = GetChangeRequestStore();
    var payload = new CreateLocalTargetAssignmentPayload()
    {
      MaintenanceIdentifier = "test", MaintenanceType = MaintenanceType.LocalSoftware
    };

    var a = CreateTargetAssignment();

    // act

    var res = await store.CreateChangeRequestForExistingEntity(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      a,
      a.Id,
      payload,
      CancellationToken.None);

    // assert
    Assert.NotNull(res);
    Assert.Equal(1, res.Id);
    Assert.Equal(a.Id, res.TargetAssignmentId);
    Assert.Equal(payload.MaintenanceIdentifier, res.NewValuesJson.TryGetProperty(nameof(payload.MaintenanceIdentifier), out var id ) ? id.GetString() : null);
    Assert.Equal(Convert.ToInt32(payload.MaintenanceType).ToString(), res.NewValuesJson.TryGetProperty(nameof(payload.MaintenanceType), out var type ) ? type.GetInt32().ToString() : null);
  }

  [Fact]
  public async Task CreateChangeRequestForExistingEntity_ShouldCreateScriptChangeRequest()
  {
    // arrange
    var store = GetChangeRequestStore();
    var payload = new CreateLocalScriptRequestBody()
    {
      Name = "NewScriptName",
      Action = "test"
    };

    var a = CreateLocalScript();

    // act

    var res = await store.CreateChangeRequestForExistingEntity(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      a,
      a.Id,
      payload,
      CancellationToken.None);

    // assert
    Assert.NotNull(res);
    Assert.Equal(1, res.Id);
    Assert.Equal(a.Id, res.ScriptId);
    Assert.Equal(payload.Action, res.NewValuesJson.TryGetProperty(nameof(payload.Action), out var action) ? action.GetString() : null);
  }

  [Fact]
  public async Task UpdateChangeRequestForExistingEntity_ShouldUpdate()
  {
    // arrange
    var store = GetChangeRequestStore();
    var payload = new CreateLocalScriptRequestBody()
    {
      Name = "OrigScriptName",
      Action = "test"
    };

    var script = CreateLocalScript();

    var changeRequest = await store.CreateChangeRequestForExistingEntity(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      script,
      script.Id,
      payload,
      CancellationToken.None);

    // act

    var newPayload = new UpdateLocalScriptRequestBody()
    {
      Name = "NewScriptName",
      Action = "test2"
    };

    var res = await store.UpdateChangeRequestForExistingEntity(
      _defaultMspUser,
      changeRequest,
      script,
      newPayload,
      CancellationToken.None);

    // assert
    Assert.Equal(newPayload.Action, res.NewValuesJson.TryGetProperty(nameof(newPayload.Action), out var action) ? action.GetString() : null);
  }

  [Fact]
  public async Task UpdateChangeRequestForNewEntity_ShouldUpdate()
  {
    // arrange
    var store = GetChangeRequestStore();
    var payload = new CreateLocalScriptRequestBody()
    {
      Name = "ScriptName",
      Action = "test"
    };

    var script = CreateLocalScript();

    var changeRequest = CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new {}),
      scriptId: script.Id);

    // act
    var res = await store.UpdateChangeRequestForNewEntity(
      _defaultMspUser,
      changeRequest,
      payload,
      CancellationToken.None);

    // assert
    Assert.Equal(payload.Action, res.NewValuesJson.TryGetProperty(nameof(payload.Action), out var action) ? action.GetString() : null);
  }

  [Fact]
  public void GetChangeRequest_ShouldReturnChangeRequest()
  {
    // arrange
    var store = GetChangeRequestStore();
    var script = CreateLocalScript();

    var changeRequest = CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(script),
      scriptId: script.Id);

    // act
    var res = store.GetChangeRequest(changeRequest.Id);

    // assert
    Assert.NotNull(res);
  }

  [Fact]
  public void GetChangeRequestsForEntity_ShouldReturnAllForEntity()
  {
    // arrange
    var store = GetChangeRequestStore();
    var script = CreateLocalScript();

    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }),
      scriptId: script.Id);

    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }),
      scriptId: script.Id);

    // should not be included
    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }));

    // act
    using var res = store.GetChangeRequestsForEntity(ChangeRequestObjectType.Script, script.Id);

    // assert
    Assert.Equal(2, res.Value.ToList().Count);
  }

  [Fact]
  public void GetChangeRequestsForType_ShouldReturnAllChangeRequestsForType()
  {
    // arrange
    var store = GetChangeRequestStore();
    var script = CreateLocalScript();

    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }));

    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }));

    // should not be included
    CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.Script,
      JsonSerializer.SerializeToElement(new { }), scriptId: script.Id);

    // act
    using var res = store.GetChangeRequestsForType(ChangeRequestObjectType.Script);

    // assert
    Assert.Equal(3, res.Value.ToList().Count);
  }

  [Fact]
  public async Task DeleteChangeRequest_ShouldDelete()
  {
    // arrange
    var store = GetChangeRequestStore();

    var cr = CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      JsonSerializer.SerializeToElement(new { }));


    // act
    await store.DeleteChangeRequest(_defaultMspUser, cr, CancellationToken.None);

    await using var ctx = _ctxFactory();
    var count = ctx.ChangeRequests.Count();
    // assert
    Assert.Equal(0, count);
  }

  [Fact]
  public async Task ApproveChangeRequest_ShouldApprove()
  {
    // arrange
    var store = GetChangeRequestStore();

    var assignment =
      new TargetAssignment() { MaintenanceIdentifier = "1", MaintenanceType = MaintenanceType.LocalSoftware };
    var cr = CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      JsonSerializer.SerializeToElement(assignment));


    // act
    await store.ApproveChangeRequest(_defaultMspUser, cr, CancellationToken.None);

    await using var ctx = _ctxFactory();
    var updated = ctx.ChangeRequests.First(a => a.Id == cr.Id);

    // assert
    Assert.Equal(ChangeRequestState.ApprovedAndApplied, updated.State);
    Assert.NotNull(updated.AcknowledgedByUserId);
    Assert.NotNull(updated.AcknowledgedByUserName);
  }

  [Fact]
  public async Task DenyChangeRequest_ShouldDeny()
  {
    // arrange
    var store = GetChangeRequestStore();

    var cr = CreateChangeRequest(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      JsonSerializer.SerializeToElement(new { }));


    // act
    await store.DenyChangeRequest(_defaultMspUser, cr, CancellationToken.None);

    await using var ctx = _ctxFactory();
    var updated = ctx.ChangeRequests.First(a => a.Id == cr.Id);

    // assert
    Assert.Equal(ChangeRequestState.Denied, updated.State);
    Assert.NotNull(updated.AcknowledgedByUserId);
    Assert.NotNull(updated.AcknowledgedByUserName);
  }

  [Fact]
  public async Task CommentOnChangeRequest_ShouldCreateComment()
  {
    // arrange
    var store = GetChangeRequestStore();

    var cr = await store.CreateChangeRequestForNewEntity(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      new CreateLocalScriptRequestBody()
      {
        Name = "ScriptName",
        Action = "test"
      },
      CancellationToken.None);

    // act
    await store.CommentOnChangeRequest(
      _defaultMspUser,
      cr,
      "Some comment",
      CancellationToken.None);

    await using var ctx = _ctxFactory();
    var comment = ctx.ChangeRequestComments.FirstOrDefault();

    // assert
    Assert.NotNull(comment);
    Assert.Equal(cr.Id, comment.ChangeRequestId);
  }

  [Fact]
  public async Task GetTargetAssignmentChangeRequestDiff_ShouldReturnExpectedParameterDiff()
  {
    // arrange
    var a = CreateTargetAssignment();
    var store = GetChangeRequestStore([a]);
    var parameterChanges =
      new Dictionary<string, DeploymentParameterValue> { { "test", new DeploymentParameterValue(null) } };
    var payload = new UpdateLocalTargetAssignmentPayload
    {
      Id = a.Id, MaintenanceIdentifier = "1", TaskParameterValues = parameterChanges.ToImmutableDictionary()
    };

    // act
    var res = await store.CreateChangeRequestForExistingEntity(
      _defaultMspUser,
      ChangeRequestObjectType.TargetAssignment,
      a,
      a.Id,
      payload,
      CancellationToken.None);

    var diff = await store.GetTargetAssignmentChangeRequestDiff(res, CancellationToken.None);

    // assert
    Assert.Single(diff.ParameterDiffs);
  }
}
