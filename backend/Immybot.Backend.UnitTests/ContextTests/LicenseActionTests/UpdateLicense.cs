using Immybot.Backend.Application.DbContextExtensions.LicenseExtensions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.UnitTests.ContextTests.LicenseActionTests;

public class UpdateLicense : ActionsTestBase
{
  [Fact]
  public void UpdatesLicense()
  {
    //Arrange
    var licenseValue = "SomeLicenseValue";
    var licenseName = "SomeLicenseName";
    var softwareIdentifier = "Chrome";
    var newLicenseName = "NameFoobar";
    var tenant = GetOrCreateMspTenant();
    var user = GetOrCreateDefaultUser(tenant.Id);
    var ctx = GetSqliteDbContext();

    var license = CreateLicense(
      licenseName,
      softwareIdentifier,
      SoftwareType.LocalSoftware,
      licenseValue,
      LicenseType.Key,
      version: null,
      tenantId: null
    );

    //Act
    var updatePayload = MockUpdateLicensePayload(license, user.Id, name: newLicenseName);
    ctx.UpdateLicense(updatePayload.Object);

    //Assert
    var updated = ctx.Licenses.Find(license.Id);
    Assert.Equal(newLicenseName, updated?.Name);
  }
}
