using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Application.DbContextExtensions.ScriptExtensions;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.UnitTests.ContextTests;

public class LocalScriptExtensionTests : ActionsTestBase
{
  [Fact]
  public void GetAllScripts()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    CreateLocalScript();

    // act
    var count = ctx.GetAllScripts().Count();

    // assert
    Assert.Equal(1, count);
  }

  [Fact]
  public void GetAllScriptsForTenant()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant1 = GetOrCreateMspTenant();
    var tenant2 = CreateTenant();
    var scriptTenants1 = new List<TenantScript>
      {
        new TenantScript
        {
          TenantId = tenant1.Id,
          Relationship = Relationship.Owned,
        },
      };

    var scriptTenants2 = new List<TenantScript>
      {
        new TenantScript
        {
          TenantId = tenant1.Id,
          Relationship = Relationship.Assigned,
        },
      };

    var scriptTenants3 = new List<TenantScript>
      {
        new TenantScript
        {
          TenantId = tenant2.Id,
          Relationship = Relationship.Owned,
        },
      };

    CreateLocalScript(name: "script1", tenants: scriptTenants1);
    CreateLocalScript(name: "script2", tenants: scriptTenants2);
    CreateLocalScript(name: "script3", tenants: scriptTenants3);

    // act
    var tenant1Count = ctx.GetAllScriptsForTenant(tenant1.Id).Count();

    // assert
    Assert.Equal(2, tenant1Count);
  }

  [Fact]
  public void GetScriptById()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var script = CreateLocalScript();

    // act
    var s = ctx.GetScriptById(script.Id);

    // assert
    Assert.NotNull(s);
  }

  [Fact]
  public void DeleteScript()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var script = CreateLocalScript();

    // act
    ctx.DeleteScript(script);
    var shouldBeNull = ctx.Scripts.Find(script.Id);

    // assert
    Assert.Null(shouldBeNull);
  }

  [Fact]
  public void CreateScript()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = CreateTenant();
    var user = GetOrCreateDefaultUser(tenant.Id);

    var tenantScripts = new List<TenantScript>
      {
        new TenantScript
        {
          TenantId = tenant.Id,
        }
      };

    var payload = MockCreateLocalScriptPayload(
      user.Id,
      name: "test",
      action: "test",
      scriptLanguage: ScriptLanguage.PowerShell,
      tenants: tenantScripts
    ).Object;

    ctx.SetUser(user);

    // act
    var created = ctx.CreateScript(payload);
    var found = ctx.Scripts
      .AsNoTracking()
      .Include(a => a.Tenants)
      .FirstOrDefault(a => a.Id == created.Id);

    // assert
    Assert.NotNull(found);
    Assert.Equal("test", found.Name);
    Assert.Equal("test", found.Action);
    Assert.Equal(ScriptLanguage.PowerShell, found.ScriptLanguage);
    Assert.NotEmpty(found.Tenants);
  }

  [Fact]
  public void UpdateScript()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = CreateTenant();
    var user = GetOrCreateDefaultUser(tenant.Id);
    var script = CreateLocalScript();
    var tenantScripts = new List<TenantScript>
      {
        new TenantScript
        {
          TenantId = tenant.Id,
        }
      };

    ctx.SetUser(user);

    var payload = MockUpdateLocalScriptPayload(
      user.Id,
      script,
      name: "test",
      action: "test",
      scriptLanguage: ScriptLanguage.PowerShell,
      tenants: tenantScripts
    ).Object;

    // act

    _ = ctx.UpdateScript(script.Id, payload);
    var found = ctx.Scripts
      .AsNoTracking()
      .Include(a => a.Tenants)
      .FirstOrDefault(a => a.Id == script.Id);

    // assert
    Assert.NotNull(found);
    Assert.Equal("test", found.Name);
    Assert.Equal("test", found.Action);
    Assert.Equal(ScriptLanguage.PowerShell, found.ScriptLanguage);
    Assert.NotEmpty(found.Tenants);
  }
}
