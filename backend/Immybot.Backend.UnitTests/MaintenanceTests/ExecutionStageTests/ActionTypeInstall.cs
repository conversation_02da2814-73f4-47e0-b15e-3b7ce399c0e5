using System.Threading.Tasks;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Moq;
using static Immybot.Backend.UnitTests.Helpers;

namespace Immybot.Backend.UnitTests.MaintenanceTests.ExecutionStageTests;
public class ActionTypeInstall
{
  [Theory]
  [InlineData(true, true, true)] // if software or version has post-install script, run it
  [InlineData(true, false, true)] // if software or version has post-install script, run it
  [InlineData(false, true, true)] // if software or version has post-install script, run it
  [InlineData(false, false, false)] // post-install phase should not get added when there is no post-install script
  public async Task Action_ShouldRunPostInstallSoftware_WhenPostInstallIsRequired(
    bool softwareHasPostInstallScript,
    bool versionHasPostInstallScript,
    bool shouldCallPostInstallSoftwareOperation)
  {
    // Arrange
    var (immyExecution, mocks) = BuildExecutionStageRunner();

    var software = MakeSoftware(
      postInstallScript: softwareHasPostInstallScript ? MakeScript() : null);
    MakeSoftwareVersion(software, "*******",
      postInstallScript: versionHasPostInstallScript ? MakeScript() : null);
    MockSoftwareActions(mocks, softwaresInDb: new[] { software });

    var (action, _) = MakeMaintenanceActionAndSoftware(software,
      desiredSoftwareVersion: "*******",
      actionType: MaintenanceActionType.Install);

    var runContext = BuildStageRunContext(mocks, stage: new() { Type = SessionStageType.Execution });
    runContext.Session.MaintenanceActions.Add(action);
    await runContext.Initialize();

    // Act
    await immyExecution.RunExecution(runContext);

    // Assert
    var times = shouldCallPostInstallSoftwareOperation ? Times.AtLeastOnce() : Times.Never();
    mocks.IMachineSoftwareOperations!.Verify(a => a.PostInstallSoftware(It.IsAny<IActionRunContext>(), It.IsAny<ILogPhaseHandle>()), times);
  }

  [Theory]
  [InlineData(true, true, true)] // if software or version requires test, run it
  [InlineData(true, false, true)] // if software or version requires test, run it
  [InlineData(false, true, true)] // if software or version requires test, run it
  [InlineData(false, false, false)] // test-software phase should not get added when software and version don't require it
  public async Task Action_ShouldTestSoftwareAfterExecution_WhenRequired(
    bool softwareRequiresTestAfterExecution,
    bool versionRequiresTestAfterExecution,
    bool shouldCallTestSoftwareOperation)
  {
    // Arrange
    var (immyExecution, mocks) = BuildExecutionStageRunner();

    var software = MakeSoftware(
      testRequired: softwareRequiresTestAfterExecution);
    var version = MakeSoftwareVersion(software, "*******",
      testRequired: versionRequiresTestAfterExecution);
    MockSoftwareActions(mocks, softwaresInDb: new[] { software });

    var (action, _) = MakeMaintenanceActionAndSoftware(software,
      desiredSoftwareVersion: "*******",
      actionType: MaintenanceActionType.Install);

    var runContext = BuildStageRunContext(mocks, stage: new() { Type = SessionStageType.Execution });
    runContext.Session.MaintenanceActions.Add(action);
    await runContext.Initialize();

    // Act
    await immyExecution.RunExecution(runContext);

    // Assert
    var times = shouldCallTestSoftwareOperation ? Times.AtLeastOnce() : Times.Never();
    mocks.IMachineSoftwareOperations!.Verify(a => a.TestSoftware(
      It.IsAny<IActionRunContext>(),
      software,
      version,
      It.IsAny<TargetAssignment>(),
      It.IsAny<ILogPhaseHandle>()), times);
  }
}
