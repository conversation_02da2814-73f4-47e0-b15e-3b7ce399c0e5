using Immybot.Backend.Application.Maintenance;
using NuGet.Versioning;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using static Immybot.Backend.UnitTests.Helpers;
using static Immybot.Backend.UnitTests.PrereqHelpers;

namespace Immybot.Backend.UnitTests.MaintenanceTests;

public class DependencyResolverTests
{
  /*
   * Some situations we should be testing:
   *
   * ✓- List of maintenance actions with no prereqs should return unmolested
   * ✓- Actions that result in added maintenance actions should have their `RequiredSuccessfulMaintenanceActions` populated
   *
   * ## FAIL IF INSTALLED PREREQS
   * ✓- Action with a "fail if installed" prereq should be failed if no other maint action will uninstall it and software is installed on machine
   * ✓- Action with a "fail if installed" prereq should be failed if another maint action will install it
   * ✓- Action with a "fail if installed" prereq should be failed if another maint action will update it
   * ✓- Action with a "fail if installed" prereq should be failed if another maint action is "no action" because the software is "present"
   * ✓- Action with a "fail if installed" prereq should be fine if another maint action is "no action" because the software is "missing"
   * ✓- Action with a "fail if installed" prereq should be fine if another maint action will uninstall it
   * ✓- Action with a "fail if installed" prereq should be fine if no maint action will install it and software is not installed on machine
   *
   * ## FAIL IF NOT INSTALLED PREREQS
   * ✓- Action with a "fail if not installed" prereq should be failed if no other maint action will install it and software is not installed on machine
   * ✓- Action with a "fail if not installed" prereq should be failed if another maint action will uninstall it
   * ✓- Action with a "fail if not installed" prereq should be failed if another maint action is "no action" because the software is "missing"
   * ✓- Action with a "fail if not installed" prereq should be fine if another maint action will install it
   * ✓- Action with a "fail if not installed" prereq should be fine if another maint action will update it
   * ✓- Action with a "fail if not installed" prereq should be fine if another maint action is "no action" because the software is "present"
   * ✓- Action with a "fail if not installed" prereq should be fine if no maint action will uninstall it and software is installed on machine
   * ✓- Action with a "fail if not installed" prereq should be fine if another of the action's prereqs will install it
   *   i.e. this setup:
   *     action: install x
   *     software x:
   *       1. install y if y is not installed
   *       2. fail if z is not installed
   *     software y:
   *       1. install z if z is not installed
   *
   * ## INSTALL IF NOT INSTALLED PREREQS
   * ✓- Action with a "install if not installed" prereq should be failed if another maint action will uninstall it
   * ✓- Action with a "install if not installed" prereq should be failed if another maint action is "no action" because the software is "missing"
   * ✓- Action with a "install if not installed" prereq should result in two maint actions (one of which will install it) if no other maint action will uninstall it or is "no action" because the software is "missing" and software is not installed on machine
   * ✓- Action with a "install if not installed" prereq should not add a maint action if no other maint action will uninstall it or is "no action" because the software is "missing" and software is installed on machine
   * ✓- Action with a "install if not installed" prereq should be failed if prereq's actionable software has no versions
   *
   * ## UNINSTALL IF INSTALLED PREREQS
   * ✓- Action with a "uninstall if installed" prereq should be failed if another maint action will install it
   * ✓- Action with a "uninstall if installed" prereq should be failed if another maint action will update it
   * ✓- Action with a "uninstall if installed" prereq should be failed if another maint action is "no action" because the software is "present"
   * ✓- Action with a "uninstall if installed" prereq should result in two maint actions (one of which will uninstall it) if no other maint action will install it or update it or is "no action" because the software is "present" and software is installed on machine
   * ✓- Action with a "uninstall if installed" prereq should not add a maint actions if no other maint action will install it or update it or is "no action" because the software is "present" and software is not installed on machine
   */

  [Fact]
  public async Task ShouldNotAffectMaintenanceActionListsThatHaveNoPrerequisites()
  {
    // Arrange
    var actionsList = new[] {
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.Install),
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.Uninstall),
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.Uninstall),
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.Update),
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.Downgrade),
      MakeMaintenanceActionAndSoftware(actionType: MaintenanceActionType.NoAction),
    };

    var preResolveStates = actionsList
      .Select(s => s.action)
      .Select(ToActionState)
      .ToList();

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: actionsList.Select(a => a.software),
      softwareVersionsOnMachine: []);

    // Act
    var stageRunContext = mocks.RunContext as StageRunContext;
    Assert.NotNull(stageRunContext);
    var resolved = await depResolver.ResolveDependencies(stageRunContext, actionsList.Select(a => a.action).ToList());

    // Assert
    Assert.Equal(preResolveStates, resolved.Select(ToActionState).ToList());
    return;

    static string ToActionState(MaintenanceAction action)
    {
      return $"{action.ActionStatus}|{action.ActionResult}|{action.ActionResultReasonMessage}";
    }
  }

  [Fact]
  public async Task ShouldAddDependentMaintenanceActionsInCorrectOrder()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");
    var s3 = MakeSoftware(version: "1.2.3");

    AddInstallIfNotInstalledPrereq(s1, s2); // s1: if s2 not installed then install s2
    AddInstallIfNotInstalledPrereq(s2, s3); // s2: if s3 not installed then install s3

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2, s3 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(3, resolved.Count);
    Assert.All(resolved, a => Assert.Contains(a.ActionStatus, new[] { MaintenanceActionStatus.NotStarted, MaintenanceActionStatus.PendingExecution }));
    Assert.Equal(2, resolved.ElementAt(2).DependsOnActions.Count);
    Assert.Single(resolved.ElementAt(1).DependsOnActions);
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldFailIfNoOtherActionWillUninstallPrereqsConditionalSoftware_WhenSoftwareIsInstalledOnMachine()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: new Dictionary<Software, SemanticVersion> { [s2] = NuGetVersion.Parse("1.2.3") }); // s2 is installed - action should fail

    var actionsList = new[] { MakeSoftwareMaintenanceAction(s1) };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Failed, a.ActionResult));
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldBeFineIfNoOtherActionWillInstallPrereqsConditionalSoftware_WhenSoftwareIsNotInstalledOnMachine()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []); // s2 is not installed - action should be good

    var actionsList = new[] { MakeSoftwareMaintenanceAction(s1) };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldFailIfAnotherActionWillInstallPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2),
      };

    // order of the actions shouldn't matter, so this should fail too
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldFailIfAnotherActionWillUpdatePrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Update, MaintenanceActionReason.UpdateAvailable),
      };

    // order of the actions shouldn't matter, so this should fail too
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Update, MaintenanceActionReason.UpdateAvailable),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldFailIfAnotherActionsTypeAndReasonAreNoActionAndUpToDateForPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.UpToDate),
      };

    // order of the actions shouldn't matter, so this should fail too
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.UpToDate),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldBeFineIfAnotherActionsTypeAndReasonAreNoActionAndMissingForPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.SoftwareMissing),
      };

    // order of the actions shouldn't matter, so this should fail too
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.SoftwareMissing),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task Action_HavingFailIfInstalledPrereq_ShouldBeFineIfAnotherActionWillUninstallPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfInstalledPrereq(s1, s2); // s1: if s2 is installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Uninstall, MaintenanceActionReason.SoftwareDetected),
      };

    // order of the actions shouldn't matter, so this should fail too
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Uninstall, MaintenanceActionReason.SoftwareDetected),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldBeFineIfNoOtherActionWillUninstallPrereqsConditionalSoftware_WhenSoftwareIsInstalledOnMachine()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: new Dictionary<Software, SemanticVersion> { [s2] = NuGetVersion.Parse("1.2.3") }); // s2 is installed - action should be fine

    var actionsList = new[] { MakeSoftwareMaintenanceAction(s1) };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldFailIfNoOtherActionWillInstallPrereqsConditionalSoftware_WhenSoftwareIsNotInstalledOnMachine()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []); // s2 is not installed - action should fail

    var actionsList = new[] { MakeSoftwareMaintenanceAction(s1) };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Failed, a.ActionResult));
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldMakeActionDependentOnExistingStaticActionsThatFailActionsPrereq()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []); // s2 is not installed - action should fail

    var actionsList = new[] { MakeSoftwareMaintenanceAction(s1), MakeSoftwareMaintenanceAction(s2) };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Single(resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).DependsOn);
    Assert.Equal(s2.Identifier, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).DependsOnActions.First().MaintenanceIdentifier);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldFailIfAnotherActionWillUninstallPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Uninstall, MaintenanceActionReason.SoftwareDetected),
      };
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Uninstall, MaintenanceActionReason.SoftwareDetected),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldFailIfAnotherActionsTypeAndReasonAreNoActionAndMissingForPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.SoftwareMissing),
      };
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.SoftwareMissing),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldBeFineIfAnotherActionWillInstallPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Install, MaintenanceActionReason.SoftwareMissing),
      };
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Install, MaintenanceActionReason.SoftwareMissing),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldBeFineIfAnotherActionWillUpdatePrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Update, MaintenanceActionReason.UpdateAvailable),
      };
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Update, MaintenanceActionReason.UpdateAvailable),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldBeFineIfAnotherActionsTypeAndReasonAreNoActionAndUpToDateForPrereqsConditionalSoftware()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed then fail

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.UpToDate),
      };
    var actionsList2 = new[]
    {
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.UpToDate),
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);
    var resolved2 = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList2);

    //Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
    Assert.Equal(2, resolved2.Count);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved2.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingFailIfNotInstalledPrereq_ShouldBeFineIfAnotherOfTheActionsPrereqsWillInstall()
  {
    //Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");
    var s3 = MakeSoftware(version: "1.2.3");

    AddFailIfNotInstalledPrereq(s1, s3); // s1: if s3 is not installed then fail
    AddInstallIfNotInstalledPrereq(s1, s2); // s1: if s2 is not installed, then install s2
    AddInstallIfNotInstalledPrereq(s2, s3); // s2: if s3 is not instaled, then install s3

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2, s3 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    //Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    //Assert
    Assert.Equal(3, resolved.Count);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
  }

  [Fact]
  public async Task MaintenanceAction_HavingInstallIfNotInstalledPrereq_ShouldFailIfAnotherActionWillUninstallPrereqsActionableSoftware()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");
    var s3 = MakeSoftware(version: "1.2.3");

    AddInstallIfNotInstalledPrereq(s1, ifNotInstalled: s2, thenInstall: s3); // s1: if s2 not installed then install s3

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2, s3 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s3, MaintenanceActionType.Uninstall, MaintenanceActionReason.SoftwareDetected),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(m => m.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(m => m.MaintenanceIdentifier == s3.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingInstallIfNotInstalledPrereq_ShouldFailIfAnotherActionsTypeAndReasonAreNoActionAndMissingForPrereqsActionableSoftware()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");
    var s3 = MakeSoftware(version: "1.2.3");

    AddInstallIfNotInstalledPrereq(s1, ifNotInstalled: s2, thenInstall: s3); // s1: if s2 not installed then install s3

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2, s3 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s3, MaintenanceActionType.NoAction, MaintenanceActionReason.SoftwareMissing),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First(m => m.MaintenanceIdentifier == s1.Identifier).ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, resolved.First(m => m.MaintenanceIdentifier == s3.Identifier).ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingInstallIfNotInstalledPrereq_ShouldAddActionToInstallActionableSoftwareIfNoOtherActionWillUninstallOrIsNoActionBecauseMissingForPrereqsActionableSoftware_WhenSoftwareIsNotInstalledOnMachine()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddInstallIfNotInstalledPrereq(s1, s2); // s1: if s2 not installed then install s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
    Assert.Equal(MaintenanceActionType.Install, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionType);
    Assert.Equal(MaintenanceActionType.Install, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionType);
  }

  [Fact]
  public async Task MaintenanceAction_HavingInstallIfNotInstalledPrereq_ShouldNotAddActionForActionableSoftwareIfNoOtherActionWillUninstallOrIsNoActionBecauseMissingForPrereqsActionableSoftware_WhenSoftwareIsInstalledOnMachine()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddInstallIfNotInstalledPrereq(s1, s2); // s1: if s2 not installed then install s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: new Dictionary<Software, SemanticVersion> { [s2] = NuGetVersion.Parse("1.2.3") });

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
  }

  [Fact]
  public async Task MaintenanceAction_HavingInstallIfNotInstalledPrereq_ShouldFailIfPrereqsActionableSoftwareHasNoVersions()
  {
    // Arrange

    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");
    var s3 = MakeSoftware(); // this software has no versions

    AddInstallIfNotInstalledPrereq(s1, s2); // s1: if s2 not installed then install s2
    AddInstallIfNotInstalledPrereq(s2, s3); // s2: if s3 not installed then install s3

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2, s3 },
      softwareVersionsOnMachine: []);

    // Act

    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert

    Assert.Single(resolved);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First().ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingUninstallIfInstalledPrereq_ShouldFailIfAnotherActionWillInstallPrereqsActionableSoftware()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddUninstallIfInstalledPrereq(s1, s2); // s1: if s2 installed then uninstall s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    var m1 = resolved.First(m => m.MaintenanceIdentifier == s1.Identifier);
    var m2 = resolved.First(m => m.MaintenanceIdentifier == s2.Identifier);
    Assert.Equal(MaintenanceActionResult.Failed, m1.ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, m2.ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingUninstallIfInstalledPrereq_ShouldFailIfAnotherActionWillUpdatePrereqsActionableSoftware()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddUninstallIfInstalledPrereq(s1, s2); // s1: if s2 installed then uninstall s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.Update, MaintenanceActionReason.UpdateAvailable),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    var m1 = resolved.First(m => m.MaintenanceIdentifier == s1.Identifier);
    var m2 = resolved.First(m => m.MaintenanceIdentifier == s2.Identifier);
    Assert.Equal(MaintenanceActionResult.Failed, m1.ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, m2.ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingUninstallIfInstalledPrereq_ShouldFailIfAnotherActionsTypeAndReasonAreNoActionAndUpToDateForPrereqsActionableSoftware()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddUninstallIfInstalledPrereq(s1, s2); // s1: if s2 installed then uninstall s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
        MakeSoftwareMaintenanceAction(s2, MaintenanceActionType.NoAction, MaintenanceActionReason.UpToDate),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    var m1 = resolved.First(m => m.MaintenanceIdentifier == s1.Identifier);
    var m2 = resolved.First(m => m.MaintenanceIdentifier == s2.Identifier);
    Assert.Equal(MaintenanceActionResult.Failed, m1.ActionResult);
    Assert.Equal(MaintenanceActionResult.Pending, m2.ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_HavingUninstallIfInstalledPrereq_ShouldAddActionToUninstallActionableSoftwareIfNoOtherActionWillInstallOrIsNoActionBecauseUpToDateForPrereqsActionableSoftware_WhenSoftwareIsInstalledOnMachine()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddUninstallIfInstalledPrereq(s1, s2); // s1: if s2 installed then uninstall s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: new Dictionary<Software, SemanticVersion> { [s2] = NuGetVersion.Parse("1.2.3") });

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Equal(2, resolved.Count);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
    Assert.Equal(MaintenanceActionType.Install, resolved.First(a => a.MaintenanceIdentifier == s1.Identifier).ActionType);
    Assert.Equal(MaintenanceActionType.Uninstall, resolved.First(a => a.MaintenanceIdentifier == s2.Identifier).ActionType);
  }

  [Fact]
  public async Task MaintenanceAction_HavingUninstallIfInstalledPrereq_ShouldNotAddActionForActionableSoftwareIfNoOtherActionWillInstallOrIsNoActionBecauseUpToDateForPrereqsActionableSoftware_WhenSoftwareIsNotInstalledOnMachine()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    AddUninstallIfInstalledPrereq(s1, s2); // s1: if s2 installed then uninstall s2

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
        MakeSoftwareMaintenanceAction(s1),
      };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Single(resolved);
    Assert.All(resolved, a => Assert.Equal(MaintenanceActionResult.Pending, a.ActionResult));
  }

  [Fact]
  public async Task MaintenanceAction_ShouldFail_WhenSoftwarePrereqIsMissingFromDatabase()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var missingPrereq = MakeSameSoftwarePrereq(
      new SoftwareSpecifier { SoftwareIdentifier = "4",
      SoftwareType = SoftwareType.LocalSoftware },
      SubjectQualifier.AllOf,
      Condition.NotInstalled,
      ActionToPerform.Install);

    s1.SoftwarePrerequisites.Add(missingPrereq);

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
      MakeSoftwareMaintenanceAction(s1),
    };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert
    Assert.Single(resolved);
    Assert.Equal(MaintenanceActionResult.Failed, resolved.First().ActionResult);
  }

  [Fact]
  public async Task MaintenanceAction_ShouldBeAdded_WhenSoftwarePrerequisiteAlreadyExists_AndIs_UpdateIfFound_NoAction()
  {
    // Arrange
    var s1 = MakeSoftware(version: "1.2.3");
    var s2 = MakeSoftware(version: "1.2.3");

    var missingPrereq = MakeSameSoftwarePrereq(
      new SoftwareSpecifier { SoftwareIdentifier = s2.Identifier,
        SoftwareType = SoftwareType.LocalSoftware },
      SubjectQualifier.AllOf,
      Condition.NotInstalled,
      ActionToPerform.Install);

    s1.SoftwarePrerequisites.Add(missingPrereq);

    var (depResolver, mocks) = BuildDependencyResolver(
      softwaresInDb: new[] { s1, s2 },
      softwareVersionsOnMachine: []);

    var actionsList = new[]
    {
      MakeSoftwareMaintenanceAction(s1),
      MakeSoftwareMaintenanceAction(s2, desiredSoftwareState: DesiredSoftwareState.UpdateIfFound, actionResult: MaintenanceActionResult.Success, actionReason: MaintenanceActionReason.SoftwareMissing, actionType: MaintenanceActionType.NoAction),
    };

    // Act
    var resolved = await depResolver.ResolveDependencies((mocks.RunContext as StageRunContext)!, actionsList);

    // Assert

    // should have 3 total actions.  The new static actions and a new action for s2 to be installed
    Assert.Equal(3, resolved.Count);
  }
}
