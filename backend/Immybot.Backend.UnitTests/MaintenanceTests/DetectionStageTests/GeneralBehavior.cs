using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;
using Moq;
using static Immybot.Backend.UnitTests.Helpers;

namespace Immybot.Backend.UnitTests.MaintenanceTests.DetectionStageTests;

public class GeneralBehavior : ActionsTestBase
{
  public static IEnumerable<object[]> CompleteActionStatuses()
    => MaintenanceAction.CompletedDetectionStatuses.Select(a => new object[] { a });
  public static IEnumerable<object[]> IncompleteActionStatuses()
    => MaintenanceAction.GetIncompletedDetectionStatuses().Select(a => new object[] { a });

  [Theory]
  [MemberData(nameof(CompleteActionStatuses))]
  public async Task ShouldSkipCompletedActions(MaintenanceActionStatus status)
  {
    // arrange
    var mocks = new MockedServices();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    var session = new MaintenanceSession();
    session.Stages.Add(new MaintenanceSessionStage { Type = SessionStageType.Detection });
    session.MaintenanceActions.Add(new MaintenanceAction
    {
      MaintenanceIdentifier = "test",
      ActionStatus = status
    });

    var runContext = BuildStageRunContext(mocks, computer: MakeComputer(isOnline: true), session: session);
    await runContext.Initialize();

    // act
    var res = await immyDetection.RunDetection(runContext,
      resolutionResults: session.MaintenanceActions
        .Select(a => (new TargetAssignment() { MaintenanceIdentifier = "1", }, a)).ToList());

    Assert.True(res.Success);
    Assert.Single(runContext.Session.MaintenanceActions);
    Assert.Equal(status, runContext.Session.MaintenanceActions.First().ActionStatus);

    mocks.IRunContextActionsList!.Verify(a => a.UpdateAction(It.IsAny<IRunContext>(), It.IsAny<MaintenanceAction>()), Times.Never());
  }


  [Theory]
  [MemberData(nameof(IncompleteActionStatuses))]
  public async Task ShouldNotSkipIncompleteActions(MaintenanceActionStatus status)
  {
    // arrange
    var mocks = new MockedServices();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    var session = new MaintenanceSession();
    session.Stages.Add(new MaintenanceSessionStage { Type = SessionStageType.Detection });
    session.MaintenanceActions.Add(new MaintenanceAction
    {
      MaintenanceIdentifier = "test",
      ActionStatus = status
    });

    var runContext = BuildStageRunContext(mocks, computer: MakeComputer(isOnline: true), session: session);
    await runContext.Initialize();

    // act
    var res = await immyDetection.RunDetection(runContext,
      resolutionResults: session.MaintenanceActions
        .Select(a => (new TargetAssignment() { MaintenanceIdentifier = "1", }, a)).ToList());

    Assert.True(res.Success);
    Assert.Single(runContext.Session.MaintenanceActions);

    mocks.IRunContextActionsList!.Verify(a => a.UpdateAction(It.IsAny<IRunContext>(), It.IsAny<MaintenanceAction>()), Times.AtLeastOnce());
  }

  [Fact]
  public async Task ShouldNotRunSoftwareDetection_WhenComputerIsOffline()
  {
    // arrange
    var mocks = new MockedServices();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    var runContext = BuildStageRunContext(mocks, computer: MakeComputer(isOnline: false));

    // act
    var act = () => immyDetection.RunDetection(runContext);
    await Assert.ThrowsAsync<CancelledComputerOfflineException>(act);
  }

  [Fact]
  public async Task ShouldThrowPendingConnectivityException_WhenComputerIsOfflineAndOfflineBehaviorIsApplyOnConnect()
  {
    // arrange
    var mocks = new MockedServices();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    var runContext = BuildStageRunContext(mocks, computer: MakeComputer(isOnline: false));
    runContext.Args.OfflineBehavior = ComputerOfflineMaintenanceSessionBehavior.ApplyOnConnect;

    // act
    var act = () => immyDetection.RunDetection(runContext);
    await Assert.ThrowsAsync<CancelledPendingConnectivityException>(act);
  }

  [Theory, CombinatorialData]
  public async Task ShouldFailIfDevicesWithMaintenanceFeatureUsageExceededOrDisabled_WhenSessionHasExecutionStageAndComputerIsOnlineAndOverSevenDaysSinceOnboarding(
    bool isUsageExceeded,
    bool isFeatureEnabled,
    bool isComputerOnline,
    bool wasComputerOnboardedOverSevenDaysAgo,
    bool doesSessionHaveExecutionStage)
  {
    // Arrange
    var mocks = new MockedServices();
    (mocks.IFeatureManager = new())
      .Setup(m => m.CanRunExecutionStageForTrackableDevice(It.IsAny<int>(), It.IsAny<bool>(), true)).Callback(
        (int _, bool _, bool _) =>
    {
      if (!isFeatureEnabled) throw new FeatureNotEnabledException { SubscriptionFeatureId = SubscriptionFeatures.TrackedDevicesFeatureId };
      if (isUsageExceeded) throw new FeatureUsageExceededException { SubscriptionFeatureId = SubscriptionFeatures.TrackedDevicesFeatureId };
    });
    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    var runContext = BuildStageRunContext(mocks,
      computer: MakeComputer(
        isOnline: isComputerOnline,
        onboardingStatus: ComputerOnboardingStatus.Onboarded,
        onboardingDate: wasComputerOnboardedOverSevenDaysAgo ? DateTime.UtcNow.AddDays(-7.1) : null));

    if (doesSessionHaveExecutionStage)
      runContext.Session.Stages.Add(new MaintenanceSessionStage { Type = SessionStageType.Execution });

    // Set this to 0 seconds (instead of 5 seconds) to prevent a 5-second delay in each test
    // where the computer is offline
    RunContext.ComputerOnlineEventPropagationDelay = TimeSpan.Zero;

    // Act
    var act = () => immyDetection.RunDetection(runContext);

    // Assert
    var ex = await Record.ExceptionAsync(act);
    if (isComputerOnline && doesSessionHaveExecutionStage && wasComputerOnboardedOverSevenDaysAgo && (isUsageExceeded || !isFeatureEnabled))
    {
      if (!isFeatureEnabled)
        Assert.IsType<FeatureNotEnabledException>(ex);
      else
        Assert.IsType<FeatureUsageExceededException>(ex);
    }
    else
    {
      if (!isComputerOnline)
        Assert.IsType<CancelledComputerOfflineException>(ex);
      else
        Assert.Null(ex);
    }
  }

  [Fact]
  public async Task ShouldGetMaintenanceActionsFromWindowsPatchResolver()
  {
    //Arrange
    var mocks = new MockedServices();

    var windowsPatchActions = new[]
    {
      MakeSoftwareMaintenanceAction(MakeSoftware(SoftwareType.WindowsUpdate)),
      MakeSoftwareMaintenanceAction(MakeSoftware(SoftwareType.WindowsUpdate)),
    };
    MockWindowsPatchResolver(mocks, windowsPatchActions);
    MockMaintenanceActionResolver(mocks);
    var stage = new MaintenanceSessionStage { Type = SessionStageType.Detection };
    var context = BuildStageRunContext(mocks, stage: stage, computer: MakeComputer(isOnline: true));
    await context.Initialize();

    var (immyDetection, _) = BuildDetectionStageRunner(mocks);

    //Act
    var detectionResult = await immyDetection.RunDetection(context);

    Assert.True(detectionResult.Success);

    //Assert
    Assert.All(windowsPatchActions, a =>
    {
      Assert.Contains(context.Session.MaintenanceActions, (_a) => _a == a);
    });
  }

  [Fact]
  public async Task ShouldResolveMaintenanceActionsWithDependencyResolver()
  {
    //Arrange
    var mocks = new MockedServices();
    var actionsAndSoftwares = new[] {
        MakeMaintenanceActionAndSoftware(),
        MakeMaintenanceActionAndSoftware(),
      };
    var windowsPatchActions = new[]
    {
      MakeMaintenanceActionAndSoftware(MakeSoftware(SoftwareType.WindowsUpdate)),
      MakeMaintenanceActionAndSoftware(MakeSoftware(SoftwareType.WindowsUpdate)),
    };
    var (a, s) = MakeMaintenanceActionAndSoftware();
    var actions = actionsAndSoftwares.Concat(windowsPatchActions).Select(i => i.action).ToArray();
    var resolvedActions = new[]
    {
      a,
      actions[0],
      actions[1],
      actions[2],
      actions[3],
    };

    MockSoftwareActions(mocks, actionsAndSoftwares.Concat(windowsPatchActions).Select(i => i.software).Concat(new[] { s }).ToArray());

    var assignment1 = new TargetAssignment
    {
      MaintenanceType = actions[0].MaintenanceType, MaintenanceIdentifier = actions[0].MaintenanceIdentifier
    };
    var assignment2 = new TargetAssignment
    {
      MaintenanceType = actions[1].MaintenanceType, MaintenanceIdentifier = actions[1].MaintenanceIdentifier
    };

    MockWindowsPatchResolver(mocks, windowsPatchActions.Select(b => b.action));

    MockDependencyResolver(mocks, resolvedActions, callback: (_, providedActions) =>
    {
      // make sure all the target assignment actions and windows patch
      // actions are passed to the dependency resolver
      // ReSharper disable once ParameterOnlyUsedForPreconditionCheck.Local
      Assert.All(actions,
        ma =>
      {
        Assert.Contains(providedActions, (ma2) => ma == ma2);
      });
      Assert.All(windowsPatchActions,
        ma =>
      {
        Assert.Contains(providedActions, (ma2) => ma2 == ma.action);
      });
    });
    var stage = new MaintenanceSessionStage { Type = SessionStageType.Detection };
    var context = BuildStageRunContext(mocks, stage: stage, computer: MakeComputer(isOnline: true));
    await context.Initialize();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks: mocks);

    //Act
    var detectionResult = await immyDetection.RunDetection(context, [(assignment1, actions[0]), (assignment2, actions[1])]);

    Assert.True(detectionResult.Success);

    //Assert
    Assert.All(resolvedActions,
      ma =>
    {
      Assert.Contains(context.Session.MaintenanceActions, (ma2) => ma2 == ma);
    });
  }

  [Fact]
  public async Task ShouldUseAdHocTargetAssignment_WhenContextSpecifiesMaintenanceItem()
  {
    // Arrange
    var mocks = new MockedServices();
    var (immyDetection, _) = BuildDetectionStageRunner(mocks,
      // mock the detection resolver so we can verify it gets called
      immyDetectionResolver: MockImmyDetectionResolver(mocks));

    var (action, task) = MakeMaintenanceActionAndTask(
      // assignment id and type are null so we can verify it creates its own ad-hoc assignment
      assignmentId: null,
      assignmentType: null);

    var maintenanceItem = new MaintenanceItem(task.Id.ToString(), task.MaintenanceType);

    // run context with specified maint item
    var runContext = BuildStageRunContext(mocks,
      maintenanceItem: maintenanceItem,
      computer: MakeComputer(isOnline: true));

    await runContext.AddMaintenanceAction(action);

    // Construction of the ad-hoc target assignment actually happens here
    await runContext.Initialize();

    // Act
    _ = await immyDetection.RunDetection(runContext,
      // Don't specify any resolution results - we want to test that
      // it automatically uses the ad-hoc target assignment instead
      resolutionResults: null);

    mocks.IImmyDetectionResolver!.Verify(r => r.RunDetectionForAction(
      It.Is<IActionRunContext>(a => a.Action.MaintenanceDisplayName == action.MaintenanceDisplayName),
      It.Is<TargetAssignment>(t => t.MaintenanceIdentifier == maintenanceItem.MaintenanceIdentifier
        && t.MaintenanceType == maintenanceItem.MaintenanceType)));

  }
}
