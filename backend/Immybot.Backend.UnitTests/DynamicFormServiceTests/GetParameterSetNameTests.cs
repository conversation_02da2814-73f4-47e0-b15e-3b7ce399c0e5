using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.DynamicFormServiceTests;

public class GetParameterSetNameTests : ActionsTestBase
{
  private (IDynamicFormService, IRunContext) GetDynamicFormService()
  {
    var (dynamicFormService, runContext, _) = Helpers.BuildDynamicFormService(ctx: GetSqliteDbContextFactory(), sftCtx: GetSqliteSoftwareDbContextFactory());
    return (dynamicFormService, runContext);
  }

  [Theory]
  [InlineData(true, false, "ParamSetOne")]
  [InlineData(false, true, "ParamSetTwo")]
  [InlineData(false, false, "")]
  [InlineData(true, true, "")]
  public async Task GetParameterSetName_ShouldReturnCorrectParameterSetName(bool useParameterSetOne, bool useParameterSetTwo, string expectedParameterSetName)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var paramBlock = @"[CmdletBinding()]
  Param(
    [Parameter(ParameterSetName = 'ParamSetOne')]
    [switch]$UseParamSetOne,
    [Parameter(ParameterSetName = 'ParamSetTwo')]
    [switch]$UseParamSetTwo
  )
";
    var parameters = new Dictionary<string, ParameterValue>();

    if (useParameterSetOne)
    {
      parameters.Add("UseParamSetOne", Helpers.CreateParameterValue(useParameterSetOne));
    }

    if (useParameterSetTwo)
    {
      parameters.Add("UseParamSetTwo", Helpers.CreateParameterValue(useParameterSetTwo));
    }

    // act
    bool shouldHaveError = (useParameterSetOne && useParameterSetTwo) || (!useParameterSetOne && !useParameterSetTwo);

    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None, parameters);

    if (shouldHaveError)
    {
      Assert.Contains(bindResult.BindErrors, a => a.Key == "AmbiguousParameterSet");
    }

    Assert.Equal(expectedParameterSetName, bindResult.ParameterSetName);
  }
}
