using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.MetascriptTests;
public class InitialSessionStateFactoryTests : ActionsTestBase
{
  [Fact]
  public async Task BuildInitialSessionState_ShouldNotHaveInvokeImmyCommand_InFilterScripts()
  {
    // arrange
    var (issFactory, _) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    //act
    var script = new Script { Name = "", ScriptCategory = ScriptCategory.FilterScriptDeploymentTarget, ScriptExecutionContext = ScriptExecutionContext.CloudScript };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, CancellationToken.None);

    Assert.DoesNotContain(iss.Commands, c => c.Name == SessionStateEntries.InvokeImmyCommandCommandEntry.Name);
    var getImmyComputerCommand = iss.Commands.FirstOrDefault(c => c.Name == "Get-ImmyComputer") as SessionStateCmdletEntry;
    Assert.NotNull(getImmyComputerCommand);
    Assert.Equal(nameof(GetImmyComputerFilterScriptCommand), getImmyComputerCommand.ImplementingType.Name);
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldHaveRealGetImmyComputer_ForNonFilterScripts()
  {
    // arrange
    var (issFactory, _) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    //act
    var script = new Script { Name = "", ScriptExecutionContext = ScriptExecutionContext.CloudScript };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, CancellationToken.None);

    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeImmyCommandCommandEntry.Name);
    var getImmyComputerCommand = iss.Commands.FirstOrDefault(c => c.Name == "Get-ImmyComputer") as SessionStateCmdletEntry;
    Assert.NotNull(getImmyComputerCommand);
    Assert.Equal(nameof(GetImmyComputerMetaScript), getImmyComputerCommand.ImplementingType.Name);
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldBeConstrainedLanguageMode()
  {
    // arrange
    var (issFactory, _) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    //act
    var script = new Script { Name = "", ScriptExecutionContext = ScriptExecutionContext.CloudScript };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, CancellationToken.None);

    Assert.Equal(PSLanguageMode.ConstrainedLanguage, iss.LanguageMode);
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldHaveMinimalCmdlets_ForSystemAndUserScripts()
  {
    // arrange
    var (issFactory, _) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    //act
    var script = new Script { Name = "", ScriptExecutionContext = ScriptExecutionContext.System };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    using var cancellationTokenSource = new CancellationTokenSource();
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, cancellationTokenSource.Token);
    using var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(iss);
    runspace.SetScriptBasedVariables(script, null);

    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeImmyCommandCommandEntry.Name);
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ServiceScopeFactoryVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable("DebugPreference"));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable("VerbosePreference"));
    Assert.Equal(runspace.SessionStateProxy.GetVariable(SessionStateEntries.CancellationTokenVariableEntry.Name), CancellationToken.None);
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptVariablesVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.SkipPreflightVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptCategoryVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.SkipBusinessHoursCheckVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptNameVariableEntry.Name));
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldHaveAllCmdlets_ForMetascripts()
  {
    // arrange
    var (issFactory, mocks) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var runContext = Helpers.BuildSessionRunContext(mocks);

    //act
    var script = new Script { Name = "", ScriptExecutionContext = ScriptExecutionContext.Metascript };
    // var iss = await issFactory.CreateBuilder(script)
    //   .WithRunContext(runContext) // TO_DO
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    using var cancellationTokenSource1 = new CancellationTokenSource();
    using var cancellationTokenSource2 = new CancellationTokenSource();
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, cancellationTokenSource1.Token);
    using var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(iss);
    runspace.SetScriptBasedVariables(script, null);
    runspace.SetRunContextBasedVariables(runContext);
    runspace.SetCancellationTokenBasedVariables(cancellationTokenSource2.Token);

    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeImmyCommandCommandEntry.Name);
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ServiceScopeFactoryVariableEntry.Name));
    Assert.Equal(runspace.SessionStateProxy.GetVariable(SessionStateEntries.CancellationTokenVariableEntry.Name), cancellationTokenSource2.Token);
    Assert.NotNull(runspace.SessionStateProxy.GetVariable("DebugPreference"));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable("VerbosePreference"));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptVariablesVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.SkipPreflightVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptCategoryVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.SkipBusinessHoursCheckVariableEntry.Name));
    Assert.NotNull(runspace.SessionStateProxy.GetVariable(SessionStateEntries.ScriptNameVariableEntry.Name));
    Assert.Contains(iss.Commands, c => c.Name == "%");
    Assert.Contains(iss.Commands, c => c.Name == "?");
    Assert.Contains(iss.Commands, c => c.Name == "ac");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.AddImmyMaintenanceActionChildCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.AddImmyMaintenanceActionDependencyCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.AddUriQueryParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "cat");
    Assert.Contains(iss.Commands, c => c.Name == "cat");
    Assert.Contains(iss.Commands, c => c.Name == "cd");
    Assert.Contains(iss.Commands, c => c.Name == "chdir");
    Assert.Contains(iss.Commands, c => c.Name == "clc");
    Assert.Contains(iss.Commands, c => c.Name == "clear");
    Assert.Contains(iss.Commands, c => c.Name == "clhy");
    Assert.Contains(iss.Commands, c => c.Name == "cli");
    Assert.Contains(iss.Commands, c => c.Name == "clp");
    Assert.Contains(iss.Commands, c => c.Name == "cls");
    Assert.Contains(iss.Commands, c => c.Name == "clv");
    Assert.Contains(iss.Commands, c => c.Name == "cnsn");
    Assert.Contains(iss.Commands, c => c.Name == "compare");
    Assert.Contains(iss.Commands, c => c.Name == "compare");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.CompareToImmyBotVersionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConnectImmyAzureADCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConvertFromBase64CommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConvertFromJsonCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConvertFromStringDataCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConvertToBase64CommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ConvertToJsonCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "copy");
    Assert.Contains(iss.Commands, c => c.Name == "cp");
    Assert.Contains(iss.Commands, c => c.Name == "cp");
    Assert.Contains(iss.Commands, c => c.Name == "cpi");
    Assert.Contains(iss.Commands, c => c.Name == "cpp");
    Assert.Contains(iss.Commands, c => c.Name == "cpp");
    Assert.Contains(iss.Commands, c => c.Name == "cvpa");
    Assert.Contains(iss.Commands, c => c.Name == "dbp");
    Assert.Contains(iss.Commands, c => c.Name == "del");
    Assert.Contains(iss.Commands, c => c.Name == "diff");
    Assert.Contains(iss.Commands, c => c.Name == "diff");
    Assert.Contains(iss.Commands, c => c.Name == "dir");
    Assert.Contains(iss.Commands, c => c.Name == "dnsn");
    Assert.Contains(iss.Commands, c => c.Name == "ebp");
    Assert.Contains(iss.Commands, c => c.Name == "echo");
    Assert.Contains(iss.Commands, c => c.Name == "epal");
    Assert.Contains(iss.Commands, c => c.Name == "epcsv");
    Assert.Contains(iss.Commands, c => c.Name == "erase");
    Assert.Contains(iss.Commands, c => c.Name == "etsn");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ExpandStringCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "Export-ModuleMember");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ExportPSSessionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "exsn");
    Assert.Contains(iss.Commands, c => c.Name == "fc");
    Assert.Contains(iss.Commands, c => c.Name == "fl");
    Assert.Contains(iss.Commands, c => c.Name == "foreach");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ForEachObjectCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.FormatHexCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.FormatListCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.FormatTableCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "ft");
    Assert.Contains(iss.Commands, c => c.Name == "fw");
    Assert.Contains(iss.Commands, c => c.Name == "gal");
    Assert.Contains(iss.Commands, c => c.Name == "gbp");
    Assert.Contains(iss.Commands, c => c.Name == "gc");
    Assert.Contains(iss.Commands, c => c.Name == "gci");
    Assert.Contains(iss.Commands, c => c.Name == "gcm");
    Assert.Contains(iss.Commands, c => c.Name == "gcs");
    Assert.Contains(iss.Commands, c => c.Name == "gdr");
    Assert.Contains(iss.Commands, c => c.Name == "gerr");
    Assert.Contains(iss.Commands, c => c.Name == "Get-ChildItem");
    Assert.Contains(iss.Commands, c => c.Name == "Get-Command");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetGlobalSoftwareCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetHashCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "Get-Help");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetImmyAuthTokenCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetImmyAzureAuthHeaderCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetImmyBotAgentFileVersionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetImmyComputerMetascriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetImmyMaintenanceActionChildrenCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetIntegrationAgentInstallTokenCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetIntegrationAgentUninstallTokenCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetIntegrationAuthenticatedDownloadCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetIntegrationDynamicVersionsCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetIntegrationTenantUninstallTokenCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "Get-Item");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetKeyedHashCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetModuleCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetOtpCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetPersonCloudScriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetProviderAgentMetaScriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetProviderInfoCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetRmmComputerAliasEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetRmmInfoAliasEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetTypeDataCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.GetUnboundParametersCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "ghy");
    Assert.Contains(iss.Commands, c => c.Name == "gi");
    Assert.Contains(iss.Commands, c => c.Name == "gjb");
    Assert.Contains(iss.Commands, c => c.Name == "gl");
    Assert.Contains(iss.Commands, c => c.Name == "gm");
    Assert.Contains(iss.Commands, c => c.Name == "gmo");
    Assert.Contains(iss.Commands, c => c.Name == "gp");
    Assert.Contains(iss.Commands, c => c.Name == "gps");
    Assert.Contains(iss.Commands, c => c.Name == "gpv");
    Assert.Contains(iss.Commands, c => c.Name == "group");
    Assert.Contains(iss.Commands, c => c.Name == "gsn");
    Assert.Contains(iss.Commands, c => c.Name == "gsv");
    Assert.Contains(iss.Commands, c => c.Name == "gu");
    Assert.Contains(iss.Commands, c => c.Name == "gv");
    Assert.Contains(iss.Commands, c => c.Name == "h");
    Assert.Contains(iss.Commands, c => c.Name == "history");
    Assert.Contains(iss.Commands, c => c.Name == "icm");
    Assert.Contains(iss.Commands, c => c.Name == "iex");
    Assert.Contains(iss.Commands, c => c.Name == "ihy");
    Assert.Contains(iss.Commands, c => c.Name == "ii");
    Assert.Contains(iss.Commands, c => c.Name == "Import-LocalizedData");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ImportModuleCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.ImportModuleInternalCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeAtomicCommandCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeCommandCachedCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.InvokeImmyCommandCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "ipal");
    Assert.Contains(iss.Commands, c => c.Name == "ipcsv");
    Assert.Contains(iss.Commands, c => c.Name == "ipmo");
    Assert.Contains(iss.Commands, c => c.Name == "irm");
    Assert.Contains(iss.Commands, c => c.Name == "iwr");
    Assert.Contains(iss.Commands, c => c.Name == "Join-Path");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.JoinScriptBlockCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.JoinStringCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "kill");
    Assert.Contains(iss.Commands, c => c.Name == "ls");
    Assert.Contains(iss.Commands, c => c.Name == "man");
    Assert.Contains(iss.Commands, c => c.Name == "md");
    Assert.Contains(iss.Commands, c => c.Name == "measure");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.MergeImmyComputersMetascriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "mi");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Core\\Export-ModuleMember");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Core\\Get-Command");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Core\\Get-Help");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Core\\Import-Module");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Core\\Out-Default");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Management\\Get-ChildItem");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Management\\Get-Item");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Management\\Set-Item");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Management\\Set-Location");
    Assert.Contains(iss.Commands, c => c.Name == "Microsoft.PowerShell.Utility\\Import-LocalizedData");
    Assert.Contains(iss.Commands, c => c.Name == "mount");
    Assert.Contains(iss.Commands, c => c.Name == "mount");
    Assert.Contains(iss.Commands, c => c.Name == "move");
    Assert.Contains(iss.Commands, c => c.Name == "mp");
    Assert.Contains(iss.Commands, c => c.Name == "mv");
    Assert.Contains(iss.Commands, c => c.Name == "mv");
    Assert.Contains(iss.Commands, c => c.Name == "nal");
    Assert.Contains(iss.Commands, c => c.Name == "ndr");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewBooleanParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewCheckboxParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewDateTimeParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewDropdownParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewHelpTextCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewImmyUploadSasUriCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewImmyWebHookCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewKeyValueParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewLiteralStringCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewMediaParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewModuleCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewNumberParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewOauthConsentParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewParameterCollectionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewPasswordParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewPersonCloudScriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewPersonParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewRadioParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewRuntimeDefinedParameterAliasEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewRuntimeDefinedParameterCollectionAliasEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewTextParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.NewUriParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "ni");
    Assert.Contains(iss.Commands, c => c.Name == "nmo");
    Assert.Contains(iss.Commands, c => c.Name == "nsn");
    Assert.Contains(iss.Commands, c => c.Name == "nv");
    Assert.Contains(iss.Commands, c => c.Name == "ogv");
    Assert.Contains(iss.Commands, c => c.Name == "ogv");
    Assert.Contains(iss.Commands, c => c.Name == "oh");
    Assert.Contains(iss.Commands, c => c.Name == "Out-Default");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.OutHostCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.OutNullCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "popd");
    Assert.Contains(iss.Commands, c => c.Name == "ps");
    Assert.Contains(iss.Commands, c => c.Name == "ps");
    Assert.Contains(iss.Commands, c => c.Name == "pushd");
    Assert.Contains(iss.Commands, c => c.Name == "pwd");
    Assert.Contains(iss.Commands, c => c.Name == "r");
    Assert.Contains(iss.Commands, c => c.Name == "rbp");
    Assert.Contains(iss.Commands, c => c.Name == "rcjb");
    Assert.Contains(iss.Commands, c => c.Name == "rcsn");
    Assert.Contains(iss.Commands, c => c.Name == "rd");
    Assert.Contains(iss.Commands, c => c.Name == "rdr");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.RefreshComputerSystemInfoCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.RegisterArgumentCompleterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "ren");
    Assert.Contains(iss.Commands, c => c.Name == "ri");
    Assert.Contains(iss.Commands, c => c.Name == "rjb");
    Assert.Contains(iss.Commands, c => c.Name == "rm");
    Assert.Contains(iss.Commands, c => c.Name == "rm");
    Assert.Contains(iss.Commands, c => c.Name == "rmdir");
    Assert.Contains(iss.Commands, c => c.Name == "rmo");
    Assert.Contains(iss.Commands, c => c.Name == "rni");
    Assert.Contains(iss.Commands, c => c.Name == "rnp");
    Assert.Contains(iss.Commands, c => c.Name == "rp");
    Assert.Contains(iss.Commands, c => c.Name == "rsn");
    Assert.Contains(iss.Commands, c => c.Name == "rv");
    Assert.Contains(iss.Commands, c => c.Name == "rvpa");
    Assert.Contains(iss.Commands, c => c.Name == "sajb");
    Assert.Contains(iss.Commands, c => c.Name == "sal");
    Assert.Contains(iss.Commands, c => c.Name == "saps");
    Assert.Contains(iss.Commands, c => c.Name == "sasv");
    Assert.Contains(iss.Commands, c => c.Name == "sbp");
    Assert.Contains(iss.Commands, c => c.Name == "select");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SelectObjectCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SendImmyEmailCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "set");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetCacheKeyExpirationCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetImmyDeviceIdCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetImmyMaintenanceActionProgressCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetImmyPrimaryUserMetascriptCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetImmySessionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "Set-Item");
    Assert.Contains(iss.Commands, c => c.Name == "Set-Location");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetParameterCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetRuntimeDefinedParameterAliasEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SetStrictModeCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "shcm");
    Assert.Contains(iss.Commands, c => c.Name == "si");
    Assert.Contains(iss.Commands, c => c.Name == "sl");
    Assert.Contains(iss.Commands, c => c.Name == "sleep");
    Assert.Contains(iss.Commands, c => c.Name == "sls");
    Assert.Contains(iss.Commands, c => c.Name == "sort");
    Assert.Contains(iss.Commands, c => c.Name == "sp");
    Assert.Contains(iss.Commands, c => c.Name == "spjb");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.SplitPathCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "spps");
    Assert.Contains(iss.Commands, c => c.Name == "spsv");
    Assert.Contains(iss.Commands, c => c.Name == "start");
    Assert.Contains(iss.Commands, c => c.Name == "start");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.StopImmySessionCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "sv");
    Assert.Contains(iss.Commands, c => c.Name == "TabExpansion2");
    Assert.Contains(iss.Commands, c => c.Name == "tee");
    Assert.Contains(iss.Commands, c => c.Name == "Test-Path");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.TraceCommandCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "type");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.WaitImmyComputerCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.WaitImmyWebHookCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "where");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.WhereObjectCommandEntry.Name);
    Assert.Contains(iss.Commands, c => c.Name == "wjb");
    Assert.Contains(iss.Commands, c => c.Name == "write");
    Assert.Contains(iss.Commands, c => c.Name == SessionStateEntries.WriteErrorFunctionEntry.Name);
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldNotHaveLocalFunctionScripts_ForGlobalScripts()
  {
    // arrange
    var (issFactory, _) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    //act
    var script = new Script { Name = "", ScriptType = DatabaseType.Global, ScriptExecutionContext = ScriptExecutionContext.Metascript };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, CancellationToken.None);

    var includeLocalScriptsVar = iss.Variables.FirstOrDefault(v => v.Name == SessionStateEntries.IncludeLocalScriptsVariableEntry.Name)?.Value;
    Assert.True(includeLocalScriptsVar is bool);
    if (includeLocalScriptsVar is bool includeLocalScripts)
    {
      Assert.False(includeLocalScripts);
    }
  }

  [Fact]
  public async Task BuildInitialSessionState_ShouldLimitFunctionsToRunContextTenant_ForLocalScripts()
  {
    // arrange
    var (issFactory, mocks) = Helpers.BuildInitialSessionStateFactory(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = Helpers.BuildSessionRunContext(mocks);
    runContext.Args.Tenant.Id = 2;
    //act
    var script = new Script { Name = "", ScriptType = DatabaseType.Local, ScriptExecutionContext = ScriptExecutionContext.Metascript };
    var runspaceAttributes = MetascriptRunspaceExtensions.GetRunspaceAttributes(script.ScriptExecutionContext, script.ScriptCategory, script.ScriptType);
    var iss = await issFactory.CreateInitialSessionStateAsync(runspaceAttributes, CancellationToken.None);
    using var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(iss);
    runspace.SetComputerBasedVariables(runContext.Args.Computer);
    runspace.SetTenantBasedVariables(runContext.TenantId);
    runspace.SetRunContextBasedVariables(runContext);

    var limitToScriptsForTenant = (int?)runspace.SessionStateProxy.GetVariable(SessionStateEntries.LimitToScriptsForTenantVariableEntry.Name);
    Assert.Equal(2, limitToScriptsForTenant);
  }
}
