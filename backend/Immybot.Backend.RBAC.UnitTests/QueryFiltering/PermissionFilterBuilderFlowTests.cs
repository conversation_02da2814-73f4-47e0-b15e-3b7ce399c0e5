using System;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Extensions;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Extensions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Extensions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.Shared.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using VerifyXunit;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;

namespace Immybot.Backend.RBAC.UnitTests.QueryFiltering;

/// <summary>
/// Integration tests for PermissionFilterBuilder - testing the complete query filtering workflow
/// using real RBAC implementations and mocking only external dependencies.
/// Includes SQL query snapshot testing to ensure generated queries remain consistent.
/// </summary>
public class PermissionFilterBuilderFlowTests : SqliteUnitTestBase
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ImmybotDbContext _dbContext;
    private readonly Mock<IUserService> _mockUserService;
    private readonly IPermissionFilterBuilder _permissionFilterBuilder;
    

    public PermissionFilterBuilderFlowTests()
    {
        var services = new ServiceCollection();

        // Register all RBAC subject and permission services (real implementations)
        services.AddSubjectPermissionsServices();

        // Register resource authorization services (real implementations)  
        services.AddResourceAuthorization();

        // Register permission evaluation services (our new system)
        services.AddPermissionEvaluationServices();

        // Set up in-memory SQLite database for testing
        _dbContext = GetSqliteDbContext();

        // Register a factory that returns a NEW context each time
        services.AddSingleton<Func<ImmybotDbContext>>(() => GetSqliteDbContext());

        // Mock the user service (external dependency)
        _mockUserService = new Mock<IUserService>();
        services.AddSingleton(_mockUserService.Object);

        _serviceProvider = services.BuildServiceProvider();
        _permissionFilterBuilder = _serviceProvider.GetRequiredService<IPermissionFilterBuilder>();
    }

    private async Task CreateTenantAsync(int tenantId)
    {
        if (!await _dbContext.Tenants.AnyAsync(t => t.Id == tenantId))
        {
            _dbContext.Tenants.Add(new Tenant { Id = tenantId, Name = $"Tenant {tenantId}" });
            await _dbContext.SaveChangesAsync();
        }
    }

    private async Task CreateUserAsync(int userId, int tenantId)
    {
        if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
        {
            _dbContext.Users.Add(new User
            {
                Id = userId,
                TenantId = tenantId,
                Email = $"user{userId}@test.com"
            });
            await _dbContext.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Helper method to show the SQL query that would be generated for a given filter
    /// </summary>
    private string GetGeneratedSql<TResource, TPermission>()
        where TResource : class
        where TPermission : class, IPermissionMetadata
    {
        var filter = _permissionFilterBuilder.BuildFilterExpression<TResource, TPermission>();
        var query = _dbContext.Set<TResource>().Where(filter);
        return query.ToQueryString();
    }


    /// <summary>
    /// Helper method to verify SQL query snapshots using Verify framework
    /// </summary>
    private async Task VerifyGeneratedSql<TResource, TPermission>(string scenarioName)
        where TResource : class
        where TPermission : class, IPermissionMetadata
    {
        var sql = GetGeneratedSql<TResource, TPermission>();
        
        // Clean up the SQL for consistent snapshots by normalizing whitespace and removing volatile parts
        var normalizedSql = NormalizeSqlForSnapshot(sql);
        
        var settings = new VerifySettings();
        settings.UseMethodName(scenarioName);
        await Verify(normalizedSql, settings);
    }

    /// <summary>
    /// Normalizes SQL queries for consistent snapshot testing by removing volatile elements
    /// </summary>
    private static string NormalizeSqlForSnapshot(string sql)
    {
        // Remove line numbers and clean up whitespace for consistent snapshots
        var lines = sql.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
            .Select(line => line.Trim())
            .Where(line => !string.IsNullOrWhiteSpace(line));
        
        return string.Join("\n", lines);
    }

    [Fact]
    public async Task BuildFilterExpression_SingleResourcePermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenantId = 100;
        var userId = 1;

        // Create tenant and user
        await CreateTenantAsync(tenantId);
        await CreateUserAsync(userId, tenantId);

        // Create computers
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer1" };
        var computer2 = new Computer { Id = 2, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer2" };
        var computer3 = new Computer { Id = 3, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer3" };
        await _dbContext.Computers.AddRangeAsync(computer1, computer2, computer3);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Single resource allow
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("computers:1:view:allow", "true"));
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query matches the expected snapshot
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleResourceAllow");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(1, filteredComputers[0].Id);

        // TEST 2: Single resource deny (with tenant allow)
        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:100:computers:view:allow", "true"));  // Allow all in tenant
        identity.AddClaim(new Claim("computers:2:view:deny", "true"));           // Deny specific computer
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for deny scenario
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleResourceDeny");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 2); // Denied
    }

    [Fact]
    public async Task BuildFilterExpression_MultipleResourcePermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenantId = 100;
        var userId = 1;

        // Create tenant and user
        await CreateTenantAsync(tenantId);
        await CreateUserAsync(userId, tenantId);

        // Create computers
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer1" };
        var computer2 = new Computer { Id = 2, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer2" };
        var computer3 = new Computer { Id = 3, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer3" };
        var computer4 = new Computer { Id = 4, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer4" };
        var computer5 = new Computer { Id = 5, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer5" };
        var computer6 = new Computer { Id = 6, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer6" };
        await _dbContext.Computers.AddRangeAsync(computer1, computer2, computer3, computer4, computer5, computer6);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Multiple resource allow
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("computers:1:view:allow", "true"));
        identity.AddClaim(new Claim("computers:3:view:allow", "true"));
        identity.AddClaim(new Claim("computers:5:view:allow", "true"));
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for multiple resource allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleResourceAllow");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.Contains(filteredComputers, c => c.Id == 5);

        // TEST 2: Multiple resource deny (with wildcard allow)
        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:*:computers:view:allow", "true"));   // Allow all
        identity.AddClaim(new Claim("computers:2:view:deny", "true"));           // Deny specific computers
        identity.AddClaim(new Claim("computers:4:view:deny", "true"));
        identity.AddClaim(new Claim("computers:6:view:deny", "true"));
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for multiple resource deny
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleResourceDeny");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.Contains(filteredComputers, c => c.Id == 5);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 2); // Denied
        Assert.DoesNotContain(filteredComputers, c => c.Id == 4); // Denied
        Assert.DoesNotContain(filteredComputers, c => c.Id == 6); // Denied
    }

    [Fact]
    public async Task BuildFilterExpression_SingleTenantPermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenantId1 = 100;
        var tenantId2 = 200;
        var userId = 1;

        // Create tenants and user
        await CreateTenantAsync(tenantId1);
        await CreateTenantAsync(tenantId2);
        await CreateUserAsync(userId, tenantId1);

        // Create computers in different tenants
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId1, ComputerName = "Computer1" };
        var computer2 = new Computer { Id = 2, DeviceId = Guid.NewGuid(), TenantId = tenantId2, ComputerName = "Computer2" };
        await _dbContext.Computers.AddRangeAsync(computer1, computer2);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Single tenant allow (specific tenant)
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:200:computers:view:allow", "true"));
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for single tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleTenantAllow");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(2, filteredComputers[0].Id);
        Assert.Equal(tenantId2, filteredComputers[0].TenantId);

        // TEST 2: User's own tenant (my tenant)
        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:my:computers:view:allow", "true"));
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for my tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MyTenantAllow");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(1, filteredComputers[0].Id);
        Assert.Equal(tenantId1, filteredComputers[0].TenantId);
    }

    [Fact]
    public async Task BuildFilterExpression_MultipleTenantPermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenantId1 = 100;
        var tenantId2 = 200;
        var tenantId3 = 300;
        var tenantId4 = 400;
        var userId = 1;

        // Create tenants and user
        await CreateTenantAsync(tenantId1);
        await CreateTenantAsync(tenantId2);
        await CreateTenantAsync(tenantId3);
        await CreateTenantAsync(tenantId4);
        await CreateUserAsync(userId, tenantId1);

        // Create computers in different tenants
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId1, ComputerName = "Computer1" };
        var computer2 = new Computer { Id = 2, DeviceId = Guid.NewGuid(), TenantId = tenantId2, ComputerName = "Computer2" };
        var computer3 = new Computer { Id = 3, DeviceId = Guid.NewGuid(), TenantId = tenantId3, ComputerName = "Computer3" };
        var computer4 = new Computer { Id = 4, DeviceId = Guid.NewGuid(), TenantId = tenantId4, ComputerName = "Computer4" };
        await _dbContext.Computers.AddRangeAsync(computer1, computer2, computer3, computer4);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Multiple tenant allow
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:100:computers:view:allow", "true"));
        identity.AddClaim(new Claim("tenant:200:computers:view:allow", "true"));
        identity.AddClaim(new Claim("tenant:300:computers:view:allow", "true"));
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for multiple tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleTenantAllow");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1 && c.TenantId == tenantId1);
        Assert.Contains(filteredComputers, c => c.Id == 2 && c.TenantId == tenantId2);
        Assert.Contains(filteredComputers, c => c.Id == 3 && c.TenantId == tenantId3);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 4);

        // TEST 2: Wildcard allow
        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:*:computers:view:allow", "true"));
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for wildcard tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("WildcardTenantAllow");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(4, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 2);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.Contains(filteredComputers, c => c.Id == 4);
    }

    [Fact]
    public async Task BuildFilterExpression_MixedTenantAndResourcePermissions_TestsComplexScenarios()
    {
        // ARRANGE
        var tenantId1 = 100;
        var tenantId2 = 200;
        var userId = 1;

        // Create tenants and user
        await CreateTenantAsync(tenantId1);
        await CreateTenantAsync(tenantId2);
        await CreateUserAsync(userId, tenantId1);

        // Create computers in different tenants
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId1, ComputerName = "Computer1" };
        var computer2 = new Computer { Id = 2, DeviceId = Guid.NewGuid(), TenantId = tenantId1, ComputerName = "Computer2" };
        var computer3 = new Computer { Id = 3, DeviceId = Guid.NewGuid(), TenantId = tenantId2, ComputerName = "Computer3" };
        var computer4 = new Computer { Id = 4, DeviceId = Guid.NewGuid(), TenantId = tenantId2, ComputerName = "Computer4" };
        await _dbContext.Computers.AddRangeAsync(computer1, computer2, computer3, computer4);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Multiple tenant allow + single resource deny
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:100:computers:view:allow", "true"));
        identity.AddClaim(new Claim("tenant:200:computers:view:allow", "true"));
        identity.AddClaim(new Claim("computers:2:view:deny", "true")); // Deny specific computer
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for mixed tenant and resource permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MixedTenantResourcePermissions");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.Contains(filteredComputers, c => c.Id == 4);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 2); // Denied despite tenant allow

        // TEST 2: Resource-specific allow + tenant deny pattern simulation
        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("computers:2:view:allow", "true")); // Allow specific computer
        identity.AddClaim(new Claim("computers:4:view:allow", "true")); // Allow specific computer
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for resource-specific only permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("ResourceSpecificOnly");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 2);
        Assert.Contains(filteredComputers, c => c.Id == 4);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 1); // No permission
        Assert.DoesNotContain(filteredComputers, c => c.Id == 3); // No permission

        // TEST 3: Complex deny pattern - exclude multiple resources AND multiple tenants
        // Create additional tenants first
        await CreateTenantAsync(300);
        await CreateTenantAsync(400);

        // Create additional computers for this scenario
        var computer5 = new Computer { Id = 5, DeviceId = Guid.NewGuid(), TenantId = 300, ComputerName = "Computer5" }; // Allowed tenant
        var computer6 = new Computer { Id = 6, DeviceId = Guid.NewGuid(), TenantId = 400, ComputerName = "Computer6" }; // Allowed tenant
        var computer7 = new Computer { Id = 7, DeviceId = Guid.NewGuid(), TenantId = 300, ComputerName = "Computer7" }; // Allowed tenant but denied resource
        await _dbContext.Computers.AddRangeAsync(computer5, computer6, computer7);
        await _dbContext.SaveChangesAsync();

        identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("tenant:*:computers:view:allow", "true"));    // Allow all tenants
        identity.AddClaim(new Claim("tenant:100:computers:view:deny", "true"));   // Deny specific tenants
        identity.AddClaim(new Claim("tenant:200:computers:view:deny", "true"));
        identity.AddClaim(new Claim("computers:3:view:deny", "true"));            // Deny specific resources
        identity.AddClaim(new Claim("computers:7:view:deny", "true"));
        user = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for complex deny pattern
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("ComplexDenyPattern_ExcludeResourcesAndTenants");
        
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        // Should only get computers from tenants 300,400 that aren't specifically denied resources
        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 5 && c.TenantId == 300); // Allowed tenant, not denied resource
        Assert.Contains(filteredComputers, c => c.Id == 6 && c.TenantId == 400); // Allowed tenant, not denied resource
        Assert.DoesNotContain(filteredComputers, c => c.Id == 1); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == 2); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == 3); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == 4); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == 7); // Denied resource (despite allowed tenant)
    }

    [Fact]
    public async Task BuildFilterExpression_EdgeCases_TestsAuthenticationAndPermissions()
    {
        // ARRANGE
        var tenantId = 100;
        var userId = 1;

        // Create tenant, user and computers
        await CreateTenantAsync(tenantId);
        await CreateUserAsync(userId, tenantId);
        var computer1 = new Computer { Id = 1, DeviceId = Guid.NewGuid(), TenantId = tenantId, ComputerName = "Computer1" };
        _dbContext.Computers.Add(computer1);
        await _dbContext.SaveChangesAsync();

        // TEST 1: Unauthenticated user
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns((ClaimsPrincipal?)null);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for unauthenticated user
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("UnauthenticatedUser");
        
        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Empty(filteredComputers);

        // TEST 2: Authenticated user with no relevant permissions
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("some:other:claim", "true"));
        var user = new ClaimsPrincipal(identity);

        var currentUser = await _dbContext.Users.FindAsync(userId);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(user);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out currentUser)).Returns(true);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
        
        // SNAPSHOT TEST: Verify the generated SQL query for user with no relevant permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("NoRelevantPermissions");
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Empty(filteredComputers);
    }
}