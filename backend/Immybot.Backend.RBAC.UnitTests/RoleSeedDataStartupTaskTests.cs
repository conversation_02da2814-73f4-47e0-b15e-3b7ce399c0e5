using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Implementations;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.UnitTests;

public class RoleSeedDataStartupTaskTests : BaseUnitTests
{
  [Fact]
  public async Task StartupTask_ShouldCreateRole_IfNotExists()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // assert we have zero roles to start
    Assert.Equal(0, await ctx.Roles.CountAsync());

    // act
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // assert
    Assert.Equal(9, await ctx.Roles.CountAsync());
  }

  [Fact]
  public async Task StartupTask_ShouldUpdateRole_WhenChanged()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // call it once to seed the roles
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // act

    // update the msp admin role
    var mspAdminRole = host.Services.GetRequiredService<MspAdminBuiltinRole>();
    mspAdminRole.Role.Name = "Updated Msp Admin Role";

    // re-run the seed task
    await startupTask.ExecuteAsync();

    // assert
    var updatedRole = await ctx.Roles.AsNoTracking().FirstOrDefaultAsync(r => r.Name == "Updated Msp Admin Role");
    Assert.NotNull(updatedRole);
  }


  [Fact]
  public async Task StartupTask_ShouldRemoveRoleClaims_WhenPermissionIsRemoved()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // call it once to seed the roles
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // act
    // remove a claim from the msp admin role
    var mspAdminRole = host.Services.GetRequiredService<MspAdminBuiltinRole>();
    var claim = mspAdminRole.Role.RoleClaims.First();
    mspAdminRole.Role.RoleClaims.Remove(claim);

    // re-run the seed task
    await startupTask.ExecuteAsync();

    // assert
    var updatedRole = await ctx.Roles.AsNoTracking().Include(a => a.RoleClaims)
      .FirstOrDefaultAsync(r => r.Id == mspAdminRole.Role.Id);
    Assert.NotNull(updatedRole);
    var shouldBeNullClaim = updatedRole.RoleClaims.FirstOrDefault(a => a.ClaimType == claim.ClaimType);
    Assert.Null(shouldBeNullClaim);
  }

  [Fact]
  public async Task StartupTask_ShouldAddRoleClaims_WhenPermissionIsAdded()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // call it once to seed the roles
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // act
    // add a claim from the msp admin role
    var mspAdminRole = host.Services.GetRequiredService<MspAdminBuiltinRole>();
    var newClaim = new RoleClaim { ClaimType = "foobar", RoleId = mspAdminRole.Role.Id };
    mspAdminRole.Role.RoleClaims.Add(newClaim);

    // re-run the seed task
    await startupTask.ExecuteAsync();

    // assert
    var updatedRole = await ctx.Roles.AsNoTracking().Include(a => a.RoleClaims)
      .FirstOrDefaultAsync(r => r.Id == mspAdminRole.Role.Id);
    Assert.NotNull(updatedRole);
    var newSavedClaim = updatedRole.RoleClaims.FirstOrDefault(a => a.ClaimType == newClaim.ClaimType);
    Assert.NotNull(newSavedClaim);
  }

  [Fact]
  public async Task StartupTask_ShouldUpdateRoleClaims_WhenPermissionIsUpdated()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // call it once to seed the roles
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // act
    // update a claim from the msp admin role
    var mspAdminRole = host.Services.GetRequiredService<MspAdminBuiltinRole>();
    var claim = mspAdminRole.Role.RoleClaims.First();
    claim.ClaimValue = "Updated Claim Value";
    claim.RoleId = 0;

    // re-run the seed task
    await startupTask.ExecuteAsync();

    // assert
    var updatedRole = await ctx.Roles.AsNoTracking().Include(a => a.RoleClaims)
      .FirstOrDefaultAsync(r => r.Id == mspAdminRole.Role.Id);
    Assert.NotNull(updatedRole);
    var updatedClaim = updatedRole.RoleClaims.FirstOrDefault(a => a.ClaimType == claim.ClaimType);
    Assert.Equal(claim.ClaimValue, updatedClaim?.ClaimValue);
  }

  [Fact]
  public async Task StartupTask_ShouldCreateRoleTypes_IfNotExists()
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // assert we have zero role types to start
    Assert.Equal(0, await ctx.RoleTypes.CountAsync());

    // act
    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // assert
    Assert.Equal(2, await ctx.RoleTypes.CountAsync());
  }

  [Theory]
  [CombinatorialData]
  public async Task StartupTask_ShouldAssignBuiltinRoles_ToPreRbacUsers_WhenUserHasNoRoles(
    bool isMsp,
    bool isAdmin)
  {
    // arrange
    var host = CreateHost();
    var ctxFactory = host.Services.GetRequiredService<IDbContextFactory<ImmybotDbContext>>();
    await using var ctx = await ctxFactory.CreateDbContextAsync();

    // create the pre-rbac user
    var tenant = new Tenant { IsMsp = isMsp, Name = "Test Tenant" };
    await ctx.Tenants.AddAsync(tenant);
    await ctx.SaveChangesAsync();

    var user = new User { HasManagementAccess = true, IsAdmin = isAdmin, TenantId = tenant.Id };
    await ctx.Users.AddAsync(user);
    await ctx.SaveChangesAsync();

    // assert we have zero role types to start
    Assert.Equal(0, await ctx.RoleTypes.CountAsync());

    // act

    var startupTask = host.Services.GetRequiredService<RoleSeedDataStartupTask>();
    await startupTask.ExecuteAsync();

    // assert

    var updatedUser = await ctx.Users
      .AsNoTracking()
      .Include(a => a.UserRoles)
      .FirstOrDefaultAsync(a => a.Id == user.Id);

    var roles = updatedUser?.UserRoles.ToList() ?? [];
    Assert.NotEmpty(roles);

    int? expectedRoleId = isMsp switch
    {
      true when isAdmin => host.Services.GetRequiredService<MspAdminBuiltinRole>().Role.Id,
      true => host.Services.GetRequiredService<MspUserBuiltinRole>().Role.Id,
      false when isAdmin => host.Services.GetRequiredService<TenantAdminBuiltinRole>().Role.Id,
      false => host.Services.GetRequiredService<TenantUserBuiltinRole>().Role.Id,
    };

    Assert.NotNull(roles.FirstOrDefault(a => a.RoleId == expectedRoleId));
  }
}
