{"ActiveDirectoryAuth": {"ImmenseTenantId": "b992c3df-0c32-4cdf-abd2-f64e86513c7b", "ImmyBotGlobalContributorGroupId": "022bb2d9-c3bf-417e-8116-2d9f288e388b", "ClientId": "", "ClientSecret": "", "RestrictToOrganizations": true, "MasterTenantId": "", "MasterTenantName": "", "UseMockAzureIdentityCreds": false, "UseMockAzureClients": false}, "MockUserAuth": {"Enabled": false, "PrincipalObjectId": "deadbeef-810d-46af-8d1e-437a90b72980", "TenantId": "b992c3df-0c32-4cdf-abd2-f64e86513c7b", "EmailAddress": "<EMAIL>", "FirstName": "<PERSON>cky", "LastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Roles": [], "Groups": ["022bb2d9-c3bf-417e-8116-2d9f288e388b"]}, "AppSettings": {"AppInsightsInstrumentationKey": "", "AppInsightsConnectionString": "", "BackendVersion": "0.0.1", "ChocolateyOrgUrl": "https://chocolatey.org/api/v2", "ChocolateyUrl": "https://choco.immense.net/nuget/chocolatey", "DevCorsOrigins": null, "DisableSyncingNewProviderAgents": false, "DoManagerSettingsLookup": false, "EnableGlobalSoftwareAutoUpdate": false, "EphemeralVersionContainerName": "ephemeral", "FrontendDevServer": "http://localhost:5173/", "ImmyBotCdn": "https://cdn.immy.bot", "ImmyBotManagerAadResource": "", "ImmyBotManagerAuthPrincipalObjectId": "", "ImmyBotManagerAuthExpectedAudience": "", "ImmyBotManagerUrl": "https://immybotmanager.azurewebsites.net", "ImmyProduct": null, "ImmySupportTechnicianAppRoleName": "<PERSON><PERSON>SupportTechnician", "ImmyUploadStorageContainerName": "uploads", "IncludePluginsDirectorySubpaths": false, "IsDevInstance": true, "MaxProviderRouteContentLength": 1000000, "PartnerCenterApiUrl": "https://api.partnercenter.microsoft.com", "PartnerCenterUserImpersonationScope": "https://api.partnercenter.microsoft.com/user_impersonation", "PasswordEncryptionIV": "", "PasswordEncryptionKey": "", "PluginsDirectory": "Plugins", "PublicBinariesContainerName": "public-binaries", "PublicScriptsContainerName": "public-scripts", "RootUrl": "http://localhost:5000", "SessionDataAttachmentsContainerName": "session-data-attachments", "UseLocalEphemeralAgent": false, "UseLocalDevelopmentAgentInstaller": false, "LocalDevelopmentAgentMsiInstallerPath": "", "UseMinfiedPowershellScripts": true, "UseUnMinifiedScripts": [], "MaintenanceActionEventHubConnectionString": "", "MaintenanceActionEventHubName": "", "UseMaintenanceActionEventHub": false, "PowerShellErrorsEventHubConnectionString": "", "PowerShellErrorsEventHubName": "", "UsePowerShellErrorsEventHub": false, "ProviderWatchdogTimeoutMs": 180000, "ImmyBotDeveloper": false, "BypassPowerShellScriptCache": false, "Profiler": "LightRun"}, "PowershellEditorServices": {"RunEditorServicesOutOfProcess": false, "SubProcessLimit": -1, "SubProcessInitTimeoutSeconds": 30, "TerminalInactivityTimeoutSeconds": -1, "EnableSubProcessLoggingToBrowser": false, "TriggerDebuggerBreakInSubProcess": false, "SubProcessLaunchFailureDetailedLogging": false, "SubProcessLogLevels": {"*PowershellEditorServicesSpawnBackgroundTask": "Debug", "*LanguageService": "Debug"}}, "ImmyAgentOptions": {"AutomaticallyEstablishEphemeralAgentOnConnect": false, "PPKGBuilderFunctionEndpoint": "https://immybotsharedfunctions.azurewebsites.net/api/ImmyPpkgBuilder", "InstallerBundlerFunctionEndpoint": "https://immybotsharedfunctions.azurewebsites.net/api/ImmyAgentInstallerBundler", "ChallengeSigningKey": null, "ChallengeValiditySpan": 10, "UseOptionsFromEnvironment": false, "FallbackVersion": "0.58.3-build.25209", "AppDistCloudStorageConnectionString": "", "TestAgentInstallerUri": "", "ImmyAgentVersionsContainerName": "immyagent-versions", "UseDevTestAgents": false, "DevTestAgents": []}, "AzureFunctionOptions": {"AutoUpdateFunctionKey": "", "BaseUrl": "", "PackageAnalyzerUrl": "", "UseMockPackageAnalyzer": false}, "AzureKeyVault": {"KeyVaultUri": null}, "DatabaseOptions": {"AutoMigrateGlobalDb": false, "AutoMigrateLocalDb": true, "GlobalBlobStorageConnectionString": "UseDevelopmentStorage=true", "GlobalBlobStorageEndpoint": "http://localhost:10000/devstoreaccount1/", "GlobalMediaContainerName": "media", "GlobalPgDbConnectionStringV2": "Host=127.0.0.1;Database=global;Username=postgres;Password=password;", "GlobalPublicMediaContainerName": "public-media", "GlobalSoftwareContainerName": "software", "LicenseContainerName": "licenses", "ImmyBotLicenseStorageConnectionString": "UseDevelopmentStorage=true", "LocalBlobStorageConnectionString": "UseDevelopmentStorage=true", "LocalBlobStorageEndpoint": "http://localhost:10000/devstoreaccount1/", "LocalMediaContainerName": "media", "LocalRemoteControlRecordingsContainerName": "remote-control-recordings", "LocalPgDbConnectionStringV2": "Host=127.0.0.1;Database=immybot;Username=postgres;Password=password;", "LocalPublicMediaContainerName": "public-media", "LocalSoftwareContainerName": "software", "PreloadUserPrincipalIds": "", "SupportFileContainerName": "support-files"}, "Features": {"RecurringInventoryJobFeature": true, "RecurringSyncAzureUsersJobFeature": true, "RecurringUserComputerAffinityJobFeature": true, "DevLabFeature": false, "SetFeatureUsagesUnlimitedWhenAbsentFromSubscriptionFeatures": false, "SelfServiceFeature": false, "OptionalDeploymentFeature": false, "PersonTasksFeature": false, "GetImmyComputerUseParentTenantFeature": false, "FeatureUsageExceededEventNotificationCheckIntervalSeconds": 5, "AgentStatusListFeature": false, "RBACFeature": false}, "Hangfire": {"RunHangfireServer": true, "FailRunningSessions": false, "ExpirationTimeInSeconds": 5, "RedisConnectionString": "", "RedisStorageDb": 0, "DefaultNumMaintenanceWorkers": 10, "InventoryWorkerCountPercentage": 20, "ProviderWorkerCountPercentage": 10, "UserAffinityWorkerCount": 1}, "TelemetryOptions": {"OtelCollectorTraceEndpoint": "", "OtelCollectorTraceHeaders": "", "OtelCollectorLogEndpoint": "", "OtelCollectorLogHeaders": "", "OtelCollectorMetricEndpoint": "", "OtelCollectorMetricHeaders": ""}, "Logging": {"LogLevel": {"Default": "Error", "Immybot": "Error", "Microsoft.EntityFrameworkCore": "Error", "Microsoft": "Error"}, "Console": {"LogLevel": {}, "FormatterName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormatterOptions": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff"}}, "SignalR": {"MaxQueueLength": 2500, "QueueFullMode": "Wait"}}, "EphemeralAgentSettings": {"AgentCheckinTimeoutSeconds": 180, "ScriptFolderSuffix": ""}, "ZEntityFrameworkExtensions": {"LicenseKey": "", "LicenseName": ""}, "DevLab": {"MaxVms": 1}, "AgentIdentification": {"MaxConcurrentlyIdentifying": 25, "MaxConcurrentlyResolving": 25}, "ServiceBus": {"ManagerConnectionString": "", "TopicName": "", "SendOnlyPolicyKey": "", "SendOnlyPolicyName": "", "NamespaceUri": ""}, "SessionObjectsUpdateHandler": {"SessionLogsBatchSize": 100, "MaintenanceActionsBatchSize": 100, "SessionPhasesBatchSize": 100, "MaintenanceSessionsBatchSize": 100, "MaintenanceSessionStagesBatchSize": 100}, "LightrunOptions": {"Secret": "", "ServerUrl": "", "TagsCollection": []}, "PendoOptions": {"Enabled": false}, "Seq": {"ServerUrl": ""}, "RunspacePool": {"RecycleStaleEntries": true, "RunspaceThreadOptions": "ReuseThread"}}