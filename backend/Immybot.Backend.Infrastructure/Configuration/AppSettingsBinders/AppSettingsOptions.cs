using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;

public class AppSettingsOptions
{
  /// <summary>
  /// The key used for this option in appsettings.json.
  /// </summary>
  public const string SectionKey = "AppSettings";

  public Uri RootUrl { get; set; }
  public bool EnableSyncToCWAutomate { get; set; }
  public bool EnableComputersSync { get; set; }
  public bool EnableAzureUserSyncJob { get; set; }
  public bool EnableNewComputersSync { get; set; }
  public string ChocolateyUrl { get; set; }
  public string ChocolateyOrgUrl { get; set; }
  public string ImmyBotManagerUrl { get; set; }
  public string ImmyBotManagerAadResource { get; set; }
  public string ImmyBotManagerAuthPrincipalObjectId { get; set; }
  public string[] AuthBearerTokenValidAudiences { get; set; }
  public string ImmySupportTechnicianAppRoleName { get; set; }
  public bool DoManagerSettingsLookup { get; set; }
  public string PasswordEncryptionKey { get; set; }
  public string PasswordEncryptionIV { get; set; }
  public bool IsDevInstance { get; set; }
  public string BackendVersion { get; set; }
  public string AppInsightsConnectionString { get; set; }
  public string AppInsightsInstrumentationKey { get; set; }
  public string PluginsDirectory { get; set; }
  public bool IncludePluginsDirectorySubpaths { get; set; }
  public bool EnableGlobalSoftwareAutoUpdate { get; set; }
  public bool EnableProviderQueryHandlerEnhancedTimeoutErrorLogging { get; set; }
  public string ImmyProduct { get; set; }
  public string ImmyUploadStorageContainerName { get; set; }
  public string PublicScriptsContainerName { get; set; }
  public string PublicBinariesContainerName { get; set; }
  public bool UseMinfiedPowershellScripts { get; set; }
  public HashSet<string> UseUnMinifiedScripts { get; set; } = [];
  public string SessionDataAttachmentsContainerName { get; set; }
  public string[] DevCorsOrigins { get; set; }
  public string FrontendDevServer { get; set; }
  public bool DisableSyncingNewProviderAgents { get; set; }

  public Uri ImmyBotCdn { get; set; }
  public string EphemeralVersionContainerName { get; set; }
  public bool UseLocalEphemeralAgent { get; set; }
  public bool UseLocalDevelopmentAgentInstaller { get; set; }
  public string LocalDevelopmentAgentMsiInstallerPath { get; set; }
  public long MaxProviderRouteContentLength { get; set; }
  public Uri PartnerCenterApiUrl { get; set; }
  public string PartnerCenterUserImpersonationScope { get; set; }

  /// <summary>
  /// The primary key of the maintenance action event hub used for authentication and authorization
  /// </summary>
  public string MaintenanceActionEventHubConnectionString { get; set; }

  /// <summary>
  /// The name of the event hub used to process maintenance action data
  /// </summary>
  public string MaintenanceActionEventHubName { get; set; }

  /// <summary>
  /// Indicates whether we should send off action data to the event hub
  /// </summary>
  public bool UseMaintenanceActionEventHub { get; set; }

  /// <summary>
  /// Indicates whether local scripts are run in full language mode; otherwise, they are run in constrained language mode; only use for testing
  /// </summary>
  public bool UseFullLanguagePowerShellModeForLocalScripts { get; set; }

  /// <summary>
  /// Indicates whether interactively-run scripts are run in full language mode; otherwise, they are run in constrained language mode; only use for testing
  /// </summary>
  public bool UseFullLanguagePowerShellModeForInteractiveScriptExecution { get; set; }

  /// <summary>
  /// The connection string for the event hub for PowerShell Errors
  /// </summary>
  public string PowerShellErrorsEventHubConnectionString { get; set; }

  /// <summary>
  /// The name of the event hub used to process maintenance action data
  /// </summary>
  public string PowerShellErrorsEventHubName { get; set; }

  /// <summary>
  /// Indicates whether we should send off action data to the event hub
  /// </summary>
  public bool UsePowerShellErrorsEventHub { get; set; }

  /// <summary>
  /// The time in ms to wait before the watchdog cancels initializing a provider
  /// </summary>
  public int ProviderWatchdogTimeoutMs { get; set; }

  /// <summary>
  /// Indicates that this instance of immy can see developer resources, such as dynamic integrations with developer tag
  /// </summary>
  public bool ImmyBotDeveloper { get; set; }

  /// <summary>
  /// In dev/test environments, this allows for bypassing the script
  /// cache so that scripts can be hot-reloaded.
  /// </summary>
  public bool BypassPowerShellScriptCache { get; set; }

  public bool AllowReset { get; set; }

  [Required]
  [EnumDataType(typeof(Profiler))]
  public Profiler Profiler { get; set; }
}
