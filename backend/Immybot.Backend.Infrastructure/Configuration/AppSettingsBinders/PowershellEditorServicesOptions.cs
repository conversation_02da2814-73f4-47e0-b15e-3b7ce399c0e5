using System.Collections.Generic;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
public class PowershellEditorServicesOptions
{
  /// <summary>
  /// The key used for this option in appsettings.json.
  /// </summary>
  public const string SectionKey = "PowershellEditorServices";

  /// <summary>
  /// Determines if *THIS* process should be in standalone "Language Server" mode. 
  /// </summary>
  public bool ShouldLaunchAsEditorServicesServer => RunEditorServicesOutOfProcess && LaunchAsEditorServicesServer;

  #region Editor Services Configuration
  /// <summary>
  /// Determines whether editor services will be launched as their own process, or within the main application process.
  /// </summary>
  public bool RunEditorServicesOutOfProcess { get; set; }
  /// <summary>
  /// The timeout in seconds for the editor services subprocess to initialize before the main process gives up.
  /// </summary>
  public int SubProcessInitTimeoutSeconds { get; set; } = 15;

  /// <summary>
  /// Enables logging from the subprocess to the browser xTerm console.
  /// </summary>
  public bool EnableSubProcessLoggingToBrowser { get; set; }

  /// <summary>
  /// The log levels for the subprocess. If not set, the subprocess will not explicitly set log level filters.
  /// </summary>
  public Dictionary<string, LogLevel> SubProcessLogLevels { get; set; } = [];

  /// <summary>
  /// If this flag is set, the subprocess will trigger a debugger break when it starts.
  /// NOTE: This only observed if built in DEBUG mode!
  /// </summary>
  public bool TriggerDebuggerBreakInSubProcess { get; set; }


  /// <summary>
  /// The maximum number of subprocesses that can be launched at once. If set to -1, there is no limit.
  /// </summary>
  public int SubProcessLimit { get; set; } = -1;

  /// <summary>
  /// If this flag is set, the subprocess will log detailed information about why it failed to launch.
  /// </summary>
  public bool SubProcessLaunchFailureDetailedLogging { get; set; }

  /// <summary>
  /// The timeout in seconds for a terminal session to be considered inactive before it is prompted, then terminated.
  /// If set to -1 or 0, there is no timeout.
  /// </summary>
  public int TerminalInactivityTimeoutSeconds { get; set; } = -1;
  #endregion

  // The following options are used when launching editor services as separate processes. They are set programmatically via envvars, and should not be set in appsettings.json nor in manager appsettings schemas.
  #region Language Server Configuration (NOT TO BE SET EXPLICITLY)
  /// <summary>
  /// If this flag is set, the observing process should 'switch' modes, launching only Powershell Editor Services, and not the main application.
  /// </summary>
  public bool LaunchAsEditorServicesServer { get; set; }
  public string LanguagePipeName { get; set; }
  public string DebugPipeName { get; set; }
  public int? ScriptId { get; set; }
  public DatabaseType? ScriptType { get; set; }
  public ScriptCategory ScriptCategory { get; set; }
  public ScriptExecutionContext ScriptExecutionContext { get; set; }
  public int ParentProcessPid { get; set; }
  public string SessionInfoFilePath { get; set; }
  #endregion

}
