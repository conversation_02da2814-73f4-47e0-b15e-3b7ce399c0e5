using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Providers.Interfaces;

namespace NCentralRmmProvider;

public partial class NCentralProvider : ISupportsSyncingAgentsOnDemand
{
  /// <summary>
  /// Fetch and sync all devices.  If clientIds param is not null then only sync devices for that client
  /// </summary>
  public async Task SyncAgents(ICollection<string>? clientIds = null, CancellationToken token = default)
  {
    await new SyncDevicesJob(this, QueryHandlerMaker, clientIds, _ncentralAPI!, _formData!, EventHandler, ProviderLinkId).Run(token);
  }
}
