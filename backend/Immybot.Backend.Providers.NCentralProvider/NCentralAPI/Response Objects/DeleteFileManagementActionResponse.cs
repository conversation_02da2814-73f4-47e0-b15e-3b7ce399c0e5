using Newtonsoft.Json;

namespace Immybot.Backend.Providers.NCentralProvider.NCentralAPI.Response_Objects;

public partial class DeleteFileManagementActionResponse
{
  [JsonProperty("error")]
  public string? Error { get; set; }

  [JsonProperty("results")]
  public Result[]? Results { get; set; }
}

public partial class Result
{
  [JsonProperty("target")]
  public string? Target { get; set; }

  [JsonProperty("status")]
  public string? Status { get; set; }

  [JsonProperty("detail")]
  public string? Detail { get; set; }
}
