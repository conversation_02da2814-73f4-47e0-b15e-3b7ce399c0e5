using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;

public class UpdateMaintenanceSessionStageResource : IJoinedSessionGroupResource
{
  public int Id { get; set; }
  public int MaintenanceSessionId { get; set; }
  public SessionStatus StageStatus { get; set; }
  public SessionStageType Type { get; set; }
  public string JobId { get; set; } = string.Empty;
  public JoinedSessionGroupType JoinedSessionGroupType { get; set; } = JoinedSessionGroupType.None;

  public UpdateMaintenanceSessionStageResource() { }

  public UpdateMaintenanceSessionStageResource(MaintenanceSessionStage stage)
  {
    if (stage == null) throw new ArgumentNullException(nameof(stage));

    Id = stage.Id;
    MaintenanceSessionId = stage.MaintenanceSessionId;
    StageStatus = stage.StageStatus;
    Type = stage.Type;
    JobId = stage.JobId ?? string.Empty;
  }
}
