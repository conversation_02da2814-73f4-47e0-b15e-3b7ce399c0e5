using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Microsoft.AspNetCore.Http;

namespace Immybot.Backend.Web.Common.Lib;

public class HttpUserService : IUserService
{
  private readonly IHttpContextAccessor _httpContextAccessor;
  private User? _currentUser;
  private ClaimsPrincipal? _currentPrincipal;

  public User GetCurrentUser() => GetCurrentUserImpl(true)!;

  private User? GetCurrentUserImpl(bool strict)
  {
    _currentUser ??= _httpContextAccessor.HttpContext?.Items["CurrentUser"] as User;
    if (_currentUser == null && strict)
      throw new MissingPrincipalException("The CurrentUser item is not set on the HttpContext. This likely means user is not authenticated");
    return _currentUser;
  }

  public bool TryGetCurrentUser([NotNullWhen(true)] out User? user)
  {
    user = GetCurrentUserImpl(false);
    return user is not null;
  }

  public ClaimsPrincipal? GetCurrentPrincipal(bool strict = true)
  {
    _currentPrincipal ??= _httpContextAccessor.HttpContext?.User;
    if (_currentPrincipal == null && strict)
      throw new MissingPrincipalException("The User item is not set on the HttpContext. This likely means user is not authenticated");
    return _currentPrincipal;
  }

  public Tenant GetCurrentTenant() =>
    GetCurrentUser()?.Tenant ?? throw new InvalidOperationException("User.Tenant has not been hydrated");

  public bool TryGetCurrentTenant([NotNullWhen(true)] out Tenant? tenant)
  {
    tenant = GetCurrentUser()?.Tenant;
    return tenant is not null;
  }

  public HttpUserService(IHttpContextAccessor httpContextAccessor)
  {
    _httpContextAccessor = httpContextAccessor;
  }

  public bool CheckCurrentUserAbility(Action<User> capabilityChecker, bool strict = false)
  {
    try
    {
      capabilityChecker.Invoke(GetCurrentUser());
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility(Action<ClaimsPrincipal> capabilityChecker, bool strict = false)
  {
    try
    {
      var userPrincipal = GetCurrentPrincipal() ??
        throw new MissingPrincipalException(
          "The User item is not set on the HttpContext. This likely means user is not authenticated");

      capabilityChecker.Invoke(userPrincipal);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility(Action<User, ClaimsPrincipal> capabilityChecker, bool strict = false)
  {
    var currentUser = GetCurrentUserImpl(false);
    var currentPrincipal = GetCurrentPrincipal(false);
    try
    {
      if (currentUser == null)
        throw new MissingPrincipalException("The CurrentUser item is not set on the HttpContext. This likely means user is not authenticated");
      if (currentPrincipal == null)
        throw new MissingPrincipalException("The User item is not set on the HttpContext. This likely means user is not authenticated");

      capabilityChecker.Invoke(currentUser, currentPrincipal);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T>(Action<User, ClaimsPrincipal, T> capabilityChecker, T arg, bool strict = false)
  {
    var currentUser = GetCurrentUserImpl(false);
    var currentPrincipal = GetCurrentPrincipal(false);
    try
    {
      if (currentUser == null)
        throw new MissingPrincipalException("The CurrentUser item is not set on the HttpContext. This likely means user is not authenticated");
      if (currentPrincipal == null)
        throw new MissingPrincipalException("The User item is not set on the HttpContext. This likely means user is not authenticated");

      capabilityChecker.Invoke(currentUser, currentPrincipal, arg);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T1, T2>(Action<User, ClaimsPrincipal, T1, T2> capabilityChecker, T1 arg, T2 arg2, bool strict = false)
  {
    var currentUser = GetCurrentUserImpl(false);
    var currentPrincipal = GetCurrentPrincipal(false);
    try
    {
      if (currentUser == null)
        throw new MissingPrincipalException("The CurrentUser item is not set on the HttpContext. This likely means user is not authenticated");
      if (currentPrincipal == null)
        throw new MissingPrincipalException("The User item is not set on the HttpContext. This likely means user is not authenticated");

      capabilityChecker.Invoke(currentUser, currentPrincipal, arg, arg2);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T>(Action<ClaimsPrincipal, T> capabilityChecker, T arg, bool strict = false)
  {
    try
    {
      var currentPrincipal = GetCurrentPrincipal() ??
        throw new MissingPrincipalException(
          "The User item is not set on the HttpContext. This likely means user is not authenticated");

      capabilityChecker.Invoke(currentPrincipal, arg);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T>(Action<User, T> capabilityChecker, T arg, bool strict = false)
  {
    try
    {
      capabilityChecker.Invoke(GetCurrentUser(), arg);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }
  public bool CheckCurrentUserAbility<T1, T2>(Action<User, T1, T2> capabilityChecker, T1 arg, T2 arg2, bool strict = false)
  {
    try
    {
      capabilityChecker.Invoke(GetCurrentUser(), arg, arg2);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T1, T2, T3>(Action<User, T1, T2, T3> capabilityChecker, T1 arg, T2 arg2, T3 arg3, bool strict = false)
  {
    try
    {
      capabilityChecker.Invoke(GetCurrentUser(), arg, arg2, arg3);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }

  public bool CheckCurrentUserAbility<T1, T2, T3, T4>(Action<User, T1, T2, T3, T4> capabilityChecker, T1 arg, T2 arg2, T3 arg3, T4 arg4, bool strict = false)
  {
    try
    {
      capabilityChecker.Invoke(GetCurrentUser(), arg, arg2, arg3, arg4);
      return true;
    }
    catch (Exception)
    {
      if (strict) throw;
      return false;
    }
  }
}
