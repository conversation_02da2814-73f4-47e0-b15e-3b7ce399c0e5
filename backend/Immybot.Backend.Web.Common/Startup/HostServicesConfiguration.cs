using System.Diagnostics;
using HealthChecks.Hangfire;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Converters;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.GlobalSoftwarePersistence.Infrastructure;
using Immybot.Backend.HtmlEmailTemplates.Infrastructure;
using Immybot.Backend.Infrastructure;
using Immybot.Backend.Infrastructure.Configuration.Application;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Infrastructure.Telemetry.Configuration;
using Immybot.Backend.Infrastructure.Telemetry.Meters;
using Immybot.Backend.Manager.StartupTasks.Implementations;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Infrastructure;
using Immybot.Backend.Providers.ImmyAgentProvider.Metrics;
using Immybot.Backend.Providers.ImmyAgentProvider.Signalr.AgentHub;
using Immybot.Backend.RBAC.AuthorizationPolicyManagement.Extensions;
using Immybot.Backend.RBAC.Infrastructure.Extensions;
using Immybot.Backend.Web.Common.Infrastructure;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.JsonConverters;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRLogger;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Lightrun;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Immybot.Backend.Web.Common.Lib.UserAuthentication;
using Immybot.Shared.JsonConverters;
using Immybot.Shared.Services.Startup;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Implementations;

namespace Immybot.Backend.Web.Common.Startup;

public static class HostServicesConfiguration
{
  /// <summary>
  /// Adds the necessary backend components to the builder's <see cref="IServiceCollection"/>
  /// depending on the execution mode.
  /// </summary>
  /// <param name="builder"></param>
  /// <returns></returns>
  public static IHostApplicationBuilder AddBackendComponents(this IHostApplicationBuilder builder)
  {
    // TODO: Remove this once the comment in AddTelemetryServices is resolved.
    if (builder.Environment.IsAspire())
    {
      builder.AddServiceDefaults();
    }

    // Determine the execution mode (to PSES or to ImmyBot, that is the question)
    // We have to do the config fetching this way because the configuration is not yet available in the services (we haven't built the host yet)
    var psesOptions = builder.Configuration.GetSection(PowershellEditorServicesOptions.SectionKey)
      .Get<PowershellEditorServicesOptions>();

    if (psesOptions?.ShouldLaunchAsEditorServicesServer == true)
    {
#if DEBUG
      if (psesOptions.TriggerDebuggerBreakInSubProcess)
        Debugger.Launch();
#endif

      builder.AddTelemetryServices();

      // For PSES, we don't need or want all the extraneous logging providers (SignalR, OpenTelemetry, etc.)
      builder.Services.AddLogging(logging =>
      {
        logging.ClearProviders();

        if (psesOptions is { SubProcessLogLevels: { } logLevels })
          foreach (var (category, level) in logLevels)
            logging.AddFilter(category, level);

        logging.AddSimpleConsole();
      });

      builder.AddPsesComponents();
    }
    else
    {
      builder.ConfigureImmybotBackendLogging();

      // add this AFTER configuring logging above (which clears the logging providers)
      builder.AddTelemetryServices(telemetryConfigurationBuilder => telemetryConfigurationBuilder
        .AddAsyncMeter<AvailableThreadsMeter>(AvailableThreadsMeter.MeterName)
        .AddAsyncMeter<AgentConnectionCountsMeter>(AgentConnectionCountsMeter.MeterName)
        .AddMeter<IAgentConnectionRateMeter, AgentConnectionRateMeter>(AgentConnectionRateMeter.MeterName));

      builder.AddImmybotBackendComponents();
    }

    return builder;
  }

  /// <summary>
  /// Adds the necessary PSES components to the <see cref="IServiceCollection"/>.
  /// </summary>
  public static IServiceCollection AddPsesComponents(this IHostApplicationBuilder builder)
  {
    var services = builder.Services;
    services.AddEditorServicesRequiredServices();
    builder.AddPersistenceServices();
    builder.AddGlobalSoftwarePersistenceServices();

    services.AddHostedService<PowershellEditorServicesSpawnBackgroundTask>();
    services.AddPsesStartupTasks();
    return services;
  }

  /// <summary>
  /// Adds Immybot backend configuration and services to the <see cref="IServiceCollection"/>.
  /// </summary>
  /// <exception cref="InvalidOperationException"></exception>
  public static IHostApplicationBuilder AddImmybotBackendComponents(this IHostApplicationBuilder builder)
  {
    var services = builder.Services;

    services.Configure<HostOptions>(options =>
    {
      options.ServicesStartConcurrently = true;
      options.ServicesStopConcurrently = true;
    });

    services.AddActivatedSingleton<LightrunAgentInitializer>();

    services.Configure<HostOptions>(options =>
    {
      // speed up startup/shutdown and avoid deadlocks where StartAsync of a HostedService depends on a different hosted service already being started
      //    (e.g. ProviderRegistrationService->StartAsync completing depends on MetascriptRunspaceServer running)
      options.ServicesStartConcurrently = true;
      options.ServicesStopConcurrently = true;
    });

    services.Configure<IISServerOptions>(options =>
    {
      options.AutomaticAuthentication = false;
    });

    // RBAC related registrations

    // 1. Configure Identity first
    services.AddIdentity<User, Role>(options =>
    {
      // Not required since we are using EasyAuth
      options.SignIn.RequireConfirmedAccount = false;
    })
    .AddEntityFrameworkStores<ImmybotDbContext>()
    .AddDefaultTokenProviders();

    // 2. Configure Authentication
    builder.AddImmybotAuthentication();

    // 3. Configure Authorization policies
    services.AddDiscoveredAuthorizationPolicies();
    services.AddAuthorizationBuilder();

    // 4. Add RBAC services related to authentication and authorization
    // Authentication-related registrations
    services.AddRBACServices();

    // todo: remove this and migrate existing timestamps to .net 6 compliance
    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

    services.AddHtmlEmailTemplateServices();
    builder.AddApplicationServices();
    builder.AddPersistenceServices();
    builder.AddGlobalSoftwarePersistenceServices();
    services.AddWebServices();

    if (builder.Environment.IsDevelopment())
    {
      services.AddOptions<CorsOptions>()
        .PostConfigure((CorsOptions opts, IOptions<AppSettingsOptions> appSettings) =>
        {
          if (appSettings.Value.DevCorsOrigins is not { } origins)
            origins = ["http://localhost:5000", "http://localhost:5173"];
          opts.AddDefaultPolicy(corsPolicyBuilder =>
          {
            corsPolicyBuilder
              .WithOrigins(origins)
              .SetIsOriginAllowedToAllowWildcardSubdomains()
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
          });
        });
      services.AddCors();
    }

    services.AddRazorPages();
    services.AddControllers()
      .AddMvcOptions(options =>
      {
        // .net core 3: default value is 8192
        options.MaxIAsyncEnumerableBufferLimit = 8192;
        options.ModelBinderProviders.Insert(0, new SemanticVersionModelBinderProvider());
        options.ModelBinderProviders.Insert(0, new JsonElementModelBinderProvider());
      })
      .AddNewtonsoftJson(options =>
      {
        options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;

        // Include will make all request bodies show
        // up as NULL if you fail to pass a required parameter.
        // E.g., if `int UpdatedBy { get; set; }` is on the request body, and the frontend does
        // not pass that property.  The request body will be invalid and show up as null.
        // Easy way to circumvent this issue is to add a JSON attribute to the property causing
        // issues
        // [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        options.SerializerSettings.NullValueHandling = NullValueHandling.Include;

        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
        options.SerializerSettings.Converters.Add(new SemanticVersionConverter());
        options.SerializerSettings.Converters.Add(new JsonElementConverter());
        options.SerializerSettings.Converters.Add(new LiteralStringConverter());
        options.SerializerSettings.Converters.Add(new OneOfConverter());
      });

    // signalR should pull from our newtonsoft JSON serializer settings
    services.AddSignalR()
      .AddHubOptions<ImmyBotUserHub>(a =>
      {
        a.EnableDetailedErrors = true;
      })
      .AddHubOptions<AgentHub>(x =>
      {
        x.MaximumReceiveMessageSize = AppConstants.AgentSignalrMaxMessageSize;
        x.MaximumParallelInvocationsPerClient = 5;
        x.EnableDetailedErrors = true;
      })
      .AddMessagePackProtocol()
      .AddNewtonsoftJsonProtocol(options =>
      {
        options.PayloadSerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
        options.PayloadSerializerSettings.NullValueHandling = NullValueHandling.Ignore;
        options.PayloadSerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
        options.PayloadSerializerSettings.Converters.Add(new SemanticVersionConverter());
        options.PayloadSerializerSettings.Converters.Add(new JsonElementConverter());
        options.PayloadSerializerSettings.Converters.Add(new LiteralStringConverter());
        options.PayloadSerializerSettings.Converters.Add(new OneOfConverter());
      });

    // Register the Swagger generator, defining 1 or more Swagger documents
    services.AddSwaggerGen(c =>
    {
      c.SwaggerDoc("v1", new OpenApiInfo { Title = "immy.bot API", Version = "v1" });
    });

    services.AddBackendStartupTasks();

    services.AddTransient<IResetInstanceCmd, ResetInstanceCmd>();
    services.AddSingleton<IUserHubDatabaseActions, UserHubDatabaseActions>();

    services.AddMetricsServices();

    // Health Checks
    services.AddSingleton(_ => new HangfireOptions { MinimumAvailableServers = 1 });
    services.AddDefaultHealthChecks()
      .AddDbContextCheck<ImmybotDbContext>()
      .AddDbContextCheck<SoftwareDbContext>()
      .AddHangfireRecurringJobCheck(tags: ["live"]);

    // Static Files
    services.AddSpaStaticFiles(cfg =>
    {
      // instruct asp net server to look in directory named 'client' for static assets
      cfg.RootPath = "client";
    });

    return builder;
  }

  public static void AddBackendStartupTasks(this IServiceCollection services)
  {
    services.AddStartupTaskRunner();

    // When the app starts up:
    // 0. set our Z.EntityFramework.Extensions license
    services.AddStartupTask<ZEntityFrameworkInitializer>();
    // 1. initialize the manager-provided settings
    services.AddStartupTask<ManagerSettingsInitializationFilter>();
    // 2. migrate the global db if configured to do so
    services.AddStartupTask<GlobalPersistenceInitializationFilter>();
    // 3. migrate the local db if configured to do so
    services.AddStartupTask<PersistenceInitializationFilter>();
    // 4. initialize local db with necessary data and perform any necessary non-ef migrations
    services.AddStartupTask<ImmybotDataInitializationFilter>();
    // 5. initialize rbac seed data
    services.AddStartupTask<RoleSeedDataStartupTask>();
    // 6. initialize the immy agent integration
    services.AddStartupTask<ImmyBotAgentFilter>();
    // 7. If `UseDevTestAgents` is enabled, ensure dev entities are created.
    services.AddStartupTask<DevEntitiesInitFilter>();
    // 8. create an audit log entry for the startup
    services.AddStartupTask<AuditStartupTask>();
  }

  public static void AddPsesStartupTasks(this IServiceCollection services)
  {
    services.AddStartupTaskRunner();

    services.AddStartupTask<ZEntityFrameworkInitializer>();
  }

  public static IHostApplicationBuilder AddConfigurationSources(this IHostApplicationBuilder builder)
  {
    builder
      .AddJsonFilesConfiguration()
      .AddAzureAppConfigStoreConfiguration()
      .AddKeyVaultConfiguration()
      .AddCustomEnvironmentVariablesConfiguration(); // let env vars override everything else

    return builder;
  }

  public static void ConfigureImmybotBackendLogging(this IHostApplicationBuilder builder)
  {
    var seqSection = builder.Configuration.GetSection("Seq");
    builder.Services.AddLogging(logging =>
    {
      logging.ClearProviders();

      if (!string.IsNullOrEmpty(seqSection["ServerUrl"]))
      {
        // to use seq, run the following docker command to spin up the seq server:
        // docker run --name seq -d --restart unless-stopped -e ACCEPT_EULA=Y -p 5341:80 datalust/seq:latest
        // Update your app settings to include the Seq:ServerUrl key with http://localhost:5431
        logging.AddSeq(seqSection);
      }

      logging.AddConsole();
      logging.AddSignalRLogger();
    });
  }

  /// <summary>
  /// Registers options to be bound to sections from IConfiguration.
  /// These can be injected into services using the Options interfaces.
  /// See <see href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/configuration/options#options-interfaces"/>.
  /// </summary>
  public static IHostApplicationBuilder ConfigureImmybotOptions(this IHostApplicationBuilder builder)
  {
    var configuration = builder.Configuration;
    var services = builder.Services;
    var environment = builder.Environment;

    services.Configure<AppSettingsOptions>(
      configuration.GetSection(AppSettingsOptions.SectionKey));

    services.Configure<SessionObjectsUpdateHandlerOptions>(
      configuration.GetSection(SessionObjectsUpdateHandlerOptions.SectionKey));

    services.Configure<AzureActiveDirectoryAuthOptions>(
      configuration.GetSection(AzureActiveDirectoryAuthOptions.SectionKey));

    services.Configure<MockUserAuthOptions>(
      AuthConstants.ImmyMockAuthenticationScheme,
      configuration.GetSection(MockUserAuthOptions.SectionKey));

    services.Configure<AzureKeyVaultOptions>(
      configuration.GetSection(AzureKeyVaultOptions.SectionKey));

    services.Configure<DatabaseOptions>(
      configuration.GetSection(DatabaseOptions.SectionKey));

    services.Configure<ImmyAgentOptions>(
      configuration.GetSection(ImmyAgentOptions.SectionKey));

    services.Configure<AzureFunctionOptions>(
      configuration.GetSection(AzureFunctionOptions.SectionKey));

    services.Configure<HangfireSettings>(
      configuration.GetSection(HangfireSettings.SectionKey));

    services.Configure<EphemeralAgentSettings>(
      configuration.GetSection(EphemeralAgentSettings.SectionKey));

    services.Configure<FeatureOptions>(
      configuration.GetSection(FeatureOptions.SectionKey));

    services.Configure<ZEntityFrameworkExtensionsOptions>(
      configuration.GetSection(ZEntityFrameworkExtensionsOptions.SectionKey));

    services.Configure<DevLabOptions>(
      configuration.GetSection(DevLabOptions.SectionKey));

    services.Configure<AgentIdentificationOptions>(
      configuration.GetSection(AgentIdentificationOptions.SectionKey));

    services.Configure<ServiceBusOptions>(
      configuration.GetSection(ServiceBusOptions.SectionKey));

    services.Configure<PendoSettings>(
      configuration.GetSection(PendoSettings.SectionKey));

    services.Configure<PowershellEditorServicesOptions>(
      configuration.GetSection(PowershellEditorServicesOptions.SectionKey));

    services.Configure<TelemetryOptions>(
      configuration.GetSection(TelemetryOptions.SectionKey));

    services.Configure<LightrunOptions>(
      configuration.GetSection(LightrunOptions.SectionKey));

    services.AddOptions<AgentOptions>()
      .Bind(configuration.GetSection(LightrunOptions.SectionKey))
      .PostConfigure(
        (AgentOptions opts, IOptionsMonitor<AppSettingsOptions> appSettings, IConfiguration config) =>
        {
          // This will get rerun any time the app settings or LightrunOptions change

          // Rebind from base configuration to reset opts.Tags so we can add to it
          config.GetSection(LightrunOptions.SectionKey).Bind(opts);

          opts.Tags =
          [
            ..(opts.Tags ?? []),
            environment.EnvironmentName,
            appSettings.CurrentValue.BackendVersion,
            appSettings.CurrentValue.RootUrl.ToString(),
          ];
        });

    services.Configure<RunspacePoolOptions>(
      configuration.GetSection(RunspacePoolOptions.SectionKey));

    return builder;
  }
}
