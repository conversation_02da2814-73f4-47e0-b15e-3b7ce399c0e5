using App.Metrics.Reporting;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Manager.Shared;
using Immybot.Manager.SitesApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.Hosting;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ManagerController : ControllerBase
{
  private readonly IManagerRequestsHandler _managerRequestsHandler;

  public ManagerController(
    IManagerRequestsHandler managerRequestsHandler)
  {
    _managerRequestsHandler = managerRequestsHandler;
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.UpdateSubscription)]
  public async Task<IActionResult> UpdateSubscription([FromBody] SubscriptionDetails body)
  {
    await _managerRequestsHandler.HandleUpdateSubscriptionRequest(body);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.UpdateImmySupportAccessGrantDetails)]
  public async Task<IActionResult> UpdateImmySupportAccessGrantDetails(
    [FromBody] MspInstanceImmySupportAccessGrantDetails body)
  {
    await _managerRequestsHandler.HandleUpdateImmySupportAccessGrantDetails(body);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.ReleaseChannelUpdated)]
  public async Task<IActionResult> ReleaseChannelUpdated(
    [FromBody] ReleaseChannelUpdatedRequestBody  body)
  {
    await _managerRequestsHandler.HandleReleaseChannelUpdated(body.ReleaseChannel);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.UpdateRelease)]
  public IActionResult UpdateRelease([FromBody] ReleaseDetails release)
  {
    _managerRequestsHandler.HandleUpdateReleaseDetailsRequest(release);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpDelete(ImmyBackendRoutes.DeleteRelease)]
  public IActionResult DeleteRelease([FromRoute] SemanticVersion tag)
  {
    _managerRequestsHandler.HandleDeleteReleaseRequest(tag);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.SetInstanceUpdating)]
  public IActionResult SetInstanceUpdating([FromQuery] string source)
  {
    _managerRequestsHandler.HandleSetInstanceUpdatingRequest(source);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.SetInstanceUpdateFailed)]
  public IActionResult SetInstanceUpdateFailed()
  {
    _managerRequestsHandler.HandleSetInstanceUpdateFailedRequest();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.TriggerMetricsRefresh)]
  public async Task<IActionResult> TriggerMetricsRefresh(
    [FromServices] IApplicationMetricsCollector dbMetricsCollector,
    [FromServices] IRunMetricsReports reportRunner,
    CancellationToken cancellationToken)
  {
    // First make sure the db metrics are up-to-date
    await dbMetricsCollector.DoCollection(cancellationToken);

    // Then flush the metrics via the mgr reporter
    await reportRunner.RunAsync<ManagerMetricsReporter>(cancellationToken);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IManagerManagePermission))]
  [HttpPost(ImmyBackendRoutes.TriggerConfigRefresh)]
  public async Task<IActionResult> TriggerConfigRefresh(
    [FromServices] IConfiguration configuration,
    [FromServices] IHostApplicationLifetime appLifetime)
  {
    if (configuration is not IConfigurationRoot configurationRoot)
    {
      throw new InvalidOperationException("Configuration must be an IConfigurationRoot");
    }
    var refresherTasks = new List<Task>();
    foreach (var provider in configurationRoot.Providers)
    {
      if (provider is IConfigurationRefresher refresher)
      {
        refresherTasks.Add(refresher.RefreshAsync(appLifetime.ApplicationStopping));
      }
      else if (provider is AzureKeyVaultConfigurationProvider p)
      {
        // AzureKeyVaultConfigurationProvider does not implement IConfigurationRefresher,
        // but its Load method does handle refreshing after it's already been loaded
        refresherTasks.Add(Task.Run(() => p.Load()));
      }
    }

    // Trigger a refresh of the app config, which will cause the app to reload all settings from
    // the app config store at the next opportunity (when its internal cache expires)
    await Task.WhenAll(refresherTasks);

    return NoContent();
  }
}
