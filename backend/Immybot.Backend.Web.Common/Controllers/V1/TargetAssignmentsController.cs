using System.Text.Json;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Commands.Payloads.TargetAssignments;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.SoftwareDbContextExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.JsonConverters;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class TargetAssignmentsController : Controller
{
  private readonly IResourceAuthorizerFlow _resourceAuthorizerFlow;
  private readonly IUserService _userService;
  private readonly Abilities _abilities;
  private static readonly JsonSerializerOptions _JsonSerializerOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
  static TargetAssignmentsController()
  {
    _JsonSerializerOptions.Converters.Add(new JObjectConverter());
  }

  public TargetAssignmentsController(
    IResourceAuthorizerFlow resourceAuthorizerFlow,
    IUserService userService,
    Abilities abilities)
  {
    _resourceAuthorizerFlow = resourceAuthorizerFlow;
    _userService = userService;
    _abilities = abilities;
  }

  private static string? VerifyDesiredSoftwareState(MaintenanceType maintenanceType, DesiredSoftwareState? desiredSoftwareState)
  {
    if (desiredSoftwareState == null && (
      maintenanceType == MaintenanceType.ChocolateySoftware
      || maintenanceType == MaintenanceType.GlobalSoftware
      || maintenanceType == MaintenanceType.LocalSoftware
      || maintenanceType == MaintenanceType.NiniteSoftware))
    {
      return "DesiredSoftwareState cannot be blank for software deployments";
    }
    return null;
  }

  private static string? VerifyTargetAssignment(ITargetAssignmentDetailsBase body)
  {
    return VerifyDesiredSoftwareState(body.MaintenanceType, body.DesiredSoftwareState);
  }

  /// <summary>
  /// Wipe out any data that should not be present and return an error string if any data is missing
  /// </summary>
  /// <param name="request"></param>
  /// <returns></returns>
  private static string? ScrubLocalTargetAssignmentRequest(LocalTargetAssignmentPayload request)
  {
    var err = VerifyDesiredSoftwareState(request.MaintenanceType, request.DesiredSoftwareState);

    // tenant id
    if (TargetTypesRequiringTenant.Contains(request.TargetType))
    {
      if (!request.TenantId.HasValue) err = $"Tenant is required for target type {request.TargetType}";
    }
    else if (!TargetTypesCanHaveTenant.Contains(request.TargetType))
    {
      request.TenantId = null;
    }

    return err;
  }

  private static List<TargetType> TargetTypesCanHaveTenant = [TargetType.ProviderDeviceGroup];

  private static List<TargetType> TargetTypesRequiringTenant => new List<TargetType>
    {
      TargetType.AllForTenant,
      TargetType.AzureGroup,
      TargetType.TenantFilterScript,
      TargetType.TenantMetascript,
      TargetType.SpecificTenant,
      TargetType.TenantTag
    };

  #region Change Requests

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetChangeRequestDiff)]
  public async Task<ActionResult<ChangeRequestDiff>> GetChangeRequestDiff(
    [FromRoute] int changeRequestId,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanSeeChangeRequest, changeRequest, true);

    var diff = await changeRequestStore.GetTargetAssignmentChangeRequestDiff(changeRequest, default);
    return Ok(diff);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateChangeRequestForExistingDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> CreateChangeRequestForExistingDeployment(
    [FromRoute] int deploymentId,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanCreateChangeRequestForExistingTargetAssignment, assignment, true);

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.CreateChangeRequestForExistingEntity(
      _userService.GetCurrentUser(),
      ChangeRequestObjectType.TargetAssignment,
      assignment,
      deploymentId,
      body,
      default);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateChangeRequestForExistingDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> UpdateChangeRequestForExistingDeployment(
    [FromRoute] int deploymentId,
    [FromRoute] int changeRequestId,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)

  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanUpdateChangeRequestForExistingTargetAssignment, changeRequest, true);

    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment is null) return NotFound();

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.UpdateChangeRequestForExistingEntity(
      _userService.GetCurrentUser(),
      changeRequest,
      assignment,
      body,
      default);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetChangeRequest)]
  public ActionResult<ChangeRequestResponse> GetDeploymentChangeRequest(
    [FromRoute] int changeRequestId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanSeeChangeRequest, changeRequest, true);

    return Ok(ChangeRequestResponse.CreateResponse(changeRequest));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllChangeRequests)]
  public ActionResult<IEnumerable<ChangeRequestResponse>> GetAllChangeRequests(
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequestsDisposable = changeRequestStore
      .GetChangeRequestsForType(ChangeRequestObjectType.TargetAssignment);
    Response.RegisterForDispose(changeRequestsDisposable);
    var changeRequests = changeRequestsDisposable.Value;

    var tenant = _userService.GetCurrentTenant();

    // non-msp tenants are limited to seeing change requests for their own tenant
    if (tenant is { IsMsp: false })
    {
      changeRequests = changeRequests.Where(a =>
        (a.TargetAssignment != null && a.TargetAssignment.TenantId == tenant.Id)
        || (a.CreatedByUser != null && a.CreatedByUser.TenantId == tenant.Id));
    }

    return Ok(changeRequests.Select(ChangeRequestResponse.Projection));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllChangeRequestsForDeployment)]
  public ActionResult<IEnumerable<ChangeRequestResponse>> GetAllChangeRequestsForDeployment(
    [FromRoute] int deploymentId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore
  )
  {
    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment is null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignment, assignment, true);

    var changeRequestsDisposable =
      changeRequestStore.GetChangeRequestsForEntity(ChangeRequestObjectType.TargetAssignment, deploymentId);
    Response.RegisterForDispose(changeRequestsDisposable);
    var changeRequests = changeRequestsDisposable.Value.Select(ChangeRequestResponse.Projection);

    return Ok(changeRequests);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateChangeRequestForNewDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> CreateChangeRequestForNewDeployment(
    [FromBody] CreateLocalTargetAssignmentPayload body,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    if (body.MaintenanceType is MaintenanceType.LocalSoftware)
    {
      var softwareId = Convert.ToInt32(body.MaintenanceIdentifier);
      await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(softwareId),
        true);
    }

    _userService.CheckCurrentUserAbility(_abilities.CanCreateTargetAssignment, body, true);
    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.CreateChangeRequestForNewEntity(
      _userService.GetCurrentUser(),
      ChangeRequestObjectType.TargetAssignment,
      body,
      default);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateChangeRequestForNewDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> UpdateChangeRequestForNewDeployment(
    [FromRoute] int changeRequestId,
    [FromBody] UpdateLocalTargetAssignmentPayload payload,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanSeeChangeRequest, changeRequest, true);

    var updatedChangeRequest = await changeRequestStore.UpdateChangeRequestForNewEntity(_userService.GetCurrentUser(), changeRequest, payload, default);

    return Ok(ChangeRequestResponse.CreateResponse(updatedChangeRequest));
  }

  #endregion

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetMaintenanceItemOrder)]
  public ActionResult<IEnumerable<MaintenanceItemOrder>> GetMaintenanceItemOrder([FromServices] IRetrieveMaintenanceItemOrdersCmd cmd)
  {
    var ordering = cmd.Run();

    return Ok(ordering.OrderBy(a => a.SortOrder));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateMaintenanceItemOrder)]
  public ActionResult<MaintenanceItemOrder> UpdatePriority(
    [FromServices] IUpdateMaintenanceItemOrderCmd cmd,
    [FromBody] UpdateMaintenanceItemOrderPayload payload)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);

    var update = cmd.Run(payload);

    return Ok(update);
  }

  #region recommended approvals

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetRecommendedApprovals)]
  public ActionResult<IEnumerable<GetRecommendedApprovalResponse>> GetRecommendedApprovals(
    [FromServices] ImmybotDbContext ctx)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    var approvals = ctx.GetRecommendedApprovals()
      .Select(GetRecommendedApprovalResponse.Projection);
    return Ok(approvals);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateRecommendedApprovals)]
  public ActionResult<IEnumerable<GetRecommendedApprovalResponse>> UpdateRecommendedApprovals(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IUpdateRecommendedApprovalCmd cmd,
    [FromBody] UpdateRecommendedApprovalsRequestBody body)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    cmd.UpdateApprovals(body.Approvals, _userService.GetCurrentUser());
    var approvalIds = body.Approvals.Select(a => a.GlobalTargetAssignmentId);
    var updatedApprovals = ctx.GetRecommendedApprovals()
      .Select(GetRecommendedApprovalResponse.Projection)
      .Where(a => approvalIds.Contains(a.GlobalTargetAssignmentId))
      .ToList();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").RecommendedTargetAssignmentApprovalsUpdated(updatedApprovals);
    }).Forget();
    return Ok(updatedApprovals);
  }

  #endregion

  #region optional target assignment approvals

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllOptionalTargetAssignmentApprovalsForComputer)]
  public ActionResult<IEnumerable<GetOptionalTargetAssignmentApprovalResponse>> GetAllOptionalTargetAssignmentApprovalsForComputer(
    [FromRoute] int computerId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFeatureTracker featureTracker)
  {
    featureTracker.IsEnabled(FeatureEnum.OptionalDeploymentFeature, strict: true);

    var computer = ctx.GetComputerById(computerId);
    if (computer is null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeComputer, computer, true);

    var approvals = ctx
      .GetOptionalTargetAssignmentApprovalsByComputerId(computerId)
      .Select(GetOptionalTargetAssignmentApprovalResponse.Projection);

    return Ok(approvals);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateOptionalTargetAssignmentApproval)]
  public async Task<ActionResult<bool>> UpdateOptionalTargetAssignmentApproval(
    [FromRoute] int id,
    [FromBody] UpdateOptionalTargetAssignmentApprovalPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFeatureTracker featureTracker)
  {
    featureTracker.IsEnabled(FeatureEnum.OptionalDeploymentFeature, strict: true);

    if (body.TargetType is TargetType.Computer)
    {
      var computer = ctx.GetComputerById(Convert.ToInt32(body.Target));
      if (computer is null) return NotFound();
      _userService.CheckCurrentUserAbility(_abilities.CanSeeComputer, computer, true);
    }
    else
    {
      throw new NotImplementedException("Only computer target types are current supported");
    }

    var updatedApproval = await ctx.UpdateOptionalTargetAssignmentApproval(body, id);

    if (updatedApproval is null) return NotFound();
    return NoContent();
  }

  #endregion

  #region global

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateNotesGlobal)]
  public async Task<ActionResult> UpdateNotesGlobal(
    [FromRoute] int id,
    [FromServices] SoftwareDbContext ctx,
    [FromBody] UpdateNotesPayload body,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentStore targetAssignmentStore,
    CancellationToken token)
  {
    var assignment = ctx.GetTargetAssignmentById(id, includeNotes: true);
    if (assignment == null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanUpdateTargetAssignment, assignment, true);

    var user = _userService.GetCurrentUser();

    await targetAssignmentStore.UpdateNotesGlobalAsync(
      user,
      assignment,
      body.Notes,
      token);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Include(a => a.Notes)
      .Where(a => a.Id == id);
    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, default)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentUpdated(populatedAssignment);
    }, token).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllGlobal)]
  public async Task<ActionResult<IEnumerable<GlobalTargetAssignmentResource>>> GetAllGlobal(
    [FromServices] SoftwareDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ApplicationSieveProcessor appSieveProcessor,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var assignments = ctx.GetAllTargetAssignments();

    if (sieveModel != null) assignments = appSieveProcessor.Apply(sieveModel, assignments);
    var resources = assignments.Select(GlobalTargetAssignmentResource.Projection);
    var populatedAssignments = await targetPopulator.Populate(resources, default);
    return Ok(populatedAssignments);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> GetGlobal(
    [FromServices] ITargetAssignmentActions actions,
    [FromRoute] int id,
    CancellationToken token)
  {
    var assignment = await actions.GetTargetAssignmentById(id, DatabaseType.Global, token, includeNotes: true);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignment, assignment, true);
    return Ok(new GlobalTargetAssignmentResource(assignment));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetGlobalTargetAssignmentType)]
  public ActionResult<GetTargetAssignmentTypeResponse> GetGlobalTargetAssignmentType(
    [FromRoute] int id,
    [FromServices] SoftwareDbContext ctx)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    return Ok(new GetTargetAssignmentTypeResponse(assignment));
  }

  // todo: Need to validate the target type / scope and ensure non-msps cannot create cross-tenant target types
  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> CreateGlobal(
    [FromBody] CreateGlobalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();

    if (VerifyTargetAssignment(body) is string error) return BadRequest(error);

    if (body.MaintenanceType is MaintenanceType.GlobalMaintenanceTask && ctx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)))
    {
      return BadRequest("New deployments cannot be created for deprecated tasks.");
    }

    var created = ctx.CreateTargetAssignment(body);

    var query = ctx.TargetAssignments.Where(a => a.Id == created.Id);

    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, default)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentCreated(populatedAssignment);
    }).Forget();

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPut(TargetAssignmentApiRoutes.UpdateGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> UpdateGlobal(
    [FromRoute] int id,
    [FromBody] UpdateGlobalTargetAssignmentPayload body,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    body.Id = id;

    if (VerifyTargetAssignment(body) is string error) return BadRequest(error);

    _ = ctx.UpdateTargetAssignment(body);

    var query = ctx.TargetAssignments.Where(a => a.Id == id);
    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, default)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentUpdated(populatedAssignment);
    }).Forget();

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(TargetAssignmentApiRoutes.DeleteGlobal)]
  public IActionResult DeleteGlobal(
    [FromRoute] int id,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory)
  {
    using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    ctx.DeleteTargetAssignment(assignment);

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentDeleted(id);
    }).Forget();

    return NoContent();
  }

  #endregion

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateNotesLocal)]
  public async Task<ActionResult> UpdateNotesLocal(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] UpdateNotesPayload body,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentStore targetAssignmentStore,
    CancellationToken token)
  {
    var assignment = ctx.GetTargetAssignmentById(id, includeNotes: true);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanUpdateTargetAssignment, assignment, true);

    var user = _userService.GetCurrentUser();

    await targetAssignmentStore.UpdateNotesLocalAsync(
      user,
      assignment,
      body.Notes,
      token);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Include(a => a.Notes)
      .Where(a => a.Id == id);
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, default)).First();

    var userTenantId = _userService.GetCurrentTenant().Id;
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedAssignment);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedAssignment);
    }, token).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllLocal)]
  public async Task<ActionResult<IEnumerable<LocalTargetAssignmentResource>>> GetAllLocal(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ApplicationSieveProcessor appSieveProcessor,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    IQueryable<TargetAssignment> assignments;
    if (_userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser))
      assignments = ctx.GetAllTargetAssignments();
    else
      assignments = ctx.GetAllTargetAssignmentsAtTenant(_userService.GetCurrentTenant().Id);

    if (sieveModel != null) assignments = appSieveProcessor.Apply(sieveModel, assignments);
    var resources = assignments.Select(LocalTargetAssignmentResource.Projection);
    var populatedAssignments = await targetPopulator.Populate(resources, default);

    if (!_userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser))
    {
      populatedAssignments = populatedAssignments.Where(a => a.TargetScopeName != "Cross Tenant").ToList();
    }

    return Ok(populatedAssignments);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPatch(TargetAssignmentApiRoutes.BatchUpdateLocal)]
  public async Task<IActionResult> BatchUpdateLocal(
    [FromBody] BatchUpdateAssignmentRequest request,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    CancellationToken token)
  {
    // Limiting this to msp admins for now due to the impending rbac changes that may require further refactoring.
    // Otherwise, we would need to check for the ability to update each individual assignment.
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    await ctx.BatchUpdateAssignments(request, token);

    // push target assignment to frontend via ws
    var query = ctx.TargetAssignments.AsNoTracking().Where(a => request.TargetAssignmentIds.Contains(a.Id));
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignments = (await targetPopulator.Populate(resources, default)).ToList();

    var userTenantId = _userService.GetCurrentTenant().Id;
    Task.Run(async () =>
      {
        foreach (var assignment in populatedAssignments)
        {
          await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(assignment);
          await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(assignment);
        }
      },
      token).Forget();

    return NoContent();
  }


  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpPost(TargetAssignmentApiRoutes.GetDuplicatesLocal)]
  public ActionResult<TargetAssignmentDuplicateResponse> GetDuplicatesLocal(
    [FromBody] DuplicateTargetAssignmentPayload req,
    [FromServices] ImmybotDbContext ctx)
  {
    // no need for authorization since they can't modify the target assignment if they do not have access to it
    var dupes = ctx.GetDuplicateTargetAssignments(req);
    return Ok(new TargetAssignmentDuplicateResponse(dupes));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> Get(
    [FromServices] ITargetAssignmentActions actions,
    [FromRoute] int id,
    CancellationToken token)
  {
    var assignment = await actions.GetTargetAssignmentById(
      id,
      DatabaseType.Local,
      token,
      includeNotes: true,
      includeVisibility: true);

    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignment, assignment, true);
    return Ok(new LocalTargetAssignmentResource(assignment));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpPost(TargetAssignmentApiRoutes.ResolveVisibilityTargetAssignments)]
  public async Task<ActionResult<IEnumerable<TargetAssignmentResource>>> ResolveVisibilityTargetAssignments(
    [FromBody] ResolveVisibilityTargetAssignmentsRequest request,
    [FromServices] IVisibilityAssignmentResolver resolver,
    CancellationToken token)
  {
    _userService.CheckCurrentUserAbility(_abilities.CanResolveVisibilityTargetAssignments, request.Visibility, true);
    var triggeredBy = _userService.GetCurrentUser().Person;
    if(triggeredBy is null) return BadRequest($"Unable to resolve person for {request.Visibility.ToString()} target assignments");

    return Ok(await resolver.GetTargetAssignmentsForVisibility(triggeredBy, request, token));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedTenants)]
  public async Task<ActionResult<IEnumerable<GetTenantResponse>>> CalculateTargetedTenants(
    [FromServices] ITenantAssignmentActions tenantAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    _userService.CheckCurrentUserAbility(_abilities.CanSeeComputersTargetedByTargetAssignment, body, true);

    try
    {
      var tenantsDisposable = await tenantAssignmentActions.GetTenantsInTarget(
        body.TargetType,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants,
        allowAccessToParentTenant: body.AllowAccessToParentTenant,
        providerLinkId: body.ProviderLinkId,
        providerClientGroupType: body.ProviderClientGroupType);
      Response.RegisterForDispose(tenantsDisposable);
      var tenants = tenantsDisposable.Value.Select(GetTenantResponse.GetProjection());

      return Ok(tenants.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedComputers)]
  public async Task<ActionResult<IEnumerable<CalculateTargetedComputerResponse>>> CalculateTargetedComputers(
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    _userService.CheckCurrentUserAbility(_abilities.CanSeeComputersTargetedByTargetAssignment, body, true);
    try
    {
      var computersDisposable = (await computerAssignmentActions.GetComputersInTarget(
        body.TargetType,
        body.TargetGroupFilter,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants,
        allowAccessToParentTenant: body.AllowAccessToParentTenant,
        providerLinkId: body.ProviderLinkId,
        providerDeviceGroupType: body.ProviderDeviceGroupType,
        providerClientGroupType: body.ProviderClientGroupType,
        excludeOnboarding: false,
        excludeOnboarded: body.OnboardingOnly));
      Response.RegisterForDispose(computersDisposable);
      var computers = computersDisposable.Value.Select(c => new CalculateTargetedComputerResponse
      {
        Id = c.Id,
        Cn = c.ComputerName ?? string.Empty,
        On = c.Agents
          .Where(a => a.SupportsRunningScripts)
          .Any(r => r.IsOnline && r.ProviderLink != null && !r.ProviderLink.Disabled && r.ProviderLink.HealthStatus != HealthStatus.Unhealthy),
        Tn = c.Tenant != null ? c.Tenant.Name : String.Empty,
        Ti = c.TenantId,
        Ppi = c.PrimaryPersonId,
        Ppn = c.PrimaryPerson != null ? c.PrimaryPerson.DisplayName : string.Empty,
        Os = c.OperatingSystem ?? string.Empty,
        Obs = c.OnboardingStatus,
        Sn = c.SerialNumber ?? string.Empty,
        Ct = c.ChassisTypes ?? new List<int>(),
        Dr = c.DomainRole,
        Sb = c.IsSandbox
      });

      return Ok(computers.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedPersons)]
  public async Task<ActionResult<IEnumerable<TargetedPerson>>> CalculateTargetedPersons(
    [FromServices] IPersonAssignmentActions personAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    _userService.CheckCurrentUserAbility(_abilities.CanSeeComputersTargetedByTargetAssignment, body, true);

    try
    {
      var personsDisposable = (await personAssignmentActions.GetPersonsInTarget(
        body.TargetType,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants));
      Response.RegisterForDispose(personsDisposable);
      var persons = personsDisposable.Value.Select(TargetedPerson.Projection);
      return Ok(persons.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetLocalTargetAssignmentType)]
  public ActionResult<GetTargetAssignmentTypeResponse> GetLocalTargetAssignmentType(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignmentType, assignment, true);
    return Ok(new GetTargetAssignmentTypeResponse(assignment));
  }

  // todo: Need to validate the target type / scope and ensure non-msps cannot create cross-tenant target types
  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> Create(
    [FromBody] CreateLocalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentEmitter targetAssignmentEmitter,
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromServices] ITargetPopulator targetPopulator)
  {
    if (body.MaintenanceType is MaintenanceType.LocalSoftware)
    {
      var softwareId = Convert.ToInt32(body.MaintenanceIdentifier);
      await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(softwareId),
        true);
    }
    await using var ctx = dbFactory();

    _userService.CheckCurrentUserAbility(_abilities.CanCreateTargetAssignment, body, true);

    if (VerifyTargetAssignment(body) is string error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is string error2) return BadRequest(error2);

    if (
      body.MaintenanceType is MaintenanceType.LocalMaintenanceTask && ctx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)) ||
      body.MaintenanceType is MaintenanceType.GlobalMaintenanceTask && globalCtx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)))
    {
      return BadRequest("New deployments cannot be created for deprecated tasks.");
    }

    var created = ctx.CreateTargetAssignment(body);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Where(a => a.Id == created.Id);

    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, default)).First();
    targetAssignmentEmitter.Emit(populatedAssignment);

    if (created.TargetCategory == TargetCategory.Computer &&
      created.TargetType != TargetType.Metascript &&
      created.TargetType != TargetType.FilterScript &&
      created.TargetType != TargetType.TenantFilterScript &&
      created.TargetType != TargetType.TenantMetascript)
    {
      var targetedComputers = await (await computerAssignmentActions.GetComputersInTarget(
          body.TargetType,
          body.TargetGroupFilter,
          body.Target,
          tenantId: body.TenantId,
          includeChildTenants: body.PropagateToChildTenants,
          allowAccessToParentTenant: body.AllowAccessToParentTenant,
          providerLinkId: body.ProviderLinkId,
          providerDeviceGroupType: body.ProviderDeviceGroupType,
          providerClientGroupType: body.ProviderClientGroupType,
          excludeOnboarding: false,
          excludeOnboarded: created.TargetEnforcement is TargetEnforcement.Onboarding))
        .Using(q => q.Select(a => a.Id).ToListAsync());

      ctx.SetDetectionOutdated(targetedComputers);

      Task.Run(async () =>
      {
        foreach (var compId in targetedComputers)
        {
          await hubContext.Clients.Group($"Computer:{compId}").UpdateComputer(new UpdateComputerResource
          {
            DetectionOutdated = true
          });
        }
      }).Forget();
    }

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPut(TargetAssignmentApiRoutes.UpdateLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> UpdateTargetAssignment(
    [FromRoute] int id,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanUpdateTargetAssignment, assignment, true);
    body.Id = id;

    if (VerifyTargetAssignment(body) is string error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is string error2) return BadRequest(error2);
    var updated = ctx.UpdateTargetAssignment(body);

    // push target assignment to frontend via ws
    var query = ctx.TargetAssignments.AsNoTracking().Where(a => a.Id == id);
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignments = (await targetPopulator.Populate(resources, default)).First();

    var userTenantId = _userService.GetCurrentTenant().Id;
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedAssignments);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedAssignments);
    }).Forget();

    if (updated is { TargetCategory: TargetCategory.Computer } &&
        updated.TargetType != TargetType.Metascript &&
        updated.TargetType != TargetType.FilterScript &&
        updated.TargetType != TargetType.TenantFilterScript &&
        updated.TargetType != TargetType.TenantMetascript)
    {
      var targetedComputers = await (await computerAssignmentActions.GetComputersInTarget(
          body.TargetType,
          body.TargetGroupFilter,
          body.Target,
          tenantId: body.TenantId,
          includeChildTenants: body.PropagateToChildTenants,
          allowAccessToParentTenant: body.AllowAccessToParentTenant,
          providerLinkId: body.ProviderLinkId,
          providerDeviceGroupType: body.ProviderDeviceGroupType,
          providerClientGroupType: body.ProviderClientGroupType,
          excludeOnboarding: false,
          excludeOnboarded: body.OnboardingOnly))
        .Using(q => q.Select(a => a.Id).ToListAsync());

      ctx.SetDetectionOutdated(targetedComputers);

      Task.Run(async () =>
      {
        foreach (var compId in targetedComputers)
        {
          await hubContext.Clients.Group($"Computer:{compId}").UpdateComputer(new UpdateComputerResource
          {
            DetectionOutdated = true
          });
        }
      }).Forget();
    }

    return Ok(populatedAssignments);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpDelete(TargetAssignmentApiRoutes.DeleteLocal)]
  public async Task<IActionResult> DeleteTargetAssignment(
    [FromRoute] int id,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanDeleteTargetAssignment, assignment, true);
    await ctx.DeleteTargetAssignment(assignment);
    await ctx.DeleteOptionalTargetAssignmentApprovalsByTargetAssignmentId(id);

    var userTenantId = _userService.GetCurrentTenant().Id;
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentDeleted(id);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentDeleted(id);
    }).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsOverridePermission))]
  [HttpPost(TargetAssignmentApiRoutes.OverrideLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> OverrideLocalTargetAssignment(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IOverrideTargetAssignmentCmd cmd,
    [FromServices] ITargetPopulator targetPopulator,
    [FromRoute] int id,
    [FromBody] OverrideTargetAssignmentRequest req)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanOverrideTargetAssignment, assignment, true);

    var updatedAssignment = cmd.Run(assignment, req.TargetType, req.DesiredSoftwareState, req.Target, _userService.GetCurrentUser());

    var resource = new LocalTargetAssignmentResource(updatedAssignment);
    var populated = (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(), default)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.OverrideGlobal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> OverrideGlobal(
    [FromServices] SoftwareDbContext ctx,
    [FromServices] IOverrideTargetAssignmentCmd cmd,
    [FromServices] ITargetPopulator targetPopulator,
    [FromRoute] int id,
    [FromBody] OverrideTargetAssignmentRequest req)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanOverrideTargetAssignment, assignment, true);

    var updatedAssignment = cmd.Run(assignment, req.TargetType, req.DesiredSoftwareState, req.Target, _userService.GetCurrentUser());

    var resource = new LocalTargetAssignmentResource(updatedAssignment);
    var populated = (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(), default)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.Duplicate)]
  public async Task<ActionResult<int>> Duplicate(
    [FromServices] IDuplicateAssignmentCmd cmd,
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromBody] DuplicateAssignmentRequestBody req)
  {
    var existing = req.DatabaseType == DatabaseType.Global ?
      globalCtx.GetTargetAssignmentById(req.Id)
      : localCtx.GetTargetAssignmentById(req.Id);

    if (existing == null) return NotFound();

    _userService.CheckCurrentUserAbility(_abilities.CanUpdateTargetAssignment, existing, true);

    var newAssignmentId = cmd.Run(req.Id, req.DatabaseType, _userService.GetCurrentUser());

    var query = localCtx.TargetAssignments.AsNoTracking().Where(a => a.Id == newAssignmentId);
    var resource = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedResource = (await targetPopulator.Populate(resource, default)).First();

    var userTenantId = _userService.GetCurrentTenant().Id;
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedResource);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedResource);
    }).Forget();

    return Ok(newAssignmentId);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsSupersedePermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateToSupersedingAssignment)]
  public async Task<ActionResult<OpResult<int>>> MigrateToSupersedingAssignment(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMigrateToSupersedingAssignmentCmd cmd,
    [FromBody] MigrateToSupersedingAssignmentRequest req)
  {
    var existing = ctx.GetTargetAssignmentById(req.OldAssignmentId);
    if (existing is null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignment, existing, true);

    var res = await cmd.ExecuteAsync(req, _userService.GetCurrentUser(), CancellationToken.None);
    return res.IsSuccess ?
      Ok(OpResult.Ok(res.Value.TargetAssignment.Id)) :
      Ok(OpResult.Fail(res.Reason));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsSupersedePermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateToSupersedingAssignmentWhatIf)]
  public async Task<ActionResult<OpResult<Contracts.V1.Responses.MigrateToSupersedingAssignmentWhatIfResponse>>> MigrateToSupersedingAssignmentWhatIf(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IMigrateToSupersedingAssignmentCmd cmd,
    [FromBody] MigrateToSupersedingAssignmentRequest req)
  {
    var existing = ctx.GetTargetAssignmentById(req.OldAssignmentId);
    if (existing is null) return NotFound();
    _userService.CheckCurrentUserAbility(_abilities.CanSeeTargetAssignment, existing, true);

    var res = await cmd.ExecuteWhatIfAsync(req, _userService.GetCurrentUser(), CancellationToken.None);

    if (res.IsSuccess)
    {
      var resource = new LocalTargetAssignmentResource(res.Value.SupersedingAssignment);
      var populated = (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(), default)).First();

      string? tenantName = null;
      if (populated.TenantId is int tenantId)
      {
        tenantName = ctx.GetTenantName(tenantId);
      }

      var policyDescription = PolicyDescriptionGenerator.Generate(
        populated.TargetText ?? string.Empty,
        res.Value.SupersededByTaskName,
        populated.DesiredSoftwareState,
        populated.MaintenanceTaskMode,
        populated.SoftwareSemanticVersion,
        populated.TargetType,
        populated.TargetGroupFilter,
        populated.ProviderDeviceGroupType,
        populated.ProviderClientGroupType,
        tenantName,
        populated.PropagateToChildTenants,
        populated.AllowAccessToParentTenant);

      return Ok(OpResult.Ok<Contracts.V1.Responses.MigrateToSupersedingAssignmentWhatIfResponse>(new(res.Value.IsNew, populated, policyDescription)));
    }

    return Ok(OpResult.Fail(res.Reason));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateDeploymentsToProviderLinks)]
  public async Task<ActionResult> MigrateDeploymentsToProviderLinks(
    [FromServices] IMigrateDeploymentsToProviderLinksService service,
    CancellationToken token)
  {
    // seems like this should use a special permission?
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    await service.PerformWork(token);
    return NoContent();
  }
}
