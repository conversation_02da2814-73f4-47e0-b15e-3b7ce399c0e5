using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class AzureController(
  IUserService _userService,
  Abilities _abilities,
  IAzureActions _azureActions) : Controller
{
  [SubjectPermissionAuthorize(typeof(IAzureOperationsViewPermission))]
  [HttpGet(AzureApiRoutes.GetPartnerTenantCustomers)]
  public async Task<ActionResult<ICollection<AzureTenantCustomersResult>>> GetDelegatedAdminCustomers(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] string partnerPrincipalId,
    CancellationToken token = default)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    var azTenant = await ctx.AzureTenants
      .FirstOrDefaultAsync(t => t.PrincipalId == partnerPrincipalId, token);
    if (azTenant == null) return NotFound();
    return Ok(await _azureActions.GetPartnerTenantsDelegatedAdminCustomers(azTenant, token));
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsViewPermission))]
  [HttpGet(AzureApiRoutes.GetPartnerTenantInfos)]
  public async Task<ActionResult<ICollection<TenantInfoResult>>> GetPartnerTenantInfos(
    [FromServices] IHostApplicationLifetime appLifetime)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    var tenantInfos = await _azureActions.GetPartnerTenantsTenantInformations(appLifetime.ApplicationStopping);
    return base.Ok(
       tenantInfos.OrderBy(a => a.Result.Value is AzureTenantInfo t ? t.TenantName : null));
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdateContractLinksPermission))]
  [HttpPost(AzureApiRoutes.SyncAzureUsersForTenants)]
  public async Task<ActionResult<ICollection<AzureTenantUserSyncResult>>> SyncAzureUsersForTenants(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromBody] SyncAzureDataForTenantsRequest request)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    if (request is not ({ TenantPrincipalIds: { Count: > 0 } } or { AllPartnerTenants: true }))
      return BadRequest("Must specify TenantPrincipalIds or AllPartnerTenants in request body");
    List<AzureTenant> azTenants =
    [
      ..(request.AllPartnerTenants
        ? ctx.GetPartnerAzureTenants()
        : ctx.GetAzureTenantsByPrincipalIds(request.TenantPrincipalIds))
    ];
    List<AzureTenantUserSyncResult> results = [];
    foreach (var azTenant in azTenants)
    {
      var result = await _azureActions.SyncUsersFromAzureTenant(azTenant, appLifetime.ApplicationStopping);
      results.Add(result);
    }
    return Ok(results);
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdateContractLinksPermission))]
  [HttpPost(AzureApiRoutes.PreconsentCustomerTenants)]
  public ActionResult PreconsentCustomerTenants(
    [FromServices] IAzureCustomerPreconsentJob preconsentJob,
    [FromBody] PreconsentCustomerTenantsRequest request)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);

    preconsentJob.Enqueue(new AzureCustomerPreconsentJobPayload(
      request.PartnerPrincipalId,
      request.CustomerPrincipalIds));

    return Accepted();
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdateContractLinksPermission))]
  [HttpPost(AzureApiRoutes.SyncAzureDetailsForTenants)]
  public async Task<ActionResult<ICollection<AzureTenantDetailsSyncResult>>> SyncAzureDetailsForTenants(
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] SyncAzureDataForTenantsRequest request)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    if (request is not ({ TenantPrincipalIds: { Count: > 0 } } or { AllPartnerTenants: true }))
      return BadRequest("Must specify TenantPrincipalIds or AllPartnerTenants in request body");
    List<AzureTenant> azTenants =
    [
      ..(request.AllPartnerTenants
        ? ctx.GetPartnerAzureTenants()
        : ctx.GetAzureTenantsByPrincipalIds(request.TenantPrincipalIds))
    ];
    List<AzureTenantDetailsSyncResult> results = [];
    foreach (var azTenant in azTenants)
    {
      var result = await _azureActions.SyncAzureDetailsForTenant(azTenant, appLifetime.ApplicationStopping);
      results.Add(result);
    }
    return Ok(results);
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsSyncContractsPermission))]
  [HttpPost(AzureApiRoutes.HandleTenantConsent)]
  public async Task<ActionResult<TenantConsentResponseBody>> HandleTenantConsent(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromBody] TenantConsentRequestBody body,
    CancellationToken cancellationToken)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    if (body?.TenantPrincipalId is not {} principalId) return BadRequest();
    await using var ctx = ctxFactory();
    var azTenant = await ctx.AzureTenants
      .FirstOrDefaultAsync(t => t.PrincipalId == principalId, cancellationToken);
    if (azTenant == null) return NotFound();
    await ctx.UpdateTenantConsentDate(azTenant, DateTime.UtcNow, body.AppType, cancellationToken);

    // When tenant is consented, it should not reset customers to partners
    await _azureActions.DisambiguateAzureTenantType(azTenant,
      allowResettingCustomerToPartner: false,
      cancellationToken: appLifetime.ApplicationStopping);

    return Ok(new TenantConsentResponseBody(
      azTenant.PrincipalId,
      azTenant.AzureTenantType == AzTenantType.Partner));
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsSyncContractsPermission))]
  [HttpPost(AzureApiRoutes.DisambiguateAzureTenantType)]
  public async Task<ActionResult<CheckTenantPartnerStatusResponseBody>> DisambiguateAzureTenantType(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] DisambiguateAzureTenantTypeRequestBody body,
    CancellationToken token)
  {
    _userService.CheckCurrentUserAbility(_abilities.CheckUserIsMspUser, true);
    if (body?.TenantPrincipalId is not {} principalId) return BadRequest();
    await using var ctx = ctxFactory();
    var azTenant = await ctx.AzureTenants
      .FirstOrDefaultAsync(t => t.PrincipalId == principalId, token);
    if (azTenant == null) return NotFound();

    // When user manually clicks the 'Check tenant type' button, it should check even if the tenant is a customer
    await _azureActions.DisambiguateAzureTenantType(azTenant,
      allowResettingCustomerToPartner: true,
      cancellationToken: token);

    var tenantInfo = await ctx.AzureTenants.Where(t => t.PrincipalId == principalId)
      .Select(t => new { t.PrincipalId, t.AzureTenantType, t.PartnerPrincipalId })
      .FirstAsync(token);

    return Ok(new CheckTenantPartnerStatusResponseBody(
      tenantInfo.PrincipalId,
      tenantInfo.PartnerPrincipalId,
      tenantInfo.AzureTenantType));
  }
}
