using System.Collections;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using App.Metrics.Formatters.Json;
using DevExtreme.AspNet.Data.ResponseModel;
using Immense.RemoteControl.Shared.Models;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Events.RunImmyServiceEvents;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.ChocolateyApi;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.SyntaxChecker;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Notifications;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Oauth;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.Providers.Shared;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Controllers.V1;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Controllers.V1.RBAC;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Immybot.Backend.Web.Common.Lib.SignalRLogger;
using Immybot.Manager.Shared;
using Immybot.Manager.SitesApi;
using Immybot.Shared.DataContracts.Agent.EphemeralRpc;
using Immybot.Shared.DataContracts.WindowsRegistry;
using Immybot.Shared.Primitives;
using Immybot.Shared.Scripts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Microsoft.Kiota.Abstractions.Store;
using Newtonsoft.Json.Linq;
using NuGet.Versioning;
using OneOf;
using Reinforced.Typings;
using Reinforced.Typings.Ast.TypeNames;
using Reinforced.Typings.Fluent;
using Reinforced.Typings.Visitors.TypeScript;
using BucketTimerMetric = App.Metrics.Formatters.Json.BucketTimerMetric;
using Person = Immybot.Backend.Domain.Models.Person;
using Schedule = Immybot.Backend.Domain.Models.Schedule;
using User = Immybot.Backend.Domain.Models.User;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.Infrastructure.Extensions;

namespace Immybot.Backend.Web.Common.ReinforcedTypings;

public static partial class Configuration
{
  private record Capability(string TypeName)
  {
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
  }

  private sealed class MyVisitor(TextWriter writer, ExportContext exportContext)
    : TypeScriptExportVisitor(writer, exportContext)
  {
    public override void VisitFile(ExportedFile file)
    {
      // controller spec tests
      if (file.FileName.EndsWith(".spec.ts"))
      {
        var apiName = file.FileName.Split("/")[^1].Replace(".spec.ts", "");
        base.VisitFile(file);
        Br();
        WriteLines("test.beforeAll(async () => {\n  await globalSetup();\n});");
        Br();
        WriteLines($"test.describe({apiName}.name, async () => new {apiName}Tests().runTests());");
        return;
      }

      if (file.FileName.EndsWith($"{_contractsTsFile}.ts"))
      {
        // add "export * from './<stuff>';" to the beginning of contracts.ts
        WriteLines($"export * from './{_requestsTsFile}';");
        WriteLines($"export * from './{_responsesTsFile}';");
        WriteLines($"export * from './{_routesTsFile}';");
        WriteLines($"export * from './{_enumsTsFile}';");
        WriteLines($"export * from './{_interfacesTsFile}';");
        WriteLines($"export * from './{_signalrTsFile}';");
        Br();

        // Write the stuff that's exported to the contracts file
        base.VisitFile(file);


        return;
      }

      if (file.FileName.EndsWith($"{_interfacesTsFile}.ts"))
      {
        // Build up the ProviderCapabilities object from the attributes on the provider interfaces
        var providerCapabilities = GetClassesAndInterfaces()
          .Where(t => t.Namespace?.StartsWith("Immybot.Backend.Providers.Interfaces") == true)
          .Aggregate(new List<Capability>(), (agg, t) =>
          {
            var att = t.GetCustomAttribute<IntegrationCapabilityAttribute>();
            if (t.Name.StartsWith("ISupports") || att != null)
            {
              var cap = new Capability(t.Name);
              if (att != null)
              {
                cap.DisplayName = att.DisplayName;
                cap.Description = att.Description;
              }
              agg.Add(cap);
            }
            return agg;
          });
        Br();

        WriteLines("export const ProviderCapabilities = {");
        Tab();
        foreach (var capability in providerCapabilities)
        {
          WriteLines($"['{capability.TypeName}']: {{ DisplayName: '{capability.DisplayName?.Replace("'", "\\'")}', Description: '{capability.Description?.Replace("'", "\\'")}' }},");
        }
        UnTab();
        WriteLines("} as const;");
        Br();
        base.VisitFile(file);
        return;
      }

      if (file.FileName.EndsWith($"{_notificationTypesTsFile}.ts"))
      {
        var rtNotification = file.TypeResolver.ResolveTypeName(typeof(Notification));
        var rtNotificationType = file.TypeResolver.ResolveTypeName(typeof(NotificationType));

        var fields = typeof(NotificationType).GetFields()
          .Select(f => (f.Name, f.GetCustomAttributes(typeof(NotificationInputTypeAttribute), false).FirstOrDefault()))
          .Where(t => t.Item2 != null)
          .Select(t => (t.Name, ((NotificationInputTypeAttribute)t.Item2!).InputType))
          .Select(t => (t.Name, t.InputType, file.TypeResolver.ResolveTypeName(t.InputType)))
          .ToList();

        base.VisitFile(file);
        Br();

        WriteLines("export type NotificationInputTypes = ");
        Tab();
        int i = 0;
        foreach (var field in fields)
        {
          i++;
          WriteLines("| { type: " + rtNotificationType + "." + field.Name + ", inputData: " + field.Item3 + " }" + (i == fields.Count ? ";" : ""));
        }
        UnTab();
        Br();
        WriteLines($"export type NotificationTyped = Omit<{rtNotification}, \"type\" | \"inputData\"> & NotificationInputTypes;");
        return;
      }

      if (file.FileName.EndsWith($"{_rbacPermissionsTsFile}.ts"))
      {
        // build out the RBAC permissions
        var builder = Host.CreateApplicationBuilder();
        builder.Services.AddRBACServices();
        var host = builder.Build();
        var services = host.Services.GetServices<IPermissionMetadata>();
        WriteLines("export type RbacPermissions = ");
        var servicesList = services.Distinct().OrderBy(a => a.Subject.Name).ThenBy(a => a.PermissionName).ToList();
        for (int i = 0; i < servicesList.Count; i++)
        {
          var service = servicesList[i];
          var isLastItem = (i == servicesList.Count - 1);
          var semicolon = isLastItem ? ";" : "";

          WriteLine("  | " + "\"" + service.AllowClaim + "\"" + semicolon);
        }

        return;
      }

      base.VisitFile(file);
    }
  }
  private const string _enumsTsFile = "enums";
  private const string _interfacesTsFile = "interfaces";
  private const string _responsesTsFile = "responses";
  private const string _notificationTypesTsFile = "notification-types";
  private const string _requestsTsFile = "requests";
  private const string _rbacPermissionsTsFile = "rbac-permissions";
  private const string _routesTsFile = "routes";
  private const string _contractsTsFile = "contracts";
  private const string _signalrTsFile = "signalr";
  private static List<Type>? _types;
  private static List<Type>? _classAndInterfaceTypes;

  private static List<Type> GetTypes()
  {
    var assemblies = new HashSet<Assembly>(AppDomain.CurrentDomain.GetAssemblies());

    // Why the Immybot.Backend.Providers.Interfaces assembly is not loaded by
    // default here is a mystery to me
    assemblies.Add(typeof(ISupportsListingClients).Assembly);

    return _types ??= assemblies.SelectMany(a => a.ExportedTypes).ToList();
  }

  private static List<Type> GetClassesAndInterfaces()
  {
    return _classAndInterfaceTypes ??= GetTypes().Where(t => t.IsClass || t.IsInterface || t.IsAbstract).ToList();
  }

  [GeneratedRegex("Controller$")]
  public static partial Regex ControllerNameReplacement();

  /// <summary>
  /// This method defines the fluent configuration for classes we
  /// want to export to typescript to be used by the frontend
  /// </summary>
  /// <remarks>
  /// Here's an easy way to debug this method, e.g. from a unit test:
  /// <code>
  /// ExportContext context = new ExportContext(new Assembly[] { typeof(ComputersController).Assembly })
  /// {
  ///   Hierarchical = false, //true if you export to multiple files
  ///   TargetDirectory = @"C:\Temp\MyExport", //substitute your path
  ///   TargetFile = @"C:\Temp\MyExport\project.ts", //substitute your file
  ///   DocumentationFilePath = @"C:\MyProject\bin\MyProject.XML", //optional
  ///   ConfigurationMethod = builder => Immybot.Backend.Web.Common.ReinforcedTypings.Configuration.Configure(builder) //here is your method
  /// };
  /// TsExporter exporter = new TsExporter(context);
  /// exporter.Export();
  /// </code>
  /// </remarks>
  public static void Configure(ConfigurationBuilder builder)
  {
    // Global settings
    builder.Global(config =>
    {
      // Switches RT to using TS modules system (--module tsc.exe parameter) with types exports/imports.
      // import directives will not appear in your export results until you set this useModules
      // parameter to true. For more flexibility, 2nd related parameter - discardNamespaces is
      // also moved to this method. When it is true then RT will totally ignore all namespaces
      // when exporting. This parameter does not work without useModules parameter
      config.UseModules(true, true);

      // This setting controls JSDOC generation for your exported
      // TypeScript from .NET XMLDOC. Check out how to use JSDOC generation [[here
      config.GenerateDocumentation(true);

      // When true, forces Reinforced.Typings to generate METHODS names
      // in "camelCase" instead of .NET-common "PascalCase"
      config.CamelCaseForMethods(true);

      // When true, forces Reinforced.Typings to generate PROPERTIES
      // names in "camelCase" instead of .NET-common "PascalCase"
      config.CamelCaseForProperties(true);

      // Tells Reinforced.Typings to generate optional props for nullable value types
      config.AutoOptionalProperties(true);

      // Use LF instead of CRLF
      config.NewLine("\n");

      // Use our custom visitor that adds 'export * ...' statements to the top of the contracts
      // file, for backwards compatibility with the old way of exporting types
      config.UseVisitor<MyVisitor>();

      config.TabSymbol("  ");
    });

    // SUBSTITUTIONS - We can manually specify how we want certain types to be converted
    builder.Substitute(typeof(DataSourceLoadOptions), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(AppSieveModel), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(IActionResult), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(ActionResult), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(CancellationToken), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(DateTime), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(DateTimeOffset), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(Guid), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(Uri), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(JObject), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(JArray), new RtArrayType(new RtSimpleTypeName("any")));
    builder.Substitute(typeof(JToken), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(JsonDocument), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(JsonElement), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(SemanticVersion), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(Type), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(Exception), new RtSimpleTypeName("unknown"));
    builder.Substitute(typeof(AgentStartupInfo), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(TimeSpan), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(IFormFile), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(ComputerNote), new RtSimpleTypeName("string"));
    builder.Substitute(typeof(IAsyncEnumerable<RegistrySearchResultDto>),
      new RtSimpleTypeName("any[]"));
    builder.Substitute(typeof(Hashtable), new RtSimpleTypeName("{ [key: string]: any }"));
    builder.Substitute(typeof(Shared.DataContracts.Signalr.AgentDtos.WindowsSession), new RtSimpleTypeName("any"));
    builder.Substitute(typeof(User), new RtSimpleTypeName("any"));
    builder.Substitute( typeof(ProviderLink), new RtSimpleTypeName("any"));
    builder.SubstituteGeneric(typeof(OneOf<,>), (t, resolver) => new RtSimpleTypeName("(" + string.Join(" | ", t.GenericTypeArguments.Select(a => resolver.ResolveTypeName(a))) + ")"));
    ExportEnumTypes(builder);
    ExportRequestTypes(builder);
    ExportResponseTypes(builder);
    ExportMiscInterfaceTypes(builder);
    ExportSignalrTypes(builder);
    ExportRouteTypes(builder);
    ExportMiscContractTypes(builder);
    ExportControllerTypes(builder);
    ExportNotificationTypes(builder);
    ExportRbacPermissionType(builder);
  }

  private static void ExportMiscInterfaceTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_interfacesTsFile}.ts";
    // export base commands that requests and responses inherit
    var appInterfaces = GetClassesAndInterfaces()
      .Where(t =>
        t.Namespace?.StartsWith("Immybot.Backend.Application.Interface.Commands.Payloads") == true
        || t.Namespace?.StartsWith("Immybot.Backend.Application.Interface.Events") == true);

    // export various other objects as interfaces
    var viewModels = GetClassesAndInterfaces()
      .Where(t => t.Namespace?.StartsWith("Immybot.Backend.Domain.Models.Notifications") == true)
      .Except(new[] { typeof(NotificationInputTypeAttribute) })
      .Concat(new[] {
      typeof(AccessRequestResponse),
      typeof(AgentOnboardingOptions),
      typeof(AgentUpdateFormSchema),
      typeof(AgentUpdateFormSchemaInput),
      typeof(ApiResponseErrorContent),
      typeof(App.Metrics.Formatters.Json.ApdexMetric),
      typeof(App.Metrics.Formatters.Json.CounterMetric),
      typeof(App.Metrics.Formatters.Json.CounterMetric.SetItem),
      typeof(App.Metrics.Formatters.Json.GaugeMetric),
      typeof(App.Metrics.Formatters.Json.HistogramMetric),
      typeof(App.Metrics.Formatters.Json.MeterMetric),
      typeof(App.Metrics.Formatters.Json.MeterMetric.SetItem),
      typeof(App.Metrics.Formatters.Json.MetricData),
      typeof(App.Metrics.Formatters.Json.MetricsContext),
      typeof(App.Metrics.Formatters.Json.TimerMetric),
      typeof(App.Metrics.Formatters.Json.TimerMetric.HistogramData),
      typeof(App.Metrics.Formatters.Json.TimerMetric.RateData),
      typeof(ApplicationPreferences),
      typeof(Audit),
      typeof(AzureCustomerCheckAccessResult),
      typeof(AzureCustomerPreconsentResult),
      typeof(AzureError),
      typeof(AzureErrorLogItem),
      typeof(AzureMessage),
      typeof(AzureSyncResult),
      typeof(AzureTenantAuthDetails),
      typeof(AzureTenantConsentDetails),
      typeof(AzureTenantCustomer),
      typeof(AzureTenantCustomersResult),
      typeof(AzureTenantDetailsSyncResult),
      typeof(AzureTenantInfo),
      typeof(AzureTenantLinkDomainFilter),
      typeof(AzureTenantProblem),
      typeof(AzureTenantTokenCredentialDetails),
      typeof(AzureTenantUserSyncResult),
      typeof(BillingPlatformDetails),
      typeof(ChangeRequestDiff),
      typeof(ChangeRequestParameterDiff),
      typeof(ChocoPackageVersion),
      typeof(ChocoSearchResult),
      typeof(ClaimDevLabVmResponse),
      typeof(ClientGroupTypeDto),
      typeof(CommandResult),
      typeof(ComputerDto),
      typeof(ComputerInDeploymentDto),
      typeof(ComputerListViewModel),
      typeof(ComputerSearch),
      typeof(GetProviderLinkResponse.LinkedExternalLink),
      typeof(DefaultScriptTimeouts),
      typeof(DeleteAzureTenantAuthDetailsCmdPayload),
      typeof(DeleteAzureTenantAuthDetailsCmdResult),
      typeof(DeploymentParameterValue),
      typeof(DeviceDetails),
      typeof(DeviceGroupTypeDto),
      typeof(DynamicFormBindResult),
      typeof(DynamicFormBindResultWithConvertedParameters),
      typeof(ExpectedRoleAssignment),
      typeof(Feature),
      typeof(FeatureEnabledFromSubscription),
      typeof(FeatureUsageDetails),
      typeof(GetBashInstallScriptParameters),
      typeof(GetBashInstallScriptParametersWithOnboardingOptions),
      typeof(GetCommandResult),
      typeof(GetCustomerPortalSessionResult),
      typeof(GetExecutableUriParameters),
      typeof(GetExecutableUriParametersWithOnboardingOptions),
      typeof(GetIpAddressesResponse),
      typeof(GetPartnerCenterOrgResult),
      typeof(GetPowerShellInstallScriptParameters),
      typeof(GetPowerShellInstallScriptParametersWithOnboardingOptions),
      typeof(GetProductCatalogItemsResponse),
      typeof(GetProvisioningPackageUriParameters),
      typeof(GetProvisioningPackageUriParametersWithOnboardingOptions),
      typeof(GetRdpInfoResponse),
      typeof(TargetAssignmentResource),
      typeof(HttpProblem),
      typeof(IClientGroup),
      typeof(IClientStatus),
      typeof(IClientType),
      typeof(IDeviceGroup),
      typeof(IProviderAgentDetails),
      typeof(IScript),
      typeof(MaintenanceSchedulingConfiguration),
      typeof(MaintenanceSpecifier),
      typeof(MaintenanceTaskParameter),
      typeof(Media),
      typeof(MigrateToSupersedingAssignmentRequest),
      typeof(MigrateToSupersedingAssignmentResponse),
      typeof(MissingAccessTokenDetails),
      typeof(MsalErrorDetails),
      typeof(MspInstanceImmySupportAccessGrantDetails),
      typeof(MultiCustomerPreconsentResult),
      typeof(Notification),
      typeof(ODataErrorDetails),
      typeof(Oauth2AccessToken),
      typeof(Oauth2AccessTokenErrorResponse),
      typeof(Oauth2AccessTokenResponse),
      typeof(Oauth2AuthCodeErrorResponse),
      typeof(OauthConsentData),
      typeof(OauthConsentParameterValue),
      typeof(OauthConsentParameterValueIdentifier),
      typeof(OauthHook),
      typeof(OauthHookFailedEvent),
      typeof(OauthHookSucceededEvent),
      typeof(Operation<>),
      typeof(OpResult),
      typeof(OpResult<>),
      typeof(Parameter),
      typeof(ParameterSet),
      typeof(ParameterType),
      typeof(ParameterValue),
      typeof(PartnerOrganizationProfile),
      typeof(PopulatedTargetGroup),
      typeof(ProviderAuditLog),
      typeof(ProviderTypeFormSchema),
      typeof(ProviderTypeFormSchemaInput),
      typeof(ProvisioningPackageOptions),
      typeof(ReleaseDetails),
      typeof(ResolveAssignmentsForMaintenanceItemResult),
      typeof(ResolveAssignmentsForMaintenanceItemResultItem),
      typeof(ScriptReference),
      typeof(SessionJobArgs),
      typeof(ShowCommandInfo),
      typeof(SoftwarePrerequisite),
      typeof(SoftwareSpecifier),
      typeof(SourceContextLogLevel),
      typeof(StartEditorServicesRequest),
      typeof(SubscriptionFeatureBasicInfo),
      typeof(SubscriptionFeatureLevelInfo),
      typeof(SubscriptionItemBasicInfo),
      typeof(SupportedCrossProviderLinkage),
      typeof(TenantInfoResult),
      typeof(TenantPreferences),
      typeof(TimelineEvent),
      typeof(UpdateAzureTenantAuthDetailsCmdPayload),
      typeof(UpdateAzureTenantAuthDetailsCmdResult),
      typeof(UpdateReleaseChannelRequest),
      typeof(UserPreferences),
      typeof(VerifyProviderResult),
      typeof(WindowsSession),
      typeof(MaintenanceItemOrder),
      typeof(MigratableDeploymentResultDto),
      typeof(RegistryKeyDto),
      typeof(RegistryValueDto),
      typeof(RegistrySearchRequestDto), typeof(RegistrySearchResultDto), typeof(SubscriptionAddonDto),
      typeof(GettingStartedChecklist), typeof(TargetedPerson), typeof(TechnicianPageInfoFromPsaTicket),
      typeof(UnifiedComputerInfo), typeof(ComputerAgentStatusDto), typeof(AgentStatusDto),
      typeof(SoftwareFileUploadData), typeof(ScriptReferenceCounts), typeof(SyntaxCheckerResult),
      typeof(ComputerInventoryAllSoftware), typeof(ComputerInventorySoftware), typeof(MaintenanceSession),
      typeof(RoleAssignment), typeof(ParameterDropdownTextValue), typeof(TargetAssignment),
      typeof(ComputerUserAffinityResponse), typeof(TenantTagAuthorization), typeof(TenantMedia),
      typeof(TenantMaintenanceTask), typeof(ScriptContextParameters), typeof(TenantScript),
      typeof(AgentIdentificationLog), typeof(ChangeRequest), typeof(MaintenanceAction), typeof(IProvider),
      typeof(LocalSoftware), typeof(GlobalSoftware), typeof(DynamicIntegrationType),
      typeof(MaintenanceOnboardingConfiguration), typeof(MaintenanceEmailConfiguration),
      typeof(MaintenanceAgentUpdatesConfiguration), typeof(MaintenanceItem), typeof(Person), typeof(Tenant),
      typeof(LocalSoftwareVersion), typeof(License), typeof(Schedule), typeof(Branding), typeof(Tag),
      typeof(RecommendedTargetAssignmentApproval), typeof(AccessRequest),
      typeof(UserSilencedNotification), typeof(ChangeRequestComment), typeof(UserImpersonation), typeof(ActiveSession),
      typeof(MaintenanceSessionStage), typeof(MaintenanceActionActivity), typeof(ProviderAgent),
      typeof(AgentIdentificationFailure), typeof(FeatureUsage), typeof(MaintenanceTask), typeof(SessionLog),
      typeof(Computer), typeof(SessionPhase), typeof(TargetAssignmentNotes), typeof(TargetAssignmentVisibility),
      typeof(MaintenanceTaskParameterValue), typeof(ProviderClient), typeof(ProviderLinkCrossReference),
      typeof(ProviderLinkInternalData), typeof(Software), typeof(MaintenanceActionDependency), typeof(TenantSoftware),
      typeof(ComputerPerson), typeof(DetectedComputerSoftware), typeof(UserAffinity), typeof(PersonTag),
      typeof(ComputerTag),
      typeof(DynamicIntegrationTypeProperties), typeof(InventoryTaskScript), typeof(BucketHistogramMetric),
      typeof(BucketTimerMetric), typeof(DefaultEmailBccList), typeof(AzureGdapRelationshipDetails),
      typeof(NuGetVersion), typeof(MaintenanceTaskParameterValueDetails), typeof(RoleDefinition), typeof(IBackingStore),
      typeof(SoftwareVersion), typeof(GlobalSoftwareVersion), typeof(AzureTenantLink), typeof(SmtpConfig),
      typeof(TenantTag), typeof(FeatureUsageItem), typeof(ComputerLatestProviderEvent),
      typeof(System.Version), typeof(AzureTenant), typeof(InventoryTask),
      typeof(RemoteControlRecording), typeof(ResourceAction), typeof(GdapRelationshipRole),
      typeof(GdapRelationshipAccessAssignment), typeof(ComputerInventoryTaskScriptResult),
      typeof(BucketTimerMetric.BucketHistogramData), typeof(BucketTimerMetric.RateData),
      typeof(RegistrySearchRequestDto),
      typeof(RegistrySearchResultDto),
      typeof(SubscriptionAddonDto),
      typeof(GettingStartedChecklist),
      typeof(UserAuthInfo),
      //RBAC
      typeof(GetRoleResponse),
      typeof(RolePermissionsResponse),
      typeof(CreateOrUpdateRoleRequest),
      typeof(CloneRoleRequest),
      typeof(RolePermissionRequest),
      typeof(SetUserRolesRequest),
      typeof(ISubjectMetadata),
      typeof(IPermissionMetadata), typeof(ComputersController.ParentTenantInfo),
    });
    builder.ExportAsInterfaces(appInterfaces,
      config => config.WithPublicProperties(p =>
      {
        if (!p.Member.CanRead)
        {
          p.Ignore();
        }

        bool isExpression = p.Member.PropertyType.IsGenericType &&
                            p.Member.PropertyType.GetGenericTypeDefinition() == typeof(Expression<>);

        if (isExpression) p.Ignore();
      }).WithPublicFields().ExportTo(filename));
    // There are some interfaces defined in viewModels that have properties defined
    // in base classes that we want to include here
    builder.ExportAsInterfaces(viewModels,
      config => config.FlattenHierarchy().WithPublicProperties(p =>
      {
        if (!p.Member.CanRead)
        {
          p.Ignore();
        }

        bool isExpression = p.Member.PropertyType.IsGenericType &&
                            p.Member.PropertyType.GetGenericTypeDefinition() == typeof(Expression<>);

        if (isExpression) p.Ignore();
      }).WithPublicFields(f => f.CamelCase()).ExportTo(filename));

    builder.ExportAsInterface<Script>()
      .WithPublicProperties(p =>
      {
        if (!p.Member.CanRead)
        {
          p.Ignore();
        }
      })
      .WithPublicFields(f => f.CamelCase())
      .OverrideName("IDomainScript")
      .ExportTo(filename);

    builder.ExportAsInterface<ProviderTypeDto>().WithProperty(d => d.ProviderCapabilities,
      cfg =>
      {
        cfg.Type("(keyof typeof ProviderCapabilities)[]");
      }).WithPublicProperties(p =>
    {
      if (!p.Member.CanRead)
      {
        p.Ignore();
      }
    }).WithPublicFields(f => f.CamelCase()).ExportTo(filename);
    builder.ExportAsInterface<GetProviderLinkResponse>()
      .WithProperty(d => d.ProviderCapabilities,
      cfg =>
      {
        cfg.Type("(keyof typeof ProviderCapabilities)[]");
      })
      .WithProperty(d => d.ExcludedCapabilities,
        cfg =>
        {
          cfg.Type("(keyof typeof ProviderCapabilities)[]");
        })
      .WithPublicProperties(p =>
    {
      if (!p.Member.CanRead)
      {
        p.Ignore();
      }
    }).WithPublicFields(f => f.CamelCase()).ExportTo(filename);
    builder.ExportAsInterfaces(new[] { typeof(OpResult<>) }, cfg => cfg
      .WithPublicProperties().WithPublicFields().ExportTo(filename)
      // This is lame, but because there are two OpResult types (one generic, one not), Reinforced
      // Typings will generate the same interface twice. This is a hack to rename the second one
      .OverrideName("OpResult2"));
  }

  private static void ExportEnumTypes(ConfigurationBuilder builder)
  {
    // export all enum types from domain
    var enums = GetTypes().Where(t => t.IsEnum && t.Namespace == "Immybot.Backend.Domain.Models")
      .Concat(new[]
      {
        typeof(AzurePermissionLevel),
        typeof(JoinedSessionGroupType),
        typeof(HealthStatus),
        typeof(ClaimCode),
        typeof(FeatureResetInterval),
        typeof(SubscriptionStatus),
        typeof(SubscriptionItemType),
        typeof(SubscriptionFeatureStatus),
        typeof(SubscriptionFeatureType),
        typeof(WindowsSessionType),
        typeof(ScriptLanguage),
        typeof(LogLevel),
        typeof(ReleaseChannel),
        typeof(RegistryHiveDto),
        typeof(RegistryViewDto),
        typeof(RegistryValueKindDto),
        typeof(RegistrySearchTargets),
        typeof(RegistrySearchResultType),
        typeof(ApplicationLockEventTypes),
        //RBAC
        typeof(PermissionCategory)
      })
      .Except(new[]
      {
        typeof(NotificationType),
      });
    builder.ExportAsEnums(enums, cfg => cfg.ExportTo($"{_enumsTsFile}.ts"));
  }

  private static void ExportRouteTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_routesTsFile}.ts";
    // export all api routes as static classes
    var routes = GetClassesAndInterfaces()
      .Where(t => t.Namespace == "Immybot.Backend.Web.Common.Contracts.V1");
    builder.ExportAsClasses(routes, config => config.FlattenHierarchy().WithPublicFields().ExportTo(filename));
  }

  private static void ExportRequestTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_requestsTsFile}.ts";
    // export api request objects as interfaces and classes
    var requests = GetClassesAndInterfaces()
      .Where(t => t.Namespace?.StartsWith("Immybot.Backend.Web.Common.Contracts.V1.Requests") == true);
    builder.ExportAsInterfaces(requests.Where(r => !r.IsInterface),
      config => config.FlattenHierarchy().WithPublicProperties().WithPublicFields().ExportTo(filename)
                      .WithCodeGenerator<RequestClassCodeGenerator>());
  }

  private static void ExportResponseTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_responsesTsFile}.ts";
    // export api response objects as interfaces
    var responses = GetClassesAndInterfaces()
      .Where(t => t.Namespace?.StartsWith("Immybot.Backend.Web.Common.Contracts.V1.Responses") == true)
      .Where(t => t != typeof(GetProviderLinkResponse)) // This one's exported in interfaces because it depends on a type that we can't automatically import with RT
      .Concat(new[]
      {
        typeof(ApplicationLockCallerInfo),
        typeof(ApplicationLocksResponse),
        typeof(ApplicationLockEvent),
      });
    builder.ExportAsInterfaces(responses,
      config => config.FlattenHierarchy().WithPublicProperties().WithPublicFields().ExportTo(filename));
  }

  private static void ExportNotificationTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_notificationTypesTsFile}.ts";

    // build up a class to represent the linkage between a notification type and the input type
    // use the attributes defined on the NotificationType enum to get the input type
    builder.ExportAsEnums(new[] { typeof(NotificationType) }, cfg =>
    {
      cfg.ExportTo(filename);
    });
  }

  private static void ExportMiscContractTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_contractsTsFile}.ts";
    var classTypes = new[] {
        // database column length constraints
        typeof(Backend.Infrastructure.Configuration.Persistence.MaxLengthConstants),
        typeof(Constants),
        // pre-defined inventory keys
        typeof(InventoryKeys),
        // inventory property names in the Computer Inventory object
        typeof(ComputerModelInventoryProperties)
      };

    builder.ExportAsClasses(
      classTypes,
      cfg => cfg.FlattenHierarchy().WithPublicProperties().WithPublicFields().ExportTo(filename));
  }

  private static void ExportControllerTypes(ConfigurationBuilder builder)
  {
    var import = new Reinforced.Typings.Ast.Dependency.RtImport
    {
      From = "devextreme/data/custom_store",
      Target = "CustomStore",
    };
    var import2 = new Reinforced.Typings.Ast.Dependency.RtImport
    {
      From = "@/composables/DxServerDataSource",
      Target = "{ createStore }",
    };
    builder.ExportAsThirdParty<LoadResult>()
      .Imports(import, import2)
      .WithName("CustomStore");

    var controllerTypes = new[]
    {
      typeof(AuthController),
      typeof(AzureController),
      typeof(PreferencesController),
      typeof(AzureErrorsController),
      typeof(BillingController),
      typeof(ComputersController),
      typeof(DynamicIntegrationTypesController),
      typeof(InventoryTasksController),
      typeof(LicensesController),
      typeof(MaintenanceActionsController),
      typeof(MaintenanceSessionsController),
      typeof(MediaController),
      typeof(MetricsController),
      typeof(ProviderAgentsController),
      typeof(ProviderTypesController),
      typeof(SmtpConfigController),
      typeof(ScriptsController),
      typeof(TenantsController),
      typeof(UsersController),
      typeof(OauthController),
      typeof(ApplicationLogsController),
      typeof(AuditsController),
      typeof(NotificationsController),
      typeof(PersonsController),
      typeof(ApplicationLocksController),
      typeof(ChangeRequestsController),
      typeof(TargetAssignmentsController),
      typeof(SystemController),
      typeof(SoftwareController),
      typeof(SchedulesController),
      typeof(GettingStartedController),
      typeof(RolesController),
      typeof(UserRolesController),
    };
    builder.ExportAsClasses(controllerTypes, cfg =>
    {
      var name = ControllerNameReplacement().Replace(cfg.Type.Name, "Api");
      cfg.OverrideName(name);
      cfg
      .WithCodeGenerator<ImmyBackendApiClassCodeGenerator>()
      .WithMethods(method => ImmyBackendApiMethodGenerator.NonGenericActionResultTypes.Contains(method.ReturnType),
        mCfg =>
        {
          // For all methods that return ActionResult, use void as the return type for the generated method
          mCfg.WithCodeGenerator<ImmyBackendApiMethodGenerator>();
        })
      .WithMethods(method =>
        method.ReturnType.IsGenericType && method.ReturnType.GetGenericTypeDefinition() == typeof(ActionResult<>),
        mCfg =>
        {
          // For all methods that return ActionResult<T>, use the T as the return type for the generated method
          mCfg.WithCodeGenerator<ImmyBackendApiMethodGenerator>().Type(mCfg.Member.ReturnType.GenericTypeArguments[0]);
        })
      .WithMethods(method =>
        method.ReturnType.IsGenericType
        && method.ReturnType.GetGenericTypeDefinition() == typeof(Task<>)
        && method.ReturnType.GenericTypeArguments[0] is { } a
        && a.IsGenericType
        && a.GetGenericTypeDefinition() == typeof(ActionResult<>),
        mCfg =>
        {
          // For all methods that return Task<ActionResult<T>>, use the T as the return type for the generated method
          mCfg.WithCodeGenerator<ImmyBackendApiMethodGenerator>().Type(mCfg.Member.ReturnType.GenericTypeArguments[0].GenericTypeArguments[0]);
        })
      .AddImport("{ $get, $post, $put, $patch, $delete }", "@/api/backend/client")
      .AddImport("{ CancelToken }", "axios")
      .ExportTo($"sdk/{name}.ts");
    });

#pragma warning disable S125
    // // test file
    // do not delete: keep for testing purposes
    // builder.ExportAsClasses(controllerTypes,
    //   cfg =>
    //   {
    //     var name = ControllerNameReplacement().Replace(cfg.Type.Name, "Api");
    //     cfg.OverrideName(name);
    //     cfg
    //       .WithCodeGenerator<ImmyBackendApiTestsCodeGenerator>()
    //       .AddImport("globalSetup", "global-setup")
    //       .AddImport("{ expect, test }", "@playwright/test")
    //       .AddImport($"{{ {name} }}", $"../../../src/api/backend/generated/sdk/{name}")
    //       .AddImport("{ ApiMethods, BaseApiTestSuite, RequireTestCoverage }", "../utils")
    //       .ExportTo($"sdk/tests/{name}.spec.ts");
    //   });
#pragma warning restore S125
  }

  private static void ExportSignalrTypes(ConfigurationBuilder builder)
  {
    const string filename = $"{_signalrTsFile}.ts";

    var signalrResources = GetClassesAndInterfaces()
      .Where(t =>
        t.Namespace?.StartsWith("Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources") == true ||
        t.Namespace?.StartsWith("Immybot.Shared.DataContracts.Signalr.UserDtos") == true);
    builder.ExportAsInterfaces(signalrResources,
      config => config.FlattenHierarchy().WithPublicProperties().WithPublicFields().ExportTo(filename));

    var ignored = new[]
    {
      nameof(ImmyBotUserHub.OnConnectedAsync),
      nameof(ImmyBotUserHub.OnDisconnectedAsync),
    };
    builder.ExportAsInterface<ImmyBotUserHub>().WithPublicMethods(builder =>
    {
      if (ignored.Contains(builder.Member.Name)) builder.Ignore();
      else builder.PascalCase();
    })
    .ExportTo(filename);

    builder.ExportAsClasses(new[]
    {
      typeof(ImmyBotUserHubMessages)
    }, config =>
    {
      config.FlattenHierarchy().WithPublicFields().ExportTo(filename);
    });
    builder.ExportAsInterfaces(new[]
    {
      typeof(IImmyBotUserHubClient),
      typeof(SessionGroupEvent),
      typeof(JoinSessionGroupsPayload),
      typeof(JoinSessionGroupsResponse),
      typeof(JoinSessionGroupsResponseSession)
    },
    builder =>
    {
      builder.WithPublicMethods(b =>
      {
        b.PascalCase();
      }).ExportTo(filename);
    });
  }

  private static void ExportRbacPermissionType(ConfigurationBuilder builder)
  {
    const string filename = $"{_rbacPermissionsTsFile}.ts";
    builder.ExportAsEnum<DummyRbacPermissionType>()
      .ExportTo(filename);
  }

  private enum DummyRbacPermissionType
  {
  }
}
