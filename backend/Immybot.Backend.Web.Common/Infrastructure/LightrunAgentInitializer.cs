using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Lightrun;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

/// <summary>
/// Starts the Lightrun agent if a secret is provided in the configuration.
/// Watches the lightrun configuration to start the agent later on if it wasn't started initially.
/// </summary>
internal class LightrunAgentInitializer
{
  private bool _didStart;

  public LightrunAgentInitializer(IOptionsMonitor<LightrunOptions> lightrunOptions)
  {
    var opts = lightrunOptions.CurrentValue;
    if (!string.IsNullOrEmpty(opts.Secret))
    {
      LightrunAgent.Start(opts);
      _didStart = true;
    }

    lightrunOptions.OnChange(OnLightrunOptionsChanged);
  }

  private void OnLightrunOptionsChanged(LightrunOptions settings)
  {
    // This is where we'd set up stopping/restarting the agent when the
    // settings change if Lightrun supported that.
    // As it is, just start it if it hasn't been started yet.
    if (_didStart || string.IsNullOrEmpty(settings.Secret)) return;

    LightrunAgent.Start(settings);
    _didStart = true;
  }
}
