using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetSessionLogResponse
{
  public GetSessionLogResponse(
    SessionLog sessionLog,
    bool includeSession = false,
    bool includeStage = false,
    bool includeAction = false)
  {
    ArgumentNullException.ThrowIfNull(sessionLog);
    Id = sessionLog.Id;
    Message = sessionLog.Message;

    MaintenanceSessionId = sessionLog.MaintenanceSessionId;
    MaintenanceSessionStageId = sessionLog.MaintenanceSessionStageId;
    MaintenanceActionId = sessionLog.MaintenanceActionId;
    Time = sessionLog.Time;
    IsPrimary = sessionLog.IsPrimary;
    ProgressCorrelationId = sessionLog.ProgressCorrelationId;
    SessionPhaseId = sessionLog.SessionPhaseId;
    MaintenanceActionStatus = sessionLog.MaintenanceActionStatus;
    ScriptId = sessionLog.ScriptId;
    Script = sessionLog.Script;
    ScriptOutput = sessionLog.ScriptOutput;
    ScriptType = sessionLog.ScriptType;
    ScriptLanguage = sessionLog.ScriptLanguage;
    ScriptParameters = sessionLog.ScriptParameters;
    ParamBlockParameters = sessionLog.ParamBlockParameters;
    UpdatedTime = sessionLog.UpdatedTime;
    SessionLogType = sessionLog.SessionLogType;
    ProgressPercentComplete = sessionLog.ProgressPercentComplete;
    ProgressActivity = sessionLog.ProgressActivity;
    ProgressStatus = sessionLog.ProgressStatus;
    ProgressSecondsRemaining = sessionLog.ProgressSecondsRemaining;
    ProgressCurrentOperation = sessionLog.ProgressCurrentOperation;
    ProgressCompleted = sessionLog.ProgressCompleted;

    if (includeSession && sessionLog.MaintenanceSession != null)
    {
      MaintenanceSession = new GetMaintenanceSessionResponse(sessionLog.MaintenanceSession, includeLogs: false);
    }
    if (includeStage && sessionLog.MaintenanceSessionStage != null)
    {
      MaintenanceSessionStage = new GetMaintenanceSessionStageResponse(sessionLog.MaintenanceSessionStage);
    }
    if (includeAction && sessionLog.MaintenanceAction != null)
    {
      MaintenanceAction = new GetMaintenanceActionResponse(sessionLog.MaintenanceAction);
    }
  }

  public int Id { get; }
  public string? Message { get; }
  public int MaintenanceSessionId { get; }
  public int? MaintenanceSessionStageId { get; }
  public int? MaintenanceActionId { get; }
  public DateTime Time { get; }
  public bool IsPrimary { get; }
  public Guid? ProgressCorrelationId { get; }
  public int? SessionPhaseId { get; }
  public MaintenanceActionStatus? MaintenanceActionStatus { get; }
  public int? ScriptId { get; }
  public string? Script { get; }
  public string? ScriptOutput { get; }
  public DatabaseType? ScriptType { get; }
  public ScriptLanguage? ScriptLanguage { get; }
  public Dictionary<string, object?> ScriptParameters { get; }
  public Dictionary<string, object?> ParamBlockParameters { get; }
  public DateTime? UpdatedTime { get; }
  public SessionLogType SessionLogType { get; }
  public decimal? ProgressPercentComplete { get; }
  public string? ProgressActivity { get; }
  public string? ProgressStatus { get; }
  public double? ProgressSecondsRemaining { get; }
  public string? ProgressCurrentOperation { get; }
  public bool ProgressCompleted { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetMaintenanceSessionResponse? MaintenanceSession { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetMaintenanceSessionStageResponse? MaintenanceSessionStage { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetMaintenanceActionResponse? MaintenanceAction { get; }
}
