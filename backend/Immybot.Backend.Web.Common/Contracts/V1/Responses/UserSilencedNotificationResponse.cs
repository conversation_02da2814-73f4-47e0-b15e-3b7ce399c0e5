using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class UserSilencedNotificationResponse
{
  public int Id { get; set; }
  public int UserId { get; set; }
  public NotificationType NotificationType { get; set; }
  public string? NotificationObjectId { get; set; }
  public DateTime DateSilencedUtc { get; set; }

  internal static Expression<Func<UserSilencedNotification, UserSilencedNotificationResponse>> Projection =>
    x => new UserSilencedNotificationResponse()
    {
      Id = x.Id,
      UserId = x.UserId,
      NotificationType = x.NotificationType,
      NotificationObjectId = x.NotificationObjectId,
      DateSilencedUtc = x.CreatedDate
    };
}
