{"Modules": [{"Name": "ADRMS", "Version": "*******", "ExportedCommands": [{"Name": "Install-ADRMS", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-ADFSUrl] <String> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Uninstall-ADRMS", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON><PERSON>] [-<PERSON>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Update-ADRMS", "CommandType": "Cmdlet", "ParameterSets": "[-ServiceAccount] <PSCredential> [[-PrivateKeyPassword] <SecureString>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}], "ExportedAliases": []}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "*******", "ExportedCommands": [{"Name": "Get-AppLockerFileInformation", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <List`1> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Directory <String> [-FileType <List`1>] [-Recurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -EventLog [-LogPath <String>] [-EventType <List`1>] [-Statistics] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-AppLockerPolicy", "CommandType": "Cmdlet", "ParameterSets": "-Local [-Xml] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Domain -Ldap <String> [-Xml] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Effective [-Xml] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-AppLockerPolicy", "CommandType": "Cmdlet", "ParameterSets": "[-FileInformation] <List`1> [-RuleType <List`1>] [-RuleNamePrefix <String>] [-User <String>] [-Optimize] [-IgnoreMissingFileInformation] [-Xml] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-AppLockerPolicy", "CommandType": "Cmdlet", "ParameterSets": "[-XmlPolicy] <String> [-Ldap <String>] [-Merge] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PolicyObject] <AppLockerPolicy> [-Ldap <String>] [-Merge] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Test-AppLockerPolicy", "CommandType": "Cmdlet", "ParameterSets": "[-XmlPolicy] <String> -Path <List`1> [-User <String>] [-Filter <List`1>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-PolicyObject] <AppLockerPolicy> -Path <List`1> [-User <String>] [-Filter <List`1>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}], "ExportedAliases": []}, {"Name": "BestPractices", "Version": "1.0", "ExportedCommands": [{"Name": "Get-BpaModel", "CommandType": "Cmdlet", "ParameterSets": "[[-BestPracticesModelId] <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-BpaResult", "CommandType": "Cmdlet", "ParameterSets": "[-BestPracticesModelId] <String> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-BpaModel", "CommandType": "Cmdlet", "ParameterSets": "[-BestPracticesModelId] <String> [[-ArgumentTable] <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-BpaResult", "CommandType": "Cmdlet", "ParameterSets": "[-BestPracticesModelId] <String> [-Exclude] [-Results] <List`1> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}], "ExportedAliases": []}, {"Name": "BitsTransfer", "Version": "*******", "ExportedCommands": [{"Name": "Add-BitsFile", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-Source] <String[]> [[-Destination] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Complete-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Get-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-AllUsers] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-JobId] <Guid[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Remove-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Resume-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-Asynchronous] [-V<PERSON>bose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-DisplayName <String>] [-Priority <String>] [-Description <String>] [-ProxyAuthentication <String>] [-RetryInterval <Int32>] [-RetryTimeout <Int32>] [-Credential <PSCredential>] [-ProxyCredential <PSCredential>] [-Authentication <String>] [-SetOwnerToCurrentUser] [-ProxyUsage <String>] [-ProxyList <Uri[]>] [-ProxyBypass <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Start-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-Source] <String[]> [[-Destination] <String[]>] [-Asynchronous] [-Authentication <String>] [-Credential <PSCredential>] [-Description <String>] [-DisplayName <String>] [-Priority <String>] [-ProxyAuthentication <String>] [-ProxyBypass <String[]>] [-ProxyCredential <PSCredential>] [-ProxyList <Uri[]>] [-ProxyUsage <String>] [-RetryInterval <Int32>] [-RetryTimeout <Int32>] [-Suspended] [-TransferType <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Suspend-BitsTransfer", "CommandType": "Cmdlet", "ParameterSets": "[-BitsJob] <BitsJob[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}], "ExportedAliases": []}, {"Name": "PSDiagnostics", "Version": "*******", "ExportedCommands": [{"Name": "Disable-PSTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Disable-PSWSManCombinedTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Disable-WSManTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Enable-PSTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Enable-PSWSManCombinedTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Enable-WSManTrace", "CommandType": "Function", "ParameterSets": ""}, {"Name": "Get-LogProperties", "CommandType": "Function", "ParameterSets": "[-Name] <Object> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-LogProperties", "CommandType": "Function", "ParameterSets": "[-LogDetails] <LogDetails> [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Start-Trace", "CommandType": "Function", "ParameterSets": "[-Session<PERSON>ame] <String> [[-OutputFilePath] <String>] [[-ProviderFilePath] <String>] [-ETS] [-Format <Object>] [-MinBuffers <Int32>] [-MaxBuffers <Int32>] [-BufferSizeInKB <Int32>] [-MaxLogFileSizeInMB <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Stop-Trace", "CommandType": "Function", "ParameterSets": "[-<PERSON><PERSON><PERSON>] <Object> [-ETS] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}], "ExportedAliases": []}, {"Name": "ServerManager", "Version": "*******", "ExportedCommands": [{"Name": "Add-WindowsFeature", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <Feature[]> [-IncludeAllSubFeature] [-LogPath <String>] [-Concurrent] [-Restart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Get-WindowsFeature", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-LogPath <String>] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Remove-WindowsFeature", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <Feature[]> [-LogPath <String>] [-Concurrent] [-Restart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}], "ExportedAliases": []}, {"Name": "TroubleshootingPack", "Version": "*******", "ExportedCommands": [{"Name": "Get-TroubleshootingPack", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-AnswerFile <String>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-TroubleshootingPack", "CommandType": "Cmdlet", "ParameterSets": "[-Pack] <DiagPack> [-AnswerFile <String>] [-Result <String>] [-Unattended] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}], "ExportedAliases": []}, {"Name": "Microsoft.PowerShell.Core", "Version": "2.0", "ExportedCommands": [{"Name": "Export-Counter", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-FileFormat <String>] [-MaxSize <UInt32>] -InputObject <PerformanceCounterSampleSet[]> [-Force] [-Circular] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Counter", "CommandType": "Cmdlet", "ParameterSets": "[[-Counter] <String[]>] [-SampleInterval <Int32>] [-MaxSamples <Int64>] [-Continuous] [-ComputerName <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ListSet] <String[]> [-ComputerName <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-WinEvent", "CommandType": "Cmdlet", "ParameterSets": "[[-LogName] <String[]>] [-MaxEvents <Int64>] [-ComputerName <String>] [-Credential <PSCredential>] [-FilterXPath <String>] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ListLog] <String[]> [-ComputerName <String>] [-Credential <PSCredential>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ListProvider] <String[]> [-ComputerName <String>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ProviderName] <String[]> [-MaxEvents <Int64>] [-ComputerName <String>] [-Credential <PSCredential>] [-FilterXPath <String>] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Path] <String[]> [-MaxEvents <Int64>] [-Credential <PSCredential>] [-FilterXPath <String>] [-Oldest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-FilterXml] <XmlDocument> [-MaxEvents <Int64>] [-ComputerName <String>] [-Credential <PSCredential>] [-Oldest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-FilterHashtable] <Hashtable[]> [-MaxEvents <Int64>] [-ComputerName <String>] [-Credential <PSCredential>] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-Counter", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-StartTime <DateTime>] [-EndTime <DateTime>] [-Counter <String[]>] [-MaxSamples <Int64>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Path] <String[]> -ListSet <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Path] <String[]> [-Summary] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Connect-WSMan", "CommandType": "Cmdlet", "ParameterSets": "[[-ComputerName] <String>] [-ApplicationName <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-UseSSL] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ConnectionURI <Uri>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Disable-WSManCredSSP", "CommandType": "Cmdlet", "ParameterSets": "[-Role] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Disconnect-WSMan", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Enable-WSManCredSSP", "CommandType": "Cmdlet", "ParameterSets": "[-Role] <String> [[-DelegateComputer] <String[]>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-WSManCredSSP", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-WSManInstance", "CommandType": "Cmdlet", "ParameterSets": "[-ResourceURI] <Uri> [-ApplicationName <String>] [-ComputerName <String>] [-ConnectionURI <Uri>] [-Dialect <Uri>] [-Fragment <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SelectorSet <Hashtable>] [-SessionOption <SessionOption>] [-UseSSL] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ResourceURI] <Uri> [-ApplicationName <String>] [-BasePropertiesOnly] [-ComputerName <String>] [-ConnectionURI <Uri>] [-Dialect <Uri>] -Enumerate [-Filter <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-Associations] [-ReturnType <String>] [-SessionOption <SessionOption>] [-Shallow] [-UseSSL] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-WSManAction", "CommandType": "Cmdlet", "ParameterSets": "[-ResourceURI] <Uri> [-Action] <String> [[-SelectorSet] <Hashtable>] [-ConnectionURI <Uri>] [-FilePath <String>] [-OptionSet <Hashtable>] [-SessionOption <SessionOption>] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ResourceURI] <Uri> [-Action] <String> [[-SelectorSet] <Hashtable>] [-ApplicationName <String>] [-ComputerName <String>] [-FilePath <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-UseSSL] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-WSManInstance", "CommandType": "Cmdlet", "ParameterSets": "[-ResourceURI] <Uri> [-SelectorSet] <Hashtable> [-ApplicationName <String>] [-ComputerName <String>] [-FilePath <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-UseSSL] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ResourceURI] <Uri> [-SelectorSet] <Hashtable> [-ConnectionURI <Uri>] [-FilePath <String>] [-OptionSet <Hashtable>] [-SessionOption <SessionOption>] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-WSManSessionOption", "CommandType": "Cmdlet", "ParameterSets": "[-ProxyAccessType <ProxyAccessType>] [-ProxyAuthentication <ProxyAuthentication>] [-ProxyCredential <PSCredential>] [-Ski<PERSON><PERSON><PERSON><PERSON><PERSON>] [-Ski<PERSON><PERSON><PERSON><PERSON><PERSON>] [-Ski<PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>] [-SPNPort <Int32>] [-OperationTimeout <Int32>] [-NoEncryption] [-UseUTF16] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Remove-WSManInstance", "CommandType": "Cmdlet", "ParameterSets": "[-ResourceURI] <Uri> [-SelectorSet] <Hashtable> [-ApplicationName <String>] [-ComputerName <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-UseSSL] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ResourceURI] <Uri> [-SelectorSet] <Hashtable> [-ConnectionURI <Uri>] [-OptionSet <Hashtable>] [-SessionOption <SessionOption>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-WSManInstance", "CommandType": "Cmdlet", "ParameterSets": "[-ResourceURI] <Uri> [[-SelectorSet] <Hashtable>] [-ApplicationName <String>] [-ComputerName <String>] [-Dialect <Uri>] [-FilePath <String>] [-Fragment <String>] [-OptionSet <Hashtable>] [-Port <Int32>] [-SessionOption <SessionOption>] [-UseSSL] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ResourceURI] <Uri> [[-SelectorSet] <Hashtable>] [-ConnectionURI <Uri>] [-Dialect <Uri>] [-FilePath <String>] [-Fragment <String>] [-OptionSet <Hashtable>] [-SessionOption <SessionOption>] [-ValueSet <Hashtable>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-WSManQuickConfig", "CommandType": "Cmdlet", "ParameterSets": "[-UseSSL] [-Force] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Test-W<PERSON>an", "CommandType": "Cmdlet", "ParameterSets": "[[-ComputerName] <String>] [-Authentication <AuthenticationMechanism>] [-Port <Int32>] [-UseSSL] [-ApplicationName <String>] [-Credential <PSCredential>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Add-History", "CommandType": "Cmdlet", "ParameterSets": "[[-InputObject] <PSObject[]>] [-Passthru] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Add-PSSnapin", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PassThru] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Clear-History", "CommandType": "Cmdlet", "ParameterSets": "[[-Id] <Int32[]>] [[-Count] <Int32>] [-Newest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-Count] <Int32>] [-CommandLine <String[]>] [-Newest] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Disable-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-Force] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Enable-PSRemoting", "CommandType": "Cmdlet", "ParameterSets": "[-Force] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Enable-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-Force] [-SecurityDescriptorSddl <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Enter-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[-ComputerName] <String> [-Credential <PSCredential>] [-Port <Int32>] [-UseSSL] [-ConfigurationName <String>] [-ApplicationName <String>] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Session] <PSSession>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-ConnectionUri] <Uri>] [-Credential <PSCredential>] [-ConfigurationName <String>] [-AllowRedirection] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InstanceId <Guid>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Id] <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Exit-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Export-Console", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String>] [-<PERSON>] [-No<PERSON><PERSON>bber] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Export-ModuleMember", "CommandType": "Cmdlet", "ParameterSets": "[[-Function] <String[]>] [-Cmdlet <String[]>] [-Variable <String[]>] [-<PERSON><PERSON> <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ForEach-Object", "CommandType": "Cmdlet", "ParameterSets": "[-Process] <ScriptBlock[]> [-InputObject <PSObject>] [-Begin <ScriptBlock>] [-End <ScriptBlock>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Command", "CommandType": "Cmdlet", "ParameterSets": "[[-ArgumentList] <Object[]>] [-Verb <String[]>] [-Noun <String[]>] [-Module <String[]>] [-TotalCount <Int32>] [-Syntax] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String[]>] [[-ArgumentList] <Object[]>] [-Module <String[]>] [-CommandType <CommandTypes>] [-TotalCount <Int32>] [-Syntax] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Help", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String>] [-Path <String>] [-Category <String[]>] [-Component <String[]>] [-Functionality <String[]>] [-Role <String[]>] [-Full] [-Online] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String>] [-Path <String>] [-Category <String[]>] [-Component <String[]>] [-Functionality <String[]>] [-Role <String[]>] [-Detailed] [-Online] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String>] [-Path <String>] [-Category <String[]>] [-Component <String[]>] [-Functionality <String[]>] [-Role <String[]>] [-Examples] [-Online] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String>] [-Path <String>] [-Category <String[]>] [-Component <String[]>] [-Functionality <String[]>] [-Role <String[]>] [-Parameter <String>] [-Online] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-History", "CommandType": "Cmdlet", "ParameterSets": "[[-Id] <Int64[]>] [[-Count] <Int32>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Job", "CommandType": "Cmdlet", "ParameterSets": "[[-Id] <Int32[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-InstanceId] <Guid[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-State <JobState>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Command <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-<PERSON><PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-All] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String[]>] [-All] [-ListAvailable] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InstanceId <Guid[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Id] <Int32[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSSnapin", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-Registered] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-Mo<PERSON>le", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Global] [-Prefix <String>] [-Function <String[]>] [-Cmdlet <String[]>] [-Variable <String[]>] [-Alias <String[]>] [-Force] [-PassThru] [-AsCustomObject] [-Version <Version>] [-ArgumentList <Object[]>] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Assembly] <Assembly[]> [-Global] [-Prefix <String>] [-Function <String[]>] [-Cmdlet <String[]>] [-Variable <String[]>] [-Alias <String[]>] [-Force] [-PassThru] [-AsCustomObject] [-ArgumentList <Object[]>] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ModuleInfo] <PSModuleInfo[]> [-Global] [-Prefix <String>] [-Function <String[]>] [-Cmdlet <String[]>] [-Variable <String[]>] [-Alias <String[]>] [-Force] [-PassThru] [-AsCustomObject] [-ArgumentList <Object[]>] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-Command", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON><PERSON>] <ScriptBlock> [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Session] <PSSession[]>] [-ScriptBlock] <ScriptBlock> [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Session] <PSSession[]>] [-FilePath] <String> [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-ComputerName] <String[]>] [-FilePath] <String> [-Credential <PSCredential>] [-Port <Int32>] [-UseSSL] [-ConfigurationName <String>] [-ApplicationName <String>] [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-ComputerName] <String[]>] [-ScriptBlock] <ScriptBlock> [-Credential <PSCredential>] [-Port <Int32>] [-UseSSL] [-ConfigurationName <String>] [-ApplicationName <String>] [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-ConnectionUri] <Uri[]>] [-ScriptBlock] <ScriptBlock> [-Credential <PSCredential>] [-ConfigurationName <String>] [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-AllowRedirection] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-ConnectionUri] <Uri[]>] [-FilePath] <String> [-Credential <PSCredential>] [-ConfigurationName <String>] [-ThrottleLimit <Int32>] [-AsJob] [-HideComputerName] [-JobName <String>] [-AllowRedirection] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-History", "CommandType": "Cmdlet", "ParameterSets": "[[-Id] <String>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "New-<PERSON><PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON><PERSON>] <ScriptBlock> [-Function <String[]>] [-Cmdlet <String[]>] [-ReturnResult] [-AsCustomObject] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name] <String> [-ScriptBlock] <ScriptBlock> [-Function <String[]>] [-Cmdlet <String[]>] [-ReturnResult] [-AsCustomObject] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-ModuleManifest", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> -NestedModules <String[]> [-Guid <Guid>] -Author <String> -CompanyName <String> -Copyright <String> -ModuleToProcess <String> [-ModuleVersion <Version>] -Description <String> [-ProcessorArchitecture <ProcessorArchitecture>] [-PowerShellVersion <Version>] [-ClrVersion <Version>] [-DotNetFrameworkVersion <Version>] [-PowerShellHostName <String>] [-PowerShellHostVersion <Version>] [-RequiredModules <Object[]>] -TypesToProcess <String[]> -FormatsToProcess <String[]> [-ScriptsToProcess <String[]>] -RequiredAssemblies <String[]> -FileList <String[]> [-ModuleList <Object[]>] [-FunctionsToExport <String[]>] [-AliasesToExport <String[]>] [-VariablesToExport <String[]>] [-CmdletsToExport <String[]>] [-PrivateData <Object>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "New-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[[-ComputerName] <String[]>] [-Credential <PSCredential>] [-Name <String[]>] [-Port <Int32>] [-UseSSL] [-ConfigurationName <String>] [-ApplicationName <String>] [-ThrottleLimit <Int32>] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ConnectionUri] <Uri[]> [-Credential <PSCredential>] [-Name <String[]>] [-ConfigurationName <String>] [-ThrottleLimit <Int32>] [-AllowRedirection] [-SessionOption <PSSessionOption>] [-Authentication <AuthenticationMechanism>] [-CertificateThumbprint <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Session] <PSSession[]>] [-Name <String[]>] [-ThrottleLimit <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-PSSessionOption", "CommandType": "Cmdlet", "ParameterSets": "[-MaximumRedirection <Int32>] [-NoCompression] [-NoMachineProfile] [-Culture <CultureInfo>] [-UICulture <CultureInfo>] [-MaximumReceivedDataSizePerCommand <Int32>] [-MaximumReceivedObjectSize <Int32>] [-ApplicationArguments <PSPrimitiveDictionary>] [-OpenTimeout <Int32>] [-CancelTimeout <Int32>] [-IdleTimeout <Int32>] [-ProxyAccessType <ProxyAccessType>] [-ProxyAuthentication <AuthenticationMechanism>] [-ProxyCredential <PSCredential>] [-Ski<PERSON><PERSON><PERSON><PERSON><PERSON>] [-Ski<PERSON><PERSON><PERSON>heck] [-Skip<PERSON><PERSON><PERSON><PERSON>heck] [-OperationTimeout <Int32>] [-NoEncryption] [-UseUTF16] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Receive-Job", "CommandType": "Cmdlet", "ParameterSets": "[-Job] <Job[]> [[-Location] <String[]>] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Job] <Job[]> [[-ComputerName] <String[]>] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Job] <Job[]> [[-Session] <PSSession[]>] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String[]>] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-InstanceId] <Guid[]>] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Id] <Int32[]> [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Register-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-ProcessorArchitecture <String>] [-ApplicationBase <String>] [-ThreadApartmentState <ApartmentState>] [-ThreadOptions <PSThreadOptions>] [-StartupScript <String>] [-MaximumReceivedDataSizePerCommandMB <Nullable`1>] [-MaximumReceivedObjectSizeMB <Nullable`1>] [-SecurityDescriptorSddl <String>] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> [-AssemblyName] <String> [-ConfigurationTypeName] <String> [-ProcessorArchitecture <String>] [-ApplicationBase <String>] [-ThreadApartmentState <ApartmentState>] [-ThreadOptions <PSThreadOptions>] [-StartupScript <String>] [-MaximumReceivedDataSizePerCommandMB <Nullable`1>] [-MaximumReceivedObjectSizeMB <Nullable`1>] [-SecurityDescriptorSddl <String>] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-Job", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Job] <Job[]> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-Name] <String[]>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-InstanceId] <Guid[]>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-State <JobState>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Command <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-Module", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-ModuleInfo] <PSModuleInfo[]> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Session] <PSSession[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-InstanceId <Guid[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-ComputerName] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-PSSnapin", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PassThru] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-PSDebug", "CommandType": "Cmdlet", "ParameterSets": "[-Trace <Int32>] [-Step] [-Strict] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Off] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-ApplicationBase <String>] [-ThreadApartmentState <ApartmentState>] [-ThreadOptions <PSThreadOptions>] [-StartupScript <String>] [-MaximumReceivedDataSizePerCommandMB <Nullable`1>] [-MaximumReceivedObjectSizeMB <Nullable`1>] [-SecurityDescriptorSddl <String>] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> [-AssemblyName] <String> [-ConfigurationTypeName] <String> [-ApplicationBase <String>] [-ThreadApartmentState <ApartmentState>] [-ThreadOptions <PSThreadOptions>] [-StartupScript <String>] [-MaximumReceivedDataSizePerCommandMB <Nullable`1>] [-MaximumReceivedObjectSizeMB <Nullable`1>] [-SecurityDescriptorSddl <String>] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-StrictMode", "CommandType": "Cmdlet", "ParameterSets": "-Version <Version> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Off [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Start-Job", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON><PERSON>] <ScriptBlock> [[-InitializationScript] <ScriptBlock>] [-Name <String>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-RunAs32] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-FilePath] <String>] [[-InitializationScript] <ScriptBlock>] [-Name <String>] [-Credential <PSCredential>] [-Authentication <AuthenticationMechanism>] [-RunAs32] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Stop-Job", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Job] <Job[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-Name] <String[]>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-InstanceId] <Guid[]>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] [-State <JobState>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Test-ModuleManifest", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Unregister-PSSessionConfiguration", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-Force] [-NoServiceRestart] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Wait-Job", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-Any] [-Timeout <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Job] <Job[]> [-Any] [-Timeout <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Name] <String[]>] [-Any] [-Timeout <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-InstanceId] <Guid[]>] [-Any] [-Timeout <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Any] [-Timeout <Int32>] [-State <JobState>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Where-Object", "CommandType": "Cmdlet", "ParameterSets": "[-FilterScript] <ScriptBlock> [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Add-Member", "CommandType": "Cmdlet", "ParameterSets": "[-MemberType] <PSMemberTypes> [-Name] <String> [[-Value] <Object>] [[-SecondValue] <Object>] -InputObject <PSObject> [-Force] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Add-Type", "CommandType": "Cmdlet", "ParameterSets": "[-TypeDefinition] <String> [-Language <Language>] [-ReferencedAssemblies <String[]>] [-CodeDomProvider <CodeDomProvider>] [-CompilerParameters <CompilerParameters>] [-OutputAssembly <String>] [-OutputType <OutputAssemblyType>] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name] <String> [-MemberDefinition] <String[]> [-Namespace <String>] [-UsingNamespace <String[]>] [-Language <Language>] [-ReferencedAssemblies <String[]>] [-CodeDomProvider <CodeDomProvider>] [-CompilerParameters <CompilerParameters>] [-OutputAssembly <String>] [-OutputType <OutputAssemblyType>] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Path] <String[]> [-ReferencedAssemblies <String[]>] [-CompilerParameters <CompilerParameters>] [-OutputAssembly <String>] [-OutputType <OutputAssemblyType>] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -AssemblyName <String[]> [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Clear-Variable", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Force] [-PassThru] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Compare-Object", "CommandType": "Cmdlet", "ParameterSets": "[-ReferenceObject] <PSObject[]> [-DifferenceObject] <PSObject[]> [-SyncWindow <Int32>] [-Property <Object[]>] [-ExcludeDifferent] [-IncludeEqual] [-PassThru] [-Culture <String>] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertFrom-Csv", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject] <PSObject[]> [[-Delimiter] <Char>] [-Header <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject] <PSObject[]> -UseCulture [-Header <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertFrom-StringData", "CommandType": "Cmdlet", "ParameterSets": "[-StringData] <String> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertTo-Csv", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject] <PSObject> [[-Delimiter] <Char>] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject] <PSObject> [-UseCulture] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertTo-Html", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [[-Head] <String[]>] [[-Title] <String>] [[-Body] <String[]>] [-InputObject <PSObject>] [-As <String>] [-CssUri <Uri>] [-PostContent <String[]>] [-PreContent <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Property] <Object[]>] [-InputObject <PSObject>] [-As <String>] [-Fragment] [-PostContent <String[]>] [-PreContent <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertTo-Xml", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject] <PSObject> [-Depth <Int32>] [-NoTypeInformation] [-As <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Disable-PSBreakpoint", "CommandType": "Cmdlet", "ParameterSets": "[-Breakpoint] <Breakpoint[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Id] <Int32[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Enable-PSBreakpoint", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Breakpoint] <Breakpoint[]> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Export-Alias", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [[-Name] <String[]>] [-PassThru] [-As <ExportAliasFormat>] [-Append] [-Force] [-NoClobber] [-Description <String>] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Export-Clixml", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-Depth <Int32>] -InputObject <PSObject> [-Force] [-NoClobber] [-Encoding <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Export-Csv", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [[-Delimiter] <Char>] -InputObject <PSObject> [-Force] [-NoClobber] [-Encoding <String>] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Path] <String> -InputObject <PSObject> [-Force] [-NoClobber] [-Encoding <String>] [-UseCulture] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Export-FormatData", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject <ExtendedTypeDefinition[]>] [-Path <String>] [-Force] [-NoClobber] [-Include<PERSON>Block] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Export-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[-Session] <PSSession> [-OutputModule] <String> [[-CommandName] <String[]>] [[-FormatTypeName] <String[]>] [-Force] [-Encoding <String>] [-AllowClobber] [-ArgumentList <Object[]>] [-CommandType <CommandTypes>] [-Module <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Format-Custom", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-Depth <Int32>] [-GroupBy <Object>] [-View <String>] [-ShowError] [-DisplayError] [-Force] [-Expand <String>] [-InputObject <PSObject>] [-Verbos<PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Format-List", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-GroupBy <Object>] [-View <String>] [-ShowError] [-DisplayError] [-Force] [-Expand <String>] [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Format-Table", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-AutoSize] [-HideTableHeaders] [-Wrap] [-GroupBy <Object>] [-View <String>] [-ShowError] [-DisplayError] [-Force] [-Expand <String>] [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Format-Wide", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object>] [-AutoSize] [-Column <Int32>] [-GroupBy <Object>] [-View <String>] [-ShowError] [-DisplayError] [-Force] [-Expand <String>] [-InputObject <PSObject>] [-Verbos<PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "<PERSON>-<PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-Exclude <String[]>] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Exclude <String[]>] [-Scope <String>] [-Definition <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Culture", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Date", "CommandType": "Cmdlet", "ParameterSets": "[[-Date] <DateTime>] [-Year <Int32>] [-Month <Int32>] [-Day <Int32>] [-Hour <Int32>] [-Minute <Int32>] [-Second <Int32>] [-DisplayHint <DisplayHintType>] [-Format <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Date] <DateTime>] [-Year <Int32>] [-Month <Int32>] [-Day <Int32>] [-Hour <Int32>] [-Minute <Int32>] [-Second <Int32>] [-DisplayHint <DisplayHintType>] [-UFormat <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Event", "CommandType": "Cmdlet", "ParameterSets": "[[-SourceIdentifier] <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-EventIdentifier] <Int32> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-EventSubscriber", "CommandType": "Cmdlet", "ParameterSets": "[[-SourceIdentifier] <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-SubscriptionId] <Int32> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-FormatData", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String[]>] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Host", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Member", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-InputObject <PSObject>] [-MemberType <PSMemberTypes>] [-View <PSMemberViewTypes>] [-Static] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSBreakpoint", "CommandType": "Cmdlet", "ParameterSets": "[[-Script] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Type] <BreakpointType[]> [-Script <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Script <String[]>] -Variable <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Script <String[]>] -Command <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Id] <Int32[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSCallStack", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Random", "CommandType": "Cmdlet", "ParameterSets": "[[-Maximum] <Object>] [-SetSeed <Nullable`1>] [-Minimum <Object>] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject] <Object[]> [-SetSeed <Nullable`1>] [-Count <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-TraceSource", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-UICulture", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Unique", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject <PSObject>] [-AsString] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject <PSObject>] [-OnType] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Variable", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-ValueOnly] [-Include <String[]>] [-Exclude <String[]>] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Group-Object", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-NoElement] [-AsHashTable] [-AsString] [-InputObject <PSObject>] [-Culture <String>] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-<PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-Scope <String>] [-PassThru] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Import-Clixml", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-Csv", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [[-Delimiter] <Char>] [-Header <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Path] <String[]> -UseCulture [-Header <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-LocalizedData", "CommandType": "Cmdlet", "ParameterSets": "[-BindingVariable] <String> [[-UICulture] <String>] [-BaseDirectory <String>] [-FileName <String>] [-SupportedCommand <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Import-PSSession", "CommandType": "Cmdlet", "ParameterSets": "[-Session] <PSSession> [[-CommandName] <String[]>] [[-FormatTypeName] <String[]>] [-Prefix <String>] [-DisableNameChecking] [-AllowClobber] [-ArgumentList <Object[]>] [-CommandType <CommandTypes>] [-Module <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-Expression", "CommandType": "Cmdlet", "ParameterSets": "[-Command] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Measure-Command", "CommandType": "Cmdlet", "ParameterSets": "[-Expression] <ScriptBlock> [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Measure-Object", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <String[]>] [-InputObject <PSObject>] [-Sum] [-Average] [-Maximum] [-Minimum] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Property] <String[]>] [-InputObject <PSObject>] [-Line] [-Word] [-Character] [-IgnoreWhiteSpace] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "<PERSON>-<PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-Value] <String> [-Description <String>] [-Option <ScopedItemOptions>] [-PassThru] [-Scope <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "New-Event", "CommandType": "Cmdlet", "ParameterSets": "[-SourceIdentifier] <String> [[-Sender] <PSObject>] [[-EventArguments] <PSObject[]>] [[-MessageData] <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-Object", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON>Name] <String> [[-ArgumentList] <Object[]>] [-Property <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComObject] <String> [-Strict] [-Property <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-TimeSpan", "CommandType": "Cmdlet", "ParameterSets": "[[-Start] <DateTime>] [[-End] <DateTime>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Days <Int32>] [-Hours <Int32>] [-Minutes <Int32>] [-Seconds <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-Variable", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [[-Value] <Object>] [-Description <String>] [-Option <ScopedItemOptions>] [-Visibility <SessionStateEntryVisibility>] [-Force] [-PassThru] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Out-Default", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject <PSObject>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Out-File", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String> [[-Encoding] <String>] [-Append] [-Force] [-No<PERSON>lobber] [-Width <Int32>] [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Out-GridView", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject <PSObject>] [-Title <String>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Out-Host", "CommandType": "Cmdlet", "ParameterSets": "[-Paging] [-InputObject <PSObject>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Out-Null", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject <PSObject>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Out-Printer", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String>] [-InputObject <PSObject>] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Out-String", "CommandType": "Cmdlet", "ParameterSets": "[-Stream] [-Width <Int32>] [-InputObject <PSObject>] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Read-Host", "CommandType": "Cmdlet", "ParameterSets": "[[-Prompt] <Object>] [-AsSecureString] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Register-EngineEvent", "CommandType": "Cmdlet", "ParameterSets": "[-SourceIdentifier] <String> [[-Action] <ScriptBlock>] [-MessageData <PSObject>] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Register-ObjectEvent", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject] <PSObject> [-EventName] <String> [[-SourceIdentifier] <String>] [[-Action] <ScriptBlock>] [-MessageData <PSObject>] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Remove-Event", "CommandType": "Cmdlet", "ParameterSets": "[-SourceIdentifier] <String> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-EventIdentifier] <Int32> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-PSBreakpoint", "CommandType": "Cmdlet", "ParameterSets": "[-Breakpoint] <Breakpoint[]> [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Id] <Int32[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-Variable", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Select-Object", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-InputObject <PSObject>] [-ExcludeProperty <String[]>] [-ExpandProperty <String>] [-Unique] [-Last <Int32>] [-First <Int32>] [-Skip <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject <PSObject>] [-Unique] [-Index <Int32[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Select-String", "CommandType": "Cmdlet", "ParameterSets": "[-Pattern] <String[]> -InputObject <PSObject> [-SimpleMatch] [-CaseSensitive] [-Quiet] [-List] [-Include <String[]>] [-Exclude <String[]>] [-NotMatch] [-AllMatches] [-Encoding <String>] [-Context <Int32[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Pattern] <String[]> [-Path] <String[]> [-SimpleMatch] [-CaseSensitive] [-Quiet] [-List] [-Include <String[]>] [-Exclude <String[]>] [-NotMatch] [-AllMatches] [-Encoding <String>] [-Context <Int32[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Select-Xml", "CommandType": "Cmdlet", "ParameterSets": "[-XPath] <String> [-Xml] <XmlNode[]> [-Namespace <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-XPath] <String> [-Path] <String[]> [-Namespace <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-XPath] <String> -Content <String[]> [-Namespace <Hashtable>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Send-MailMessage", "CommandType": "Cmdlet", "ParameterSets": "[-To] <String[]> [-Subject] <String> [[-Body] <String>] [[-SmtpServer] <String>] [-Attachments <String[]>] [-Bcc <String[]>] [-BodyAsHtml] [-Encoding <Encoding>] [-Cc <String[]>] [-DeliveryNotificationOption <DeliveryNotificationOptions>] -From <String> [-Priority <MailPriority>] [-Credential <PSCredential>] [-UseSsl] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-Value] <String> [-Description <String>] [-Option <ScopedItemOptions>] [-PassThru] [-Scope <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-Date", "CommandType": "Cmdlet", "ParameterSets": "[-Date] <DateTime> [-DisplayHint <DisplayHintType>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Adjust] <TimeSpan> [-DisplayHint <DisplayHintType>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-PSBreakpoint", "CommandType": "Cmdlet", "ParameterSets": "[-Script] <String[]> [-Line] <Int32[]> [[-Column] <Int32>] [-Action <ScriptBlock>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Script] <String[]>] [-Action <ScriptBlock>] -Command <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Script] <String[]>] [-Action <ScriptBlock>] -Variable <String[]> [-Mode <VariableAccessMode>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-TraceSource", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [[-Option] <PSTraceSourceOptions>] [-ListenerOption <TraceOptions>] [-FilePath <String>] [-Force] [-Debugger] [-PSHost] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name] <String[]> [-RemoveListener <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name] <String[]> [-RemoveFileListener <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-Variable", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [[-Value] <Object>] [-Include <String[]>] [-Exclude <String[]>] [-Description <String>] [-Option <ScopedItemOptions>] [-Force] [-Visibility <SessionStateEntryVisibility>] [-PassThru] [-Scope <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Sort-Object", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <Object[]>] [-Descending] [-Unique] [-InputObject <PSObject>] [-Culture <String>] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Start-Sleep", "CommandType": "Cmdlet", "ParameterSets": "[-Seconds] <Int32> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Milliseconds <Int32> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Tee-Object", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String> [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-InputObject <PSObject>] -Variable <String> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Trace-Command", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Expression] <ScriptBlock> [[-Option] <PSTraceSourceOptions>] [-InputObject <PSObject>] [-ListenerOption <TraceOptions>] [-FilePath <String>] [-Force] [-Debugger] [-PSHost] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Name] <String[]> [-Command] <String> [[-Option] <PSTraceSourceOptions>] [-InputObject <PSObject>] [-ArgumentList <Object[]>] [-ListenerOption <TraceOptions>] [-FilePath <String>] [-Force] [-Debugger] [-PSHost] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Unregister-Event", "CommandType": "Cmdlet", "ParameterSets": "[-SourceIdentifier] <String> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-SubscriptionId] <Int32> [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Update-FormatData", "CommandType": "Cmdlet", "ParameterSets": "[[-AppendPath] <String[]>] [-PrependPath <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Update-List", "CommandType": "Cmdlet", "ParameterSets": "[[-Property] <String>] [-Add <Object[]>] [-Remove <Object[]>] [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Property] <String>] -Replace <Object[]> [-InputObject <PSObject>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Update-TypeData", "CommandType": "Cmdlet", "ParameterSets": "[[-AppendPath] <String[]>] [-PrependPath <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Wait-Event", "CommandType": "Cmdlet", "ParameterSets": "[[-SourceIdentifier] <String>] [-Timeout <Int32>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Debug", "CommandType": "Cmdlet", "ParameterSets": "[-Message] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Error", "CommandType": "Cmdlet", "ParameterSets": "[-Message] <String> [-Category <ErrorCategory>] [-ErrorId <String>] [-TargetObject <Object>] [-RecommendedAction <String>] [-CategoryActivity <String>] [-CategoryReason <String>] [-CategoryTargetName <String>] [-CategoryTargetType <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Exception <Exception> [-Message <String>] [-Category <ErrorCategory>] [-ErrorId <String>] [-TargetObject <Object>] [-RecommendedAction <String>] [-CategoryActivity <String>] [-CategoryReason <String>] [-CategoryTargetName <String>] [-CategoryTargetType <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -ErrorRecord <ErrorRecord> [-RecommendedAction <String>] [-CategoryActivity <String>] [-CategoryReason <String>] [-CategoryTargetName <String>] [-CategoryTargetType <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Host", "CommandType": "Cmdlet", "ParameterSets": "[[-Object] <Object>] [-<PERSON>New<PERSON>] [-Separator <Object>] [-ForegroundColor <ConsoleColor>] [-BackgroundColor <ConsoleColor>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Output", "CommandType": "Cmdlet", "ParameterSets": "[-InputObject] <PSObject[]> [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Progress", "CommandType": "Cmdlet", "ParameterSets": "[-Activity] <String> [-Status] <String> [[-Id] <Int32>] [-PercentComplete <Int32>] [-SecondsRemaining <Int32>] [-CurrentOperation <String>] [-ParentId <Int32>] [-Completed] [-SourceId <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Verbose", "CommandType": "Cmdlet", "ParameterSets": "[-Message] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-Warning", "CommandType": "Cmdlet", "ParameterSets": "[-Message] <String> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Start-Transcript", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String>] [-Append] [-<PERSON>] [-<PERSON><PERSON><PERSON><PERSON><PERSON>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Stop-Transcript", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Add-Computer", "CommandType": "Cmdlet", "ParameterSets": "[-DomainName] <String> [-Credential <PSCredential>] [-OUPath <String>] [-PassThru] [-Server <String>] [-UnSecure] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-WorkGroupName] <String> [-Credential <PSCredential>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Add-Content", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Value] <Object[]> [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding <FileSystemCmdletProviderEncoding>] [-LiteralPath] <String[]> [-Value] <Object[]> [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding <FileSystemCmdletProviderEncoding>]"}, {"Name": "Checkpoint-Computer", "CommandType": "Cmdlet", "ParameterSets": "[-Description] <String> [[-RestorePointType] <String>] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Clear-Content", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Clear-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String[]> [[-<PERSON>Name] <String[]>] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Clear-Item", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Clear-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Name] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Name] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Complete-Transaction", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Convert-Path", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [[-Destination] <String>] [-Container] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-PassThru] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [[-Destination] <String>] [-Container] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-PassThru] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Copy-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Destination] <String> [-Name] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Destination] <String> [-Name] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Debug-Process", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Verbos<PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Id] <Int32[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -InputObject <Process[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Disable-ComputerRestore", "CommandType": "Cmdlet", "ParameterSets": "[-Drive] <String[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Enable-ComputerRestore", "CommandType": "Cmdlet", "ParameterSets": "[-Drive] <String[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Get-ChildItem", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String[]>] [[-Filter] <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-Force] [-Name] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [[-Filter] <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-Force] [-Name] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-ComputerRestorePoint", "CommandType": "Cmdlet", "ParameterSets": "[[-RestorePoint] <Int32[]>] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -LastStatus [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Content", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-ReadCount <Int64>] [-TotalCount <Int64>] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Delimiter <String>] [-Wait] [-Encoding <FileSystemCmdletProviderEncoding>] [-LiteralPath] <String[]> [-ReadCount <Int64>] [-TotalCount <Int64>] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Delimiter <String>] [-Wait] [-Encoding <FileSystemCmdletProviderEncoding>]"}, {"Name": "Get-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String> [[-InstanceId] <Int64[]>] [-ComputerName <String[]>] [-Newest <Int32>] [-After <DateTime>] [-Before <DateTime>] [-UserName <String[]>] [-Index <Int32[]>] [-EntryType <String[]>] [-Source <String[]>] [-Message <String>] [-AsBaseObject] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComputerName <String[]>] [-List] [-AsString] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-HotFix", "CommandType": "Cmdlet", "ParameterSets": "[[-Id] <String[]>] [-ComputerName <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Description <String[]>] [-ComputerName <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Item", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [[-Name] <String[]>] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [[-Name] <String[]>] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-Location", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON> <String[]>] [-PSDrive <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Stack] [-StackName <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-Process", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-ComputerName <String[]>] [-Module] [-FileVersionInfo] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] -Id <Int32[]> [-ComputerName <String[]>] [-Module] [-FileVersionInfo] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComputerName <String[]>] [-Module] [-FileVersionInfo] -InputObject <Process[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PSDrive", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-Scope <String>] [-PS<PERSON><PERSON>ider <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralName] <String[]> [-Scope <String>] [-PSProvider <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-PSProvider", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON><PERSON>] <String[]>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Service", "CommandType": "Cmdlet", "ParameterSets": "[[-Name] <String[]>] [-ComputerName <String[]>] [-DependentServices] [-RequiredServices] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComputerName <String[]>] [-DependentServices] [-RequiredServices] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComputerName <String[]>] [-DependentServices] [-RequiredServices] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Transaction", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-WmiObject", "CommandType": "Cmdlet", "ParameterSets": "[-Class] <String> [[-Property] <String[]>] [-Filter <String>] [-Amended] [-DirectRead] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Class] <String>] [-Recurse] [-Amended] [-List] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Amended] [-DirectRead] -Query <String> [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Amended] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Amended] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Invoke-Item", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Invoke-WmiMethod", "CommandType": "Cmdlet", "ParameterSets": "[-Class] <String> [-Name] <String> [[-ArgumentList] <Object[]>] [-As<PERSON>ob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> -InputObject <ManagementObject> [-ArgumentList <Object[]>] [-AsJob] [-ThrottleLimit <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> -Path <String> [-ArgumentList <Object[]>] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Name] <String> [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Join-<PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Child<PERSON>ath] <String> [-Resolve] [-Credential <PSCredential>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Limit-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String[]> [-ComputerName <String[]>] [-RetentionDays <Int32>] [-OverflowAction <OverflowAction>] [-MaximumSize <Int64>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Move-Item", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [[-Destination] <String>] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-PassThru] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [[-Destination] <String>] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-PassThru] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Move-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Destination] <String> [-Name] <String[]> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Destination] <String> [-Name] <String[]> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "New-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String> [-Source] <String[]> [[-ComputerName] <String[]>] [-CategoryResourceFile <String>] [-MessageResourceFile <String>] [-ParameterResourceFile <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "New-<PERSON>em", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-ItemType <String>] [-Value <Object>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [[-Path] <String[]>] -Name <String> [-ItemType <String>] [-Value <Object>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "New-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Name] <String> [-PropertyType <String>] [-Value <Object>] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Name] <String> [-PropertyType <String>] [-Value <Object>] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "New-PSDrive", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-PS<PERSON><PERSON>ider] <String> [-Root] <String> [-Description <String>] [-Scope <String>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "New-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-BinaryPathName] <String> [-DisplayName <String>] [-Description <String>] [-StartupType <ServiceStartMode>] [-Credential <PSCredential>] [-DependsOn <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "New-WebServiceProxy", "CommandType": "Cmdlet", "ParameterSets": "[-Uri] <Uri> [[-Class] <String>] [[-Namespace] <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Uri] <Uri> [[-Class] <String>] [[-Namespace] <String>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Uri] <Uri> [[-Class] <String>] [[-Namespace] <String>] [-UseDefaultCredential] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Pop-Location", "CommandType": "Cmdlet", "ParameterSets": "[-PassThru] [-<PERSON>ack<PERSON><PERSON> <String>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Push-Location", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String>] [-PassThru] [-StackName <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [[-LiteralPath] <String>] [-PassThru] [-StackName <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Register-WmiEvent", "CommandType": "Cmdlet", "ParameterSets": "[-Class] <String> [[-SourceIdentifier] <String>] [[-Action] <ScriptBlock>] [-Namespace <String>] [-Credential <PSCredential>] [-ComputerName <String>] [-Timeout <Int64>] [-MessageData <PSObject>] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Query] <String> [[-SourceIdentifier] <String>] [[-Action] <ScriptBlock>] [-Namespace <String>] [-Credential <PSCredential>] [-ComputerName <String>] [-Timeout <Int64>] [-MessageData <PSObject>] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Remove-Computer", "CommandType": "Cmdlet", "ParameterSets": "[[-Credential] <PSCredential>] [-Force] [-PassThru] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String[]> [[-<PERSON>Name] <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [[-ComputerName] <String[]>] [-Source <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Remove-Item", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Recurse] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Remove-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Name] <String[]> [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Name] <String[]> [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Remove-PSDrive", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PS<PERSON><PERSON>ider <String[]>] [-Scope <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralName] <String[]> [-PSProvider <String[]>] [-Scope <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Remove-WmiObject", "CommandType": "Cmdlet", "ParameterSets": "[-Class] <String> [-As<PERSON>ob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -InputObject <ManagementObject> [-AsJob] [-ThrottleLimit <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -Path <String> [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Rename-<PERSON><PERSON>", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-NewName] <String> [-Force] [-PassThru] [-Credential <PSCredential>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Rename-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String> [-Name] <String> [-NewName] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String> [-Name] <String> [-NewName] <String> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Reset-ComputerMachinePassword", "CommandType": "Cmdlet", "ParameterSets": "[-Server <String>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Resolve-Path", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Relative] [-Credential <PSCredential>] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [-Relative] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Restart-Computer", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String[]>] [[-Credential] <PSCredential>] [-<PERSON><PERSON><PERSON>] [-Authentication <AuthenticationLevel>] [-Force] [-Impersonation <ImpersonationLevel>] [-ThrottleLimit <Int32>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Restart-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Force] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Force] [-PassThru] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Force] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Restore-Computer", "CommandType": "Cmdlet", "ParameterSets": "[-RestorePoint] <Int32> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Resume-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-Content", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Value] <Object[]> [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding <FileSystemCmdletProviderEncoding>] [-LiteralPath] <String[]> [-Value] <Object[]> [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Force] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding <FileSystemCmdletProviderEncoding>]"}, {"Name": "Set-<PERSON>em", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [[-Value] <Object>] [-Force] [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [[-Value] <Object>] [-Force] [-PassThru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Set-ItemProperty", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Name] <String> [-Value] <Object> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-Path] <String[]> -InputObject <PSObject> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> -InputObject <PSObject> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] <String[]> [-Name] <String> [-Value] <Object> [-PassThru] [-Force] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Set-Location", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String>] [-PassThru] [-Ver<PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String> [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-PassThru] [-StackName <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Set-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String> [-ComputerName <String[]>] [-DisplayName <String>] [-Description <String>] [-StartupType <ServiceStartMode>] [-Status <String>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-ComputerName <String[]>] [-DisplayName <String>] [-Description <String>] [-StartupType <ServiceStartMode>] [-Status <String>] [-InputObject <ServiceController>] [-PassThru] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-WmiInstance", "CommandType": "Cmdlet", "ParameterSets": "[-Class] <String> [[-Arguments] <Hashtable>] [-PutType <PutType>] [-As<PERSON>ob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -InputObject <ManagementObject> [-Arguments <Hashtable>] [-PutType <PutType>] [-AsJob] [-ThrottleLimit <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -Path <String> [-Arguments <Hashtable>] [-PutType <PutType>] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PutType <PutType>] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PutType <PutType>] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PutType <PutType>] [-AsJob] [-Impersonation <ImpersonationLevel>] [-Authentication <AuthenticationLevel>] [-Locale <String>] [-EnableAllPrivileges] [-Authority <String>] [-Credential <PSCredential>] [-ThrottleLimit <Int32>] [-ComputerName <String[]>] [-Namespace <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Show-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String>] [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Split-Path", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-LiteralPath <String[]>] [-Parent] [-Resolve] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Path] <String[]> [-Qualifier] [-LiteralPath <String[]>] [-Resolve] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Path] <String[]> [-LiteralPath <String[]>] [-NoQualifier] [-Resolve] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Path] <String[]> [-LiteralPath <String[]>] [-Leaf] [-Resolve] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-Path] <String[]> [-LiteralPath <String[]>] [-Resolve] [-IsAbsolute] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Start-Process", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String> [[-ArgumentList] <String[]>] [-Credential <PSCredential>] [-WorkingDirectory <String>] [-LoadUserProfile] [-NoNewWindow] [-PassThru] [-RedirectStandardError <String>] [-RedirectStandardInput <String>] [-RedirectStandardOutput <String>] [-Wait] [-UseNewEnvironment] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-FilePath] <String> [[-ArgumentList] <String[]>] [-WorkingDirectory <String>] [-PassThru] [-Verb <String>] [-Wait] [-WindowStyle <ProcessWindowStyle>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Start-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Start-Transaction", "CommandType": "Cmdlet", "ParameterSets": "[-Timeout <Int32>] [-Independent] [-RollbackPreference <RollbackSeverity>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Stop-Computer", "CommandType": "Cmdlet", "ParameterSets": "[[-<PERSON><PERSON><PERSON>] <String[]>] [[-Credential] <PSCredential>] [-<PERSON><PERSON><PERSON>] [-Authentication <AuthenticationLevel>] [-Impersonation <ImpersonationLevel>] [-ThrottleLimit <Int32>] [-Force] [-Verbos<PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Stop-Process", "CommandType": "Cmdlet", "ParameterSets": "[-Id] <Int32[]> [-PassThru] [-Force] [-Verbos<PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] -Name <String[]> [-PassThru] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] [-Force] -InputObject <Process[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Stop-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-Force] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Force] [-PassThru] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-Force] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Suspend-Service", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] -DisplayName <String[]> [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-PassThru] [-Include <String[]>] [-Exclude <String[]>] [-InputObject <ServiceController[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Test-ComputerSecureChannel", "CommandType": "Cmdlet", "ParameterSets": "[-Repair] [-Server <String>] [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Test-Connection", "CommandType": "Cmdlet", "ParameterSets": "[-ComputerName] <String[]> [[-Source] <String[]>] [-<PERSON><PERSON>ob] [-Authentication <AuthenticationLevel>] [-BufferSize <Int32>] [-Count <Int32>] [-Credential <PSCredential>] [-Impersonation <ImpersonationLevel>] [-ThrottleLimit <Int32>] [-TimeToLive <Int32>] [-Delay <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-ComputerName] <String[]> [[-Source] <String[]>] [-Authentication <AuthenticationLevel>] [-BufferSize <Int32>] [-Count <Int32>] [-Credential <PSCredential>] [-Impersonation <ImpersonationLevel>] [-TimeToLive <Int32>] [-Delay <Int32>] [-Quiet] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Test-Path", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-PathType <TestPathType>] [-IsValid] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction] [-LiteralPath] <String[]> [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-PathType <TestPathType>] [-IsValid] [-Credential <PSCredential>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Undo-Transaction", "CommandType": "Cmdlet", "ParameterSets": "[-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Use-Transaction", "CommandType": "Cmdlet", "ParameterSets": "[-TransactedScript] <ScriptBlock> [-Ver<PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Wait-Process", "CommandType": "Cmdlet", "ParameterSets": "[-Name] <String[]> [[-Timeout] <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-Id] <Int32[]> [[-Timeout] <Int32>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [[-Timeout] <Int32>] -InputObject <Process[]> [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Write-EventLog", "CommandType": "Cmdlet", "ParameterSets": "[-LogName] <String> [-Source] <String> [-EventId] <Int32> [[-EntryType] <EventLogEntryType>] [-Message] <String> [-Category <Int16>] [-RawData <Byte[]>] [-ComputerName <String>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertFrom-SecureString", "CommandType": "Cmdlet", "ParameterSets": "[-SecureString] <SecureString> [[-SecureKey] <SecureString>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-SecureString] <SecureString> [-Key <Byte[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "ConvertTo-SecureString", "CommandType": "Cmdlet", "ParameterSets": "[-String] <String> [[-Se<PERSON><PERSON><PERSON>] <SecureString>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-String] <String> [-AsPlainText] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-String] <String> [-Key <Byte[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Acl", "CommandType": "Cmdlet", "ParameterSets": "[[-Path] <String[]>] [-Audit] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-UseTransaction]"}, {"Name": "Get-AuthenticodeSignature", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-Credential", "CommandType": "Cmdlet", "ParameterSets": "[-Credential] <PSCredential> [-<PERSON><PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-ExecutionPolicy", "CommandType": "Cmdlet", "ParameterSets": "[[-Scope] <ExecutionPolicyScope>] [-List] [-V<PERSON><PERSON>e] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Get-PfxCertificate", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String[]> [-V<PERSON><PERSON><PERSON>] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>]"}, {"Name": "Set-Acl", "CommandType": "Cmdlet", "ParameterSets": "[-Path] <String[]> [-AclObject] <ObjectSecurity> [-Passthru] [-Filter <String>] [-Include <String[]>] [-Exclude <String[]>] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm] [-UseTransaction]"}, {"Name": "Set-AuthenticodeSignature", "CommandType": "Cmdlet", "ParameterSets": "[-FilePath] <String[]> [-Certificate] <X509Certificate2> [-<PERSON><PERSON><PERSON><PERSON><PERSON> <String>] [-TimestampServer <String>] [-HashAlgorithm <String>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}, {"Name": "Set-ExecutionPolicy", "CommandType": "Cmdlet", "ParameterSets": "[-ExecutionPolicy] <ExecutionPolicy> [[-Scope] <ExecutionPolicyScope>] [-Force] [-Verbose] [-Debug] [-ErrorAction <ActionPreference>] [-WarningAction <ActionPreference>] [-ErrorVariable <String>] [-WarningVariable <String>] [-OutVariable <String>] [-OutBuffer <Int32>] [-WhatIf] [-Confirm]"}], "ExportedAliases": "ise"}], "SchemaVersion": "0.0.1"}