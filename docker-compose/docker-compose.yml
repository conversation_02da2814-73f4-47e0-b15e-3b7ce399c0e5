name: immybot

services:
  # Use this service in CI
  server-from-prebuilt:
    build:
      context: ../
      dockerfile: ./docker-compose/prod.Dockerfile
      target: prebuilt
      ulimits:
        nofile:
          # NuGet apparently needs a lot of file handles. Increase our soft limit to 8192.
          soft: 8192
          hard: 16384
    init: true
    expose:
      - 5000
    ports:
      - 5000:5000
    env_file:
      - path: 'docker-variables.env'
        required: false
    environment:
      - ImmyAgentOptions__AppDistCloudStorageConnectionString=${APP_DIST_CLOUD_STORAGE_CONNECTION_STRING}
    depends_on:
      postgres:
        condition: service_healthy
      azurite:
        condition: service_started
      redis:
        condition: service_started
    profiles:
      - prebuilt

  server:
    build:
      context: ../backend-dist/
      dockerfile: ./docker-compose/prod.Dockerfile
      target: builder
      ulimits:
        nofile:
          # NuGet apparently needs a lot of file handles. Increase our soft limit to 8192.
          soft: 8192
          hard: 16384
    init: true
    expose:
      - 5000
    ports:
      - 5000:5000
    env_file:
      - path: 'docker-variables.env'
        required: false
    depends_on:
      postgres:
        condition: service_healthy
      azurite:
        condition: service_started
      redis:
        condition: service_started
    profiles:
      - prebuilt

  playwright-prebuilt:
    build:
      context: ../
      dockerfile:  ./docker-compose/prod.Dockerfile
      target: playwright
    depends_on:
      server-from-prebuilt:
        condition: service_healthy
    profiles:
      - prebuilt
    environment:
      - VITE_PLAYWRIGHT_BASE_URL=http://server-from-prebuilt:5000
      - VITE_BACKEND_URI=http://server-from-prebuilt:5000
      - CI=true

  playwright:
    build:
      context: ../
      dockerfile: ./docker-compose/prod.Dockerfile
      target: playwright
    depends_on:
      server:
        condition: service_healthy
    profiles:
      - prebuilt
    environment:
      - VITE_PLAYWRIGHT_BASE_URL=http://server:5000
      - VITE_BACKEND_URI=http://server:5000
      - CI=true

  immybot.backend.web:
    image: immybot.backend.web
    build:
      context: ../
      dockerfile: backend/Immybot.Backend.Web/Dockerfile
      args:
        GITHUB_USER: $GITHUB_USER
        GITHUB_TOKEN: $GITHUB_TOKEN
    env_file:
      - path: 'docker-variables.env'
        required: false
    ports:
      - 5000:5000
    depends_on:
      postgres:
        condition: service_healthy
      azurite:
        condition: service_started
      redis:
        condition: service_started

  redis:
    image: redis:6.2-alpine
    expose:
      - 6379
    ports:
      - 6379:6379
    volumes:
      - redis:/data

  redis-no-ports:
    image: redis:6.2-alpine
    expose:
      - 6379
    volumes:
      - e2eredis:/data

  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:latest
    command: "azurite --loose --blobHost 0.0.0.0 --skipApiVersionCheck"
    expose: [10000, 10001, 10002]
    ports:
      - 10000:10000
      - 10001:10001
      - 10002:10002
    volumes:
      - azurite:/data

  azurite-no-ports:
    image: mcr.microsoft.com/azure-storage/azurite:latest
    command: "azurite --loose --blobHost 0.0.0.0 --skipApiVersionCheck"
    expose:
      - 10000
    volumes:
      - e2eazurite:/data

  postgres:
    image: timescale/timescaledb:latest-pg12
    environment:
      - "POSTGRES_USER=postgres"
      - "POSTGRES_PASSWORD=password"
    volumes:
      - db:/var/lib/postgresql/data
    expose:
      - 5432
    ports:
      - 5432:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-no-ports:
    image: timescale/timescaledb:latest-pg12
    environment:
      - "POSTGRES_USER=postgres"
      - "POSTGRES_PASSWORD=password"
    volumes:
      - e2edb:/var/lib/postgresql/data
    expose:
      - 5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5

  db-migrator:
    image: timescale/timescaledb:latest-pg12
    environment:
      - "PGHOST=postgres"
      - "PGPORT=5432"
      - "PGUSER=postgres"
      - "PGPASSWORD=password"
    command: bash -xc "createdb -D pg_default -O postgres -E UTF8 'immybot';
      createdb -D pg_default -O postgres -E UTF8 'immybot-global';
      printf 'CREATE EXTENSION IF NOT EXISTS pg_trgm CASCADE;\\nCREATE EXTENSION IF NOT EXISTS intarray CASCADE;\\nCREATE EXTENSION IF NOT EXISTS \"uuid-ossp\" CASCADE;' > /t.txt;
      cat /t.txt;
      psql -q -d immybot -f /t.txt;
      psql -q -d immybot-global -f /t.txt;"
    profiles:
      - tools
    depends_on:
      postgres:
        condition: service_healthy

  # This is only necessary when building for production.
  # Use this service to generate local.sql and global.sql migration files from the persistance projects.
  db-migration-generator:
    build:
      context: ../
      dockerfile: ./docker-compose/prod.Dockerfile
      target: backend-builder
      ulimits:
        nofile:
          # NuGet apparently needs a lot of file handles. Increase our soft limit to 8192.
          soft: 8192
          hard: 16384
    command: sh -c "dotnet tool restore;
      dotnet ef migrations script --no-transactions --configuration RELEASE --no-build --context ImmybotDbContext --project ./backend/Immybot.Backend.Persistence/Immybot.Backend.Persistence.csproj --startup-project ./backend/Immybot.Backend.Web/Immybot.Backend.Web.csproj --output /local.sql --idempotent;
      dotnet ef migrations script --no-transactions --configuration RELEASE --no-build --context SoftwareDbContext --project ./backend/Immybot.Backend.GlobalSoftwarePersistence/Immybot.Backend.GlobalSoftwarePersistence.csproj --startup-project ./backend/Immybot.Backend.Web/Immybot.Backend.Web.csproj --output /global.sql --idempotent;"
    profiles:
      - tools

volumes:
  e2edb:
  e2eredis:
  e2eazurite:
  db:
  redis:
  azurite:

networks:
  immybot:
